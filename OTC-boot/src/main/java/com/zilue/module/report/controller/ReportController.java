package com.zilue.module.report.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.enums.YesOrNoEnum;
import com.zilue.common.model.result.R;
import com.zilue.common.page.ConventPage;
import com.zilue.common.page.PageOutput;
import com.zilue.common.utils.VoToColumnUtil;
import com.zilue.module.report.dto.ReportListDto;
import com.zilue.module.report.dto.ReportPageDto;
import com.zilue.module.report.entity.ProfessionalReport;
import com.zilue.module.report.entity.Report;
import com.zilue.module.report.service.IProfessionalReportService;
import com.zilue.module.report.service.IReportService;
import com.zilue.module.report.vo.ReportPageVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-23
 */
@RestController
@RequestMapping(GlobalConstant.REPORT_MODULE_PREFIX)
@Api(value = GlobalConstant.REPORT_MODULE_PREFIX,tags = "报表模块")
@AllArgsConstructor
public class ReportController {

    private final IReportService reportService;

    private final IProfessionalReportService professionalReportService;

    @GetMapping(value = "/page")
    @ApiOperation(value="ureport 报表列表(分页)")
    public R page(@Valid ReportPageDto dto) {
        LambdaQueryWrapper<Report> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(dto.getKeyword()), Report::getName,dto.getKeyword())
                    .select(Report.class,x -> VoToColumnUtil.fieldsToColumns(ReportPageVo.class).contains(x.getProperty()))
                    .orderByDesc(StrUtil.isBlank(dto.getField()), Report::getCreateDate);

        IPage<Report> page = reportService.page(ConventPage.getPage(dto), queryWrapper);
        PageOutput<ReportPageVo> pageOutput = ConventPage.getPageOutput(page, ReportPageVo.class);
        return R.ok(pageOutput);
    }

    @GetMapping(value = "/list")
    @ApiOperation(value="ureport 报表列表(不分页)")
    public R list(@Valid ReportListDto dto) {
        LambdaQueryWrapper<Report> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StrUtil.isNotBlank(dto.getKeyword()), Report::getName,dto.getKeyword())
                .select(Report.class,x -> VoToColumnUtil.fieldsToColumns(ReportPageVo.class).contains(x.getProperty()))
                .orderByAsc(Report::getSortCode);

        List<Report> list = reportService.list(queryWrapper);
        List<ReportPageVo> reportPageVos = BeanUtil.copyToList(list, ReportPageVo.class);
        for (ReportPageVo reportPageVo : reportPageVos) {
            reportPageVo.setDataType(YesOrNoEnum.NO.getCode());
        }

        LambdaQueryWrapper<ProfessionalReport> professionalReportLambdaQueryWrapper = new LambdaQueryWrapper<>();
        professionalReportLambdaQueryWrapper.eq(StrUtil.isNotBlank(dto.getKeyword()), ProfessionalReport::getName,dto.getKeyword())
                .select(ProfessionalReport.class,x -> VoToColumnUtil.fieldsToColumns(ReportPageVo.class).contains(x.getProperty()));
        List<ProfessionalReport> professionalReportList = professionalReportService.list(professionalReportLambdaQueryWrapper);
        List<ReportPageVo> professionalReportVo = BeanUtil.copyToList(professionalReportList, ReportPageVo.class);
        for (ReportPageVo reportPageVo : professionalReportVo) {
            reportPageVo.setDataType(YesOrNoEnum.YES.getCode());
        }
        reportPageVos.addAll(professionalReportVo);
        return R.ok(reportPageVos);
    }

    @DeleteMapping
    @ApiOperation(value = "删除")
    public R delete(@Valid @RequestBody List<Long> ids){
        return R.ok(reportService.removeBatchByIds(ids));
    }



}
