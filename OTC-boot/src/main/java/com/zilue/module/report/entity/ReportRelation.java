package com.zilue.module.report.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 报表菜单关联关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-23
 */
@TableName("xjr_report_relation")
@ApiModel(value = "ReportRelation对象", description = "报表菜单关联关系表")
@Data
public class ReportRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键值")
    private Long id;

    @ApiModelProperty("菜单主键值")
    private Long menuId;

    @ApiModelProperty("报表文件主键值")
    private Long reportId;


}
