package com.zilue.module.report.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date: 2023/11/1 16:40
 */
@Data
public class UpdateProfessionalReportDto {
    private Long id;
    @ApiModelProperty("名称")
    @NotBlank(message = "菜单名不能为空！")
    private String name;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("报表类型id")
    private Long typeId;

    @ApiModelProperty("报表类型值")
    private String typeValue;

    @ApiModelProperty("文件json")
    private String fileJson;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("排序")
    private Integer sortCode;
}
