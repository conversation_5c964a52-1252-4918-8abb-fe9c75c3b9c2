package com.zilue.module.desktop.service.impl;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.zilue.common.enums.YesOrNoEnum;
import com.zilue.module.desktop.dto.SetActivityDto;
import com.zilue.module.desktop.entity.DesktopHistory;
import com.zilue.module.desktop.entity.DesktopSchema;
import com.zilue.module.desktop.mapper.DesktopHistoryMapper;
import com.zilue.module.desktop.mapper.DesktopSchemaMapper;
import com.zilue.module.desktop.service.IDesktopHistoryService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Service
@AllArgsConstructor
public class DesktopHistoryServiceImpl extends MPJBaseServiceImpl<DesktopHistoryMapper, DesktopHistory> implements IDesktopHistoryService {

    private final DesktopSchemaMapper desktopSchemaMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setActivity(SetActivityDto dto) {
        DesktopHistory history = getById(dto.getId());
        history.setActivityFlag(YesOrNoEnum.YES.getCode());

        DesktopSchema desktopSchema = desktopSchemaMapper.selectById(history.getSchemaId());

        desktopSchema.setJsonContent(history.getJsonContent());

        updateById(history);

        desktopSchemaMapper.updateById(desktopSchema);
        return true;
    }
}
