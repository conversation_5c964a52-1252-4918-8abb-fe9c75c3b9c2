package com.zilue.module.desktop.dto;

import com.zilue.common.page.PageInput;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 2023/9/19 10:06
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ComplexPageDto extends PageInput {

    @ApiModelProperty("表单id")
    @NotNull(message = "表单id")
    private Long formId;

    @ApiModelProperty("发布id")
    private Long releaseId;

    /**
     * 自定义表单 查询参数
     */
    private Map<String,Object> params;

    /**
     * 导出配置（是否是导出模板）
     */
    private Boolean isTemplate = false;

    /**
     * 是否翻译为显示值
     */
    private Boolean isTrans  = true;
}
