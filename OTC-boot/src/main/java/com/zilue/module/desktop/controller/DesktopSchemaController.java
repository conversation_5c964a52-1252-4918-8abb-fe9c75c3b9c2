package com.zilue.module.desktop.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zilue.common.annotation.XjrLog;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.enums.EnabledMark;
import com.zilue.common.enums.YesOrNoEnum;
import com.zilue.common.model.result.R;
import com.zilue.common.page.ConventPage;
import com.zilue.common.page.PageOutput;
import com.zilue.common.utils.VoToColumnUtil;
import com.zilue.module.desktop.dto.*;
import com.zilue.module.desktop.entity.DesktopSchema;
import com.zilue.module.desktop.service.IDesktopSchemaService;
import com.zilue.module.desktop.vo.DesktopSchemaInfoVo;
import com.zilue.module.desktop.vo.DesktopSchemaPageVo;
import com.zilue.module.form.service.IFormExecuteService;
import com.zilue.module.system.entity.Menu;
import com.zilue.module.system.service.IMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 桌面设计历史记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */

@RestController
@RequestMapping(GlobalConstant.DESKTOP_MODULE_PREFIX + "/schema")
@Api(value = GlobalConstant.SYSTEM_MODULE_PREFIX + "/schema", tags = "桌面设计模块接口")
@AllArgsConstructor
public class DesktopSchemaController {

    private final IDesktopSchemaService desktopSchemaService;

    private final IMenuService menuService;

    private final IFormExecuteService formExecuteService;

    @GetMapping(value = "/page")
    @ApiOperation(value = "桌面设计列表(分页)")
    public R page(@Valid DesktopSchemaPageDto dto) {

        LambdaQueryWrapper<DesktopSchema> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .like(StrUtil.isNotBlank(dto.getKeyword()), DesktopSchema::getCode, dto.getKeyword())
                .or()
                .like(StrUtil.isNotBlank(dto.getKeyword()), DesktopSchema::getName, dto.getKeyword())
                .select(DesktopSchema.class, x -> VoToColumnUtil.fieldsToColumns(DesktopSchemaPageVo.class).contains(x.getProperty()));
        IPage<DesktopSchema> page = desktopSchemaService.page(ConventPage.getPage(dto), queryWrapper);
        PageOutput<DesktopSchemaPageVo> pageOutput = ConventPage.getPageOutput(page, DesktopSchemaPageVo.class);
        return R.ok(pageOutput);
    }

    @GetMapping(value = "/enabled-page")
    @ApiOperation(value = "桌面设计列表(分页, 有效标记为1，不是默认首页)")
    public R enabledPage(@Valid DesktopSchemaPageDto dto) {

        LambdaQueryWrapper<DesktopSchema> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .like(StrUtil.isNotBlank(dto.getKeyword()), DesktopSchema::getCode, dto.getKeyword())
                .or()
                .like(StrUtil.isNotBlank(dto.getKeyword()), DesktopSchema::getName, dto.getKeyword())
                .select(DesktopSchema.class, x -> VoToColumnUtil.fieldsToColumns(DesktopSchemaPageVo.class).contains(x.getProperty()))
                .eq(DesktopSchema::getIsFirst, YesOrNoEnum.NO.getCode())
                .eq(DesktopSchema::getEnabledMark, EnabledMark.ENABLED.getCode());
        IPage<DesktopSchema> page = desktopSchemaService.page(ConventPage.getPage(dto), queryWrapper);
        PageOutput<DesktopSchemaPageVo> pageOutput = ConventPage.getPageOutput(page, DesktopSchemaPageVo.class);
        return R.ok(pageOutput);
    }

    @GetMapping(value = "/info")
    @ApiOperation(value = "桌面设计详情")
    public R info(@RequestParam Long id){
        DesktopSchema desktopSchema = desktopSchemaService.getById(id);
        DesktopSchemaInfoVo infoVo = BeanUtil.toBean(desktopSchema, DesktopSchemaInfoVo.class);
        Integer isMenu = desktopSchema.getIsMenu();
        if (isMenu != null && isMenu == 1) {
            Menu menu = menuService.getById(desktopSchema.getMenuId());
            if (menu == null) {
                infoVo.setIsMenu(0);
                infoVo.setMenuId(0L);
            } else {
                infoVo.setParentId(menu.getParentId());
                infoVo.setIcon(menu.getIcon());
            }
        }
        return R.ok(infoVo);
    }

    @PostMapping
    @ApiOperation(value = "桌面设计新增")
    public R add(@Valid @RequestBody AddDesktopSchemaDto dto){
        return R.ok(desktopSchemaService.add(dto));
    }

    @PostMapping("/copy")
    @ApiOperation(value = "复制桌面设计")
    public R copy(@Valid @RequestBody CopyDesktopSchemaDto dto){
        return R.ok(desktopSchemaService.copy(dto));
    }


    @PutMapping
    @ApiOperation(value = "桌面设计修改")
    public R update(@Valid @RequestBody UpdateDesktopSchemaDto dto){
        return R.ok(desktopSchemaService.update(dto));
    }

    @PutMapping("/set-default")
    @ApiOperation(value = "设置桌面设计为默认首页")
    public R setFirst(@Valid @RequestBody SetFirstDto dto){
        return R.ok(desktopSchemaService.setFirst(dto));
    }



    @DeleteMapping
    @ApiOperation(value = "桌面设计删除")
    public R delete(@RequestBody List<Long> ids){
        return R.ok(desktopSchemaService.delete(ids));
    }

    @GetMapping("/export")
    @ApiOperation(value = "导出模板")
    @SneakyThrows
    public R export(@RequestParam Long id) {
        DesktopSchema desktopSchema = desktopSchemaService.getById(id);
        return R.ok(JSONUtil.toJsonStr(desktopSchema));
    }

    @PostMapping("/import")
    @ApiOperation(value = "导入模板")
    @SneakyThrows
    public R importSchema(@RequestParam(value = "file") MultipartFile multipartFile) {
        return R.ok(desktopSchemaService.importDesktopSchema(multipartFile));
    }

    @PutMapping("/enabled")
    @ApiOperation(value = "启用/禁用")
    public R enabled(@Valid @RequestBody DesktopEnabledDto dto) {
        DesktopSchema desktopSchema = new DesktopSchema();
        desktopSchema.setId(dto.getId());
        desktopSchema.setEnabledMark(dto.getEnabledMark());
        return R.ok(desktopSchemaService.updateById(desktopSchema));

    }

    @PutMapping("/first")
    @ApiOperation(value = "设置首页")
    public R setFirst(@Valid @RequestBody DesktopFirstDto dto) {
        DesktopSchema desktopSchema = new DesktopSchema();
        desktopSchema.setId(dto.getId());
        desktopSchema.setIsFirst(dto.getIsFirst());
        if (dto.getIsFirst() != null && dto.getIsFirst().equals(1)) {
            // 将其他桌面设计设置为非首页
            desktopSchemaService.update(Wrappers.<DesktopSchema>update().lambda().set(DesktopSchema::getIsFirst, 0).eq(DesktopSchema::getIsFirst, 1));
        }
        return R.ok(desktopSchemaService.updateById(desktopSchema));

    }

    @PostMapping("/complex-add")
    @ApiOperation(value = "桌面设计-复杂列表页-新增(公共接口)")
    public R complexAdd(@Valid @RequestBody AddDeskComplexDto dto){
        return R.ok(formExecuteService.complexAdd(dto));
    }

    @PutMapping("/complex-update")
    @ApiOperation(value = "桌面设计-复杂列表页-编辑(公共接口)")
    public R complexUpdate(@Valid @RequestBody UpdateDeskComplexDto dto){
        return R.ok(formExecuteService.complexUpdate(dto));
    }

    @DeleteMapping("/complex-delete")
    @ApiOperation(value = "桌面设计-复杂列表页-删除(公共接口)")
    public R complexDelete(@Valid @RequestBody DeleteDeskComplexDto dto){
        return R.ok(formExecuteService.complexDelete(dto));
    }
    @PostMapping(value = "/complex-info")
    @ApiOperation(value = "桌面设计-复杂列表页-数据(公共接口)")
    public R complexInfo(@Valid @RequestBody DeskComplexInfoDto dto) {
        return R.ok(formExecuteService.complexInfo(dto));
    }

    @GetMapping("/get-release-info")
    @ApiOperation(value = "桌面设计-复杂列表页-根据表单id查询发布信息")
    @SneakyThrows
    public R getReleaseInfo(@RequestParam Long formId) {
        return R.ok(formExecuteService.getReleaseInfo(formId));
    }

    @PostMapping("/complex-query")
    @ApiOperation(value = "桌面设计-复杂列表页-根据发布id获取查询配置数据")
    @SneakyThrows
    public R complexQuery(@Valid  @RequestBody ComplexQueryDto dto) {
        return R.ok(formExecuteService.complexQuery(dto));
    }

    /**
     * 桌面设计-复杂列表页-获取列表数据 分页
     *
     * @param dto
     */
    @PostMapping(value = "/complex-page")
    @ApiOperation(value = "桌面设计-复杂列表页-获取列表数据 分页")
    @XjrLog(value = "桌面设计-复杂列表页-获取列表数据 分页")
    public R complexPage(@Valid  @RequestBody ComplexPageDto dto) {
        return R.ok(formExecuteService.complexPage(dto));
    }

    @GetMapping("/get-table-info")
    @ApiOperation(value = "桌面设计-复杂列表页-根据表单id查询表单信息")
    @SneakyThrows
    public R getTableInfo(@RequestParam Long formId) {
        return R.ok(formExecuteService.getTableInfo(formId));
    }

}
