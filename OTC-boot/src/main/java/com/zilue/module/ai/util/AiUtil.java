package com.zilue.module.ai.util;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.zilue.module.ai.bean.AiOcrData;
import com.zilue.module.ai.bean.AiOcrDataV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @program: OTC-boot
 * @description: ai相关的工具类
 * @packagename: com.zilue.module.ai.util
 * @author: 曹凌
 * @date: 2025-03-25 11:27
 **/
@Slf4j
public class AiUtil {

    private static String BASE64_PREFIX = "data:image/jpeg;base64,";
    private static String apiKey;
    private static String apiModel;

    public static String getApiKey() {
        return apiKey;
    }

    public static void setApiKey(String apiKey) {
        AiUtil.apiKey = apiKey;
    }

    public static String getApiModel() {
        return apiModel;
    }

    public static void setApiModel(String apiModel) {
        AiUtil.apiModel = apiModel;
    }

    public static List<AiOcrData> getOcrData(MultipartFile file) {
        if (ObjectUtil.isNotNull(file)) {
            try {
                return getOcrData(BASE64_PREFIX + Base64.encode(file.getBytes()));
            } catch (IOException e) {
                log.error("文件解析异常", e);
            }
        }
        return CollUtil.newArrayList();
    }


    public static List<AiOcrData> getOcrData(InputStream inputStream) {
        String base64 = BASE64_PREFIX + Base64.encode(IoUtil.readBytes(inputStream));
        return getOcrData(base64);
    }

    private static List<AiOcrData> getOcrData(String base64Image) {
        if (!StrUtil.startWith(base64Image, BASE64_PREFIX)) {
            base64Image = BASE64_PREFIX + base64Image;
        }
        List<AiOcrData> res = CollUtil.newArrayList();
        Map<String, String> params = new HashMap<>();
        String template = "{\"model\":\"{model}\",\"messages\":[{\"role\":\"user\",\"content\":[{\"type\":\"image_url\",\"image_url\":{\"url\":\"{imgUrl}\"}}]},{\"role\":\"system\",\"content\":[{\"type\":\"text\",\"text\":\"{prompt}\"}]}]}\n";
        params.put("imgUrl", base64Image);
        params.put("model", getApiModel());
        params.put("prompt", "只返回小票中的药品名称，数量，单价，按照{name ,number,price} 格式返回，其它的数据都不返回，num为整数，不要小数点。返回结果如果有药品名称重复的，就只返回第二个结果。");
        String reqParam = StrUtil.format(template, params);
        try (HttpResponse ocrResp = HttpUtil.createPost("https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions")
                .header("Authorization", "Bearer " + getApiKey())
                .header("Content-Type", "application/json")
                .body(reqParam).execute()) {
            String body = ocrResp.body();
            Pattern pattern = Pattern.compile("```json\\\\n(.*?)\\\\n```", Pattern.DOTALL);
            Matcher matcher = pattern.matcher(body);
            if (matcher.find()) {
                // 获取匹配的内容并处理转义字符
                String escapedJson = matcher.group(1);
                String jsonStr = escapedJson.replace("\\\\", "\\")
                        .replace("\\n", "\n")
                        .replace("\\\"", "\"");
                return JSONUtil.toList(jsonStr, AiOcrData.class);
            }
        } catch (Exception e) {
            log.error("请求模型失败或解析失败", e);
        }
        return res;
    }

    public static List<AiOcrData> getOcrDataByUrl(String picUrl) {
        List<AiOcrData> res = CollUtil.newArrayList();
        Map<String, String> params = new HashMap<>();
        String template = "{\"model\":\"{model}\",\"messages\":[{\"role\":\"user\",\"content\":[{\"type\":\"image_url\",\"image_url\":{\"url\":\"{imgUrl}\"}}]},{\"role\":\"system\",\"content\":[{\"type\":\"text\",\"text\":\"{prompt}\"}]}]}\n";
        params.put("imgUrl", picUrl);
        params.put("model", getApiModel());
        params.put("prompt", "只返回小票中的药品名称，数量，单价，按照{name ,number,price} 格式返回，其它的数据都不返回，num为整数，不要小数点。返回结果如果有药品名称重复的，就只返回第二个结果。");
        String reqParam = StrUtil.format(template, params);
        try (HttpResponse ocrResp = HttpUtil.createPost("https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions")
                .header("Authorization", "Bearer " + getApiKey())
                .header("Content-Type", "application/json")
                .body(reqParam).execute()) {
            String body = ocrResp.body();
            Pattern pattern = Pattern.compile("```json\\\\n(.*?)\\\\n```", Pattern.DOTALL);
            Matcher matcher = pattern.matcher(body);
            if (matcher.find()) {
                // 获取匹配的内容并处理转义字符
                String escapedJson = matcher.group(1);
                String jsonStr = escapedJson.replace("\\\\", "\\")
                        .replace("\\n", "\n")
                        .replace("\\\"", "\"");
                return JSONUtil.toList(jsonStr, AiOcrData.class);
            }
        } catch (Exception e) {
            log.error("请求模型失败或解析失败", e);
        }
        return res;
    }

    public static List<AiOcrDataV2> getOcrDataByUrlV2(List<String> picUrlList) {
        if (CollUtil.isEmpty(picUrlList)) {
            return CollUtil.newArrayList();
        }

        // 使用线程安全的队列收集结果
        ConcurrentLinkedQueue<AiOcrDataV2> resultQueue = new ConcurrentLinkedQueue<>();

        // 创建CompletableFuture任务列表
        List<CompletableFuture<Void>> futures = picUrlList.stream()
                .map(picUrl -> CompletableFuture.runAsync(() -> {
                    try {
                        AiOcrDataV2 result = processOcrForSingleUrl(picUrl);
                        if (ObjectUtil.isNotNull(result)) {
                            resultQueue.offer(result);
                        }
                    } catch (Exception e) {
                        log.error("处理图片URL失败: {}", picUrl, e);
                    }
                }))
                .collect(Collectors.toList());

        // 等待所有任务完成，设置超时时间为5分钟
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                    .get(5, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.error("等待OCR任务完成时发生异常", e);
        }

        // 将结果转换为List并返回
        return CollUtil.newArrayList(resultQueue);
    }

    /**
     * 处理单个图片URL的OCR识别
     * @param picUrl 图片URL
     * @return OCR识别结果
     */
    private static AiOcrDataV2 processOcrForSingleUrl(String picUrl) {
        Map<String, String> params = new HashMap<>();
        String template = "{\"model\":\"{model}\",\"messages\":[{\"role\":\"user\",\"content\":[{\"type\":\"image_url\",\"image_url\":{\"url\":\"{imgUrl}\"}}]},{\"role\":\"system\",\"content\":[{\"type\":\"text\",\"text\":\"{prompt}\"}]}]}\n";
        String prompt = "从小票中提取以下信息，并以 JSON 格式返回：- 流水号：waterNo - 日期时间：ticketTime（格式为 yyyy-MM-dd HH:mm:ss） - 店铺名称/门店名称：shopName - 药品信息列表：list 对于每种药品，提取以下信息： - 药品名称：name - 数量：number（整数，不带小数点） - 单价：price - 总金额：totalPrice 注意事项：- 如果药品名称在小票中重复，仅返回该药品的第二个记录。- 其余数据不需返回。返回结构示例：json{\"waterNo\": \"123456\",\"ticketTime\": \"2023-10-05 14:30:00\",\"shopName\": \"某某药店/门店\",\"list\": [{\"name\": \"药品A\",\"number\": 2,\"price\": 10.00,\"totalPrice\": 20.00},{\"name\": \"药品B\",\"number\": 1,\"price\": 15.00,\"totalPrice\": 15.00}]}";

        params.put("imgUrl", picUrl);
        params.put("model", getApiModel());
        params.put("prompt", prompt);
        String reqParam = StrUtil.format(template, params);

        try (HttpResponse ocrResp = HttpUtil.createPost("https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions")
                .header("Authorization", "Bearer " + getApiKey())
                .header("Content-Type", "application/json")
                .body(reqParam).execute()) {
            String body = ocrResp.body();
            Pattern pattern = Pattern.compile("```json\\\\n(.*?)\\\\n```", Pattern.DOTALL);
            Matcher matcher = pattern.matcher(body);
            if (matcher.find()) {
                // 获取匹配的内容并处理转义字符
                String escapedJson = matcher.group(1);
                String jsonStr = escapedJson.replace("\\\\", "\\")
                        .replace("\\n", "\n")
                        .replace("\\\"", "\"");
                return JSONUtil.toBean(jsonStr, AiOcrDataV2.class);
            }
        } catch (Exception e) {
            log.error("请求模型失败或解析失败，图片URL: {}", picUrl, e);
        }
        return null;
    }
}
