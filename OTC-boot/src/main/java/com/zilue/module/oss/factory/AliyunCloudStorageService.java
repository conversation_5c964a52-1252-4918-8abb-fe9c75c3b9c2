package com.zilue.module.oss.factory;

import com.aliyun.oss.OSSClient;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.zilue.common.exception.MyException;
import com.zilue.config.OSSConfig;

import java.io.ByteArrayInputStream;
import java.io.InputStream;

/**
 * 阿里云存储
 *
 * <AUTHOR>
 */
public class AliyunCloudStorageService extends CloudStorageService {
    private OSSClient client;

    public AliyunCloudStorageService(OSSConfig config){
        this.config = config;

        //初始化
        init();
    }

    private void init(){
        client = new OSSClient(config.getEndpoint(), config.getAccessKey(),
                config.getSecretKey());
    }

    @Override
    public String upload(byte[] data, String path) {
        return upload(new ByteArrayInputStream(data), path);
    }

    @Override
    public String upload(InputStream inputStream, String path) {
        try {
            client.putObject(config.getBucketName(), path, inputStream);
        } catch (Exception e){
            throw new MyException("上传文件失败，请检查配置信息", e);
        }

        return "https://" + config.getBucketName() + StringPool.DOT + config.getEndpoint() + StringPool.SLASH + path;
    }

    @Override
    public String uploadSuffix(byte[] data, String suffix) {
        return upload(data, getPath(config.getPrefix(), suffix));
    }

    @Override
    public String uploadSuffix(InputStream inputStream, String suffix) {
        return upload(inputStream, getPath(config.getPrefix(), suffix));
    }
}
