package com.zilue.module.job;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zilue.common.utils.StringUtil;
import com.zilue.module.business.salesreport.service.ISalesReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Year;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/15 上午11:32
 * @description
 */
@Component
@Slf4j
public class SalesDataSyncJob {

    @Autowired
    private ISalesReportService salesReportService;

    /**
     * 按代表汇总数据（按月）
     */
    @XxlJob("salesReportMonth")
    public void salesReportMonth() {
        String param = XxlJobHelper.getJobParam();
        try {
            log.info("[按代表汇总数据按月] 任务开始，参数{}", param);
            List<String> monthList;
            if (StringUtil.isBlank(param)) {
                String lastYearMonth = Year.now()+"-12";
                monthList = generateMonthRange(YearMonth.now().toString(), lastYearMonth);
            } else {
                //2024-01;2024-06
                String[] pms = param.split(";");
                monthList = generateMonthRange(pms[0], pms[1]);
            }
            for (String month : monthList) {
                salesReportService.salesReportMonth(month);
            }
            log.info("[按代表汇总数据按月] 执行结束，参数{}", param);
        } catch (Exception e) {
            log.error("[按代表汇总数据按月] 执行异常结束，参数{}", param, e);
            throw e;
        }

    }


    /**
     * 按代表汇总数据（按年）
     */
    @XxlJob("salesReportYear")
    public void salesReportYear() {
        String param = XxlJobHelper.getJobParam();
        try {
            log.info("[按代表汇总数据按年] 任务开始，参数{}", param);
            List<String> yearList;
            if (StringUtil.isBlank(param)) {
                yearList = new ArrayList<>();
                yearList.add(Year.now().toString());
            } else {
                //2024;2024
                String[] pms = param.split(";");
                yearList = generateYearRange(pms[0], pms[1]);
            }
            for (String year : yearList) {
                salesReportService.salesReportYear(year);
            }
            log.info("[按代表汇总数据按年] 执行结束，参数{}", param);
        } catch (Exception e) {
            log.error("[按代表汇总数据按年] 执行异常结束，参数{}", param, e);
            throw e;
        }

    }

    /**
     * 按团队汇总数据（按月）
     */
    @XxlJob("salesReportDeptMonth")
    public void salesReportDeptMonth() {
        String param = XxlJobHelper.getJobParam();
        try {
            log.info("[按团队汇总数据按月] 任务开始，参数{}", param);
            List<String> monthList;
            if (StringUtil.isBlank(param)) {
                monthList = new ArrayList<>();
               // monthList.add(YearMonth.now().toString());
                String lastYearMonth = Year.now()+"-12";
                monthList = generateMonthRange(YearMonth.now().toString(), lastYearMonth);
            } else {
                //2024-01;2024-06
                String[] pms = param.split(";");
                monthList = generateMonthRange(pms[0], pms[1]);
            }
            for (String month : monthList) {
                salesReportService.salesReportDeptMonth(month);
            }
            log.info("[按团队汇总数据按月] 执行结束，参数{}", param);
        } catch (Exception e) {
            log.error("[按团队汇总数据按月] 执行异常结束，参数{}", param, e);
            throw e;
        }

    }


    /**
     * 按团队汇总数据（按年）
     */
    @XxlJob("salesReportDeptYear")
    public void salesReportDeptYear() {
        String param = XxlJobHelper.getJobParam();
        try {
            log.info("[按团队汇总数据按年] 任务开始，参数{}", param);
            List<String> yearList;
            if (StringUtil.isBlank(param)) {
                yearList = new ArrayList<>();
                yearList.add(Year.now().toString());
            } else {
                //2024;2024
                String[] pms = param.split(";");
                yearList = generateYearRange(pms[0], pms[1]);
            }
            for (String year : yearList) {
                salesReportService.salesReportDeptYear(year);
            }
            log.info("[按团队汇总数据按年] 执行结束，参数{}", param);
        } catch (Exception e) {
            log.error("[按团队汇总数据按年] 执行异常结束，参数{}", param, e);
            throw e;
        }

    }



    /**
     * 连锁总部（按月）
     */
    @XxlJob("salesReportHeadStoreMonth")
    public void salesReportHeadStoreMonth() {
        String param = XxlJobHelper.getJobParam();
        try {
            log.info("[连锁总部数据按月] 任务开始，参数{}", param);
            List<String> monthList;
            if (StringUtil.isBlank(param)) {
                String lastYearMonth = Year.now()+"-12";
                monthList = generateMonthRange(YearMonth.now().toString(), lastYearMonth);
            } else {
                //2024-01;2024-06
                String[] pms = param.split(";");
                monthList = generateMonthRange(pms[0], pms[1]);
            }
            for (String month : monthList) {
                salesReportService.salesReportHeadStoreMonth(month);
            }
            log.info("[连锁总部数据按月] 执行结束，参数{}", param);
        } catch (Exception e) {
            log.error("[连锁总部数据按月] 执行异常结束，参数{}", param, e);
            throw e;
        }

    }


    /**
     * 连锁总部（按年）
     */
    @XxlJob("salesReportHeadStoreYear")
    public void salesReportHeadStoreYear() {
        String param = XxlJobHelper.getJobParam();
        try {
            log.info("[连锁总部数据按年] 任务开始，参数{}", param);
            List<String> yearList;
            if (StringUtil.isBlank(param)) {
                yearList = new ArrayList<>();
                yearList.add(Year.now().toString());
            } else {
                //2024;2024
                String[] pms = param.split(";");
                yearList = generateYearRange(pms[0], pms[1]);
            }
            for (String year : yearList) {
                salesReportService.salesReportHeadStoreYear(year);
            }
            log.info("[连锁总部数据按年] 执行结束，参数{}", param);
        } catch (Exception e) {
            log.error("[连锁总部数据按年] 执行异常结束，参数{}", param, e);
            throw e;
        }

    }



    /**
     * KA经理（按月）
     */
    @XxlJob("salesReportKaMonth")
    public void salesReportKaMonth() {
        String param = XxlJobHelper.getJobParam();
        try {
            log.info("[KA经理按月] 任务开始，参数{}", param);
            List<String> monthList;
            if (StringUtil.isBlank(param)) {
                String lastYearMonth = Year.now()+"-12";
                monthList = generateMonthRange(YearMonth.now().toString(), lastYearMonth);
            } else {
                //2024-01;2024-06
                String[] pms = param.split(";");
                monthList = generateMonthRange(pms[0], pms[1]);
            }
            for (String month : monthList) {
                salesReportService.salesReportKaMonth(month);
            }
            log.info("[KA经理按月] 执行结束，参数{}", param);
        } catch (Exception e) {
            log.error("[KA经理按月] 执行异常结束，参数{}", param, e);
            throw e;
        }

    }


    /**
     * 连锁总部（按年）
     */
    @XxlJob("salesReportKaYear")
    public void salesReportKaYear() {
        String param = XxlJobHelper.getJobParam();
        try {
            log.info("[KA经理按年] 任务开始，参数{}", param);
            List<String> yearList;
            if (StringUtil.isBlank(param)) {
                yearList = new ArrayList<>();
                yearList.add(Year.now().toString());
            } else {
                //2024;2024
                String[] pms = param.split(";");
                yearList = generateYearRange(pms[0], pms[1]);
            }
            for (String year : yearList) {
                salesReportService.salesReportKaYear(year);
            }
            log.info("[KA经理按年] 执行结束，参数{}", param);
        } catch (Exception e) {
            log.error("[KA经理按年] 执行异常结束，参数{}", param, e);
            throw e;
        }

    }

    /**
     * 按基层业务员汇总数据（按月）
     */
    @XxlJob("salesLevelReportMonth")
    public void salesLevelReportMonth() {
        String param = XxlJobHelper.getJobParam();
        try {
            log.info("[按代表汇总数据按月] 任务开始，参数{}", param);
            List<String> monthList;
            if (StringUtil.isBlank(param)) {
                String lastYearMonth = Year.now()+"-12";
                monthList = generateMonthRange(YearMonth.now().toString(), lastYearMonth);
            } else {
                //2024-01;2024-06
                String[] pms = param.split(";");
                monthList = generateMonthRange(pms[0], pms[1]);
            }
            for (String month : monthList) {
                salesReportService.salesLevelReportMonth(month);
            }
            log.info("[按代表汇总数据按月] 执行结束，参数{}", param);
        } catch (Exception e) {
            log.error("[按代表汇总数据按月] 执行异常结束，参数{}", param, e);
            throw e;
        }

    }


    /**
     * 按基层业务员汇总数据（按年）
     */
    @XxlJob("salesLevelReportYear")
    public void salesLevelReportYear() {
        String param = XxlJobHelper.getJobParam();
        try {
            log.info("[按代表汇总数据按年] 任务开始，参数{}", param);
            List<String> yearList;
            if (StringUtil.isBlank(param)) {
                yearList = new ArrayList<>();
                yearList.add(Year.now().toString());
            } else {
                //2024;2024
                String[] pms = param.split(";");
                yearList = generateYearRange(pms[0], pms[1]);
            }
            for (String year : yearList) {
                salesReportService.salesLevelReportYear(year);
            }
            log.info("[按代表汇总数据按年] 执行结束，参数{}", param);
        } catch (Exception e) {
            log.error("[按代表汇总数据按年] 执行异常结束，参数{}", param, e);
            throw e;
        }

    }




    public static List<String> generateMonthRange(String startMonth, String endMonth) {
        List<String> monthList = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        // 解析开始月份和结束月份
        YearMonth start = YearMonth.parse(startMonth, formatter);
        YearMonth end = YearMonth.parse(endMonth, formatter);
        // 生成月份列表
        while (!start.isAfter(end)) {
            monthList.add(start.format(formatter));
            start = start.plusMonths(1);
        }
        return monthList;
    }


    public static List<String> generateYearRange(String startYearStr, String endYearStr) {
        List<String> yearList = new ArrayList<>();
        // 将字符串转换为整数
        int startYear = Integer.parseInt(startYearStr);
        int endYear = Integer.parseInt(endYearStr);
        // 生成年份列表
        for (int year = startYear; year <= endYear; year++) {
            yearList.add(String.valueOf(year));
        }
        return yearList;
    }
}
