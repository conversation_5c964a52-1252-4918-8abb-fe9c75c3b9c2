package com.zilue.module.job;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zilue.module.business.taskManagement.service.ITaskManagementService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class TaskManagerJob {
    @Autowired
    private ITaskManagementService taskManagementService;
    /**
     * 更新任务状态
     *
     * @throws Exception
     */
    @XxlJob("updateTaskStatus")
    public void updateTaskStatus() throws Exception {

        try {
            XxlJobHelper.log("TaskManagerJob.updateTaskStatus", "更新任务状态执行开始");
            taskManagementService.updateSalesmanTaskStatusForJob();
            XxlJobHelper.handleSuccess();
        } catch (Exception e) {
            XxlJobHelper.log("TaskManagerJob.updateTaskStatus 执行失败", e.getMessage());
            log.error("CrmXxlJob.updateTaskStatus 执行失败", e);
        }
    }
}
