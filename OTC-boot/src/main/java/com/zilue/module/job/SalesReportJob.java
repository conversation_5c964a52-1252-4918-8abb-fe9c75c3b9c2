package com.zilue.module.job;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zilue.module.business.salesindicators.service.ILevelSalesIndicatorsService;
import com.zilue.module.business.salesindicators.service.ISalesIndicatorsService;
import com.zilue.module.business.salesperformance.service.ISalesPerformanceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/1/15 上午11:32
 * @description
 */
@Component
@Slf4j
public class SalesReportJob {

    @Autowired
    private ISalesPerformanceService performanceService;
    @Autowired
    private ISalesIndicatorsService indicatorsService;

    @Autowired
    private ILevelSalesIndicatorsService indicatorsLevelService;

    /**
     * 同步业绩
     */
    @XxlJob("syncPerformanceData")
    public void syncPerformanceData() {
        String syncTime = XxlJobHelper.getJobParam();
        try {
            log.info("[同步业绩] 任务开始，参数{}", syncTime);
            performanceService.syncPerformance(syncTime);
            log.info("[同步业绩] 执行结束，参数{}", syncTime);
        } catch (Exception e) {
            log.error("[同步业绩] 执行异常结束，参数{}", syncTime, e);
            throw e;
        }
    }

    /**
     * 同步指标
     */
    @XxlJob("syncIndicatorsData")
    public void syncIndicatorsData() {
        String syncTime = XxlJobHelper.getJobParam();
        try {
            log.info("[同步指标] 任务开始，参数{}", syncTime);
            indicatorsService.syncIndicators(syncTime);
            log.info("[同步指标] 执行结束，参数{}", syncTime);
        } catch (Exception e) {
            log.error("[同步指标] 执行异常结束，参数{}", syncTime, e);
            throw e;
        }
    }

    /**
     * 同步基层代表指标
     */
    @XxlJob("syncLevelIndicatorsData")
    public void syncLevelIndicatorsData() {
        String syncTime = XxlJobHelper.getJobParam();
        try {
            log.info("[同步指标] 任务开始，参数{}", syncTime);
            indicatorsLevelService.syncIndicators(syncTime);
            log.info("[同步指标] 执行结束，参数{}", syncTime);
        } catch (Exception e) {
            log.error("[同步指标] 执行异常结束，参数{}", syncTime, e);
            throw e;
        }
    }

}
