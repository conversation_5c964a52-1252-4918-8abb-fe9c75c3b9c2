package com.zilue.module.job;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zilue.common.utils.StringUtil;
import com.zilue.module.business.inventory.service.IOtcInventoryCustomerService;
import com.zilue.module.business.inventory.service.IOtcInventoryDayHeadService;
import com.zilue.module.business.inventory.service.IOtcInventoryDayPartService;
import com.zilue.module.business.inventory.service.IOtcInventoryDayProductService;
import com.zilue.module.business.salesreport.service.ISalesReportService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * @program: OTC-boot
 * @description: 每日排序拜访达成率的定时任务
 * @packagename: com.zilue.module.job
 * @author: 曹凌
 * @date: 2025-01-17 13:51
 **/
@Slf4j
@Component
@AllArgsConstructor
public class InventorySchedulerJob {

    @Autowired
    private IOtcInventoryCustomerService otcInventoryCustomerService;
    @Autowired
    private IOtcInventoryDayPartService otcInventoryDayPartService;
    @Autowired
    private IOtcInventoryDayHeadService otcInventoryDayHeadService;
    @Autowired
    private IOtcInventoryDayProductService otcInventoryDayProductService;

    /**每日跑批分部的定时任务
     * 2025年03月
     */
    @XxlJob("syncPartInventoryScheduler")
    public void visitTargetScheduler() {
        String param = XxlJobHelper.getJobParam();
        log.info("每日跑批分部的定时任务开始，现在获取：{} 的达成率", param);
        otcInventoryDayPartService.syncPartInventory(param);
        log.info("每日跑批分部的定时任务结束");
    }

    /**每日跑批总部的定时任务
     *  2025-03
     */
    @XxlJob("syncInventoryDayHeadScheduler")
    public void syncInventoryDayHeadScheduler() {
        String param = XxlJobHelper.getJobParam();
        // 获取当前日期
        LocalDate today = LocalDate.now();
        // 创建格式化器，格式为"yyyy-MM"，其中MM表示两位数字的月份
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        // 格式化当前日期
        String formattedDate = today.format(formatter);
        if (StringUtil.isBlank(param)) {
            param = formattedDate;
        }
        log.info("每日跑批总部的定时任务开始，现在获取：{} 的达成率", param);
        otcInventoryDayHeadService.syncInventoryDayHead(param);
        log.info("每日跑批总部的定时任务结束");
    }

    /**每日跑批品规的定时任务
     * 2025-03
     */
    @XxlJob("syncInventoryDayProductScheduler")
    public void syncInventoryDayProductScheduler() {
        String param = XxlJobHelper.getJobParam();
        // 获取当前日期
        LocalDate today = LocalDate.now();
        // 创建格式化器，格式为"yyyy-MM"，其中MM表示两位数字的月份
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        // 格式化当前日期
        String formattedDate = today.format(formatter);
        if (StringUtil.isBlank(param)) {
            param = formattedDate;
        }
        log.info("每日跑批品规的定时任务开始，现在获取：{} 的达成率", param);
        otcInventoryDayProductService.syncInventoryDayProduct(param);
        log.info("每日跑批品规的定时任务结束");
    }
}
