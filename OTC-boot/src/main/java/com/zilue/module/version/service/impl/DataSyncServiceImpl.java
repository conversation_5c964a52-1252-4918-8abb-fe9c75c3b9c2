package com.zilue.module.version.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zilue.module.system.entity.CodeRule;
import com.zilue.module.system.entity.Databaselink;
import com.zilue.module.system.entity.DictionaryDetail;
import com.zilue.module.system.entity.DictionaryItem;
import com.zilue.module.system.entity.Menu;
import com.zilue.module.system.entity.MenuButton;
import com.zilue.module.system.entity.MenuColumn;
import com.zilue.module.system.entity.MenuForm;
import com.zilue.module.system.service.ICodeRuleService;
import com.zilue.module.system.service.IDatabaselinkService;
import com.zilue.module.system.service.IDictionarydetailService;
import com.zilue.module.system.service.IDictionaryitemService;
import com.zilue.module.system.service.IMenuButtonService;
import com.zilue.module.system.service.IMenuColumnService;
import com.zilue.module.system.service.IMenuFormService;
import com.zilue.module.system.service.IMenuService;
import com.zilue.module.version.dto.ExportDataDto;
import com.zilue.module.version.service.IDataSyncService;
import com.zilue.module.version.vo.MenuExportVo;
import java.io.BufferedReader;
import java.io.InputStreamReader;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2024/6/16 14:12
 */
@Service
@AllArgsConstructor
public class DataSyncServiceImpl implements IDataSyncService {

  private IMenuService menuService;
  private IMenuButtonService menuButtonService;
  private IMenuColumnService menuColumnService;
  private IMenuFormService menuFormService;
  private IDatabaselinkService databaselinkService;
  private IDictionaryitemService dictionaryitemService;
  private IDictionarydetailService dictionarydetailService;
  private ICodeRuleService codeRuleService;

  @Override
  public String exportData(ExportDataDto exportDataDto) {
    List<MenuButton> buttonList = new ArrayList<MenuButton>();// 按钮列表
    List<MenuColumn> columnList = new ArrayList<MenuColumn>();// List页字段列表
    List<MenuForm> formList = new ArrayList<MenuForm>();// form页字段列表
    List<Menu> menuList = new ArrayList<Menu>();// 菜单列表
    // List<Menu> menuList = menuService.listByIds(exportDataDto.getMenuIds());//
    // 菜单列表
    List<DictionaryItem> dictionaryList = new ArrayList<DictionaryItem>();// 字典项目列表
    List<DictionaryDetail> dictionaryDetailList = new ArrayList<DictionaryDetail>();// 字典明细列表
    List<Databaselink> databaselinkList = new ArrayList<Databaselink>();
    List<CodeRule> codeRuleList = new ArrayList<CodeRule>();
    if (exportDataDto.getMenuIds().size() > 0) {
      menuList = menuService.listByIds(exportDataDto.getMenuIds());
      for (Long menuId : exportDataDto.getMenuIds()) {
        List<MenuButton> bList = menuButtonService
            .list(Wrappers.<MenuButton>lambdaQuery().eq(menuId != 0, MenuButton::getMenuId, menuId)
                .select());
        buttonList.addAll(bList);
        List<MenuColumn> cList = menuColumnService
            .list(Wrappers.<MenuColumn>lambdaQuery().eq(menuId != 0, MenuColumn::getMenuId, menuId)
                .select());
        columnList.addAll(cList);
        List<MenuForm> fList = menuFormService
            .list(Wrappers.<MenuForm>lambdaQuery().eq(menuId != 0, MenuForm::getMenuId, menuId)
                .select());
        formList.addAll(fList);
      }
    }
    if (exportDataDto.getDicIds().size() > 0) {
      dictionaryList = dictionaryitemService.listByIds(exportDataDto.getDicIds());
      for (Long dicItem : exportDataDto.getDicIds()) {
        List<DictionaryDetail> dicdetailList = dictionarydetailService
            .list(Wrappers.<DictionaryDetail>lambdaQuery().eq(dicItem != 0, DictionaryDetail::getItemId, dicItem)
                .select());
        dictionaryDetailList.addAll(dicdetailList);
      }
    }
    if (exportDataDto.getDataLinkIds().size() > 0) {
      databaselinkList = databaselinkService.listByIds(exportDataDto.getDataLinkIds());
    }
    if (exportDataDto.getCodeRuleIds().size() > 0) {
      codeRuleList = codeRuleService.listByIds(exportDataDto.getCodeRuleIds());
    }
    MenuExportVo menuExportVo = new MenuExportVo();
    menuExportVo.setMenus(menuList);
    menuExportVo.setButtons(buttonList);
    menuExportVo.setColumns(columnList);
    menuExportVo.setFormcolumns(formList);
    menuExportVo.setDictionaryItems(dictionaryList);
    menuExportVo.setDictionaryDetails(dictionaryDetailList);
    menuExportVo.setDatabaselinks(databaselinkList);
    menuExportVo.setCodeRules(codeRuleList);
    return JSONUtil.toJsonStr(menuExportVo);
  }

  @Override
  public String exportOne(String type) {
    List<MenuButton> buttonList = new ArrayList<MenuButton>();// 按钮列表
    List<MenuColumn> columnList = new ArrayList<MenuColumn>();// List页字段列表
    List<MenuForm> formList = new ArrayList<MenuForm>();// form页字段列表
    List<Menu> menuList = new ArrayList<Menu>();// 菜单列表
    // 菜单列表
    List<DictionaryItem> dictionaryList = new ArrayList<DictionaryItem>();// 字典项目列表
    List<DictionaryDetail> dictionaryDetailList = new ArrayList<DictionaryDetail>();// 字典明细列表
    List<Databaselink> databaselinkList = new ArrayList<Databaselink>();
    List<CodeRule> codeRuleList = new ArrayList<CodeRule>();
    // 按类型导出数据
    switch (type) {
      case "menu":
        menuList = menuService
            .list(Wrappers.<Menu>lambdaQuery().eq(Menu::getDeleteMark, 0).and(m -> m.eq(Menu::getEnabledMark, 1))
                .select());
        buttonList = menuButtonService.list();
        formList = menuFormService.list();
        columnList = menuColumnService.list();
        break;
      case "dic":
        dictionaryList = dictionaryitemService.list();
        dictionaryDetailList = dictionarydetailService.list();
        break;
      case "link":
        databaselinkList = databaselinkService.list();
        break;
      case "billcode":
        codeRuleList = codeRuleService.list();
        break;

      default:
        break;
    }
    MenuExportVo menuExportVo = new MenuExportVo();
    menuExportVo.setMenus(menuList);
    menuExportVo.setButtons(buttonList);
    menuExportVo.setColumns(columnList);
    menuExportVo.setFormcolumns(formList);
    menuExportVo.setDictionaryItems(dictionaryList);
    menuExportVo.setDictionaryDetails(dictionaryDetailList);
    menuExportVo.setDatabaselinks(databaselinkList);
    menuExportVo.setCodeRules(codeRuleList);
    return JSONUtil.toJsonStr(menuExportVo);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  @SneakyThrows
  public boolean importData(MultipartFile multipartFile) {
    String encoding = "UTF-8";
    // 用流读取文件
    BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(multipartFile.getInputStream(), encoding));
    String line;
    StringBuilder content = new StringBuilder();
    // 读取想定文件
    while ((line = bufferedReader.readLine()) != null) {
      content.append(line);
    }

    MenuExportVo menuExportVo = JSONUtil.toBean(content.toString(), MenuExportVo.class);
    List<Menu> menus = BeanUtil.copyToList(menuExportVo.getMenus(), Menu.class);
    boolean isSuccess = menuService.saveOrUpdateBatch(menus);

    // 按钮保存
    List<MenuButton> menuButtons = BeanUtil.copyToList(menuExportVo.getButtons(), MenuButton.class);
    menuButtonService.saveOrUpdateBatch(menuButtons);
    // 列表字段保存
    List<MenuColumn> menuColumns = BeanUtil.copyToList(menuExportVo.getColumns(), MenuColumn.class);
    menuColumnService.saveOrUpdateBatch(menuColumns);
    // 表单字段保存
    List<MenuForm> menuForms = BeanUtil.copyToList(menuExportVo.getFormcolumns(), MenuForm.class);
    menuFormService.saveOrUpdateBatch(menuForms);
    // 字典保存
    List<DictionaryItem> dictionaryItems = BeanUtil.copyToList(menuExportVo.getDictionaryItems(), DictionaryItem.class);
    List<DictionaryDetail> dictionaryDetails = BeanUtil.copyToList(menuExportVo.getDictionaryDetails(),
        DictionaryDetail.class);
    dictionaryitemService.saveOrUpdateBatch(dictionaryItems);
    dictionarydetailService.saveOrUpdateBatch(dictionaryDetails);
    // 数据库连接保存
    List<Databaselink> dbLinks = BeanUtil.copyToList(menuExportVo.getDatabaselinks(), Databaselink.class);
    databaselinkService.saveOrUpdateBatch(dbLinks);
    // 单据编码
    List<CodeRule> codeRules = BeanUtil.copyToList(menuExportVo.getCodeRules(), CodeRule.class);
    codeRuleService.saveOrUpdateBatch(codeRules);
    return true;
  }
}
