package com.zilue.module.version.controller;

import com.zilue.common.model.result.R;
import com.zilue.module.version.dto.ExportDataDto;
import com.zilue.module.version.service.IDataSyncService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;

import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 系统配置
 * 
 * <AUTHOR>
 * @Date: 2024/7/1 14:13
 */
@RestController
@RequestMapping("/version/datasync")
@Api(value = "/version/datasync", tags = "数据同步")
@AllArgsConstructor
public class DataSyncController {

    private final IDataSyncService versionManagerService;

    @PostMapping("/export")
    @ApiOperation("导出数据")
    public R export(@RequestBody ExportDataDto exportDataDto) {

        String exportData = versionManagerService.exportData(exportDataDto);
        return R.ok(exportData);
    }

    @PostMapping("/import")
    @ApiOperation(value = "导入数据")
    @SneakyThrows
    public R importSchema(@RequestParam(value = "file") MultipartFile multipartFile) {
        return R.ok(versionManagerService.importData(multipartFile));
    }

    @GetMapping("/exportone")
    @ApiOperation(value = "导出单种数据")
    public R exportone(@RequestParam String type) {
        String exportData = versionManagerService.exportOne(type);
        return R.ok(exportData);
    }
    


}
