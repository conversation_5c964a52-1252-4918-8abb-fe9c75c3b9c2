package com.zilue.module.version.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

import com.zilue.module.system.entity.CodeRule;
import com.zilue.module.system.entity.Databaselink;
import com.zilue.module.system.entity.DictionaryDetail;
import com.zilue.module.system.entity.DictionaryItem;
import com.zilue.module.system.entity.Menu;
import com.zilue.module.system.entity.MenuButton;
import com.zilue.module.system.entity.MenuColumn;
import com.zilue.module.system.entity.MenuForm;

/**
 * <AUTHOR>
 * @Date: 2024/6/16 17:00
 */
@Data
public class MenuExportVo    
{



    @ApiModelProperty("菜单列表")
    private List<Menu> menus;

    @ApiModelProperty("按钮列表")
    private List<MenuButton> buttons;

    @ApiModelProperty("字段列表")
    private List<MenuColumn> columns;

    @ApiModelProperty("表单字段列表")
    private List<MenuForm> formcolumns;

    @ApiModelProperty("字典项目")
    private List<DictionaryItem> dictionaryItems;

    @ApiModelProperty("字典详情")
    private List<DictionaryDetail> dictionaryDetails;

    @ApiModelProperty("数据连接列表")
    private List<Databaselink> databaselinks;

    @ApiModelProperty("单据编码列表")
    private List<CodeRule> codeRules;
}
