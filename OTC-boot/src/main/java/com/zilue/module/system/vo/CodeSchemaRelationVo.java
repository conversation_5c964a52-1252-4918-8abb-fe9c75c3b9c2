package com.zilue.module.system.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CodeSchemaRelationVo {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("类型（0-数据优先模板,1-界面优先模板,2-简易模板）")
    private Integer type;

    @ApiModelProperty("当前模板所关联的表单id")
    private Long formId;

    @ApiModelProperty("当前模板所关联的菜单id")
    private Long menuId;
}
