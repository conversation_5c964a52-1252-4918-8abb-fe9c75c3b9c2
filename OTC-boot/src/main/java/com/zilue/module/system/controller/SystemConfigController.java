package com.zilue.module.system.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.model.result.R;
import com.zilue.module.system.dto.UpdateSystemConfigDto;
import com.zilue.module.system.entity.SystemConfig;
import com.zilue.module.system.service.ISystemConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 系统配置
 * @Author: zilue
 * @Date: 2024/1/16 14:13
 */
@RestController
@RequestMapping(GlobalConstant.SYSTEM_MODULE_PREFIX + "/system-config")
@Api(value = GlobalConstant.SYSTEM_MODULE_PREFIX + "/system-config", tags = "系统配置")
@AllArgsConstructor
public class SystemConfigController {

    private final ISystemConfigService systemConfigService;

    @GetMapping("/list")
    @ApiOperation("查询所有系统配置")
    public R list() {
        List<SystemConfig> list = systemConfigService.list();
        return R.ok(list);
    }

    @GetMapping("/info")
    @ApiOperation("查询某一个配置")
    public R info(@RequestParam Long id){
        return R.ok(systemConfigService.getById(id));
    }

    @GetMapping("/info-by-type")
    @ApiOperation("根据类型查询某一个配置")
    public R infoByType(@RequestParam Integer type){
        SystemConfig one = systemConfigService.getOne(Wrappers.lambdaQuery(SystemConfig.class).eq(SystemConfig::getConfigType, type));

        return R.ok(one);
    }


    @PutMapping
    @ApiOperation("修改某个配置")
    public  R update(@Valid @RequestBody UpdateSystemConfigDto dto){
        return R.ok(systemConfigService.updateSystemConfig(dto));
    }
}
