package com.zilue.module.system.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigInteger;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class BaiduRoutingVO {
    @ApiModelProperty("0：成功1：服务内部错误2：参数无效7：无返回结果 ")
    private Integer status;

    @ApiModelProperty("状态码对应的信息")
    private String message;

    @ApiModelProperty("默认返回2，开发者无需关注")
    private String type;

    @ApiModelProperty("返回的结果")
    private POI result;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class POI {
        @ApiModelProperty("限行结果提示信息 若无限行路线，则返回空 若无法规避限行，则返回限行提示信息")
        private String restriction;
        @ApiModelProperty("返回方案的总数")
        private Integer total;
        @ApiModelProperty("返回的方案集")
        private List<Route> routes;
        @ApiModelProperty("终点步导路线")
        private List<WalkInfo> end_walkinfo;
        @ApiModelProperty("起点步导路线")
        private List<WalkInfo> start_walkinfo;
        @Data
        @AllArgsConstructor
        @NoArgsConstructor
        public static class Route {
            @ApiModelProperty("限行信息")
            private RestrictionInfo restriction_info;
            @ApiModelProperty("起点经纬度")
            private Location origin;
            @ApiModelProperty("终点经纬度")
            private Location destination;
            @ApiModelProperty("方案标签")
            private String tag;
            @ApiModelProperty("红绿灯数量")
            private Integer traffic_light;
            @ApiModelProperty("如无特殊需要，开发者无需关注")
            private String route_id;
            @ApiModelProperty("方案距离，单位：米")
            private Integer distance;
            @ApiModelProperty("未来驾车路线耗时，单位：秒")
            private Integer duration;
            @ApiModelProperty("驾车路线历史耗时（扩展），单位：秒")
            private Integer ext_duration;
            @ApiModelProperty("建议出发时间，单位：秒")
            private BigInteger suggest_departure_time;
            @ApiModelProperty("出租车费用，单位：元")
            private Integer taxi_fee;
            @ApiModelProperty("此路线道路收费，单位：元")
            private Integer toll;
            @ApiModelProperty("收费路段里程，单位：米")
            private Integer toll_distance;
            @ApiModelProperty("路线分段，单位：米")
            private List<Step> steps;
            @ApiModelProperty("总线路")
            private String totalpath;

            /**
             * 路线分段信息
             */
            @Data
            @AllArgsConstructor
            @NoArgsConstructor
            public static class Step {
                @ApiModelProperty("途径点序号")
                private Integer leg_index;
                @ApiModelProperty("进入道路的角度：枚举值，返回值在0-11之间的一个值，共12个枚举值，以30度递进，即每个值代表角度范围为30度；其中返回 0代表345度到15度，以此类推")
                private Integer direction;
                @ApiModelProperty("step的距离信息 单位米")
                private Integer distance;
                @ApiModelProperty("起点道路类型，0 普通道路[默认。绑路策略不参考此入参]；1高架上；2高架下；3主路；4辅路；(高速和匝道切换其实相当于主辅路切换)")
                private Integer origin_road_type;
                @ApiModelProperty("分段的道路名称")
                private String road_name;
                @ApiModelProperty("分段的道路类型：枚举值：返回0-9之间的值0：高速路,1：城市高速路,2：国道,3：省道,4：县道,5：乡镇村道,6：其他道路,7：九级路,8：航线(轮渡)9：行人道路")
                private Integer road_type;
                @ApiModelProperty("分段道路收费， 单位:元")
                private Integer toll;
                @ApiModelProperty("分段道路收费路程，单位：米")
                private Integer toll_distance;
                @ApiModelProperty("收费站名称")
                private String toll_gate_name;
                @ApiModelProperty("收费站经纬度")
                private Location toll_gate_location;
                @ApiModelProperty("分段起点经纬度")
                private Location start_location;
                @ApiModelProperty("分段终点经纬度")
                private Location end_location;
                @ApiModelProperty("分段坐标")
                private String path;
                @ApiModelProperty("分段途经的城市 编码")
                private String adcodes;
                @ApiModelProperty("收费站名称")
                private List<TrafficCondition> traffic_condition;
                /**
                 * 分段路况详情
                 */
                @Data
                @AllArgsConstructor
                @NoArgsConstructor
                public static class TrafficCondition {
                    @ApiModelProperty("路况指数：0: 无路况 1: 畅通 2: 缓行 3: 拥堵 4: 非常拥堵")
                    private Integer status;
                    @ApiModelProperty("从当前坐标点开 始，path中路况相 同的坐标点个数")
                    private Integer geo_cnt;
                    @ApiModelProperty("距离，从当前坐 标点开始path 中 路况相同的距 离，单位:米 注意：单条线路中 所有distance的和 会与route的 distance字段存在 差异，不是完全 一致")
                    private Float distance;
                }

            }
            /**
             * 地理位置坐标
             */
            @Data
            @AllArgsConstructor
            @NoArgsConstructor
            public static class Location {
                /** 纬度 */
                @ApiModelProperty("纬度")
                private double lat;
                /** 经度 */
                @ApiModelProperty("经度")
                private double lng;
            }
            @Data
            @AllArgsConstructor
            @NoArgsConstructor
            public static class RestrictionInfo {
                @ApiModelProperty("限行状态 取值范围【0,3】 0:无限行 1:已规避限行， 路线合法 2:无法规避限 行，路线非法 3：疫情相关的信息")
                private Integer status;
                @ApiModelProperty("当限行status为1或 2时，会有相应的 限行描述信息。 若该路线有多条 提示信息，则以 英文竖线分隔符 分隔，" +
                        "如: 已为您避开北京 限行区域 无法为您避开北 京限行区域，请 合理安排出行 起点在北京限行 区域，请合理安 排出行 终点在北京限行 区域，" +
                        "请合理安 排出行 起点在北京限行 区域，请合理安 排出行|终点在北 京限行区域，请 合理安排出行")
                private String desc;
            }
        }

        @Data
        @AllArgsConstructor
        @NoArgsConstructor
        public static class WalkInfo {
            @ApiModelProperty("步导路线距离(单位米)")
            private Integer distance;
            @ApiModelProperty("步导路线坐标点")
            private String path;

        }
    }

}
