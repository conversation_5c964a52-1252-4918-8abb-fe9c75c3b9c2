package com.zilue.module.system.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/11 9:39
 */
@Data
public class DatabaselinkExportVo {
    @ApiModelProperty("数据库名称")
    @ExcelProperty("数据库名称(*)")
    private String dbName;

    @ApiModelProperty("数据库类型")
    @ExcelProperty("数据库类型(*)")
    private String dbType;

    @ApiModelProperty("数据库版本")
    @ExcelProperty("数据库版本")
    private String dbVersion;

    @ApiModelProperty("链接")
    @ExcelProperty("链接(*)")
    private String host;

    @ApiModelProperty("账号")
    @ExcelProperty("账号(*)")
    private String username;

    @ApiModelProperty("密码")
    @ExcelProperty("密码(*)")
    private String password;

}
