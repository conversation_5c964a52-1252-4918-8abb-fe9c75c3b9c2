package com.zilue.module.system.service.impl;

import com.zilue.module.system.entity.LoginConfig;
import com.zilue.module.system.mapper.LoginConfigMapper;
import com.zilue.module.system.service.ILoginConfigService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * Login登录配置表【xjr_login_config】 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-20
 */
@Service
public class LoginConfigServiceImpl extends ServiceImpl<LoginConfigMapper, LoginConfig> implements ILoginConfigService {

}
