package com.zilue.module.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.zilue.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-16
 */
@TableName("xjr_message_template")
@ApiModel(value = "MessageTemplate对象", description = "消息模板表")
@Data
@EqualsAndHashCode(callSuper = true)
public class MessageTemplate extends AuditEntity implements Serializable {

     private static final long serialVersionUID = 1L;

      private Long id;

      @ApiModelProperty("名称")
      private String name;

      @ApiModelProperty("编号")
      private String code;

      @ApiModelProperty("模板类型-数据字典id")
      private Long templateType;

      @ApiModelProperty("消息类型（0-邮件，1-短信，2-企业微信，3-钉钉，4-页面钩子webhook，5-微信公众号，6-系统消息）")
      private Integer messageType;

      @ApiModelProperty("排序")
      private Integer sortCode;

      @ApiModelProperty("备注")
      private String remark;

      @ApiModelProperty("消息配置信息")
      private String messageConfig;

}
