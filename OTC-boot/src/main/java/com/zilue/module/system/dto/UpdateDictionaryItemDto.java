package com.zilue.module.system.dto;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 * @Author: zilue
 * @Date:2022/3/30 13:46
 */
@Data
public class UpdateDictionaryItemDto {

    @NotNull(message = "id不能为空!")
    private Long id;

    @NotNull(message = "数据字典项名称不能为空!")
    @Length(min = 1,max = 20,message = "项目名长度不能大于20字符！")
    private String name;

    @NotNull(message = "数据字典项编码不能为空!")
    @Length(min = 1,max = 10,message = "编码长度不能大于20字符！")
    private String code;

    private Integer sortCode;

    @Length(max = 255,message = "备注长度不能大于20字符！")
    private String remark;

    @NotNull(message = "所属数据字典项分类不能为空!")
    private Long typeId = 1L;
}
