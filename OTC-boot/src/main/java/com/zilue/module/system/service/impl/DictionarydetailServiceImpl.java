package com.zilue.module.system.service.impl;

import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.utils.RedisUtil;
import com.zilue.module.system.entity.DictionaryDetail;
import com.zilue.module.system.mapper.DictionarydetailMapper;
import com.zilue.module.system.service.IDictionarydetailService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 数据字典详情 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-17
 */
@Service
@Slf4j
@AllArgsConstructor
public class DictionarydetailServiceImpl extends ServiceImpl<DictionarydetailMapper, DictionaryDetail> implements IDictionarydetailService {

    private RedisUtil redisUtil;

    @Async
    public void loadCaches() {
        log.info("zilue: 加载所有数据字典详情缓存开始");
        List<DictionaryDetail> list = this.list();
        redisUtil.set(GlobalConstant.DIC_DETAIL_CACHE_KEY, list);
        log.info("zilue: 加载所有数据字典详情缓存结束");
    }
}
