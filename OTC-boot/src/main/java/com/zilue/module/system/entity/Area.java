package com.zilue.module.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 行政区域表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-16
 */
@TableName("xjr_area")
@ApiModel(value = "Area对象", description = "行政区域表")
@Data
@EqualsAndHashCode(callSuper = false)
public class Area implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("区域主键")
    private Long id;

    @ApiModelProperty("父级主键")
    private Long parentId;

    @ApiModelProperty("区域编码")
    private String code;

    @ApiModelProperty("区域名称")
    private String name;

    @ApiModelProperty("快速查询")
    private String quickQuery;

    @ApiModelProperty("简拼")
    private String simpleSpelling;

    @ApiModelProperty("层次")
    private Integer layer;

    @ApiModelProperty("排序码")
    private Integer sortCode;

    @ApiModelProperty("备注")
    private String remark;

}
