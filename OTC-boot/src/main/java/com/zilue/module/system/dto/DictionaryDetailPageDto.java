package com.zilue.module.system.dto;

import com.zilue.common.page.PageInput;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

/**
 * @Author: zilue
 * @Date:2022/3/30 14:39
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DictionaryDetailPageDto extends PageInput {

    @Length(max = 20,message = "字典名不超过20字符！")
    private String name;

    @Length(max = 10,message = "编码不超过20字符！")
    private String code;

    private Long itemId;
}
