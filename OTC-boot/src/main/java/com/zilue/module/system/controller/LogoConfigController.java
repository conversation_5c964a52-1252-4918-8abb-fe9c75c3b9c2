package com.zilue.module.system.controller;

import cn.hutool.core.bean.BeanUtil;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.model.result.R;
import com.zilue.module.system.dto.UpdateLogoDto;
import com.zilue.module.system.entity.LogoConfig;
import com.zilue.module.system.service.ILogoConfigService;
import com.zilue.module.system.vo.LogoInfoVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * Logo信息配置表【xjr_logo_config】 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-20
 */
@RestController
@RequestMapping(GlobalConstant.SYSTEM_MODULE_PREFIX+"/logoConfig")
@Api(value = GlobalConstant.SYSTEM_MODULE_PREFIX + "/logoConfig", tags = "Logo信息配置")
@AllArgsConstructor
public class LogoConfigController {

    private final ILogoConfigService logoConfigService;

    @GetMapping(value = "/logo-info")
    @ApiOperation(value = "登录时获取Logo信息配置")
    public R logoInfo() {
        LogoInfoVo logoInfoVo = logoConfigService.logoInfo();
        return R.ok(logoInfoVo);
    }


    @GetMapping(value = "/info")
    @ApiOperation(value = "Logo配置页获取Logo信息配置")
    public R info() {
        List<LogoConfig> list = logoConfigService.list();
        if (list.size() == 0){//如果没有，则设置为默认配置,并进行保存
            LogoConfig logoConfig = new LogoConfig();
            logoConfig.setId(1L);
            logoConfig.setCompanyName("资略信息技术有限公司");
            logoConfig.setShortName("资略");
            list.add(logoConfig);
            logoConfigService.save(logoConfig);
        }
        return R.ok(list.get(0));
    }

    @PutMapping
    @ApiOperation(value = "修改Logo信息配置")
    @Transactional(rollbackFor = Exception.class)
    public R edit(@Valid @RequestBody UpdateLogoDto dto) {
        LogoConfig logoConfig = BeanUtil.toBean(dto, LogoConfig.class);
        return R.ok(logoConfigService.updateById(logoConfig));
    }

}
