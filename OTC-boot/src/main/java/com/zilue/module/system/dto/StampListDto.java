package com.zilue.module.system.dto;

import com.zilue.common.page.ListInput;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * @Author: zilue
 * @Date: 2023/2/22 15:48
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StampListDto extends ListInput {

    @ApiModelProperty("签章类型 0 私人 1 公共")
    @NotNull(message = "签章类型不能为空！")
    private Integer stampType;

    @ApiModelProperty("启用 停用")
    private Integer enabledMark;

    @ApiModelProperty("签章分类(关联数据字典)")
    private Long stampCategory;
}
