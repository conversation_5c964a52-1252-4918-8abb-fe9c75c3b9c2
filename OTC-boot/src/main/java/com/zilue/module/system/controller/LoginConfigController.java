package com.zilue.module.system.controller;

import cn.dev33.satoken.SaManager;
import cn.dev33.satoken.config.SaTokenConfig;
import cn.hutool.core.bean.BeanUtil;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.enums.YesOrNoEnum;
import com.zilue.common.model.result.R;
import com.zilue.module.system.dto.UpdateLoginConfigDto;
import com.zilue.module.system.entity.LoginConfig;
import com.zilue.module.system.service.ILoginConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * Login登录配置表【xjr_login_config】 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-20
 */
@RestController
@RequestMapping(GlobalConstant.SYSTEM_MODULE_PREFIX+"/loginConfig")
@Api(value = GlobalConstant.SYSTEM_MODULE_PREFIX + "/logoConfig", tags = "Login登录配置")
@AllArgsConstructor
public class LoginConfigController {

    private final ILoginConfigService loginConfigService;

    @GetMapping(value = "/info")
    @ApiOperation(value = "获取Login信息配置")
    public R info() {
        List<LoginConfig> list = loginConfigService.list();
        if (list.size() == 0){//如果没有，则设置为默认配置,并进行保存
            LoginConfig loginConfig = new LoginConfig();
            loginConfig.setId(1L);
            loginConfig.setMulLogin("0,1");
            loginConfig.setMutualExclusion(YesOrNoEnum.YES.getCode());
            loginConfig.setWithoutLogin(YesOrNoEnum.YES.getCode());
            loginConfig.setPasswordStrategy(YesOrNoEnum.YES.getCode());
            loginConfig.setStrategyMaxNumber(7);
            list.add(loginConfig);
            loginConfigService.save(loginConfig);
        }
        return R.ok(list.get(0));
    }

    @PutMapping
    @ApiOperation(value = "修改Login信息配置")
    @Transactional(rollbackFor = Exception.class)
    public R edit(@Valid @RequestBody UpdateLoginConfigDto dto) {
        LoginConfig loginConfig = BeanUtil.toBean(dto, LoginConfig.class);
        LoginConfig oldLoginConfig = loginConfigService.getById(loginConfig.getId());
        loginConfigService.updateById(loginConfig);

        SaTokenConfig oldConfig = SaManager.getConfig();
        //改完之后，需要判断同端互斥是否有所改动
        if (oldLoginConfig.getMutualExclusion() != loginConfig.getMutualExclusion()){
            if (loginConfig.getMutualExclusion() == YesOrNoEnum.NO.getCode()){
                //不开启同端互斥
                oldConfig.setIsConcurrent(true);
            }else {
                //开启同端互斥
                oldConfig.setIsConcurrent(false);
            }
            // 注入到 SaManager 中
            SaManager.setConfig(oldConfig);
        }
        return R.ok(1);
    }

}
