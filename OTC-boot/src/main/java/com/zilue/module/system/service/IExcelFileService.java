package com.zilue.module.system.service;

import com.zilue.common.page.PageInput;
import com.zilue.common.page.PageOutput;
import com.zilue.module.system.dto.UpdateExcelAuthDto;
import com.zilue.module.system.entity.ExcelFile;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zilue.module.system.vo.ExcelAuthVo;
import com.zilue.module.system.vo.ExcelFilePageVo;

import java.util.List;

/**
 * <p>
 * 在线excel表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-25
 */
public interface IExcelFileService extends IService<ExcelFile> {

    PageOutput<ExcelFilePageVo> getPageList(PageInput dto);

    Boolean deleteExcel(List<Long> ids);

    Boolean updateExcelAuth(UpdateExcelAuthDto dto);

    List<ExcelAuthVo> excelAuthInfo(Long id);

}
