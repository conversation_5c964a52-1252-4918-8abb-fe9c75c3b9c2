package com.zilue.module.system.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zilue.module.system.entity.CodeRuleSeed;
import com.zilue.module.system.mapper.CodeRuleSeedMapper;
import com.zilue.module.system.service.ICodeRuleSeedService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 编号规则种子表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-23
 */
@Service
public class CodeRuleSeedServiceImpl extends ServiceImpl<CodeRuleSeedMapper, CodeRuleSeed> implements ICodeRuleSeedService {

    public CodeRuleSeed getCodeRuleSeedBy(Long ruleId, Long userId) {
        return this.getOne(Wrappers.<CodeRuleSeed>query().lambda()
                .eq(CodeRuleSeed::getRuleId, ruleId)
                .eq(CodeRuleSeed::getUserId, userId), false);
    }
}
