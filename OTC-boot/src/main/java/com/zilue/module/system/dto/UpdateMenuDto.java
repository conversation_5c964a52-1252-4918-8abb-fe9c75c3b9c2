package com.zilue.module.system.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @title: UpdateMenuDto
 * <AUTHOR>
 * @Date: 2023/4/4 19:04
 * @Version 1.0
 */
@Data
public class UpdateMenuDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "菜单ID不能为空")
    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("上级Id")
    private Long parentId = 0L;

    @Length(min = 1,max = 20,message = "组件名称不能大于20个字符！")
    @ApiModelProperty("组件名（路由名称） --  与vue代码组件名必须一直 才能做到缓存页面 相关联")
    private String name;

    @NotNull(message = "菜单名称不能为空!")
    @Length(max = 20,message = "菜单名称不能大于20个字符！")
    @ApiModelProperty("菜单名")
    private String title;

    @NotNull(message = "菜单编码不能为空!")
    @Length(max = 20,message = "菜单编码不能大于20个字符！")
    @ApiModelProperty("菜单编号")
    private String code;

    @Length(max = 50,message = "菜单图标不能大于20个字符！")
    @ApiModelProperty("菜单图标")
    private String icon;

    @NotNull(message = "菜单地址不能为空!")
    @Length(max = 100,message = "菜单地址不能大于100个字符！")
    @ApiModelProperty("地址")
    private String path;

//    @NotNull(message = "组件地址不能为空!")
    @Length(max = 100,message = "组件地址不能大于100个字符！")
    @ApiModelProperty("组件地址")
    private String component;

    @ApiModelProperty("组件类型 默认组件 0 普通需要注册的组件 1 自定义表单 桌面设计 等已经默认注册进来的组件 ")
    private Integer componentType;

    @NotNull(message = "组件类型不能为空!")
    @ApiModelProperty("组件类型")
    private Integer menuType;

    @Range(min = 0,max = 1,message = "菜单显示或者隐藏只能是0 或者 1！")
    @ApiModelProperty("菜单显示或者隐藏")
    private Integer display;

    @Range(min = 0,max = 1,message = "菜单是否允许修改只能是0 或者 1！")
    @ApiModelProperty("是否允许修改")
    private Integer allowModify;

    @Range(min = 0,max = 1,message = "菜单是否允许删除只能是0 或者 1！")
    @ApiModelProperty("是否允许删除")
    private Integer allowDelete;

    @Range(min = 0,max = 1,message = "菜单是否为外链只能是0 或者 1！")
    @ApiModelProperty("是否外链")
    private Integer outLink;

    @ApiModelProperty("外链地址")
    private String iframeSrc;

    @Range(min = 0,max = 1,message = "菜单是否为缓存只能是0 或者 1！")
    @ApiModelProperty("页面持久化")
    private Integer keepAlive;

    @ApiModelProperty("排序码")
    private Integer sortCode;

    @Length(max = 255,message = "备注不能大于255个字符！")
    @ApiModelProperty("备注")
    private String remark;

    @Range(min = 0,max = 1,message = "菜单是否为缓存只能是0 或者 1！")
    @ApiModelProperty("启用状态")
    private Integer enabledMark;

    @ApiModelProperty("系统主键")
    private Long systemId = 1L;

    @ApiModelProperty("按钮列表")
    private List<UpdateMenuButtonDto> buttonList;

    @ApiModelProperty("列表字段列表")
    private List<UpdateMenuColumnDto> columnList;

    @ApiModelProperty("表单字段列表")
    private List<UpdateMenuFormDto> formList;

    @ApiModelProperty("自己菜单主键集合")
    private List<Long> childIds;

    @ApiModelProperty("打开方式")
    private String openMode;

}