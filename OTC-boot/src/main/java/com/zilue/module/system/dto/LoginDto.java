package com.zilue.module.system.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;


/**
 * <AUTHOR>
 */
@Data
public class LoginDto  {

    @ApiModelProperty(value = "账号")
    @NotBlank(message = "账号不能为空！")
    private String userName;

    @ApiModelProperty(value = "密码")
    @NotBlank(message = "密码不能为空！")
//    @Length(min = 6,max = 32,message = "密码长度不得小于6个字符，不得大于32个字符！")
    private String password;

    @ApiModelProperty(value = "设备类型-默认为PC，pc为0，app为1",required = false)
    private Integer deviceType;

}
