package com.zilue.module.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.zilue.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 文件关联关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-08
 */
@Data
@TableName("xjr_file")
@ApiModel(value = "File对象", description = "文件关联关系表")
@EqualsAndHashCode(callSuper = false)
public class File extends AuditEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("文件主键")
    private Long id;

    @ApiModelProperty("附件夹主键")
    private Long folderId;

    @ApiModelProperty("文件名称")
    private String fileName;

    @ApiModelProperty("文件路径")
    private String fileUrl;

    @ApiModelProperty("文件大小")
    private Long fileSize;

    @ApiModelProperty("文件后缀")
    private String fileSuffiex;

    @ApiModelProperty("文件类型")
    private String fileType;

    @ApiModelProperty("缩略图名称")
    private String thName;

    @ApiModelProperty("缩略图路径")
    private String thUrl;

    @ApiModelProperty("缩略图文件大小")
    private Long thSize;

    @ApiModelProperty("缩略图文件类型")
    private String thType;


    @ApiModelProperty("下载次数")
    private Integer downloadCount;

    @ApiModelProperty("关联的流程id")
    private String processId;

    @ApiModelProperty("备注")
    private String remark;


}
