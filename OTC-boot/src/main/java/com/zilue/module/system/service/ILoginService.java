package com.zilue.module.system.service;

import com.zilue.common.model.result.R;
import com.zilue.module.system.dto.CreateTokenDto;
import com.zilue.module.system.dto.KeycloakLoginDto;
import com.zilue.module.system.dto.LoginDto;
import com.zilue.module.system.dto.ThirdLoginDto;
import com.zilue.module.system.vo.CreateTokenVo;
import com.zilue.module.system.vo.LoginVo;

/**
 * @Author: zilue
 * @Date: 2023/4/21 14:20
 */
public interface ILoginService {

    LoginVo login(LoginDto dto);

    CreateTokenVo createToken(CreateTokenDto dto);

    LoginVo loginByKeycloak(KeycloakLoginDto dto);

    String developerLogin(String appId, String appSecret);


    R thirdLogin(ThirdLoginDto dto);
}
