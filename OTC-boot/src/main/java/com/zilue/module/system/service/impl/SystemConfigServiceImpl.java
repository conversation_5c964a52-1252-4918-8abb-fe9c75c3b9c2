package com.zilue.module.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.extra.mail.MailAccount;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zilue.common.enums.SystemConfigEnum;
import com.zilue.common.utils.SystemConfigUtil;
import com.zilue.config.*;
import com.zilue.module.system.dto.UpdateSystemConfigDto;
import com.zilue.module.system.entity.SystemConfig;
import com.zilue.module.system.mapper.SystemConfigMapper;
import com.zilue.module.system.model.DingtalkConfigModel;
import com.zilue.module.system.model.EmailConfigModel;
import com.zilue.module.system.model.MessageConfigModel;
import com.zilue.module.system.model.WechatEnterpriseConfigModel;
import com.zilue.module.system.service.ISystemConfigService;
import org.springframework.stereotype.Service;

/**
 * @Author: zilue
 * @Date: 2024/1/16 14:12
 */
@Service
public class SystemConfigServiceImpl  extends ServiceImpl<SystemConfigMapper, SystemConfig> implements ISystemConfigService {


    @Override
    public boolean updateSystemConfig(UpdateSystemConfigDto dto) {

        long count = this.count(Wrappers.lambdaQuery(SystemConfig.class).eq(SystemConfig::getConfigType, dto.getConfigType()));
        SystemConfig systemConfig = BeanUtil.toBean(dto, SystemConfig.class);
        //如果存在 就代表此配置存在数据库  只需要修改
        if(count > 0){
            this.update(systemConfig,Wrappers.lambdaQuery(SystemConfig.class).eq(SystemConfig::getConfigType, dto.getConfigType()));
        }
        else {
            this.save(systemConfig);
        }

        //如果是邮件配置
        if (systemConfig.getConfigType() == SystemConfigEnum.EMAIL.getCode()) {
            EmailConfigModel emailConfigModel = JSONUtil.toBean(systemConfig.getConfigJson(), EmailConfigModel.class);
            if (emailConfigModel != null) {
                //由于启动的时候就会把对应的bean注册一次，修改之前先移除之前的
                SpringUtil.unregisterBean(MailAccount.class.getName());
                SpringUtil.registerBean(MailAccount.class.getName(), SystemConfigUtil.createMailAccount(emailConfigModel));
            }
        }
        //如果是短信配置
        if (systemConfig.getConfigType() == SystemConfigEnum.MESSAGE.getCode()) {
            MessageConfigModel messageConfigModel = JSONUtil.toBean(systemConfig.getConfigJson(), MessageConfigModel.class);
            if (messageConfigModel != null) {
                //由于启动的时候就会把对应的bean注册一次，修改之前先移除之前的
                SpringUtil.unregisterBean(XjrSmsConfig.class.getName());
                SpringUtil.registerBean(XjrSmsConfig.class.getName(), SystemConfigUtil.createSmsConfig(messageConfigModel));
            }
        }

        if (systemConfig.getConfigType() == SystemConfigEnum.WECHAT.getCode()) {
            WechatEnterpriseConfigModel wechatEnterpriseConfigModel = JSONUtil.toBean(systemConfig.getConfigJson(), WechatEnterpriseConfigModel.class);
            if (wechatEnterpriseConfigModel != null) {
                //由于启动的时候就会把对应的bean注册一次，修改之前先移除之前的
                SpringUtil.unregisterBean(WechatEnterpriseConfig.class.getName());
                SpringUtil.registerBean(WechatEnterpriseConfig.class.getName(), SystemConfigUtil.createWechatEnterpriseConfig(wechatEnterpriseConfigModel));
            }
        }

        if (systemConfig.getConfigType() == SystemConfigEnum.DINGTALK.getCode()) {
            DingtalkConfigModel dingtalkConfigModel = JSONUtil.toBean(systemConfig.getConfigJson(), DingtalkConfigModel.class);
            if (dingtalkConfigModel != null) {
                //由于启动的时候就会把对应的bean注册一次，修改之前先移除之前的
                SpringUtil.unregisterBean(DingtalkConfig.class.getName());
                SpringUtil.registerBean(DingtalkConfig.class.getName(), SystemConfigUtil.createDingtalkConfig(dingtalkConfigModel));
            }
        }

        if (systemConfig.getConfigType() == SystemConfigEnum.WEBHOOK.getCode()) {
            WebhookConfig webhookConfig = JSONUtil.toBean(systemConfig.getConfigJson(), WebhookConfig.class);
            if (webhookConfig != null) {
                //由于启动的时候就会把对应的bean注册一次，修改之前先移除之前的
                SpringUtil.unregisterBean(WebhookConfig.class.getName());
                SpringUtil.registerBean(WebhookConfig.class.getName(), webhookConfig);
            }
        }

        if (systemConfig.getConfigType() == SystemConfigEnum.WECHAT_OFFICIAL.getCode()) {
            WechatOfficalConfig wechatOfficalConfig = JSONUtil.toBean(systemConfig.getConfigJson(), WechatOfficalConfig.class);
            if (wechatOfficalConfig != null) {
                //由于启动的时候就会把对应的bean注册一次，修改之前先移除之前的
                SpringUtil.unregisterBean(WechatOfficalConfig.class.getName());
                SpringUtil.registerBean(WechatOfficalConfig.class.getName(), wechatOfficalConfig);
            }
        }
        return true;
    }
}
