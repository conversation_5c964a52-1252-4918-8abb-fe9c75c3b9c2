package com.zilue.module.system.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/11 9:39
 */
@Data
public class DictionaryItemExportVo {

    @ApiModelProperty("项目名")
    @ExcelProperty("项目名(*)")
    private String name;

    @ApiModelProperty("编号")
    @ExcelProperty("编号(*)")
    private String code;

    @ApiModelProperty("排序")
    @ExcelProperty("排序(*)")
    private Integer sortCode;

    @ApiModelProperty("备注")
    @ExcelProperty("备注(*)")
    private String remark;


}
