package com.zilue.module.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zilue.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 调查问卷
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-15
 */
@Data
@TableName("xjr_questionnaire")
@ApiModel(value = "Questionnaire对象", description = "调查问卷")
public class Questionnaire extends AuditEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("分类")
    private Long category;

    @ApiModelProperty("问卷内容配置")
    private String content;

    @ApiModelProperty("备注")
    private String remark;
}
