package com.zilue.module.system.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class LoginCaptchaDto {

    @ApiModelProperty(value = "手机")
    @NotBlank(message = "手机不能为空！")
    private String mobile;

    @ApiModelProperty(value = "设备类型-默认为PC，pc为0，app为1",required = false)
    private Integer deviceType;

    @ApiModelProperty(value = "验证码")
    @NotBlank(message = "验证码不能为空！")
    private String code;
}
