package com.zilue.module.system.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/7 14:05
 */
@Data
public class FileManagementPageVo {
    @ApiModelProperty("文件主键")
    private Long id;

    @ApiModelProperty("文件名称")
    private String fileName;

    @ApiModelProperty("文件路径")
    private String fileUrl;

    @ApiModelProperty("文件大小")
    private Long fileSize;

    @ApiModelProperty("文件后缀")
    private String fileSuffiex;

    @ApiModelProperty("文件类型")
    private String fileType;

    @ApiModelProperty("备注")
    private String remark;

    private LocalDateTime modifyDate;

    private String createUserName;
}
