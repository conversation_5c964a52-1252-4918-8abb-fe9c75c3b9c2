package com.zilue.module.system.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import cn.hutool.db.Page;
import cn.hutool.db.PageResult;
import cn.hutool.db.sql.Direction;
import cn.hutool.db.sql.Order;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.handler.XjrEntityHandler;
import com.zilue.common.page.PageOutput;
import com.zilue.common.utils.DatasourceUtil;
import com.zilue.common.utils.LocalDateTimeUtil;
import com.zilue.module.form.utils.FormDataTransUtil;
import com.zilue.module.generator.entity.ColumnConfig;
import com.zilue.module.generator.entity.TableFieldConfig;
import com.zilue.module.generator.entity.TableStructureConfig;
import com.zilue.module.system.dto.*;
import com.zilue.module.system.entity.Questionnaire;
import com.zilue.module.system.service.IQuestionnaireExecuteService;
import com.zilue.module.system.service.IQuestionnaireService;
import lombok.AllArgsConstructor;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.StringValue;
import net.sf.jsqlparser.expression.TimestampValue;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.expression.operators.conditional.OrExpression;
import net.sf.jsqlparser.expression.operators.relational.GreaterThanEquals;
import net.sf.jsqlparser.expression.operators.relational.LikeExpression;
import net.sf.jsqlparser.expression.operators.relational.MinorThanEquals;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.Select;
import net.sf.jsqlparser.util.SelectUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <p>
 * 调查问卷 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-15
 */
@Service
@AllArgsConstructor
public class QuestionnaireExecuteServiceImpl implements IQuestionnaireExecuteService {

    private final IQuestionnaireService questionnaireService;

    @Override
    public PageOutput<Entity> page(QuestionnaireExecutePageDto dto) throws Exception {
        Questionnaire questionnaire = questionnaireService.getById(dto.getId());
        UpdateQuestionnaireDto updateQuestionnaireDto = JSONUtil.toBean(questionnaire.getContent(), UpdateQuestionnaireDto.class);
        List<TableStructureConfig> tableStructureConfigs = updateQuestionnaireDto.getTableStructureConfigs();
        PageResult<Entity> pageResult = null;
        if (CollectionUtils.isNotEmpty(tableStructureConfigs)) {
            String databaseId = updateQuestionnaireDto.getDatabaseId();
            String startTime = dto.getStartTime();
            String endTime = dto.getEndTime();
            TableStructureConfig tableStructureConfig = tableStructureConfigs.get(0);
            String tableName = tableStructureConfig.getTableName();
            List<ColumnConfig> columnConfigs = updateQuestionnaireDto.getColumnConfigs();
            List<String> columnList = new ArrayList<>(columnConfigs.size() + 2);
            columnList.add(GlobalConstant.DEFAULT_PK);
            for (ColumnConfig columnConfig : columnConfigs) {
                columnList.add(columnConfig.getColumnName());
            }
            //构建分页参数
            Page page = new Page(dto.getLimit() - 1, dto.getSize(), new Order(GlobalConstant.CREATE_DATE, Direction.DESC));
            if (ObjectUtils.isNotEmpty(dto.getKeyword()) || ObjectUtils.isNotEmpty(startTime) || ObjectUtils.isNotEmpty(endTime)) {
                Select select = SelectUtils.buildSelectFromTableAndExpressions(new Table(tableName), columnList.toArray(new String[0]));
                PlainSelect plainSelect = (PlainSelect) select.getSelectBody(); // 转换为更细化的Select对象
                Expression expression = null;
                GreaterThanEquals geq = null;
                if (ObjectUtils.isNotEmpty(startTime)) {
                    geq = new GreaterThanEquals(); // ">="
                    geq.setLeftExpression(new net.sf.jsqlparser.schema.Column(GlobalConstant.CREATE_DATE));
                    geq.setRightExpression(new TimestampValue().withValue(Timestamp.valueOf(LocalDateTimeUtil.parseDateByLength(startTime))));
                }
                if (ObjectUtils.isNotEmpty(endTime)) {
                    MinorThanEquals meq = new MinorThanEquals(); // "<="
                    meq.setLeftExpression(new net.sf.jsqlparser.schema.Column(GlobalConstant.CREATE_DATE));
                    meq.setRightExpression(new TimestampValue().withValue(Timestamp.valueOf(LocalDateTimeUtil.parseDateByLength(endTime))));
                    if (geq != null) {
                        expression = new AndExpression(geq, meq);
                    } else {
                        expression = meq;
                    }
                }
                if (ObjectUtils.isNotEmpty(dto.getKeyword())) {
                    OrExpression orExpression = null;
                    for (String column : columnList) {
                        if (StrUtil.equalsIgnoreCase(column, GlobalConstant.DEFAULT_PK)
                                || StrUtil.equalsIgnoreCase(column, GlobalConstant.CREATE_DATE)) {
                            continue;
                        }
                        LikeExpression like  = new LikeExpression();
                        like.setLeftExpression(new Column(column));
                        like.setRightExpression(new StringValue(StringPool.PERCENT + dto.getKeyword() + StringPool.PERCENT));
                        if (orExpression == null) {
                            orExpression = new OrExpression();
                            orExpression.setLeftExpression(like);
                        } else if (orExpression.getRightExpression() == null) {
                            orExpression.setRightExpression(like);
                        } else {
                            orExpression = new OrExpression(orExpression, like);
                        }
                    }
                    if (expression == null) {
                        expression = orExpression;
                    } else {
                        expression = new AndExpression(expression, orExpression);
                    }
                }
                plainSelect.setWhere(expression);
                pageResult = Db.use(DatasourceUtil.getDataSource(databaseId)).page(plainSelect.toString(), page);
            } else {
                Entity where = Entity.create(tableName).setFieldNames(columnList);
                pageResult = Db.use(DatasourceUtil.getDataSource(databaseId)).page(where, page);
            }

            // 列表数据转换
            FormDataTransUtil.transData(pageResult, updateQuestionnaireDto.getFormJson().getList());

            PageOutput<Entity> pageOutput = new PageOutput<>();
            pageOutput.setPageSize(pageResult.getPageSize());
            pageOutput.setCurrentPage(pageResult.getPage());
            pageOutput.setTotal(pageResult.getTotal());
            pageOutput.setList(pageResult);

            return pageOutput;
        }
        return null;
    }

    @Override
    public Object info(QuestionnaireExecuteInfoDto dto) throws SQLException {
        Questionnaire questionnaire = questionnaireService.getById(dto.getId());
        UpdateQuestionnaireDto updateQuestionnaireDto = JSONUtil.toBean(questionnaire.getContent(), UpdateQuestionnaireDto.class);
        List<TableStructureConfig> tableStructureConfigs = updateQuestionnaireDto.getTableStructureConfigs();
        Set<String> fieldList = new LinkedHashSet<>();
        Entity where = Entity.create();
        for (TableStructureConfig tableStructureConfig : tableStructureConfigs) {
            where.setTableName(tableStructureConfig.getTableName());
            where.set(GlobalConstant.DEFAULT_PK, dto.getDataId());
            fieldList.add(GlobalConstant.DEFAULT_PK);
            for (TableFieldConfig fieldConfig : tableStructureConfig.getTableFieldConfigs()) {
                fieldList.add(fieldConfig.getFieldName());
            }
        }
        return Db.use(DatasourceUtil.getDataSource(updateQuestionnaireDto.getDatabaseId())).find(fieldList, where, new XjrEntityHandler());
    }

    @Override
    public Boolean add(AddQuestionnaireExecuteDto dto) throws Exception {
        Questionnaire questionnaire = questionnaireService.getById(dto.getId());
        UpdateQuestionnaireDto updateQuestionnaireDto = JSONUtil.toBean(questionnaire.getContent(), UpdateQuestionnaireDto.class);
        List<TableStructureConfig> tableStructureConfigs = updateQuestionnaireDto.getTableStructureConfigs();
        if (CollectionUtils.isNotEmpty(tableStructureConfigs)) {
            TableStructureConfig tableStructureConfig = tableStructureConfigs.get(0);
            Map<String, Object> data = dto.getFormData();
            // 添加主键值
            data.put(GlobalConstant.DEFAULT_PK, IdUtil.getSnowflakeNextId());
            Entity entity = Entity.create(tableStructureConfig.getTableName());
            entity.putAll(data);
            // 添加创建时间
            entity.put(GlobalConstant.CREATE_DATE, Timestamp.valueOf(LocalDateTime.now()));
            return Db.use(DatasourceUtil.getDataSource(updateQuestionnaireDto.getDatabaseId())).insert(entity) > 0;
        }
        return Boolean.FALSE;
    }

    @Override
    public Boolean delete(DeleteQuestionnaireExecuteDto dto) throws Exception {
        Questionnaire questionnaire = questionnaireService.getById(dto.getId());
        UpdateQuestionnaireDto updateQuestionnaireDto = JSONUtil.toBean(questionnaire.getContent(), UpdateQuestionnaireDto.class);
        List<TableStructureConfig> tableStructureConfigs = updateQuestionnaireDto.getTableStructureConfigs();
        if (CollectionUtils.isNotEmpty(tableStructureConfigs)) {
            TableStructureConfig tableStructureConfig = tableStructureConfigs.get(0);
            return Db.use(DatasourceUtil.getDataSource(updateQuestionnaireDto.getDatabaseId()))
                    .del(tableStructureConfig.getTableName(), GlobalConstant.DEFAULT_PK, dto.getDataId()) > 0;
        }
        return null;
    }
}
