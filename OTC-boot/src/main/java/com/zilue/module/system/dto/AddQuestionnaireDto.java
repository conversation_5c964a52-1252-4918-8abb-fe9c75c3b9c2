package com.zilue.module.system.dto;

import com.zilue.common.model.generator.FormConfig;
import com.zilue.module.form.entity.MenuConfig;
import com.zilue.module.generator.entity.ColumnConfig;
import com.zilue.module.generator.entity.TableStructureConfig;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.util.List;

@Data
public class AddQuestionnaireDto {

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("分类")
    private Long category;

    @ApiModelProperty("备注")
    private String remark;

    /**
     * 功能类名（必须英文字母开头 首字母不能使用字符以及数字）
     */
    @Length(max = 20, message = "功能名称不能超过20字符！")
    @Pattern(regexp = "^[a-zA-Z][a-zA-Z0-9]*$", message = "功能名称只能是数字和字母组成，必须英文字母开头 首字母不能使用字符以及数字！")
    private String className;

    /**
     * 描述 注释
     */
    @Length(max = 200, message = "描述不能超过200字符！")
    private String comment;

    /**
     * 输出区域（数据字典显示,数据字典id）
     */
    private String outputArea;

    @ApiModelProperty("数据库id")
    @NotBlank(message = "数据库id不能为空！")
    private String databaseId;

    /**
     * 表结构配置  代码优先！！！！
     */
    @Valid
    private List<TableStructureConfig> tableStructureConfigs;

    @Valid
    private FormConfig formJson;

    /**
     * 是否添加审计字段
     */
    private Boolean isCommonFields = false;

    @Valid
    private MenuConfig menuConfig;

    /**
     * 列配置
     */
    private List<ColumnConfig> columnConfigs;
}
