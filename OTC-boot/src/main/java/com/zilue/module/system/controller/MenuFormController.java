package com.zilue.module.system.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.model.result.R;
import com.zilue.common.utils.TreeUtil;
import com.zilue.common.utils.VoToColumnUtil;
import com.zilue.module.system.entity.MenuForm;
import com.zilue.module.system.service.IMenuFormService;
import com.zilue.module.system.vo.MenuFormListVo;
import com.zilue.module.system.vo.MenuFormVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 菜单表单字段 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-08
 */
@RestController
@RequestMapping(GlobalConstant.SYSTEM_MODULE_PREFIX + "/menu-form")
@Api(value = GlobalConstant.SYSTEM_MODULE_PREFIX + "/menu-form",tags = "菜单表单")
@AllArgsConstructor
public class MenuFormController {

    private final IMenuFormService menuFormService;

    @GetMapping(value = "/list")
    @ApiOperation(value="根据菜单id查询表单列表")
    public R getMenuColumnListByMenuId(@RequestParam(required = false) Long menuId){
        if (menuId == null) {
            return R.ok(new ArrayList<>(0));
        }
        List<MenuForm> list = menuFormService.list(Wrappers.<MenuForm>lambdaQuery().eq(menuId != 0, MenuForm::getMenuId, menuId)
                .select(MenuForm.class, x -> VoToColumnUtil.fieldsToColumns(MenuFormVo.class).contains(x.getProperty())));
        List<MenuFormListVo> menuFormListVos = BeanUtil.copyToList(list, MenuFormListVo.class);
        return R.ok(TreeUtil.build(menuFormListVos));
    }
}
