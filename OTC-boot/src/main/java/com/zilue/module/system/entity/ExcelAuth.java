package com.zilue.module.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.zilue.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 在线excel权限表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-25
 */
@TableName("xjr_excel_auth")
@ApiModel(value = "ExcelAuth对象", description = "在线excel权限表")
@Data
@EqualsAndHashCode(callSuper = false)
public class ExcelAuth extends AuditEntity implements Serializable {

    private static final long serialVersionUID = 1L;

      @ApiModelProperty("主键")
        private Long id;

      @ApiModelProperty("excel表主键id")
      private Long excelId;

      @ApiModelProperty("权限类型，0-用户，1-角色，2-岗位")
      private Integer authType;

      @ApiModelProperty("岗位、用户、角色的主键id")
      private Long objectId;

      @ApiModelProperty("备注")
      private String remark;
}
