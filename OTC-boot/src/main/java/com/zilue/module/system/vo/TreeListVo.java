package com.zilue.module.system.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class TreeListVo {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("父级图标")
    private String parentIcon;

    @ApiModelProperty("子级图标")
    private String childIcon;

    private Long createUserId;

    private String createUserName;

    private LocalDateTime createDate;

    private Long modifyUserId;

    private String modifyUserName;

    private LocalDateTime modifyDate;

}
