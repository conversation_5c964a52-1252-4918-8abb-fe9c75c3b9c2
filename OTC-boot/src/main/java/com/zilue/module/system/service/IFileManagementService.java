package com.zilue.module.system.service;

import com.zilue.common.page.PageInput;
import com.zilue.common.page.PageOutput;
import com.zilue.module.system.dto.UpdateFileAuthDto;
import com.zilue.module.system.entity.FileManagement;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zilue.module.system.vo.FileAuthVo;
import com.zilue.module.system.vo.FileManagementPageVo;

import java.util.List;

/**
 * <p>
 * 文件管理表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-07
 */
public interface IFileManagementService extends IService<FileManagement> {
    PageOutput<FileManagementPageVo> getPageList( PageInput dto);

    Boolean updateFileAuth(UpdateFileAuthDto dto);

    List<FileAuthVo> fileAuthInfo(Long id);
}
