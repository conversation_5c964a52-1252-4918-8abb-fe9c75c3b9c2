package com.zilue.module.system.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @title: MenuButtonVo
 * <AUTHOR>
 * @Date: 2023/4/4 19:10
 * @Version 1.0
 */
@Data
public class MenuButtonVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("按钮名")
    private String name;

    @ApiModelProperty("菜单Id")
    private Long menuId;

    @ApiModelProperty("图标")
    private String icon;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("请求地址")
    private String url;

    @ApiModelProperty("请求方式")
    private Integer method;

}

