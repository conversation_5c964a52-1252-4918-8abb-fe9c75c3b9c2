package com.zilue.module.system.service;

import com.github.yulichang.base.MPJBaseService;
import com.zilue.common.page.PageInput;
import com.zilue.common.page.PageOutput;
import com.zilue.module.system.entity.CodeRule;
import com.zilue.module.system.vo.CodeRuleVo;

import java.util.List;

/**
 * <p>
 * 编号规则表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-23
 */
public interface ICodeRuleService extends MPJBaseService<CodeRule> {

    PageOutput<CodeRuleVo> getCodeRulePageList(PageInput dto);

    String genEncode(String encode);

    boolean useEncode(String encode);

    boolean useEncode(List<String> encodeList);
}
