package com.zilue.module.system.dto;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date:2024/8/27 11:40
 */
@Data
public class AddDictionaryTypeDto {

    @NotNull(message = "数据字典项分类名称不能为空!")
    @Length(min = 1,max = 50,message = "数据字典项分类名称长度不能大于50字符！")
    private String typeName;

    private Integer sortCode;

    @Length(max = 255,message = "备注长度不能大于255字符！")
    private String remark;
}
