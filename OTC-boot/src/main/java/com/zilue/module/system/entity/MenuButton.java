package com.zilue.module.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 菜单按钮
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-16
 */
@Data
@TableName("xjr_menu_button")
@ApiModel(value = "MenuButton对象", description = "菜单按钮")
public class MenuButton implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("按钮名")
    private String name;

    @ApiModelProperty("菜单Id")
    private Long menuId;

    @ApiModelProperty("图标")
    private String icon;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("请求地址")
    private String url;

    @ApiModelProperty("请求方式")
    private Integer method;


}
