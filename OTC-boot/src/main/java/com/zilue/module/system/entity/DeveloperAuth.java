package com.zilue.module.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 开发者权限表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Data
@TableName("xjr_developer_auth")
@ApiModel(value = "DeveloperAuth对象", description = "开发者权限表")
public class DeveloperAuth implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("开发者主键")
    private Long devId;

    @ApiModelProperty("接口主键")
    private String interfaceId;
}
