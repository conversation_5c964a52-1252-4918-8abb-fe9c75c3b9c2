package com.zilue.module.system.service.impl;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.zilue.module.system.entity.Developer;
import com.zilue.module.system.mapper.DeveloperMapper;
import com.zilue.module.system.service.IDeveloperService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 开发者表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Service
public class DeveloperServiceImpl extends MPJBaseServiceImpl<DeveloperMapper, Developer> implements IDeveloperService {

}
