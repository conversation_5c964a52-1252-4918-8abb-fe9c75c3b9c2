package com.zilue.module.system.service.impl;

import com.zilue.module.system.entity.FileAuth;
import com.zilue.module.system.mapper.FileAuthMapper;
import com.zilue.module.system.service.IFileAuthService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 文件管理权限表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-07
 */
@Service
public class FileAuthServiceImpl extends ServiceImpl<FileAuthMapper, FileAuth> implements IFileAuthService {

}
