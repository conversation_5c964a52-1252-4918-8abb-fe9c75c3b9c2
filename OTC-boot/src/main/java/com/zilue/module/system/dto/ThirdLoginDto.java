package com.zilue.module.system.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;


/**
 * <AUTHOR>
 */
@Data
public class ThirdLoginDto {

    @ApiModelProperty(value = "用户工号")
    @NotBlank(message = "用户工号不能为空！")
    private String userCode;

    @ApiModelProperty(value = "设备类型-默认为PC，pc为0，app为1",required = false)
    private Integer deviceType;

}
