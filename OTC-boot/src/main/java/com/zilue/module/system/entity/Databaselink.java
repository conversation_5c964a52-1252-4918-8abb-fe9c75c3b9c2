package com.zilue.module.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.zilue.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 数据库连接表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-04
 */
@TableName("xjr_databaselink")
@ApiModel(value = "Databaselink对象", description = "数据库连接表")
@Data
@EqualsAndHashCode(callSuper = false)
public class Databaselink extends AuditEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("数据库连接主键")
    private Long id;

    @ApiModelProperty("连接地址")
    private String host;

    @ApiModelProperty("账号")
    private String username;

    @ApiModelProperty("密码")
    private String password;

    @ApiModelProperty("驱动")
    private String driver;

    @ApiModelProperty("数据库名称")
    private String dbName;

    @ApiModelProperty("数据库别名")
    private String dbAlisa;

    @ApiModelProperty("数据库类型")
    private String dbType;

    @ApiModelProperty("数据库版本")
    private String dbVersion;

    @ApiModelProperty("排序码")
    private Integer sortCode;

    @ApiModelProperty("排序码")
    private String remark;

}
