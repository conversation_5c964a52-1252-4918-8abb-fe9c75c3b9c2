package com.zilue.module.system.service.impl;

import com.zilue.module.system.entity.MessageTemplate;
import com.zilue.module.system.mapper.MessageTemplateMapper;
import com.zilue.module.system.service.IMessageTemplateService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-16
 */
@Service
public class MessageTemplateServiceImpl extends ServiceImpl<MessageTemplateMapper, MessageTemplate> implements IMessageTemplateService {

}
