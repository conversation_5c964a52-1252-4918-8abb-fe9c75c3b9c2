package com.zilue.module.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 菜单表单字段
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-08
 */
@Data
@TableName("xjr_menu_form")
@ApiModel(value = "MenuForm对象", description = "菜单表单字段")
public class MenuForm implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("菜单主键")
    private Long menuId;

    @ApiModelProperty("父级字段")
    private Long parentId;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("排序码")
    private Integer sortCode;

    @ApiModelProperty("是否必填，0-非必填，1-必填")
    private Integer isRequired;
}
