package com.zilue.module.system.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class UpdateStampDto implements Serializable {

    @ApiModelProperty("主键id")
    @NotNull(message = "修改操作id不能为空")
    private Long id;

    @ApiModelProperty("签章名")
    @NotNull(message = "签章名称不能为空")
    private String name;

    @ApiModelProperty("签章类型 0 私人  1  公共")
    @NotNull(message = "类型不能为空")
    private Integer stampType;

    @NotNull(message = "签章分类不能为空")
    @ApiModelProperty("签章分类(数据字典)")
    private Long stampCategory;

    @ApiModelProperty("1,上传图片，2,手写签名的类型")
    @NotNull(message = "不能为空")
    private Integer fileType;

    @ApiModelProperty("图片地址")
    @NotNull(message = "图片地址不能为空")
    private String fileUrl;

    @ApiModelProperty("密码")
    @NotNull(message = "密码不能为空")
    @Length(min = 6,max = 20,message = "密码最少六个字符，最大20个字符")
    private String password;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("排序")
    @NotNull(message = "排序字段不能为空")
    private Integer sortCode;
}
