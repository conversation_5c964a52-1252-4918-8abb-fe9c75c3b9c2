package com.zilue.module.system.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zilue.module.business.sign.vo.OtcSignInAdminPageVo;
import com.zilue.module.system.dto.DictionaryItemPageDto;
import com.zilue.module.system.entity.DictionaryItem;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zilue.module.system.vo.DictionaryItemVo;
import com.zilue.module.wechat.sign.dto.OtcSignInPageDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 数据字典项目 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-17
 */
@Mapper
public interface DictionaryitemMapper extends BaseMapper<DictionaryItem> {
    List<DictionaryItemVo> dicItemList(@Param("dto") DictionaryItemPageDto dto);
}
