package com.zilue.module.system.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/11 9:39
 */
@Data
public class MenuColumnExportVo {
    @ApiModelProperty("编码")
    @ExcelProperty("名称(*)")
    private String code;

    @ApiModelProperty("名称")
    @ExcelProperty("名称(*)")
    private String name;

    @ApiModelProperty("排序码")
    @ExcelProperty("排序码")
    private Integer sortCode;

    @ApiModelProperty("描述")
    @ExcelProperty("描述")
    private String description;

}
