package com.zilue.module.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.zilue.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 用户菜单列表页列展示关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-14
 */
@TableName("xjr_user_menu_column_show")
@ApiModel(value = "UserMenuColumnShow对象", description = "用户菜单列表页列展示关系表")
@Data
@EqualsAndHashCode(callSuper = true)
public class UserMenuColumnShow extends AuditEntity implements Serializable {

    private static final long serialVersionUID = 1L;

      @ApiModelProperty("主键")
        private Long id;

      @ApiModelProperty("用户主键")
      private Long userId;

      @ApiModelProperty("菜单主键")
      private Long menuId;

      @ApiModelProperty("桌面设计主键")
      private String columnJson;
}
