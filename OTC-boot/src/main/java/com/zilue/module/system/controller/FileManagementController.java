package com.zilue.module.system.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.model.result.R;
import com.zilue.common.page.PageInput;
import com.zilue.common.page.PageOutput;
import com.zilue.common.utils.UploadUtil;
import com.zilue.module.system.dto.*;
import com.zilue.module.system.entity.FileManagement;
import com.zilue.module.system.service.IFileManagementService;
import com.zilue.module.system.vo.FileManagementPageVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 文件管理表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-07
 */
@RestController
@RequestMapping(GlobalConstant.SYSTEM_MODULE_PREFIX +"/file-management")
@Api(value = GlobalConstant.SYSTEM_MODULE_PREFIX +"/file-management", tags = "文件管理")
@AllArgsConstructor
public class FileManagementController {

    private final IFileManagementService fileManagementService;

    @GetMapping("/page")
    @ApiOperation(value = "查询文件列表(分页)")
    public R page(@Valid PageInput dto) {
        PageOutput<FileManagementPageVo> pageList = fileManagementService.getPageList(dto);
        return R.ok(pageList);
    }

    @DeleteMapping
    @ApiOperation(value = "删除文件(可批量)")
    public R delete(@RequestBody List<Long> ids) {
        return R.ok(fileManagementService.removeBatchByIds(ids));
    }

    @PutMapping("update-file-name")
    @ApiOperation(value = "修改fileName")
    @Transactional(rollbackFor = Exception.class)
    public R updateFileName(@Valid @RequestBody UpdateExcelNameDto dto) {
        FileManagement fileManagement = BeanUtil.toBean(dto, FileManagement.class);
        return R.ok(fileManagementService.updateById(fileManagement));
    }

    @PostMapping
    @ApiOperation(value = "单文件上传")
    public R uploadFile(@RequestParam(value = "file", required = true) MultipartFile multipartFile) throws Exception {
        Long fileId = IdWorker.getId();
        String filename = multipartFile.getOriginalFilename();
        String suffix = StringUtils.substringAfterLast(filename, StringPool.DOT);
        //保存到云服务器
        String filePath = UploadUtil.uploadFileNew(multipartFile);

        FileManagement fileEntity = new FileManagement();
        fileEntity.setId(fileId);
        fileEntity.setFileName(filename);
        fileEntity.setFileUrl(filePath);
        fileEntity.setFileSize(multipartFile.getSize());
        fileEntity.setFileSuffiex(StringPool.DOT + suffix);
        fileEntity.setFileType(suffix);
        fileManagementService.save(fileEntity);
        return R.ok(filePath);
    }

    @PutMapping("update-file-auth")
    @ApiOperation(value = "修改file权限")
    @Transactional(rollbackFor = Exception.class)
    public R updateFileAuth(@Valid @RequestBody UpdateFileAuthDto dto) {
        return R.ok(fileManagementService.updateFileAuth(dto));
    }

    @GetMapping("/file-auth-info")
    @ApiOperation(value = "获取文件权限数据")
    public R fileAuthInfo(@RequestParam(required = true) Long id) {
        return R.ok(fileManagementService.fileAuthInfo(id));
    }

}
