package com.zilue.module.system.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.zilue.module.system.entity.Tree;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-23
 */
@Mapper
public interface TreeMapper extends MPJBaseMapper<Tree> {


    @Select("select name,code from (select * from xjr_tree) t where name like concat('%', #{keyword}, '%')")
    List<Tree> treeList(@Param("keyword") String keyword);
}
