package com.zilue.module.system.vo;

import com.zilue.common.model.tree.ITreeNode;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class MenuSimpleTreeVo implements ITreeNode<MenuSimpleTreeVo, Long>, Serializable {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("上级Id")
    private Long parentId;

    @ApiModelProperty("组件名（路由名称） --  与vue代码组件名必须一直 才能做到缓存页面 相关联")
    private String name;

    @ApiModelProperty("菜单名")
    private String title;

    @ApiModelProperty("菜单编号")
    private String code;

    @ApiModelProperty("菜单图标")
    private String icon;

    @ApiModelProperty("地址")
    private String path;

    @ApiModelProperty("组件地址")
    private String component;

    @ApiModelProperty("组件类型 默认组件 0 普通需要注册的组件 1 自定义表单 桌面设计 等已经默认注册进来的组件 ")
    private Integer componentType;

    @ApiModelProperty("组件类型")
    private Integer menuType;

    /**
     * 外链地址
     */
    private String iframeSrc;

    @ApiModelProperty("菜单显示或者隐藏")
    private Integer display;

    @ApiModelProperty("是否允许修改")
    private Integer allowModify;

    @ApiModelProperty("是否允许删除")
    private Integer allowDelete;

    @ApiModelProperty("是否外链")
    private Integer outLink;

    @ApiModelProperty("页面持久化")
    private Integer keepAlive;

    @ApiModelProperty("排序码")
    private Integer sortCode;

    @ApiModelProperty("排序码")
    private String remark;

    @ApiModelProperty("系统主键（主系统默认为0）")
    private Long systemId;

    @ApiModelProperty("系统名称")
    private String systemName;

    private String openMode;

    private List<MenuSimpleTreeVo> children;
}
