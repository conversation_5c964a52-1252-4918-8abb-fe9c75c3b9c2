package com.zilue.module.system.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/11 9:39
 */
@Data
public class DictionaryDetailExportVo {
    @ApiModelProperty("字典名字")
    @ExcelProperty("字典名字(*)")
    private String name;

    @ApiModelProperty("字典编码")
    @ExcelProperty("字典编码(*)")
    private String code;

    @ApiModelProperty("字典值")
    @ExcelProperty("字典值(*)")
    private String value;

    @ApiModelProperty("所属字典项id")
    @ExcelProperty("所属字典项id(*)")
    private Long itemId;

    @ApiModelProperty("排序")
    private Integer sortCode;

    @ApiModelProperty("备注")
    private String remark;

}
