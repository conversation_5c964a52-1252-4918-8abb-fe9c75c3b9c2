package com.zilue.module.system.vo;

import com.zilue.common.model.tree.ITreeNode;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class MenuFormTreeVo implements ITreeNode<MenuFormTreeVo,Long>, Serializable {
    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("菜单主键")
    private Long menuId;

    @ApiModelProperty("父级字段")
    private Long parentId;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("排序码")
    private Integer sortCode;

    @ApiModelProperty("是否必填，0-非必填，1-必填")
    private Integer isRequired;

    private List<MenuFormTreeVo> children;
}
