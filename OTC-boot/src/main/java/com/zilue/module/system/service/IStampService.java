package com.zilue.module.system.service;

import com.github.yulichang.base.MPJBaseService;

import com.zilue.module.system.dto.AddMemberDto;
import com.zilue.module.system.dto.UpdateStampDto;
import com.zilue.module.system.entity.Stamp;
import com.zilue.module.system.vo.StampMemberVo;

import java.util.List;

/**
 * <p>
 * 印章表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24
 */
public interface IStampService extends MPJBaseService<Stamp> {

    List<StampMemberVo> selectMember(Long id);

    boolean addMember(AddMemberDto dto);

    List<StampMemberVo> selectMaintain(Long id);

    boolean addMaintain(AddMemberDto dto);

    boolean updateStamp(UpdateStampDto dto);

    boolean deleteStamp(List<Long> ids);

    boolean enabled(Long id);

    boolean setDefaultStamp(Long id);


//    PageOutput stampPage(StampPageDto dto);
}
