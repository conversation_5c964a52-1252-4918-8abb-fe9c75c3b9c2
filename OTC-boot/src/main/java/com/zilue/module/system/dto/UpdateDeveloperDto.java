package com.zilue.module.system.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class UpdateDeveloperDto {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("名称")
    private String name;

    private String appId;

    private String appSecret;

    @ApiModelProperty("应用名称")
    private String appName;

    @ApiModelProperty("验证签名")
    private Integer validateSign;

    @ApiModelProperty("使用期限")
    private LocalDateTime expireDate;

    @ApiModelProperty("白名单")
    private String whiteList;

    @ApiModelProperty("备注")
    private String remark;

    private Integer enabledMark;
}
