package com.zilue.module.system.controller;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.toolkit.MPJWrappers;
import com.jdcloud.sdk.utils.StringUtils;
import com.zilue.common.annotation.XjrLog;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.enums.AuthorizeType;
import com.zilue.common.enums.EnabledMark;
import com.zilue.common.enums.MenuType;
import com.zilue.common.enums.YesOrNoEnum;
import com.zilue.common.handler.FormContentStyleStrategy;
import com.zilue.common.model.result.R;
import com.zilue.common.utils.ExcelUtil;
import com.zilue.common.utils.TreeUtil;
import com.zilue.common.utils.VoToColumnUtil;
import com.zilue.module.organization.service.IUserRoleRelationService;
import com.zilue.module.system.dto.*;
import com.zilue.module.system.entity.*;
import com.zilue.module.system.service.*;
import com.zilue.module.system.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 用户 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-16
 */
@RestController
@RequestMapping(GlobalConstant.SYSTEM_MODULE_PREFIX + "/menu")
@Api(value = GlobalConstant.SYSTEM_MODULE_PREFIX + "/menu", tags = "菜单模块")
@AllArgsConstructor
public class MenuController {

    private final IMenuService menuService;

    private final IMenuButtonService menuButtonService;

    private final IMenuColumnService menuColumnService;

    private final IMenuFormService menuFormService;

    private final IAuthorizeService authorizeService;

    private final IUserRoleRelationService userRoleRelationService;

    @GetMapping("/list")
    @ApiOperation(value = "获取所有菜单（不分页）")
    public R list() {

        List<Menu> list = menuService.list(Wrappers.lambdaQuery(Menu.class)
                .select(Menu.class, x -> VoToColumnUtil.fieldsToColumns(MenuListVo.class).contains(x.getProperty())));
        List<MenuListVo> menuListVos = BeanUtil.copyToList(list, MenuListVo.class);
        return R.ok(TreeUtil.build(menuListVos));
    }

    @GetMapping("/tree")
    @ApiOperation(value = "获取所有菜单（树结构）")
    public R menuTree(@Valid MenuTreeDto dto) {
        List<MenuVo> list = menuService.getAuthMenuList(dto);
        List<MenuTreeVo> voList = BeanUtil.copyToList(list, MenuTreeVo.class);
        List<MenuTreeVo> treeVoList = TreeUtil.build(voList);

        return R.ok(treeVoList);
    }

    @GetMapping("/all-tree")
    @ApiOperation(value = "获取所有菜单（树结构）")
    public R allMenuTree(@Valid MenuTreeDto dto) {
        if (ObjectUtil.isNotNull(dto.getSystemId())) dto.setEnabledMark(1);
        List<MenuTreeVo> list = menuService.selectJoinList(MenuTreeVo.class,
                MPJWrappers.<Menu>lambdaJoin()
                        .disableSubLogicDel()
                        .like(StrUtil.isNotEmpty(dto.getTitle()), Menu::getTitle, dto.getTitle())
                        .like(StrUtil.isNotEmpty(dto.getName()), Menu::getName, dto.getName())
                        .eq(ObjectUtil.isNotNull(dto.getSystemId()), Menu::getSystemId, dto.getSystemId())
                        .select(Menu::getId)
                        .select(Menu.class, x -> VoToColumnUtil.fieldsToColumns(MenuTreeVo.class).contains(x.getProperty()))
                        .selectAs(Subsystem::getName, MenuTreeVo::getSystemName)
                        .eq(ObjectUtils.isNotEmpty(dto.getEnabledMark()), Menu::getEnabledMark, dto.getEnabledMark())
                        .leftJoin(Subsystem.class, Subsystem::getId, Menu::getSystemId)
                        .orderByAsc(Menu::getSortCode));
        List<MenuTreeVo> treeVoList = TreeUtil.build(list);

        return R.ok(treeVoList);
    }

    @GetMapping("/child-tree")
    @ApiOperation(value = "获取所有菜单（树结构，禁用父级）")
    public R menuChildTree(@Valid MenuTreeDto dto) {
        List<MenuVo> list = menuService.getAuthMenuList(dto);
        List<MenuChildTreeVo> voList = BeanUtil.copyToList(list, MenuChildTreeVo.class);
        List<MenuChildTreeVo> treeVoList = TreeUtil.build(voList);
        voList.forEach(treeVo -> treeVo.setFullPath(true));
        return R.ok(treeVoList);
    }

    @GetMapping("/simple-tree")
    @ApiOperation(value = "获取所有菜单（树结构）")
    public R menuAuthTree(@Valid MenuTreeDto dto) {
        List<MenuVo> list = menuService.getAuthMenuList(dto);
        List<MenuSimpleTreeVo> voList = BeanUtil.copyToList(list, MenuSimpleTreeVo.class);
        List<MenuSimpleTreeVo> treeVoList = TreeUtil.build(voList);
        return R.ok(treeVoList);
    }

    @GetMapping(value = "/info")
    @ApiOperation(value = "根据id查询菜单信息")
    public R info(Long id) {
        Menu menu = menuService.getById(id);
        if (menu == null) {
            R.error("找不到此菜单！");
        }
        return R.ok(BeanUtil.toBean(menu, MenuVo.class));
    }

    @PostMapping
    @ApiOperation(value = "新增菜单")
    @Transactional(rollbackFor = Exception.class)
    public R add(@Valid @RequestBody AddMenuDto dto) {
        long count = menuService.count(Wrappers.<Menu>query().lambda().eq(Menu::getName, dto.getName()).or().eq(Menu::getCode, dto.getCode()));
        if (count > 0) {
            return R.error("组件名称或编码已经存在！");
        }
        Menu menu = BeanUtil.toBean(dto, Menu.class);

        if (ObjectUtil.isEmpty(menu.getSystemId())){
            menu.setSystemId(1L);
        }

        //判断是否为菜单
        if (menu.getMenuType() == YesOrNoEnum.NO.getCode()) {
            //如果是菜单 需要判断 path 第一个字符 是否为 /  菜单必须要 / 开头
            if (!menu.getPath().startsWith(StringPool.SLASH)) {
                menu.setPath(StringPool.SLASH + menu.getPath());
            }
        }

        //判断是否为外链
        if (menu.getOutLink() == YesOrNoEnum.YES.getCode()) {
            //如果是外链 所有组件地址 改为 IFrame
            menu.setComponent("IFrame");
        }

        menu.setAllowDelete(YesOrNoEnum.YES.getCode());
        menu.setAllowModify(YesOrNoEnum.YES.getCode());

        boolean isSuccess = menuService.save(menu);

        //按钮保存
        List<MenuButton> menuButtons = BeanUtil.copyToList(dto.getButtonList(), MenuButton.class);
        menuButtons.forEach(menuButton -> menuButton.setMenuId(menu.getId()));
        //删除原按钮
        menuButtonService.remove(Wrappers.<MenuButton>query().lambda().eq(MenuButton::getMenuId, menu.getId()));
        menuButtonService.saveBatch(menuButtons);

        // 列表字段保存
        List<MenuColumn> columnList = BeanUtil.copyToList(dto.getColumnList(), MenuColumn.class);
        columnList.forEach(menuColumn -> menuColumn.setMenuId(menu.getId()));
        menuColumnService.remove(Wrappers.<MenuColumn>query().lambda().eq(MenuColumn::getMenuId, menu.getId()));
        menuColumnService.saveBatch(columnList);

        // 表单字段保存
        List<MenuForm> formList = new ArrayList<>();
        for (AddMenuFormDto addMenuFormDto : dto.getFormList()) {
            formList.add(BeanUtil.toBean(addMenuFormDto, MenuForm.class));
            List<AddMenuFormDto> children = addMenuFormDto.getChildren();
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(children)) {
                formList.addAll(BeanUtil.copyToList(children, MenuForm.class));
            }
        }
        formList.forEach(menuForm -> menuForm.setMenuId(menu.getId()));
        menuFormService.remove(Wrappers.<MenuForm>query().lambda().eq(MenuForm::getMenuId, menu.getId()));
        menuFormService.saveBatch(formList);


        return R.ok(isSuccess);
    }

    @PutMapping
    @ApiOperation(value = "修改菜单")
    @Transactional(rollbackFor = Exception.class)
    public R edit(@Valid @RequestBody UpdateMenuDto dto) {
        long count = menuService.count(Wrappers.<Menu>query().lambda()
                .ne(Menu::getId, dto.getId()).and(wrapper ->
                        wrapper.eq(Menu::getName, dto.getName())
                        .or()
                        .eq(Menu::getCode, dto.getCode())
                        )
                );

        if (count > 0) {
            return R.error("组件名称或编码已经存在！");
        }
        Menu menu = BeanUtil.toBean(dto, Menu.class);

        //判断是否为菜单
        if (menu.getMenuType() == YesOrNoEnum.NO.getCode()) {
            //如果是菜单 需要判断 path 第一个字符 是否为 /  菜单必须要 / 开头
            if (!menu.getPath().startsWith(StringPool.SLASH)) {
                menu.setPath(StringPool.SLASH + menu.getPath());
            }
        }

        //判断是否为外链
        if (menu.getOutLink() == YesOrNoEnum.YES.getCode()) {
            //如果是外链 所有组件地址 改为 IFrame
            menu.setComponent("IFrame");
        }

        Wrapper<Menu> updateSubSystemIdWrapper = null;
        List<Long> childIds = dto.getChildIds();
        if (CollectionUtils.isNotEmpty(childIds)) {
            Menu oldMenu = menuService.getById(menu.getId());
            if (oldMenu.getSystemId() == null || !oldMenu.getSystemId().equals(dto.getSystemId())) {
                updateSubSystemIdWrapper = Wrappers.lambdaUpdate(Menu.class)
                        .set(Menu::getSystemId, dto.getSystemId())
                        .in(Menu::getId, childIds);
            }
        }

        menuService.updateById(menu);

        if (updateSubSystemIdWrapper != null) {
            menuService.update(updateSubSystemIdWrapper);
        }

        List<MenuButton> menuButtons = BeanUtil.copyToList(dto.getButtonList(), MenuButton.class);
        menuButtons.forEach(menuButton -> menuButton.setMenuId(menu.getId()));
        //删除原按钮
        menuButtonService.remove(Wrappers.<MenuButton>query().lambda().eq(MenuButton::getMenuId, menu.getId()));

        // 列表字段保存
        List<MenuColumn> columnList = BeanUtil.copyToList(dto.getColumnList(), MenuColumn.class);
        columnList.forEach(menuColumn -> menuColumn.setMenuId(menu.getId()));
        menuColumnService.remove(Wrappers.<MenuColumn>query().lambda().eq(MenuColumn::getMenuId, menu.getId()));

        // 表单字段保存
        List<MenuForm> formList = new ArrayList<>();
        for (UpdateMenuFormDto updateMenuFormDto : dto.getFormList()) {
            formList.add(BeanUtil.toBean(updateMenuFormDto, MenuForm.class));
            List<UpdateMenuFormDto> children = updateMenuFormDto.getChildren();
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(children)) {
                formList.addAll(BeanUtil.copyToList(children, MenuForm.class));
            }
        }
        formList.forEach(menuForm -> menuForm.setMenuId(menu.getId()));
        menuFormService.remove(Wrappers.<MenuForm>query().lambda().eq(MenuForm::getMenuId, menu.getId()));

        if (CollectionUtils.isNotEmpty(menuButtons)) {
            menuButtonService.saveBatch(menuButtons);
        }
        if (CollectionUtils.isNotEmpty(columnList)) {
            menuColumnService.saveBatch(columnList);
        }
        if (CollectionUtils.isNotEmpty(formList)) {
            menuFormService.saveBatch(formList);
        }
        return R.ok();
    }

    @GetMapping(value = "/button")
    @ApiOperation(value = "根据id查询按钮")
    public R button(@NotNull @Valid Long id) {
        List<MenuButton> list = menuButtonService.list(Wrappers.<MenuButton>query().lambda().eq(MenuButton::getMenuId, id));
        return R.ok(list);
    }


    @DeleteMapping
    @ApiOperation(value = "批量删除菜单")
    public R delete(@Valid @RequestBody List<Long> ids) {
        return R.ok(menuService.removeByIds(ids));
    }

    @GetMapping("/auth-tree")
    @ApiOperation(value = "获取功能授权页面的菜单树结构")
    public R fullAuthTree() {
        List<Long> roleIdList = StpUtil.getTokenSession().get(GlobalConstant.LOGIN_USER_ROLE_ID_KEY, new ArrayList<>());
        LambdaQueryWrapper<Menu> queryMenuWrapper = Wrappers.<Menu>query().lambda().eq(Menu::getEnabledMark, EnabledMark.ENABLED.getCode());
        LambdaQueryWrapper<MenuButton> queryBtnWrapper = Wrappers.<MenuButton>query().lambda();
        LambdaQueryWrapper<MenuColumn> queryColWrapper = Wrappers.<MenuColumn>query().lambda();
        LambdaQueryWrapper<MenuForm> queryFormWrapper = Wrappers.<MenuForm>query().lambda();
        boolean isAdmin = roleIdList.contains(GlobalConstant.SUPER_ADMIN_ROLE_ID);
        // 不是管理员
        if (!isAdmin) {
            List<Authorize> authorizeList = authorizeService.list(Wrappers.<Authorize>query().lambda()
                    .select(Authorize::getObjectId, Authorize::getAuthorizeType)
                    .in(Authorize::getAuthorizeType, AuthorizeType.MENU.getCode(), AuthorizeType.BUTTON.getCode(), AuthorizeType.COLUMN.getCode(), AuthorizeType.FORM.getCode())
                    .in(Authorize::getRoleId, roleIdList));
            if (CollectionUtils.isEmpty(authorizeList)) {
                return R.ok();
            }
            List<Long> authMenuIdList = new ArrayList<>();
            List<Long> authMenuBtnIdList = new ArrayList<>();
            List<Long> authMenuIdColList = new ArrayList<>();
            List<Long> authMenuIdFormList = new ArrayList<>();
            for (Authorize authorize : authorizeList) {
                Long id = authorize.getId();
                switch (authorize.getAuthorizeType()) {
                    case 1:
                        authMenuIdList.add(id);
                        break;
                    case 2:
                        authMenuBtnIdList.add(id);
                        break;
                    case 3:
                        authMenuIdColList.add(id);
                        break;
                    case 4:
                        authMenuIdFormList.add(id);
                        break;
                    default:
                        break;
                }
            }
            queryMenuWrapper.in(Menu::getId, authMenuIdList);
            queryBtnWrapper.in(MenuButton::getId, authMenuBtnIdList);
            queryColWrapper.in(MenuColumn::getId, authMenuIdColList);
            queryFormWrapper.in(MenuForm::getId, authMenuIdFormList);
        }
        List<Menu> menuList = menuService.list(queryMenuWrapper);
        List<MenuButton> menuBtnList = menuButtonService.list(queryBtnWrapper);
        List<MenuColumn> menuColList = menuColumnService.list(queryColWrapper);
        List<MenuForm> menuFormList = menuFormService.list(queryFormWrapper);
        List<MenuListVo> menuVoList = BeanUtil.copyToList(menuList, MenuListVo.class);
        AuthMenuVo resultVo = new AuthMenuVo();
        //菜单
        resultVo.setMenuList(TreeUtil.build(menuVoList));
        //按钮
        for (MenuButton menuBtn : menuBtnList) {
            resultVo.addButton(menuBtn.getMenuId(), BeanUtil.toBean(menuBtn, MenuButtonVo.class));
        }
        //列表
        for (MenuColumn menuColumn : menuColList) {
            resultVo.addColumn(menuColumn.getMenuId(), BeanUtil.toBean(menuColumn, MenuColumnVo.class));
        }
        //表单
        for (MenuForm menuForm : menuFormList) {
            resultVo.addForm(menuForm.getMenuId(), BeanUtil.toBean(menuForm, MenuFormVo.class));
        }
        return R.ok(resultVo);
    }

    @GetMapping("/parent-tree")
    @ApiOperation(value = "获取菜单树结构, 不包含页面类型的菜单")
    public R menuTree() {
        List<Menu> menuList = menuService.list(Wrappers.<Menu>query().lambda()
                .eq(Menu::getMenuType, MenuType.MENU.getCode())
                .eq(Menu::getEnabledMark, EnabledMark.ENABLED.getCode())
                .select(Menu.class, x -> VoToColumnUtil.fieldsToColumns(MenuListVo.class).contains(x.getProperty()))
                .orderByAsc(Menu::getSortCode));
        List<MenuListVo> voList = BeanUtil.copyToList(menuList, MenuListVo.class);
        List<MenuListVo> treeVoList = TreeUtil.build(voList);
        return R.ok(treeVoList);
    }

    @GetMapping("/validate-url")
    @ApiOperation(value = "验证菜单路由是否存在")
    public R validateUrl(@RequestParam String outputArea, String className) {
        String component = StringPool.SLASH + StringUtils.lowerCase(outputArea) + StringPool.SLASH + StringUtils.lowerCase(className) + StringPool.SLASH + "index";
        long count = menuService.count(Wrappers.lambdaQuery(Menu.class).eq(Menu::getComponent, component));
        return R.ok(count > 0);
    }

    @GetMapping("/validate-code")
    @ApiOperation(value = "验证菜单编码是否存在")
    public R validateCode(@RequestParam(required = false) Long id, @RequestParam String code) {
        long count = menuService.count(Wrappers.lambdaQuery(Menu.class).eq(Menu::getCode, code).ne(id != null, Menu::getId, id));
        return R.ok(count > 0);
    }


    @PostMapping(value = "/export")
    @ApiOperation(value = "导出菜单数据")
    @XjrLog(value = "导出菜单数据")
    public ResponseEntity<byte[]> exportData() {
        List<Menu> list = menuService.list();
        List<MenuExportVo> menuExportVos = BeanUtil.copyToList(list, MenuExportVo.class);
        ByteArrayOutputStream bot = new ByteArrayOutputStream();
        EasyExcel.write(bot).head(MenuExportVo.class).automaticMergeHead(false)
                .registerWriteHandler(new FormContentStyleStrategy())
                .excelType(ExcelTypeEnum.XLSX).sheet().doWrite(menuExportVos);
        ByteArrayOutputStream resultBot = ExcelUtil.renderExportRequiredHead(bot);
        return R.fileStream(resultBot.toByteArray(), "菜单导出" + ExcelTypeEnum.XLSX.getValue());
    }

    @GetMapping("/download-template")
    @ApiOperation(value = "下载菜单导入模板")
    @SneakyThrows
    public ResponseEntity<byte[]> downloadTemplate() {
        List<MenuExportVo> menuExportVos = new ArrayList<>();
        ByteArrayOutputStream bot = new ByteArrayOutputStream();
        EasyExcel.write(bot).head(MenuExportVo.class).automaticMergeHead(false)
                .registerWriteHandler(new FormContentStyleStrategy())
                .excelType(ExcelTypeEnum.XLSX).sheet().doWrite(menuExportVos);
        ByteArrayOutputStream resultBot = ExcelUtil.renderExportRequiredHead(bot);
        return R.fileStream(resultBot.toByteArray(), "菜单导入模板" + ExcelTypeEnum.XLSX.getValue());
    }

    @PostMapping("/import")
    @ApiOperation(value = "导入菜单数据")
    public R importData(@RequestParam MultipartFile file) throws IOException {
        List<MenuExportVo> menuExportVos = EasyExcel.read(file.getInputStream()).head(MenuExportVo.class).sheet().doReadSync();
        List<Menu> menuList = BeanUtil.copyToList(menuExportVos, Menu.class);
        //获取所有的字典项
        List<Menu> list = menuService.list();
        for (Menu menu : menuList) {
            List<Menu> count = list.stream().filter(x -> x.getCode().equals(menu.getCode())
                    || x.getName().equals(menu.getName())).collect(Collectors.toList());
            if (count.size() > 0){
                return R.error("导入的菜单名或者编码已经存在！项目名称为：" + menu.getName());
            }
        }
        return R.ok(menuService.saveBatch(menuList));
    }

    @PostMapping(value = "/export-button")
    @ApiOperation(value = "导出菜单按钮数据")
    @XjrLog(value = "导出菜单按钮数据")
    public ResponseEntity<byte[]> exportButtonData(@Valid @RequestBody Long menuId) {
        List<MenuButton> list = menuButtonService.list(Wrappers.<MenuButton>lambdaQuery().eq(MenuButton::getMenuId,menuId));
        List<MenuButtonExportVo> menuButtonExportVos = BeanUtil.copyToList(list, MenuButtonExportVo.class);
        ByteArrayOutputStream bot = new ByteArrayOutputStream();
        EasyExcel.write(bot).head(MenuButtonExportVo.class).automaticMergeHead(false)
                .registerWriteHandler(new FormContentStyleStrategy())
                .excelType(ExcelTypeEnum.XLSX).sheet().doWrite(menuButtonExportVos);
        ByteArrayOutputStream resultBot = ExcelUtil.renderExportRequiredHead(bot);
        return R.fileStream(resultBot.toByteArray(), "菜单按钮导出" + ExcelTypeEnum.XLSX.getValue());
    }

    @GetMapping("/download-button-template")
    @ApiOperation(value = "下载按钮导入模板")
    @SneakyThrows
    public ResponseEntity<byte[]> downloadButtonTemplate() {
        List<MenuButtonExportVo> menuButtonExportVos = new ArrayList<>();
        ByteArrayOutputStream bot = new ByteArrayOutputStream();
        EasyExcel.write(bot).head(MenuButtonExportVo.class).automaticMergeHead(false)
                .registerWriteHandler(new FormContentStyleStrategy())
                .excelType(ExcelTypeEnum.XLSX).sheet().doWrite(menuButtonExportVos);
        ByteArrayOutputStream resultBot = ExcelUtil.renderExportRequiredHead(bot);
        return R.fileStream(resultBot.toByteArray(), "菜单按钮导入模板" + ExcelTypeEnum.XLSX.getValue());
    }


    @PostMapping("/import-button")
    @ApiOperation(value = "导入菜单按钮数据")
    public R importButtonData(@RequestParam MultipartFile file,@RequestParam Long menuId) throws IOException {
        List<MenuButtonExportVo> menuButtonExportVos = EasyExcel.read(file.getInputStream()).head(MenuButtonExportVo.class).sheet().doReadSync();
        List<MenuButton> menuButtonList = BeanUtil.copyToList(menuButtonExportVos, MenuButton.class);
        //获取所有的项
        List<MenuButton> list = menuButtonService.list(Wrappers.<MenuButton>lambdaQuery().eq(MenuButton::getMenuId,menuId));
        List<MenuButton> saveMenuButtonList = new ArrayList<>(menuButtonList.size());
        for (MenuButton menuButton : menuButtonList) {
            menuButton.setMenuId(menuId);
            List<MenuButton> count = list.stream().filter(x -> x.getName().equals(menuButton.getName())
                    || x.getCode().equals(menuButton.getCode())).collect(Collectors.toList());
            if (count.size() > 0){
                return R.error("导入的菜单按钮名或者编码已经存在！按钮名称为：" + menuButton.getName());
            }
            saveMenuButtonList.add(menuButton);
        }
        return R.ok(menuButtonService.saveBatch(saveMenuButtonList));
    }

    @PostMapping(value = "/export-column")
    @ApiOperation(value = "导出菜单列数据")
    @XjrLog(value = "导出菜单列数据")
    public ResponseEntity<byte[]> exportColumnData(@Valid @RequestBody Long menuId) {
        List<MenuColumn> list = menuColumnService.list(Wrappers.<MenuColumn>lambdaQuery().eq(MenuColumn::getMenuId,menuId));
        List<MenuColumnExportVo> menuColumnExportVos = BeanUtil.copyToList(list, MenuColumnExportVo.class);
        ByteArrayOutputStream bot = new ByteArrayOutputStream();
        EasyExcel.write(bot).head(MenuColumnExportVo.class).automaticMergeHead(false)
                .registerWriteHandler(new FormContentStyleStrategy())
                .excelType(ExcelTypeEnum.XLSX).sheet().doWrite(menuColumnExportVos);
        ByteArrayOutputStream resultBot = ExcelUtil.renderExportRequiredHead(bot);
        return R.fileStream(resultBot.toByteArray(), "菜单列导出" + ExcelTypeEnum.XLSX.getValue());
    }

    @GetMapping("/download-column-template")
    @ApiOperation(value = "下载列字段导入模板")
    @SneakyThrows
    public ResponseEntity<byte[]> downloadColumnTemplate() {
        List<MenuColumnExportVo> menuColumnExportVos = new ArrayList<>();
        ByteArrayOutputStream bot = new ByteArrayOutputStream();
        EasyExcel.write(bot).head(MenuColumnExportVo.class).automaticMergeHead(false)
                .registerWriteHandler(new FormContentStyleStrategy())
                .excelType(ExcelTypeEnum.XLSX).sheet().doWrite(menuColumnExportVos);
        ByteArrayOutputStream resultBot = ExcelUtil.renderExportRequiredHead(bot);
        return R.fileStream(resultBot.toByteArray(), "菜单列导入模板" + ExcelTypeEnum.XLSX.getValue());
    }

    @PostMapping("/import-column")
    @ApiOperation(value = "导入菜单列表字段数据")
    public R importColumnData(@RequestParam MultipartFile file,@RequestParam Long menuId) throws IOException {
        List<MenuColumnExportVo> menuColumnExportVos = EasyExcel.read(file.getInputStream()).head(MenuColumnExportVo.class).sheet().doReadSync();
        List<MenuColumn> menuColumnList = BeanUtil.copyToList(menuColumnExportVos, MenuColumn.class);
        //获取所有的项
        List<MenuColumn> list = menuColumnService.list(Wrappers.<MenuColumn>lambdaQuery().eq(MenuColumn::getMenuId,menuId));
        List<MenuColumn> saveMenuColumnList = new ArrayList<>(menuColumnList.size());
        for (MenuColumn menuColumn : menuColumnList) {
            menuColumn.setMenuId(menuId);
            List<MenuColumn> count = list.stream().filter(x -> x.getName().equals(menuColumn.getName())
                    || x.getCode().equals(menuColumn.getCode())).collect(Collectors.toList());
            if (count.size() > 0){
                return R.error("导入的菜单列名或者编码已经存在！列名称为：" + menuColumn.getName());
            }
            saveMenuColumnList.add(menuColumn);
        }
        return R.ok(menuColumnService.saveBatch(saveMenuColumnList));
    }

    @PostMapping(value = "/export-form")
    @ApiOperation(value = "导出菜单表单数据")
    @XjrLog(value = "导出菜单表单数据")
    public ResponseEntity<byte[]> exportFormData(@Valid @RequestBody Long menuId) {
        List<MenuForm> list = menuFormService.list(Wrappers.<MenuForm>lambdaQuery().eq(MenuForm::getMenuId,menuId));
        List<MenuFormExportVo> menuFormExportVos = BeanUtil.copyToList(list, MenuFormExportVo.class);
        ByteArrayOutputStream bot = new ByteArrayOutputStream();
        EasyExcel.write(bot).head(MenuFormExportVo.class).automaticMergeHead(false)
                .registerWriteHandler(new FormContentStyleStrategy())
                .excelType(ExcelTypeEnum.XLSX).sheet().doWrite(menuFormExportVos);
        ByteArrayOutputStream resultBot = ExcelUtil.renderExportRequiredHead(bot);
        return R.fileStream(resultBot.toByteArray(), "菜单表单字段导出" + ExcelTypeEnum.XLSX.getValue());
    }


    @GetMapping("/download-form-template")
    @ApiOperation(value = "下载菜单表单字段导入模板")
    @SneakyThrows
    public ResponseEntity<byte[]> downloadFormTemplate() {
        List<MenuFormExportVo> menuFormExportVos = new ArrayList<>();
        ByteArrayOutputStream bot = new ByteArrayOutputStream();
        EasyExcel.write(bot).head(MenuFormExportVo.class).automaticMergeHead(false)
                .registerWriteHandler(new FormContentStyleStrategy())
                .excelType(ExcelTypeEnum.XLSX).sheet().doWrite(menuFormExportVos);
        ByteArrayOutputStream resultBot = ExcelUtil.renderExportRequiredHead(bot);
        return R.fileStream(resultBot.toByteArray(), "菜单表单字段导入模板" + ExcelTypeEnum.XLSX.getValue());
    }

    @PostMapping("/import-form")
    @ApiOperation(value = "导入菜单表单字段数据")
    public R importFormData(@RequestParam MultipartFile file,@RequestParam Long menuId) throws IOException {
        List<MenuFormExportVo> menuFormExportVos = EasyExcel.read(file.getInputStream()).head(MenuFormExportVo.class).sheet().doReadSync();
        List<MenuForm> menuColumnList = BeanUtil.copyToList(menuFormExportVos, MenuForm.class);
        //获取所有的项
        List<MenuForm> list = menuFormService.list(Wrappers.<MenuForm>lambdaQuery().eq(MenuForm::getMenuId,menuId));
        List<MenuForm> saveMenuFormList = new ArrayList<>(menuColumnList.size());
        for (MenuForm menuForm : menuColumnList) {
            menuForm.setMenuId(menuId);
            List<MenuForm> count = list.stream().filter(x -> x.getName().equals(menuForm.getName())
                    || x.getCode().equals(menuForm.getCode())).collect(Collectors.toList());
            if (count.size() > 0){
                return R.error("导入的菜单表单字段名称或者编码已经存在！名称为：" + menuForm.getName());
            }
            saveMenuFormList.add(menuForm);
        }
        return R.ok(menuFormService.saveBatch(saveMenuFormList));
    }
}
