package com.zilue.module.liteflow.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author: zilue
 * @Date: 2023/4/6 15:37
 */
@Data
public class AddChainDto {

    @ApiModelProperty("应用名")
    @NotBlank(message = "应用名不能为空！")
    private String applicationName;

    @ApiModelProperty("规则名")
    @NotBlank(message = "规则名不能为空！")
    private String chainName;

    @ApiModelProperty("备注")
    private String chainDesc;

    @ApiModelProperty("规则文件")
    @NotBlank(message = "规则文件不能为空！")
    private String elData;

}
