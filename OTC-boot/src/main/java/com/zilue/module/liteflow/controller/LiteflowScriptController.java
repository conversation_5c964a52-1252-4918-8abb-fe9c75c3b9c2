package com.zilue.module.liteflow.controller;

import com.zilue.common.constant.GlobalConstant;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 规则脚本表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-06
 */
@RestController
@RequestMapping(GlobalConstant.LITEFLOW_MODULE_PREFIX + "/script")
@Api(value = GlobalConstant.LITEFLOW_MODULE_PREFIX + "/script", tags = "规则引擎脚本文件模块")
@AllArgsConstructor
public class LiteflowScriptController {

}
