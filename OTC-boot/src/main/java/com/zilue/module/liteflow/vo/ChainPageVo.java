package com.zilue.module.liteflow.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author: zilue
 * @Date: 2023/4/6 15:34
 */
@Data
public class ChainPageVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("应用名")
    private String applicationName;

    @ApiModelProperty("规则名")
    private String chainName;

    @ApiModelProperty("备注")
    private String chainDesc;

    @ApiModelProperty("规则文件")
    private String elData;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
}
