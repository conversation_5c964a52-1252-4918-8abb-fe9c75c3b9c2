package com.zilue.module.liteflow.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author: zilue
 * @Date: 2023/4/6 15:37
 */
@Data
public class UpdateLiteflowCodeDto {

    private Long id;

    @ApiModelProperty("规则代码方法名")
    @NotBlank(message = "规则代码方法名不能为空！")
    private String methodName;

    @ApiModelProperty("规则代码编号")
    @NotBlank(message = "规则代码编号不能为空！")
    private String codeNumber;

    @ApiModelProperty("规则代码内容")
    @NotBlank(message = "规则代码内容不能为空！")
    private String codeData;

    @ApiModelProperty("备注")
    private String remark;

}
