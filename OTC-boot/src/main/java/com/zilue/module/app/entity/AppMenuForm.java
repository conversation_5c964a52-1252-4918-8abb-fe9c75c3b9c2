package com.zilue.module.app.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * app菜单表单字段
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-25
 */
@TableName("xjr_app_menu_form")
@ApiModel(value = "AppMenuForm对象", description = "app菜单表单字段")
@Data
public class AppMenuForm implements Serializable {

    private static final long serialVersionUID = 1L;

      @ApiModelProperty("主键")
        private Long id;

      @ApiModelProperty("菜单主键")
      private Long menuId;

      @ApiModelProperty("编码")
      private String code;

      @ApiModelProperty("名称")
      private String name;

      @ApiModelProperty("排序码")
      private Integer sortCode;

      @ApiModelProperty("是否必填，0-非必填，1-必填")
      private Integer isRequired;

      @ApiModelProperty("父级字段")
      private Long parentId;
}
