package com.zilue.module.app.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zilue.common.annotation.XjrLog;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.model.result.R;
import com.zilue.common.page.ConventPage;
import com.zilue.common.page.PageOutput;
import com.zilue.common.utils.VoToColumnUtil;
import com.zilue.module.app.dto.*;
import com.zilue.module.app.entity.AppPageDesign;
import com.zilue.module.app.service.IAppMenuService;
import com.zilue.module.app.service.IAppPageDesignService;
import com.zilue.module.app.vo.AppPageDesignVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * xjr_data_display【数据展示表】 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-25
 */
@RestController
@RequestMapping(GlobalConstant.APP_MODEL +"/page-design")
@Api(value = GlobalConstant.APP_MODEL +"/page-design", tags = "app端展示页面设计")
@AllArgsConstructor
public class AppPageDesignController {

    private IAppPageDesignService pageDesignService;

    private IAppMenuService appMenuService;

    @GetMapping(value = "/page")
    @ApiOperation("app端展示页面设计分页")
    @XjrLog("app端展示页面设计分页")
    public R page(AppPageDesignDto dto) {
        LambdaQueryWrapper<AppPageDesign> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(dto.getKeyword()), AppPageDesign::getName, dto.getKeyword())
                .like(StrUtil.isNotBlank(dto.getName()), AppPageDesign::getName, dto.getName())
                .like(StrUtil.isNotBlank(dto.getCode()), AppPageDesign::getCode, dto.getCode())
                .eq(ObjectUtil.isNotEmpty(dto.getEnabledMark()), AppPageDesign::getEnabledMark,dto.getEnabledMark())
                .select(AppPageDesign.class, x -> VoToColumnUtil.fieldsToColumns(AppPageDesignVo.class).contains(x.getProperty()))
                .orderByDesc(AppPageDesign::getCreateDate);

        IPage<AppPageDesign> page = pageDesignService.page(ConventPage.getPage(dto), queryWrapper);
        PageOutput<AppPageDesignVo> pageOutput = ConventPage.getPageOutput(page, AppPageDesignVo.class);
        return R.ok(pageOutput);
    }

    @PostMapping
    @ApiOperation(value = "新增app端展示页面设计")
    @XjrLog("新增app端展示页面设计")
    public R add(@Valid @RequestBody AddAppPageDesignDto dto) {

        return R.ok(pageDesignService.add(dto));
    }

    @PostMapping("/draft")
    @ApiOperation(value = "app端展示页面设计保存草稿")
    @XjrLog("app端展示页面设计保存草稿")
    public R draft(@Valid @RequestBody AppPageDesignDraftDto dto) {
        AppPageDesign bean = BeanUtil.toBean(dto, AppPageDesign.class);
        return R.ok(pageDesignService.saveOrUpdate(bean));
    }

    @PutMapping
    @ApiOperation(value = "修改app端展示页面设计")
    @XjrLog("修改app端展示页面设计")
    public R update(@Valid @RequestBody UpdateAppPageDesignDto dto) {
        return R.ok(pageDesignService.modify(dto));
    }

    @GetMapping(value = "/info")
    @ApiOperation(value = "根据id查询数app端展示页面设计详细信息")
    @XjrLog("根据id查询数app端展示页面设计详细信息")
    public R info(@RequestParam Long id) {
        AppPageDesign appPageDesign = pageDesignService.getById(id);
        if (appPageDesign == null) {
            R.error("找不到此数据展示页！");
        }
        return R.ok(BeanUtil.toBean(appPageDesign, AppPageDesign.class));
    }


    @DeleteMapping
    @ApiOperation(value = "删除")
    @XjrLog(value = "删除app端展示页面设计")
    @Transactional(rollbackFor = Exception.class)
    public R delete(@Valid @RequestBody DeleteAppPageDesignDto dto) {

        AppPageDesign pageDesign = pageDesignService.getById(dto.getId());

        if (ObjectUtils.isNotEmpty(pageDesign.getAppMenuId())) {
            appMenuService.removeById(pageDesign.getAppMenuId());
        }

        pageDesignService.removeById(dto.getId());
        return R.ok(true);
    }

    @PutMapping("/update-enable-mark")
    @ApiOperation(value = "修改启用状态")
    @XjrLog(value = "修改启用状态")
    public R updateEnableMark(@Valid @RequestBody UpdateEnableMarkDto dto) {
        AppPageDesign appPageDesign = new AppPageDesign();
        appPageDesign.setId(dto.getId());
        appPageDesign.setEnabledMark(dto.getEnabledMark());
        pageDesignService.updateById(appPageDesign);
        return R.ok(true);
    }

}
