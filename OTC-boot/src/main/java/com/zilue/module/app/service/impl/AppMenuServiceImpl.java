package com.zilue.module.app.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.enums.AuthorizeType;
import com.zilue.module.app.entity.AppAuthorize;
import com.zilue.module.app.entity.AppMenu;
import com.zilue.module.app.mapper.AppAuthorizeMapper;
import com.zilue.module.app.mapper.AppMenuMapper;
import com.zilue.module.app.service.IAppMenuService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zilue.module.organization.entity.UserRoleRelation;
import com.zilue.module.organization.mapper.UserRoleRelationMapper;
import com.zilue.module.system.dto.MenuTreeDto;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-26
 */
@Service
@AllArgsConstructor
public class AppMenuServiceImpl extends ServiceImpl<AppMenuMapper, AppMenu> implements IAppMenuService {

    private final UserRoleRelationMapper userRoleRelationMapper;

    private final AppAuthorizeMapper appAuthorizeMapper;

    @Override
    public List<AppMenu> getAppAuthMenuList(MenuTreeDto dto) {
        List<UserRoleRelation> relations = userRoleRelationMapper.selectList(Wrappers.lambdaQuery(UserRoleRelation.class)
                .select(UserRoleRelation::getRoleId)
                .eq(UserRoleRelation::getUserId, StpUtil.getLoginIdAsLong()));
        if (CollectionUtils.isEmpty(relations)) {
            return new ArrayList<>();
        }
        List<Long> roleIds = relations.stream().map(UserRoleRelation::getRoleId).collect(Collectors.toList());
        List<Long> authMenuIdList = null;
        // 非超级管理员权限过滤
        if (!roleIds.contains(GlobalConstant.SUPER_ADMIN_ROLE_ID)) {
            List<AppAuthorize> authorizeList = appAuthorizeMapper.selectList(Wrappers.<AppAuthorize>lambdaQuery()
                    .eq(AppAuthorize::getAppAuthorizeType, AuthorizeType.MENU.getCode())
                    .in(AppAuthorize::getRoleId, roleIds));
            if (CollectionUtils.isEmpty(authorizeList)) {
                return new ArrayList<>();
            }
            authMenuIdList = authorizeList.stream().map(AppAuthorize::getObjectId).collect(Collectors.toList());
        }

        return this.list(Wrappers.<AppMenu>lambdaQuery()
                .like(StrUtil.isNotBlank(dto.getCode()), AppMenu::getCode, dto.getCode())
                .like(StrUtil.isNotBlank(dto.getName()), AppMenu::getName, dto.getName())
                .in(CollectionUtils.isNotEmpty(authMenuIdList), AppMenu::getId, authMenuIdList)
                .eq(ObjectUtil.isNotEmpty(dto.getEnabledMark()),AppMenu::getEnabledMark, dto.getEnabledMark())
                .eq(ObjectUtil.isNotEmpty(dto.getDetailId()),AppMenu::getCategoryId,dto.getDetailId())
                .orderByAsc(AppMenu::getSortCode)
        );
    }
}
