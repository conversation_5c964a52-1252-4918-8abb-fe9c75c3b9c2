package com.zilue.module.app.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zilue.common.annotation.RepeatSubmit;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.enums.EnabledMark;
import com.zilue.common.model.result.R;
import com.zilue.common.page.ConventPage;
import com.zilue.common.page.PageOutput;
import com.zilue.common.utils.VoToColumnUtil;
import com.zilue.module.app.dto.AddAppMenuCategoryDto;
import com.zilue.module.app.dto.AppMenuCategoryListDto;
import com.zilue.module.app.dto.AppMenuCategoryPageDto;
import com.zilue.module.app.dto.UpdateAppMenuCategoryDto;
import com.zilue.module.app.entity.AppMenuCategory;
import com.zilue.module.app.service.IAppMenuCategoryService;
import com.zilue.module.app.vo.AppMenuCategoryListVo;
import com.zilue.module.app.vo.AppMenuCategoryPageVo;
import com.zilue.module.app.vo.AppMenuCategoryVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @title: 手机菜单分类
 * <AUTHOR>
 * @Date: 2025-01-14
 * @Version 1.0
 */
@RestController
@RequestMapping(GlobalConstant.APP_MODEL + "/menuCategory")
@Api(value = GlobalConstant.APP_MODEL + "/menuCategory", tags = "手机菜单分类")
@AllArgsConstructor
public class AppMenuCategoryController {


    private final IAppMenuCategoryService menucategoryService;

    @GetMapping("/page")
    @ApiOperation(value = "XjrAppMenuCategory列表(分页)")
    @SaCheckPermission("menucategory:detail")
    public R page(@Valid AppMenuCategoryPageDto dto) {

        LambdaQueryWrapper<AppMenuCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(ObjectUtil.isNotNull(dto.getId()), AppMenuCategory::getId, dto.getId())
                .like(StrUtil.isNotBlank(dto.getName()), AppMenuCategory::getName, dto.getName())
                .eq(ObjectUtil.isNotNull(dto.getCreateUserId()), AppMenuCategory::getCreateUserId, dto.getCreateUserId())
                .between(ObjectUtil.isNotNull(dto.getCreateDateStart()) && ObjectUtil.isNotNull(dto.getCreateDateEnd()), AppMenuCategory::getCreateDate, dto.getCreateDateStart(), dto.getCreateDateEnd())
                .eq(ObjectUtil.isNotNull(dto.getModifyUserId()), AppMenuCategory::getModifyUserId, dto.getModifyUserId())
                .between(ObjectUtil.isNotNull(dto.getModifyDateStart()) && ObjectUtil.isNotNull(dto.getModifyDateEnd()), AppMenuCategory::getModifyDate, dto.getModifyDateStart(), dto.getModifyDateEnd())
                .eq(ObjectUtil.isNotNull(dto.getDeleteMark()), AppMenuCategory::getDeleteMark, dto.getDeleteMark())
                .eq(ObjectUtil.isNotNull(dto.getEnabledMark()), AppMenuCategory::getEnabledMark, dto.getEnabledMark())
                .orderByDesc(AppMenuCategory::getId)
                .select(AppMenuCategory.class, x -> VoToColumnUtil.fieldsToColumns(AppMenuCategoryPageVo.class).contains(x.getProperty()));
        IPage<AppMenuCategory> page = menucategoryService.page(ConventPage.getPage(dto), queryWrapper);
        PageOutput<AppMenuCategoryPageVo> pageOutput = ConventPage.getPageOutput(page, AppMenuCategoryPageVo.class);
        return R.ok(pageOutput);
    }

    @GetMapping("/list")
    @ApiOperation("手机菜单分类 列表 不分页")
    public R list(AppMenuCategoryListDto dto) {
        LambdaQueryWrapper<AppMenuCategory> queryWrapper = Wrappers.lambdaQuery(AppMenuCategory.class)
                .like(StrUtil.isNotBlank(dto.getName()), AppMenuCategory::getName, dto.getName())
                .eq(AppMenuCategory::getEnabledMark, EnabledMark.ENABLED.getCode())
                .select(AppMenuCategory.class, x -> VoToColumnUtil
                        .fieldsToColumns(AppMenuCategoryListDto.class).contains(x.getProperty()));
        List<AppMenuCategory> list = menucategoryService.list(queryWrapper);
        return R.ok(BeanUtil.copyToList(list, AppMenuCategoryListVo.class));
    }

    @GetMapping("/info")
    @ApiOperation(value = "根据id查询XjrAppMenuCategory信息")
    @SaCheckPermission("menucategory:detail")
    public R info(@RequestParam Long id) {
        AppMenuCategory appMenuCategory = menucategoryService.getById(id);
        if (appMenuCategory == null) {
            return R.error("找不到此数据！");
        }
        return R.ok(BeanUtil.toBean(appMenuCategory, AppMenuCategoryVo.class));
    }


    @PostMapping("/add")
    @ApiOperation(value = "新增XjrAppMenuCategory")
    @SaCheckPermission("menucategory:add")
    @RepeatSubmit
    public R add(@Valid @RequestBody AddAppMenuCategoryDto dto) {
        AppMenuCategory appMenuCategory = BeanUtil.toBean(dto, AppMenuCategory.class);
        boolean isSuccess = menucategoryService.save(appMenuCategory);
        return R.ok(appMenuCategory.getId());
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改XjrAppMenuCategory")
    @SaCheckPermission("menucategory:edit")
    @RepeatSubmit
    public R update(@Valid @RequestBody UpdateAppMenuCategoryDto dto) {

        AppMenuCategory appMenuCategory = BeanUtil.toBean(dto, AppMenuCategory.class);
        return R.ok(menucategoryService.updateById(appMenuCategory));

    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除")
    @SaCheckPermission("menucategory:delete")
    @RepeatSubmit
    public R delete(@Valid @RequestBody List<Long> ids) {
        return R.ok(menucategoryService.removeBatchByIds(ids));

    }

}