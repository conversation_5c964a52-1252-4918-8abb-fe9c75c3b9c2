package com.zilue.module.app.dto;

import com.zilue.module.generator.dto.GeneratorAppDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: zilue
 * @Date: 2023/7/26 8:56
 */
@Data
public class AddFuncDesignDto {


    @ApiModelProperty("功能模块所选id")
    private Long funcModule;

    @ApiModelProperty("功能描述")
    private String funcDescribe;


    @ApiModelProperty("代码模板id")
    @NotNull(message = "代码模板id不能为空！")
    private Long codeSchemaId;

    @ApiModelProperty("页面类型:0 系统表单 1 自定义表单")
    @NotNull(message = "页面类型不能为空！")
    private Integer formType;

    @ApiModelProperty("是否生成代码  如果 formType 为自定义表单  需要传入  0 不生成  1 生成")
    private Integer isGeneratorCode;

    @ApiModelProperty("页面配置json")
    private String jsonContent;

    @ApiModelProperty("菜单配置")
    private AddAppMenuDto menuConfigs;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("排序")
    private Integer sortCode;

    @ApiModelProperty("前端代码")
    private GeneratorAppDto codes;

    @ApiModelProperty("状态")
    private Integer enabledMark;


}
