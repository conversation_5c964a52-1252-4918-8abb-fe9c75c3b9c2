package com.zilue.module.app.dto;

import com.zilue.module.generator.dto.GeneratorAppDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: zilue
 * @Date: 2023/7/26 9:07
 */
@Data
public class UpdateAppFuncDesignDto {

    private Long id;

    @ApiModelProperty("功能模块所选id")
    private Long funcModule;

    @ApiModelProperty("功能描述")
//    @NotBlank(message = "功能描述不能为空！")
    private String funcDescribe;

    @ApiModelProperty("app菜单id")
    private Long appMenuId;

    @ApiModelProperty("代码模板id")
    private Long codeSchemaId;

    @ApiModelProperty("页面类型:0 系统表单 1 自定义表单")
    private Integer formType;

    @ApiModelProperty("是否生成代码  如果 formType 为自定义表单  需要传入  0 不生成  1 生成")
    private Integer isGeneratorCode;

    @ApiModelProperty("页面配置json")
    private String jsonContent;

    @ApiModelProperty("备注")
    private String remark;


    @ApiModelProperty("排序")
    private Integer sortCode;



    @ApiModelProperty("状态")
    private Integer enabledMark;


    @ApiModelProperty("菜单配置")
    private AddAppMenuDto menuConfigs;


    @ApiModelProperty("前端代码")
    private GeneratorAppDto codes;

}
