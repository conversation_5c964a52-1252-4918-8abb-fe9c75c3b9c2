package com.zilue.module.app.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

import com.zilue.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-26
 */
@TableName("xjr_app_menu")
@ApiModel(value = "AppMenu对象", description = "手机端菜单")
@Data
@EqualsAndHashCode(callSuper = true)
public class AppMenu extends AuditEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("菜单名称")
    private String name;

    @ApiModelProperty("菜单编码")
    private String code;

    @ApiModelProperty("菜单图标")
    private String icon;

    @ApiModelProperty("菜单类别")
    private Long categoryId;

    @ApiModelProperty("url 路径")
    private String url;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("排序")
    private Integer sortCode;


}
