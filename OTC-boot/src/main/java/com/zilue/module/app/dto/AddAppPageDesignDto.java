package com.zilue.module.app.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class AddAppPageDesignDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("页面名称")
    private String name;

    @ApiModelProperty("页面编码")
    private String code;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("是否是菜单：（0：否，1：是）")
    private Integer isMenu;

    @ApiModelProperty("功能类别id")
    private Long categoryId;

    @ApiModelProperty("图标")
    private String icon;

    @ApiModelProperty("排序码")
    private Integer sortCode;

    @ApiModelProperty("json内容")
    private String jsonContent;

    @ApiModelProperty("1:启用，0：禁用，-1：草稿")
    private Integer enabledMark;

    @ApiModelProperty("页面代码")
    private String pageCode;

}
