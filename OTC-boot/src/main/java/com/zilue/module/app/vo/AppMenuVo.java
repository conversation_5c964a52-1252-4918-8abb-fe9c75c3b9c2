package com.zilue.module.app.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: zilue
 * @Date: 2023/7/31 10:26
 */
@Data
public class AppMenuVo {

    private Long id;

    @ApiModelProperty("菜单名称")
    private String name;

    @ApiModelProperty("菜单编码")
    private String code;

    @ApiModelProperty("菜单图标")
    private String icon;

    @ApiModelProperty("分类id")
    private Long categoryId;

    @ApiModelProperty("分类名称")
    private String categoryName;


    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("url 路径")
    private String url;

    @ApiModelProperty("排序")
    private Integer sortCode;

    @ApiModelProperty("状态，1-启用，0-禁用")
    private Integer enabledMark;

}
