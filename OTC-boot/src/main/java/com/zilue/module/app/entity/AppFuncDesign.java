package com.zilue.module.app.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

import com.zilue.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-26
 */
@TableName("xjr_app_func_design")
@ApiModel(value = "AppFuncDesign对象", description = "")
@Data
@EqualsAndHashCode(callSuper = false)
public class AppFuncDesign extends AuditEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("app菜单id")
    private Long appMenuId;

    @ApiModelProperty("功能模块所选id")
    private Long funcModule;

    @ApiModelProperty("功能描述")
    private String funcDescribe;

    @ApiModelProperty("代码模板id")
    private Long codeSchemaId;

    @ApiModelProperty("页面类型:0 系统表单 1 自定义表单")
    private Integer formType;

    @ApiModelProperty("页面配置json")
    private String jsonContent;

    @ApiModelProperty("备注")
    private String remark;


    @ApiModelProperty("排序")
    private Integer sortCode;


}
