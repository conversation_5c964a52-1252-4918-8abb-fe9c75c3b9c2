package com.zilue.module.app.service;

import com.zilue.module.app.dto.AddAppPageDesignDto;
import com.zilue.module.app.dto.UpdateAppPageDesignDto;
import com.zilue.module.app.entity.AppPageDesign;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * xjr_data_display【数据展示表】 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-25
 */
public interface IAppPageDesignService extends IService<AppPageDesign> {

    Boolean add(AddAppPageDesignDto dto);

    Boolean modify(UpdateAppPageDesignDto dto);

}
