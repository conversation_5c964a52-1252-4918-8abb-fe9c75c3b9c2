package com.zilue.module.app.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * app菜单按钮
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-25
 */
@TableName("xjr_app_menu_button")
@ApiModel(value = "AppMenuButton对象", description = "app菜单按钮")
@Data
public class AppMenuButton  implements Serializable{

    private static final long serialVersionUID = 1L;

      private Long id;

      @ApiModelProperty("按钮名")
      private String name;

      @ApiModelProperty("app菜单Id")
      private Long menuId;

      @ApiModelProperty("图标")
      private String icon;

      @ApiModelProperty("编码")
      private String code;

      @ApiModelProperty("请求地址")
      private String url;

      @ApiModelProperty("请求方式")
      private Integer method;

}
