package com.zilue.module.app.dto;

import com.zilue.module.system.dto.AddMenuButtonDto;
import com.zilue.module.system.dto.AddMenuColumnDto;
import com.zilue.module.system.dto.AddMenuFormDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: zilue
 * @Date: 2023/7/26 11:06
 */
@Data
public class AddAppMenuDto {

    @ApiModelProperty("菜单名称")
    private String name;

    @ApiModelProperty("菜单编码")
    private String code;

    @ApiModelProperty("菜单图标")
    private String icon;

    @ApiModelProperty("分类id")
    private Long categoryId;


    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("url 路径")
    private String url = "/pages/customForm/list";


    @ApiModelProperty("排序")
    private Integer sortCode;

    @ApiModelProperty("按钮列表")
    private List<AddMenuButtonDto> buttonList;

    @ApiModelProperty("列表字段列表")
    private List<AddMenuColumnDto> columnList;

    @ApiModelProperty("表单字段列表")
    private List<AddMenuFormDto> formList;

}
