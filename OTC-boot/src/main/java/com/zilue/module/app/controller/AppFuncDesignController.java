package com.zilue.module.app.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.toolkit.MPJWrappers;
import com.zilue.common.annotation.XjrLog;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.model.result.R;
import com.zilue.common.page.ConventPage;
import com.zilue.common.page.PageOutput;
import com.zilue.common.utils.VoToColumnUtil;
import com.zilue.module.app.dto.*;
import com.zilue.module.app.entity.AppFuncDesign;
import com.zilue.module.app.entity.AppMenu;
import com.zilue.module.app.service.IAppFuncDesignService;
import com.zilue.module.app.service.IAppMenuService;
import com.zilue.module.app.vo.AppFuncDesignPageVo;
import com.zilue.module.app.vo.AppFuncDesignVo;
import com.zilue.module.app.vo.AppMenuVo;
import com.zilue.module.generator.entity.MenuConfig;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-26
 */
@RestController
@RequestMapping(GlobalConstant.APP_MODEL +"/func-design")
@Api(value = GlobalConstant.APP_MODEL +"/func-design", tags = "app端功能页面设计")
@AllArgsConstructor
public class AppFuncDesignController {

    private IAppFuncDesignService funcDesignService;

    private IAppMenuService appMenuService;

    @GetMapping(value = "/page")
    @ApiOperation("app端功能页面设计分页")
    public R page(AppFuncDesignPageDto dto) {

        IPage<AppFuncDesignPageVo> page = funcDesignService.selectJoinListPage(ConventPage.getPage(dto), AppFuncDesignPageVo.class, MPJWrappers.<AppFuncDesign>lambdaJoin()
                .like(StrUtil.isNotBlank(dto.getKeyword()), AppMenu::getName, dto.getKeyword())
                .eq(ObjectUtil.isNotEmpty(dto.getEnabledMark()), AppFuncDesign::getEnabledMark, dto.getEnabledMark())
                .eq(dto.getFormType() != null, AppFuncDesign::getFormType, dto.getFormType())
                .select(AppFuncDesign::getId)
                .select(AppFuncDesign.class, x -> VoToColumnUtil.fieldsToColumns(AppFuncDesignPageVo.class).contains(x.getProperty()))
                .selectAs(AppMenu::getName, AppFuncDesignPageVo::getName)
                .selectAs(AppMenu::getRemark, AppFuncDesignPageVo::getMenuRemark)
                .leftJoin(AppMenu.class, AppMenu::getId, AppFuncDesign::getAppMenuId)
                .orderByDesc(AppFuncDesign::getCreateDate)
        );

        List<AppFuncDesignPageVo> records = page.getRecords();

        for (AppFuncDesignPageVo record : records) {
            if (StrUtil.isBlank(record.getName())){//防止当菜单被删除之后，不会显示名称了
                JSONObject configJson = JSON.parseObject(record.getJsonContent());
                MenuConfig menuConfig = JSON.parseObject(configJson.getString("menuConfig"), MenuConfig.class);
                record.setName(menuConfig.getName());
                record.setMenuRemark(menuConfig.getRemark());
            }
        }

        PageOutput<AppFuncDesignPageVo> pageOutput = ConventPage.getPageOutput(page, AppFuncDesignPageVo.class);
        return R.ok(pageOutput);
    }

    @PostMapping
    @ApiOperation(value = "新增app端功能页面设计")
    public R add(@Valid @RequestBody AddFuncDesignDto dto) {
        return R.ok(funcDesignService.add(dto));
    }

    @PutMapping
    @ApiOperation(value = "修改app端功能页面设计")
    public R update(@Valid @RequestBody UpdateAppFuncDesignDto dto) {
        return R.ok(funcDesignService.modify(dto));
    }

    @GetMapping(value = "/info")
    @ApiOperation(value = "根据id查询数app端功能页面设计详细信息")
    public R info(@RequestParam Long id) {
        AppFuncDesign appFuncDesign = funcDesignService.getById(id);
        if (appFuncDesign == null) {
            return R.error("找不到此功能页面设计！");
        }
        AppFuncDesignVo appFuncDesignVo = BeanUtil.toBean(appFuncDesign, AppFuncDesignVo.class);
        AppMenu appMenu = appMenuService.getById(appFuncDesign.getAppMenuId());

        appFuncDesignVo.setMenuConfigs(BeanUtil.toBean(appMenu, AppMenuVo.class));



        return R.ok(appFuncDesignVo);
    }


    @DeleteMapping
    @ApiOperation(value = "删除")
    @XjrLog(value = "删除app端展示页面设计")
    @Transactional(rollbackFor = Exception.class)
    public R delete(@Valid @RequestBody DeleteAppPageDesignDto dto) {
        AppFuncDesign appFuncDesign = funcDesignService.getById(dto.getId());

        appMenuService.removeById(appFuncDesign.getAppMenuId());


        funcDesignService.removeById(dto.getId());
        return R.ok(Boolean.TRUE);
    }

    @PutMapping("/update-enable-mark")
    @ApiOperation(value = "修改启用状态")
    @XjrLog(value = "修改启用状态")
    public R updateEnableMark(@Valid @RequestBody UpdateEnableMarkDto dto) {
        AppFuncDesign appFuncDesign = new AppFuncDesign();
        appFuncDesign.setId(dto.getId());
        appFuncDesign.setEnabledMark(dto.getEnabledMark());
        funcDesignService.updateById(appFuncDesign);

        AppFuncDesign one = funcDesignService.getOne(Wrappers.lambdaQuery(AppFuncDesign.class).eq(AppFuncDesign::getId, dto.getId()).select(AppFuncDesign::getAppMenuId));
        AppMenu appMenu = new AppMenu();
        appMenu.setId(one.getAppMenuId());
        appMenu.setEnabledMark(dto.getEnabledMark());
        appMenuService.updateById(appMenu);
        return R.ok(true);
    }

}
