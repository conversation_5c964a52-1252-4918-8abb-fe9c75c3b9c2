package com.zilue.module.app.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-25
 */
@TableName("xjr_app_authorize")
@ApiModel(value = "AppAuthorize对象", description = "")
@Data
public class AppAuthorize implements Serializable {

    private static final long serialVersionUID = 1L;

      private Long id;

      @ApiModelProperty("角色id")
      private Long roleId;

      @ApiModelProperty("菜单id")
      private Long objectId;

      @ApiModelProperty("权限类型(0-菜单，1-按钮，2-列表字段，3-表单字段)")
      private Integer appAuthorizeType;

}
