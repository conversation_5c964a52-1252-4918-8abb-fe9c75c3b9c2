package com.zilue.module.generator.service;

import com.zilue.module.generator.dto.*;
import com.zilue.module.generator.vo.GeneratorCodeVo;
import com.zilue.module.generator.vo.TableInfoVo;

import java.util.List;

public interface IGeneratorService {
    /**
     * 根据参数生产代码预览
     *
     * @return
     */
    GeneratorCodeVo getPreviewCodes(DataFirstPreviewDto dto);

    /**
     * 生成代码
     *
     * @return
     */
    Boolean generateCodes(DataFirstGeneratorDto dto);

    /**
     * 预览代码优先 的代码
     * @param dto
     * @return
     */
    GeneratorCodeVo getCodeFirstPreviewCodes(CodeFirstPreviewDto dto) throws Exception ;


    /**
     * 生成 代码优先的代码
     * @param dto
     * @return
     */
    Boolean generateCodeFirstCodes(CodeFirstGeneratorDto dto) throws Exception ;


    /**
     * 生成 代码优先的代码
     * @param dto
     * @return
     */
    Boolean generatorCodeBatch(BatchGeneratorDto dto);


    /**
     * 根据 tabconfig 获取 表结构信息  用于生成前端model
     * @param dto  表关系配置
     * @return
     */
    List<TableInfoVo> getTableInfo(TableInfoDto dto);

    /**
     * 生成APP代码
     *
     * @return
     */
    Boolean generateAppCodes(GeneratorAppDto dto);
}
