package com.zilue.module.generator.entity;

import lombok.Data;

/**
 * 前端代码结构
 * @Author: zilue
 * @Date: 2023/6/2 15:51
 */
@Data
public class FrontCode {
    /**
     * 列表页面代码
     */
    private String listCode;
    /**
     * 表单页面代码
     */
    private String formCode;
    /**
     * 接口请求代码
     */
    private String apiCode;

    /**
     * 数据模型代码
     */
    private String modelCode;

    /**
     * 配置json 代码
     */
    private String configJsonCode;

    private String workflowPermissionCode;

    private String simpleFormCode;

    /**
     * 表单容器代码
     */
    private String containerCode;

    /**
     * 推单代码
     */
    private String pushOrderCode;
}
