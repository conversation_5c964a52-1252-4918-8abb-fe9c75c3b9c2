package com.zilue.module.generator.utils;

import com.baomidou.mybatisplus.annotation.DbType;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: zilue
 * @Date: 2023/5/5 11:54
 */
public class SqlUtil {


    public static List<String> buildAddDataAuthFieldSqls(DbType dbType, String tableName) {
        List<String> sqlList = new ArrayList<>();
        switch (dbType) {
            case MYSQL:
                sqlList.add("ALTER TABLE " + tableName + " ADD rule_user_id bigint NULL COMMENT '权限所属人员id'");
                break;
            case SQL_SERVER:
            case SQL_SERVER2005:
                sqlList.add("ALTER TABLE " + tableName + " ADD rule_user_id bigint NULL");
                sqlList.add("EXEC sp_addextendedproperty 'MS_Description', '权限所属人员id', 'user', 'dbo', 'table', '" + tableName + "', 'column', 'rule_user_id'");
                break;
        }
        return sqlList;
    }
}
