package com.zilue.module.generator.entity;

import com.zilue.common.model.generator.FormConfig;
import lombok.Data;

import java.util.List;

/**
 * @Author: zilue
 * @Date: 2023/4/28 14:45
 */
@Data
public class GeneratorConfig {
    /**
     * 包名
     */
    private String databaseId;

    /**
     * 表关联信息
     */
    private List<TableConfig> tableConfigs;

    /**
     * 表单设计json
     */
    private FormConfig formJson;

    /**
     * 列表配置
     */
    private ListConfig listConfig;

    /**
     * 输出配置
     */
    private OutputConfig outputConfig;

    /**
     * 菜单配置
     */
    private MenuConfig menuConfig;

    /**
     * 表结构配置
     */
    private List<TableStructureConfig> tableStructureConfigs;
}
