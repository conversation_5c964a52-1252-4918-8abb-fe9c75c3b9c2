package com.zilue.module.generator.dto;

import com.zilue.module.generator.entity.FrontCode;
import com.zilue.module.generator.entity.MenuConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * @Author: zilue
 * @Date: 2023/6/6 16:05
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CodeFirstGeneratorDto extends CodeFirstPreviewDto {

    /**
     * 代码模板id
     */
    private Long id;
    /**
     * 前端代码
     */
    private FrontCode frontCode;

    /**
     * 分类id
     */
    private Long categoryId;

    /**
     * 代码模板名称
     */
    private String name;

    /**
     * 菜单配置
     */
    @NotNull(message = "菜单配置不能为空！")
    @Valid
    private MenuConfig menuConfig;
}
