package com.zilue.module.generator.entity;

import lombok.Data;

/**
 * @title: 字段配置
 * <AUTHOR>
 * @Date: 2023/4/17 0:24
 * @Version 1.0
 */
@Data
public class FieldConfig {


    /**
     * 是否主键
     */
    private Boolean pk;

    /**
     * 字段名称
     */
    private String fieldName;

    /**
     * 字段长度
     */
    private String fieldLength;

    /**
     * 字段类型
     */
    private String fieldType;

    /**
     * 字段备注
     */
    private String fieldComment;


    /**
     * 是否为 新增 自动填充字段
     */
    private Boolean autoInsert;

    /**
     * 是否为 修改 自动填充字段
     */
    private Boolean autoUpdate;


    /**
     * 是否为 逻辑删除字段
     */
    private Boolean deleteMark;

    /**
     * 字段对应的组件类型
     */
    private String componentType;

    /**
     * 数据来源类型
     */
    private String datasourceType;

    /**
     * 数据来源id
     */
    private String datasourceId;

    /**
     * 格式
     */
    private String pattern;

    /**
     * 是否是多选字段
     */
    private boolean isMulti;

    /**
     * 多个值分隔符
     */
    private String separator;

    /**
     * 多个值显示格式
     */
    private String showFormat;

    /**
     * label
     */
    private String label;

    /**
     * 是否必填
     */
    private boolean isRequired;
}
