package com.zilue.module.generator.entity;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @title: 左侧菜单配置
 * <AUTHOR>
 * @Date: 2023/4/12 22:14
 * @Version 1.0
 */
@Data
public class LeftMenuConfig {
    /**
     * 左侧菜单是否为树结构
     */
    private Boolean isTree;

    /**
     * 左侧菜单宽度 按照 1/3 | 1/4 | 1/5
     */
    private Integer leftWidth;

    /**
     * datasource | dic
     */
    private String datasourceType;

    /**
     * 如果是数据源 就需要有数据源id
     */
    private String datasourceId;

    /**
     * 关联字段
     */
    private String relationFieldName;

    /**
     * 列表关联字段关联字段
     */
    private String listFieldName;

    /**
     * 如果是数据源 则需要配置 数据字段名 （与parentFiledName 搭配拼接树结构）
     */
    private String fieldName;


    /**
     * 如果是数据源 则需要配置 父级字段名 （与fieldName 搭配拼接树结构）
     */
    private String parentFiledName;

    /**
     * 显示字段
     */
    private String showFieldName;

    /**
     * 数据项Id 用户查询数据字典详情
     */
    private String dictionaryItemId;


    /**
     * 菜单显示名称
     */
    private String menuName;

    /**
     * 父级菜单图标
     */
    private String parentIcon;

    /**
     * 子级菜单图标
     */
    private String childIcon;

    /**
     * api配置
     */
    private Map<String, Object> apiConfig;

    /**
     * 静态数据
     */
    private List<Map<String, Object>> staticData;

    /**
     * 树结构配置
     */
    private TreeConfig treeConfig;
}
