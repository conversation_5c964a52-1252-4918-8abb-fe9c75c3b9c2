package com.zilue.module.generator.entity;

import lombok.Data;

import java.util.List;

/**
 * @title: 代码优先表结构配置
 * <AUTHOR>
 * @Date: 2023/4/30 22:51
 * @Version 1.0
 */
@Data
public class TableStructureConfig {

    /**
     *   操作 operator
     *   1  沿用旧表
     *   2  创建新表（重新生成表名）
     *   3  覆盖旧表
     *   4  不操作
     */
    private Integer operator = 4;

    /**
     * 表名
     */
    private String tableName;
    /**
     * 表注释
     */
    private String tableComment;

    /**
     * 是否主表
     */
    private Boolean isMain;

    /**
     * 主键名
     */
    private String pkField;

    /**
     * 上级表key值（孙表）
     */
    private String parentKey;

    /**
     * 上级表名（孙表）
     */
    private String parentTable;

    /**
     * 表的字段配置
     */
    private List<TableFieldConfig> tableFieldConfigs;

}
