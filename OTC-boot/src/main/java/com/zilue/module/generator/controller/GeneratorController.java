package com.zilue.module.generator.controller;

import com.zilue.common.annotation.XjrLog;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.model.result.R;
import com.zilue.module.generator.dto.*;
import com.zilue.module.generator.service.IGeneratorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * @title: GeneratorController
 * <AUTHOR>
 * @Date: 2023/4/12 21:00
 * @Version 1.0
 */
@RestController
@RequestMapping(GlobalConstant.SYSTEM_MODULE_PREFIX + "/generator")
@Api(value = GlobalConstant.SYSTEM_MODULE_PREFIX + "/generator", tags = "代码生成器")
@AllArgsConstructor
public class GeneratorController {

    private final IGeneratorService generatorService;

    @PostMapping("/preview-code/data-first")
    @ApiOperation(value = "数据优先-预览代码", notes = "数据优先-预览代码")
    @XjrLog(value = "数据优先预览代码")
    public R previewCode(@Valid @RequestBody DataFirstPreviewDto dto) {
        return R.ok(generatorService.getPreviewCodes(dto));
    }

    @PostMapping("/generator-code/data-first")
    @ApiOperation(value = "数据优先-生成代码", notes = "数据优先-生成代码")
    @XjrLog(value = "数据优先生成代码")
    public R generatorCode(@Valid @RequestBody DataFirstGeneratorDto dto) {
        return R.ok(generatorService.generateCodes(dto));
    }

    @PostMapping("/preview-code/code-first")
    @ApiOperation(value = "代码优先-预览代码", notes = "代码优先-预览代码")
    @XjrLog(value = "界面优先预览代码")
    public R previewCodeByCodeFirst(@Valid @RequestBody CodeFirstPreviewDto dto) throws Exception  {
        return R.ok(generatorService.getCodeFirstPreviewCodes(dto));
    }

    @PostMapping("/generator-code/code-first")
    @ApiOperation(value = "代码优先-生成代码", notes = "代码优先-生成代码")
    @XjrLog(value = "界面优先生成代码")
    public R generatorCodeByCodeFirst(@Valid @RequestBody CodeFirstGeneratorDto dto)  throws Exception {
        return R.ok(generatorService.generateCodeFirstCodes(dto));
    }


    @PostMapping("/generator-code/batch")
    @ApiOperation(value = "批量生成模板-生成代码", notes = "批量生成模板-生成代码")
    @XjrLog(value = "批量生成代码")
    public R generatorCodeBatch(@Valid @RequestBody BatchGeneratorDto dto) {
        return R.ok(generatorService.generatorCodeBatch(dto));
    }

    @GetMapping("/table-info")
    @ApiOperation(value = "根据 tabconfig 获取 表结构信息  用于生成前端model", notes = "根据 tabconfig 获取 表结构信息  用于生成前端model")
    @XjrLog(value = "获取表结构信息生成前端model")
    public R tableInfo(@Valid @RequestBody TableInfoDto dto) {
        return R.ok(generatorService.getTableInfo(dto));
    }


    @PostMapping("/generator-app-code")
    @ApiOperation(value = "生成手机端代码", notes = "生成手机端代码")
    @XjrLog(value = "生成手机端代码")
    public R generatorAppCode(@Valid @RequestBody GeneratorAppDto dto) {
        return R.ok(generatorService.generateAppCodes(dto));
    }
}
