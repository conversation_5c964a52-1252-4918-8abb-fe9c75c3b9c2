package com.zilue.module.generator.entity;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class TreeConfig {

    /**
     * 主键
     */
    private String id;

    /**
     *  是否多选
     */
    private Boolean isMultiple = false;

    /**
     *  名称
     */
    private String name;

    /**
     *  类型
     */
    private Integer type;

    /**
     * 配置提示
     */
    private String configTip;

    /**
     *  数据配置
     */
    private List<Map<String, Object>> config;
}
