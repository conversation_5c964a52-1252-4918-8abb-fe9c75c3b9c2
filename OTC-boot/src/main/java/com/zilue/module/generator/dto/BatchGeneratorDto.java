package com.zilue.module.generator.dto;

import com.zilue.module.generator.entity.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: zilue
 * @Date: 2023/9/1 17:43
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BatchGeneratorDto extends  DataFirstPreviewDto {
    /**
     * 代码模板id
     */
    private Long id;
    /**
     * 前端代码
     */
    private FrontCode frontCode;
    /**
     * 分类id
     */
    private Long categoryId;

    /**
     * 代码模板名称
     */
    private String name;



}
