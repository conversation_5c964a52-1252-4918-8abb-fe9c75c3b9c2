package com.zilue.module.generator.entity;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 * @title: 列表页合计配置
 * <AUTHOR>
 * @Date: 2023/4/14 21:42
 * @Version 1.0
 */
@Data
public class TotalConfig {
    /**
     * 需要合计字段
     */
    @NotNull(message = "合计字段不能为空！")
    @Length(max = 30, message = "合计字段长度不能超过30！")
    private String fieldName;
}
