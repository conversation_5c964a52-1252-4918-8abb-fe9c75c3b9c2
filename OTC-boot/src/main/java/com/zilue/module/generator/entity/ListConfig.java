package com.zilue.module.generator.entity;

import com.baomidou.mybatisplus.core.toolkit.StringPool;
import lombok.Data;

import java.util.List;

/**
 * @title: 列表配置
 * <AUTHOR>
 * @Date: 2023/4/12 21:55
 * @Version 1.0
 */
@Data
public class ListConfig {

    /**
     * 是否有左侧菜单
     */
    private Boolean isLeftMenu = false;

    /**
     * 是否分页
     */
    private Boolean isPage = true;

    /**
     * 每页显示数量
     */
    private Integer pageSize;

    /**
     * 左侧菜单配置
     */
    private LeftMenuConfig leftMenuConfig;

    /**
     * 查询配置
     */
    private List<QueryConfig> queryConfigs;

    /**
     * 列配置
     */
    private List<ColumnConfig> columnConfigs;

    /**
     * 按钮配置
     */
    private List<ButtonConfig> buttonConfigs;

    /**
     * 合计配置
     */
    private List<TotalConfig> totalConfigs;

    /**
     * 是否默认排序
     */
    private Boolean defaultOrder = false;

    /**
     * 排序字段
     */
    private String orderBy;

    /**
     * 排序类型
     */
    private String orderType = StringPool.EMPTY;

    /**
     * 列表标题
     */
    private String listTitle;

    /**
     * 排列方式
     */
    private String arrangeType;

    /**
     * 列表样式
     */
    private String listStyle;


    /**
     * 是否高级查询，默认false不开启
     */
    private Boolean isAdvancedQuery = false;

    /**
     * 高级查询配置
     */
    private String querySelectOption;

}
