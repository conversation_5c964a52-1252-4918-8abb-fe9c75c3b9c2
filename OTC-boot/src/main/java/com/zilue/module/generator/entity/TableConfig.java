package com.zilue.module.generator.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 * @title: 数据表配置
 * <AUTHOR>
 * @Date: 2023/4/12 21:19
 * @Version 1.0
 */
@Data
public class TableConfig {

    /**
     * 表名
     */
    @NotNull(message = "表名不能为空！")
    @Length(max = 50, message = "表名长度不能超过30！")
    private String tableName;

    /**
     * 是否主表
     */
//    @NotNull(message = "是否主表不能为空！")
    private Boolean isMain = false;

    /**
     * 主键字段
     */
    @NotNull(message = "主键字段不能为空！")
    @Length(max = 30, message = "主键长度不能超过30！")
    private String pkField;

    /**
     * 主键字段类型
     */
    @NotNull(message = "主键字段类型不能为空！")
    @Length(max = 30, message = "主键类型长度不能超过30！")
    private String pkType;

    /**
     * 关联字段
     */
    @Length(max = 30, message = "关联字段长度不能超过30！")
    private String relationField;

    /**
     * 关联表对应字段
     */
    @Length(max = 30, message = "关联表对应字段长度不能超过30！")
    private String relationTableField;

    /**
     * 是否是子表
     */
    private Boolean isSubForm;

    /**
     * 父表
     */
    private String parentTable;

    /**
     * 次级表
     */
    @JsonIgnore
    private TableConfig subTable;
}
