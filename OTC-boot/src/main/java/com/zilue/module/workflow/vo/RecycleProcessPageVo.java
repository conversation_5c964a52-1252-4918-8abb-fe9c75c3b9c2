package com.zilue.module.workflow.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: zilue
 * @Date: 2023/11/3 10:05
 */
@Data
public class RecycleProcessPageVo {


    /**
     * 流程id
     */
    private String processId;
    /**
     * 流水号
     */
    private Long serialNumber;

    /**
     * 模板id
     */
    private Long schemaId;

    /**
     * 任务id
     */
    private String taskId;

    private String processName;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 流程模板名称
     */
    private String schemaName;

    /**
     * 当前进度
     */
    private Integer currentProgress;

    /**
     * 发起人
     */
    private String originator;

    /**
     * 当前任务
     */
    private String currentTaskName;

    /**
     * 时间
     */
    private LocalDateTime createTime;
}
