package com.zilue.module.workflow.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zilue.module.workflow.entity.WorkflowSchemaAuth;
import com.zilue.module.workflow.mapper.WorkflowSchemaAuthMapper;
import com.zilue.module.workflow.service.IWorkflowSchemaAuthService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 流程模板权限 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-04
 */
@Service
@AllArgsConstructor
public class WorkflowSchemaAuthServiceImpl extends ServiceImpl<WorkflowSchemaAuthMapper, WorkflowSchemaAuth> implements IWorkflowSchemaAuthService {

}
