package com.zilue.module.workflow.model;

import lombok.Data;

import java.util.List;

/**
 * @Author: zilue
 * @Date: 2023/4/3 9:31
 */
@Data
public class WorkflowListResult {

    /**
     * 流程状态
     */
    private String status;

    /**
     * 流程id
     */
    private String processId;

    /**
     * 流程模板id
     */
    private Long schemaId;


    /**
     * 流水号
     */
    private Long serialNumber;

    /**
     * 当前任务id
     */
    private List<String> taskIds;

    /**
     * 是否启用 关联流程
     */
    private Boolean enabled = Boolean.TRUE;

    /**
     * 草稿id
     */

    private Long draftId;
}
