package com.zilue.module.workflow.service.impl;

import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.toolkit.MPJWrappers;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.enums.EnabledMark;
import com.zilue.common.exception.MyException;
import com.zilue.common.page.ConventPage;
import com.zilue.common.page.PageOutput;
import com.zilue.common.utils.IdempotentUtils;
import com.zilue.common.utils.VoToColumnUtil;
import com.zilue.module.organization.entity.Post;
import com.zilue.module.system.entity.DictionaryDetail;
import com.zilue.module.workflow.constant.WorkflowConstant;
import com.zilue.module.workflow.dto.*;
import com.zilue.module.workflow.entity.WorkflowSchema;
import com.zilue.module.workflow.entity.WorkflowSchemaAuth;
import com.zilue.module.workflow.entity.WorkflowSchemaDraft;
import com.zilue.module.workflow.entity.WorkflowSchemaHistory;
import com.zilue.module.workflow.mapper.WorkflowSchemaDraftMapper;
import com.zilue.module.workflow.mapper.WorkflowSchemaHistoryMapper;
import com.zilue.module.workflow.mapper.WorkflowSchemaMapper;
import com.zilue.module.workflow.model.AuthConfig;
import com.zilue.module.workflow.model.FormInitConfig;
import com.zilue.module.workflow.model.MemberConfig;
import com.zilue.module.workflow.model.WorkflowSchemaConfig;
import com.zilue.module.workflow.service.IWorkflowSchemaAuthService;
import com.zilue.module.workflow.service.IWorkflowSchemaService;
import com.zilue.module.workflow.utils.WorkFlowUtil;
import com.zilue.module.workflow.vo.WorkflowSchemaDraftPageVo;
import com.zilue.module.workflow.vo.WorkflowSchemaPageVo;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.camunda.bpm.engine.RepositoryService;
import org.camunda.bpm.engine.TaskService;
import org.camunda.bpm.engine.repository.Deployment;
import org.camunda.bpm.engine.repository.ProcessDefinition;
import org.camunda.bpm.engine.spring.SpringProcessEngineConfiguration;
import org.camunda.commons.utils.IoUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <p>
 * 流程模板表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-04
 */
@Service
@AllArgsConstructor
public class WorkflowSchemaServiceImpl extends MPJBaseServiceImpl<WorkflowSchemaMapper, WorkflowSchema> implements IWorkflowSchemaService {

    private final RepositoryService repositoryService;

    private final SpringProcessEngineConfiguration processEngineConfiguration;

    private final WorkflowSchemaDraftMapper workflowSchemaDraftMapper;

    private final WorkflowSchemaHistoryMapper workflowSchemaHistoryMapper;

    private final TaskService taskService;

    private final IWorkflowSchemaAuthService workflowSchemaAuthService;

    @Override
    public PageOutput<WorkflowSchemaPageVo> getPage(WorkflowSchemaPageDto dto) {

        SaSession tokenSession = StpUtil.getTokenSession();
        List<Long> roleIds = tokenSession.get(GlobalConstant.LOGIN_USER_ROLE_ID_KEY, new ArrayList<>());
        Post post = tokenSession.get(GlobalConstant.LOGIN_USER_POST_INFO_KEY, new Post());

        List<Long> allSchemaId = new ArrayList<>();

        //是否需要管控权限
        if (dto.getIsAuth()) {
            LambdaQueryWrapper<WorkflowSchemaAuth> query = Wrappers.lambdaQuery(WorkflowSchemaAuth.class)
                    .eq(WorkflowSchemaAuth::getObjType, -1)
                    .or(x -> x.eq(WorkflowSchemaAuth::getObjType, 2).in(WorkflowSchemaAuth::getObjId, post.getId()))
                    .or(x -> x.eq(WorkflowSchemaAuth::getObjType, 1).in(WorkflowSchemaAuth::getObjId, roleIds))
                    .or(x -> x.eq(WorkflowSchemaAuth::getObjType, 0).in(WorkflowSchemaAuth::getObjId, StpUtil.getLoginIdAsLong())
                    );
            List<WorkflowSchemaAuth> authList = workflowSchemaAuthService.list(query);
            allSchemaId = authList.stream().map(WorkflowSchemaAuth::getSchemaId).collect(Collectors.toList());

            if (CollectionUtil.isEmpty(allSchemaId)) {
                //如果权限为空  返回空数组
                PageOutput<WorkflowSchemaPageVo> pageOutput = new PageOutput<>();
                pageOutput.setList(new ArrayList<>());
                pageOutput.setCurrentPage(0);
                pageOutput.setTotalPage(0);
                pageOutput.setPageSize(0);
                return pageOutput;
            }

        }

        //因为多表关联 会有多个表都使用了id字段，  所以必须专门指定主表的Id
        IPage<WorkflowSchemaPageVo> page = selectJoinListPage(ConventPage.getPage(dto), WorkflowSchemaPageVo.class,
                MPJWrappers.<WorkflowSchema>lambdaJoin()
                        .disableSubLogicDel()
                        .orderByDesc(WorkflowSchema::getId)
                        .eq(ObjectUtil.isNotNull(dto.getEnabledMark()), WorkflowSchema::getEnabledMark, dto.getEnabledMark())
                        .like(StrUtil.isNotBlank(dto.getKeyword()), WorkflowSchema::getName, dto.getKeyword())
                        .like(StrUtil.isNotBlank(dto.getName()), WorkflowSchema::getName, dto.getName())
                        .like(StrUtil.isNotBlank(dto.getCode()), WorkflowSchema::getCode, dto.getCode())
                        .eq(ObjectUtil.isNotNull(dto.getCategory()), WorkflowSchema::getCategory, dto.getCategory())
                        .in(dto.getIsAuth() && CollectionUtil.isNotEmpty(allSchemaId), WorkflowSchema::getId, allSchemaId)
                        .and(wrapper -> wrapper.isNull(WorkflowSchema::getFirstSchemaId)
                                .or()
                                .eq(WorkflowSchema::getActivityFlag,EnabledMark.ENABLED.getCode()))
                        .select(WorkflowSchema::getId)
                        .select(WorkflowSchema.class, x -> VoToColumnUtil.fieldsToColumns(WorkflowSchemaPageVo.class).contains(x.getProperty()))
                        .selectAs(DictionaryDetail::getName, WorkflowSchemaPageVo::getCategoryName)
                        .leftJoin(DictionaryDetail.class, DictionaryDetail::getId, WorkflowSchema::getCategory));

        PageOutput<WorkflowSchemaPageVo> pageOutput = ConventPage.getPageOutput(page, WorkflowSchemaPageVo.class);

        return pageOutput;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @SneakyThrows
    public boolean add(AddWorkflowSchemaDto dto) {
        LambdaQueryWrapper<WorkflowSchema> countQuery = Wrappers.lambdaQuery(WorkflowSchema.class).eq(WorkflowSchema::getActivityFlag,EnabledMark.ENABLED.getCode()).eq(WorkflowSchema::getName, dto.getProcessConfig().getName()).or().eq(WorkflowSchema::getCode, dto.getProcessConfig().getCode());
        if (count(countQuery) > 0) {
            throw new MyException("当前模板名称和模板编号重复！");
        }

        WorkflowSchemaConfig workflowSchemaConfig = BeanUtil.toBean(dto, WorkflowSchemaConfig.class);
        WorkflowSchema workflowSchema = BeanUtil.toBean(dto.getProcessConfig(), WorkflowSchema.class);

        //表单发起流程
        FormInitConfig formInitConfig = workflowSchemaConfig.getProcessConfig().getFormInitConfig();
        if (formInitConfig.getEnabled()) {
            LambdaQueryWrapper<WorkflowSchema> select = Wrappers.lambdaQuery(WorkflowSchema.class).eq(WorkflowSchema::getFormId, formInitConfig.getFormId()).select(WorkflowSchema::getId);

            if (count(select) > 0) {
                throw new MyException("当前表单已经绑定过流程，请重新选择！");
            }
            workflowSchema.setFormId(formInitConfig.getFormId());
        }

        Deployment deploy = repositoryService.createDeployment()
                .addInputStream(workflowSchemaConfig.getProcessConfig().getName() + StringPool.DOT + WorkflowConstant.WORKFLOW_SUFFIX, IoUtil.stringAsInputStream(workflowSchemaConfig.getProcessConfig().getXmlContent())).name(workflowSchemaConfig.getProcessConfig().getName()).deploy();

        //存储流程部署id
        workflowSchema.setDeploymentId(deploy.getId());

        //获取流程定义id
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().deploymentId(deploy.getId()).singleResult();
        //存储流程定义key
        workflowSchema.setDefinitionKey(processDefinition.getKey());

        //存储流程定义id
        workflowSchema.setDefinitionId(processDefinition.getId());

        //把配置json存储  (清除掉xml)
        workflowSchemaConfig.getProcessConfig().setXmlContent("");

        ObjectMapper mapper = new ObjectMapper();
        SimpleModule simpleModule = new SimpleModule();
        simpleModule.addSerializer(Long.class, ToStringSerializer.instance);
        simpleModule.addSerializer(Long.TYPE, ToStringSerializer.instance);
        mapper.registerModule(simpleModule);

        String jsonContent = mapper.writeValueAsString(workflowSchemaConfig);

        workflowSchema.setJsonContent(jsonContent);

        //使用雪花算法id作为主键
        workflowSchema.setId(IdUtil.getSnowflakeNextId());
        //设置第一版本号，作为查询标识
        workflowSchema.setFirstSchemaId(workflowSchema.getId());
        //设置版本号
        workflowSchema.setVersion(1);
        //活动版本
        workflowSchema.setActivityFlag(EnabledMark.ENABLED.getCode());

        save(workflowSchema);

        AuthConfig authConfig = workflowSchemaConfig.getProcessConfig().getAuthConfig();

        //如果是指定人员
        if (authConfig.getAuthType() == 1) {
            List<WorkflowSchemaAuth> authList = new ArrayList<>();
            List<MemberConfig> authMemberConfigs = authConfig.getAuthMemberConfigs();
            for (MemberConfig authMemberConfig : authMemberConfigs) {
                WorkflowSchemaAuth auth = new WorkflowSchemaAuth();
                auth.setSchemaId(workflowSchema.getId());
                auth.setObjId(Long.parseLong(authMemberConfig.getId()));
                auth.setObjName(authMemberConfig.getName());
                auth.setObjType(authMemberConfig.getMemberType());
                authList.add(auth);
            }
            workflowSchemaAuthService.saveBatch(authList);
        }
        //如果所有人
        else {
            WorkflowSchemaAuth auth = new WorkflowSchemaAuth();
            auth.setSchemaId(workflowSchema.getId());
            auth.setObjType(-1);
            workflowSchemaAuthService.save(auth);
        }


        //将新增的数据 记录模板历史记录
        WorkflowSchemaHistory history = new WorkflowSchemaHistory();
        history.setSchemaId(workflowSchema.getId());
        history.setVersion(1);
        history.setActivityFlag(EnabledMark.ENABLED.getCode());
        history.setXmlContent(workflowSchema.getXmlContent());
        history.setJsonContent(workflowSchema.getJsonContent());
        history.setDefinitionKey(workflowSchema.getDefinitionKey());
        history.setDeploymentId(workflowSchema.getDeploymentId());
        history.setDefinitionId(workflowSchema.getDefinitionId());


        //缓存节点监听器数据
        CompletableFuture.runAsync(() -> {
            WorkFlowUtil.cacheNodeListener(deploy.getId(), workflowSchemaConfig.getChildNodeConfig());
        });


        return workflowSchemaHistoryMapper.insert(history) > 0;
    }

    @Override
    public boolean addDraft(AddWorkflowSchemaDraftDto dto) {
        WorkflowSchemaConfig workflowSchemaConfig = BeanUtil.toBean(dto, WorkflowSchemaConfig.class);
        WorkflowSchemaDraft workflowSchemaDraft = BeanUtil.toBean(dto.getProcessConfig(), WorkflowSchemaDraft.class);

        //把配置json存储
        workflowSchemaDraft.setJsonContent(JSONUtil.toJsonStr(workflowSchemaConfig));

        return workflowSchemaDraftMapper.insert(workflowSchemaDraft) > 0;
    }

    @Override
    public PageOutput<WorkflowSchemaDraftPageVo> getDraftPage(WorkflowSchemaDraftPageDto dto) {

        LambdaQueryWrapper<WorkflowSchemaDraft> queryWrapper = Wrappers.lambdaQuery(WorkflowSchemaDraft.class)
                .like(StrUtil.isNotBlank(dto.getKeyword()), WorkflowSchemaDraft::getName, dto.getKeyword())
                .between(ObjectUtil.isNotNull(dto.getStartTime()) && ObjectUtil.isNotNull(dto.getEndTime()), WorkflowSchemaDraft::getCreateDate, dto.getStartTime(), dto.getEndTime())
                .select(WorkflowSchemaDraft.class, x -> VoToColumnUtil.fieldsToColumns(WorkflowSchemaPageVo.class).contains(x.getProperty()));

        IPage<WorkflowSchemaDraft> workflowSchemaDraftPage = workflowSchemaDraftMapper.selectPage(ConventPage.getPage(dto), queryWrapper);

        return ConventPage.getPageOutput(workflowSchemaDraftPage, WorkflowSchemaDraftPageVo.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @SneakyThrows
    public boolean update(UpdateWorkflowSchemaDto dto) {

        WorkflowSchema workflowSchema = getById(dto.getId());

        if (workflowSchema == null) {
            throw new MyException("找不到此模板！");
        }

        if (ObjectUtil.isNotNull(workflowSchema.getFirstSchemaId()) && workflowSchema.getActivityFlag() == EnabledMark.DISABLED.getCode()){
            throw new MyException("存在其它人员已经对本模板进行修改，当前版本不是活动中的版本，请退出后重新编辑！！");
        }

        LambdaQueryWrapper<WorkflowSchema> countQuery = Wrappers.lambdaQuery(WorkflowSchema.class)
                .ne(ObjectUtil.isNotEmpty(workflowSchema.getFirstSchemaId()),WorkflowSchema::getFirstSchemaId, workflowSchema.getFirstSchemaId())
                .ne(ObjectUtil.isEmpty(workflowSchema.getFirstSchemaId()),WorkflowSchema::getId,workflowSchema.getId())  //第一个版本的模板id存在就是更新后的逻辑，新逻辑走上面的条件、老逻辑走下面的条件
                .eq(WorkflowSchema::getActivityFlag,EnabledMark.ENABLED.getCode())
                .and(x -> x.eq(WorkflowSchema::getName, dto.getProcessConfig().getName())
                        .or().eq(WorkflowSchema::getCode, dto.getProcessConfig().getCode()));

        if (count(countQuery) > 0) {
            throw new MyException("当前模板名称和模板编号重复！");
        }

        //保存一份新的模板,给id进行加锁，防止同时重复提交
        if (!IdempotentUtils.judge(dto.getId(), this.getClass())) {
            throw new MyException("当前存在其它人员正在操作当前模板,请勿重复请求");
        }

        WorkflowSchema newWorkflowSchema = new WorkflowSchema();

        //设置第一版本模板的id,不为空就使用修改之前的，为空就表明可能是老数据，就需要拿id去历史模板表里面找到第一个版本的模板id
        //设置版本号、当前活跃版本
        if (ObjectUtil.isNotEmpty(workflowSchema.getFirstSchemaId())){
            newWorkflowSchema.setFirstSchemaId(workflowSchema.getFirstSchemaId());
            //查询当前的最高版本，并+1给当前版本，并将查到的所有的数据的是否为活动版本设置为否，当前设置为是
            List<WorkflowSchema> list = list(Wrappers.<WorkflowSchema>lambdaQuery().eq(WorkflowSchema::getFirstSchemaId, workflowSchema.getFirstSchemaId()).orderByDesc(WorkflowSchema::getVersion));
            if (list.size() > 0){
                Integer version = list.get(0).getVersion();
                newWorkflowSchema.setVersion(version + 1);
            }else {
                newWorkflowSchema.setVersion(1);
            }
        }else {
            workflowSchema.setFirstSchemaId(workflowSchema.getId());
            workflowSchema.setVersion(1);
            newWorkflowSchema.setFirstSchemaId(workflowSchema.getId());
            newWorkflowSchema.setVersion(2);
        }
        //将模板设置为非活动版本，新的模板为活动版本
        workflowSchema.setActivityFlag(EnabledMark.DISABLED.getCode());
        newWorkflowSchema.setActivityFlag(EnabledMark.ENABLED.getCode());

        newWorkflowSchema.setName(dto.getProcessConfig().getName());
        newWorkflowSchema.setCode(dto.getProcessConfig().getCode());
        newWorkflowSchema.setRemark(dto.getProcessConfig().getRemark());
        newWorkflowSchema.setXmlContent(dto.getProcessConfig().getXmlContent());
        newWorkflowSchema.setWorkflowChat(dto.getProcessConfig().getWorkflowChat());
        newWorkflowSchema.setCategory(Long.valueOf(dto.getProcessConfig().getCategory()));

        WorkflowSchemaConfig workflowSchemaConfig = BeanUtil.toBean(dto, WorkflowSchemaConfig.class);


        //表单发起流程
        FormInitConfig formInitConfig = workflowSchemaConfig.getProcessConfig().getFormInitConfig();
        if (formInitConfig.getEnabled()) {
            LambdaQueryWrapper<WorkflowSchema> select = Wrappers.lambdaQuery(WorkflowSchema.class).ne(WorkflowSchema::getId, dto.getId()).ne(WorkflowSchema::getFirstSchemaId,newWorkflowSchema.getFirstSchemaId()).eq(WorkflowSchema::getFormId, formInitConfig.getFormId()).select(WorkflowSchema::getId);

            if (count(select) > 0) {
                throw new MyException("当前表单已经绑定过流程，请重新选择！");
            }
            newWorkflowSchema.setFormId(formInitConfig.getFormId());
        } else {
            newWorkflowSchema.setFormId(0L);
        }


//        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().deploymentId(workflowSchema.getDeploymentId()).singleResult();
//
//
//        //如果需要级联删除  把第二个参数设置为true
//        //级联删除 会删除所有关联数据 包括 历史记录
//        repositoryService.deleteProcessDefinition(processDefinition.getId());
//
//        //如果需要级联删除  把第二个参数设置为true
//        //级联删除 会删除所有关联数据 包括 历史记录
//        repositoryService.deleteDeployment(workflowSchema.getDeploymentId());


        //因为流程图会被缓存  如果修改了流程图 缓存会没变。 需要清理本地缓存
//        processEngineConfiguration.getDeploymentCache().removeDeployment(workflowSchema.getDeploymentId());

        //更新的流程重新部署
        Deployment deploy = repositoryService.createDeployment()
                .addInputStream(dto.getProcessConfig().getName() + StringPool.DOT + WorkflowConstant.WORKFLOW_SUFFIX, IoUtil.stringAsInputStream(dto.getProcessConfig().getXmlContent())).name(dto.getProcessConfig().getName()).deploy();

        //获取流程定义id
        ProcessDefinition newProcessDefinition = repositoryService.createProcessDefinitionQuery().deploymentId(deploy.getId()).singleResult();
        //存储流程定义key
        newWorkflowSchema.setDefinitionKey(newProcessDefinition.getKey());
        //存储流程定义id
        newWorkflowSchema.setDefinitionId(newProcessDefinition.getId());
        newWorkflowSchema.setDeploymentId(deploy.getId());

        //把配置json存储  (清除掉xml)
        workflowSchemaConfig.getProcessConfig().setXmlContent("");

        ObjectMapper mapper = new ObjectMapper();
        SimpleModule simpleModule = new SimpleModule();
        simpleModule.addSerializer(Long.class, ToStringSerializer.instance);
        simpleModule.addSerializer(Long.TYPE, ToStringSerializer.instance);
        mapper.registerModule(simpleModule);

        String jsonContent = mapper.writeValueAsString(workflowSchemaConfig);

        newWorkflowSchema.setJsonContent(jsonContent);

        newWorkflowSchema.setId(IdUtil.getSnowflakeNextId());

        save(newWorkflowSchema);

        AuthConfig authConfig = workflowSchemaConfig.getProcessConfig().getAuthConfig();
        //如果是指定人员
        if (authConfig.getAuthType() == 1) {
            workflowSchemaAuthService.remove(Wrappers.lambdaQuery(WorkflowSchemaAuth.class).eq(WorkflowSchemaAuth::getSchemaId, workflowSchema.getId()));
            List<WorkflowSchemaAuth> authList = new ArrayList<>();
            List<MemberConfig> authMemberConfigs = authConfig.getAuthMemberConfigs();
            for (MemberConfig authMemberConfig : authMemberConfigs) {
                WorkflowSchemaAuth auth = new WorkflowSchemaAuth();
                auth.setSchemaId(newWorkflowSchema.getId());
                auth.setObjId(Long.parseLong(authMemberConfig.getId()));
                auth.setObjName(authMemberConfig.getName());
                auth.setObjType(authMemberConfig.getMemberType());
                authList.add(auth);
            }
            workflowSchemaAuthService.saveBatch(authList);
        }
        //如果所有人
        else {
            workflowSchemaAuthService.remove(Wrappers.lambdaQuery(WorkflowSchemaAuth.class).eq(WorkflowSchemaAuth::getSchemaId, workflowSchema.getId()));
            WorkflowSchemaAuth auth = new WorkflowSchemaAuth();
            auth.setSchemaId(newWorkflowSchema.getId());
            auth.setObjType(-1);
            workflowSchemaAuthService.save(auth);
        }


        //找到上一个版本号 只需要版本号 以及 id
        LambdaQueryWrapper<WorkflowSchemaHistory> queryWrapper = Wrappers.lambdaQuery(WorkflowSchemaHistory.class)
                .eq(WorkflowSchemaHistory::getSchemaId, newWorkflowSchema.getFirstSchemaId())
                .select(WorkflowSchemaHistory::getVersion, WorkflowSchemaHistory::getId,WorkflowSchemaHistory::getActivityFlag)
                .orderByDesc(WorkflowSchemaHistory::getVersion);
        
        List<WorkflowSchemaHistory> oldHistoryList = workflowSchemaHistoryMapper.selectList(queryWrapper);
        
        if (oldHistoryList.size() > 0){
            Integer version = oldHistoryList.get(0).getVersion();

            //将修改后的数据 记录模板历史记录
            WorkflowSchemaHistory history = new WorkflowSchemaHistory();
            history.setSchemaId(newWorkflowSchema.getFirstSchemaId());
            history.setVersion(version + 1);
            history.setActivityFlag(EnabledMark.ENABLED.getCode());
            history.setXmlContent(newWorkflowSchema.getXmlContent());
            history.setJsonContent(newWorkflowSchema.getJsonContent());
            history.setWorkflowChat(newWorkflowSchema.getWorkflowChat());
            history.setDefinitionId(newWorkflowSchema.getDefinitionId());
            history.setDefinitionKey(newWorkflowSchema.getDefinitionKey());
            workflowSchemaHistoryMapper.insert(history);

            Optional<WorkflowSchemaHistory> first = oldHistoryList.stream().filter(x -> x.getActivityFlag().equals(EnabledMark.ENABLED.getCode())).findFirst();
            if (first.isPresent()){
                //将上一个版本的 历史数据 flag  改为 非活动版本
                first.get().setActivityFlag(EnabledMark.DISABLED.getCode());
                workflowSchemaHistoryMapper.updateById(first.get());
            }
        }

        //缓存节点监听器数据
        CompletableFuture.runAsync(() -> {
            WorkFlowUtil.cacheNodeListener(deploy.getId(), workflowSchemaConfig.getChildNodeConfig());
        });

        updateById(workflowSchema);
        //清除幂等校验的key值
        IdempotentUtils.removeKey(dto.getId());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(List<Long> ids) {

        for (Long id : ids) {
            long count = taskService.createTaskQuery().processVariableValueEquals(WorkflowConstant.PROCESS_SCHEMA_ID_KEY, id).count();
            if (count > 0) {
                throw new MyException("有流程正在执行 无法删除！");
            }
        }
        QueryWrapper<WorkflowSchema> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .in(WorkflowSchema::getId, ids)
                .select(WorkflowSchema::getDeploymentId, WorkflowSchema::getDefinitionId);

        List<WorkflowSchema> workflowSchemas = list(queryWrapper);

        //获取到所有流程定义id
        List<String> definitionIds = workflowSchemas.stream().map(WorkflowSchema::getDefinitionId).collect(Collectors.toList());
        //获取到所有deployIds
        List<String> deploymentIds = workflowSchemas.stream().map(WorkflowSchema::getDeploymentId).collect(Collectors.toList());

        try {
            repositoryService.deleteProcessDefinitions().byIds(StrUtil.join(",", definitionIds)).delete();
        }catch (Exception e){
            if (e.getMessage().contains("since there exists")) {
                throw new MyException("有流程正在执行 无法删除！");//避免有外部任务正在执行的流程删除时，提示引擎删除错误。
            }
        }
        for (String deploymentId : deploymentIds) {
            repositoryService.deleteDeployment(deploymentId);
        }

        //缓存节点监听器数据
        CompletableFuture.runAsync(() -> {
            for (String deploymentId : deploymentIds) {
                WorkFlowUtil.removeNodeListener(deploymentId);
            }
        });

        return removeBatchByIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @SneakyThrows
    public boolean importSchema(MultipartFile multipartFile) {

        //用流读取文件
        BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(multipartFile.getInputStream()));
        String line;
        StringBuilder content = new StringBuilder();
        // 读取想定文件
        while ((line = bufferedReader.readLine()) != null) {
            content.append(line);
        }

        WorkflowSchemaConfig workflowSchemaConfig = JSONUtil.toBean(content.toString(), WorkflowSchemaConfig.class);

        LambdaQueryWrapper<WorkflowSchema> countQuery = Wrappers.lambdaQuery(WorkflowSchema.class).eq(WorkflowSchema::getName, workflowSchemaConfig.getProcessConfig().getName()).or().eq(WorkflowSchema::getCode, workflowSchemaConfig.getProcessConfig().getCode());
        if (count(countQuery) > 0) {
            throw new MyException("当前模板名称和模板编号重复！");
        }

        WorkflowSchema workflowSchema = BeanUtil.toBean(workflowSchemaConfig.getProcessConfig(), WorkflowSchema.class);

        Deployment deploy = repositoryService.createDeployment()
                .addInputStream(workflowSchemaConfig.getProcessConfig().getName() + StringPool.DOT + WorkflowConstant.WORKFLOW_SUFFIX, IoUtil.stringAsInputStream(workflowSchemaConfig.getProcessConfig().getXmlContent())).name(workflowSchemaConfig.getProcessConfig().getName()).deploy();

        //存储流程部署id
        workflowSchema.setDeploymentId(deploy.getId());

        //获取流程定义id
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().deploymentId(deploy.getId()).singleResult();
        //存储流程定义key
        workflowSchema.setDefinitionKey(processDefinition.getKey());

        //存储流程定义id
        workflowSchema.setDefinitionId(processDefinition.getId());

        //把配置json存储  (清除掉xml)
        workflowSchemaConfig.getProcessConfig().setXmlContent("");

        ObjectMapper mapper = new ObjectMapper();
        SimpleModule simpleModule = new SimpleModule();
        simpleModule.addSerializer(Long.class, ToStringSerializer.instance);
        simpleModule.addSerializer(Long.TYPE, ToStringSerializer.instance);
        mapper.registerModule(simpleModule);

        String jsonContent = mapper.writeValueAsString(workflowSchemaConfig);

        workflowSchema.setJsonContent(jsonContent);

        //使用雪花算法id作为主键
        workflowSchema.setId(IdUtil.getSnowflakeNextId());
        //设置第一版本号，作为查询标识
        workflowSchema.setFirstSchemaId(workflowSchema.getId());
        //设置版本号
        workflowSchema.setVersion(1);
        //活动版本
        workflowSchema.setActivityFlag(EnabledMark.ENABLED.getCode());

        save(workflowSchema);

        //将新增的数据 记录模板历史记录
        WorkflowSchemaHistory history = new WorkflowSchemaHistory();
        history.setSchemaId(workflowSchema.getId());
        history.setVersion(1);
        history.setActivityFlag(EnabledMark.ENABLED.getCode());
        history.setXmlContent(workflowSchema.getXmlContent());
        history.setJsonContent(workflowSchema.getJsonContent());

        //缓存节点监听器数据
        CompletableFuture.runAsync(() -> {
            WorkFlowUtil.cacheNodeListener(deploy.getId(), workflowSchemaConfig.getChildNodeConfig());
        });


        return workflowSchemaHistoryMapper.insert(history) > 0;
    }


}
