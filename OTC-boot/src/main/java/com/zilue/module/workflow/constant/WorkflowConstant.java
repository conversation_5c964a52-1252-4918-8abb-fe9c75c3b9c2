package com.zilue.module.workflow.constant;

/**
 * @Author: zilue
 * @Date: 2023/9/5 16:14
 */
public interface WorkflowConstant {
    /**
     * 类型（后缀）
     */
    String WORKFLOW_SUFFIX = "bpmn20.xml";

    /**
     * 流程参数 存储 key
     */
    String PROCESS_PARAM_KEY = "processParamKey";

    /**
     * 流程任务发起人idKey
     */
    String PROCESS_START_USER_ID_KEY = "startUserIdKey";

    /**
     * 流程任务发起人名称Key
     */
    String PROCESS_START_USER_NAME_KEY = "startUserNameKey";

    /**
     * 流程任务发起人发起是所选岗位Key
     */
    String PROCESS_START_USER_POST_ID_KEY = "startUserPostIdKey";


    /**
     * 流程名称 key
     */
    String PROCESS_NAME = "processNameKey";

    /**
     * 流程任务模板id key
     */
    String PROCESS_SCHEMA_ID_KEY = "processSchemaIdKey";

    /**
     * 流程任务模板名称 key
     */
    String PROCESS_SCHEMA_NAME_KEY = "processSchemaNameKey";

    /**
     * 流程 流水号 变量 key
     */
    String PROCESS_SERIAL_NUMBER_KEY = "serialNumberKey";

    /**
     * 流程 所有表单ID 以及 主键值 key
     * {templateId}:{formKey}
     */
    String PROCESS_FOMRID_KEYVALUE_KEY = "processFormIdAndKeyValueMapKey";

    /**
     * 关联任务存储key
     */
    String RELATION_TASK_KEY = "relationTask";

    /**
     * 流程表单数据参数 前缀 规则  form_{formId}_key
     */
    String PROCESS_FORMDATA_PREFIX_KEY = "form_";


    /**
     * 查询我的流程  是否移入回收站
     */
    String PROCESS_ISRECYCLE_FLAG_KEY = "processIsRecycle";

    /**
     * 流程表单数据参数 前缀 规则  flow:{flowId}:{nodeId}:{formId}:{formField}
     */
    String FLOW_PARAM_PREFIX_KEY = "flow:";


    /**
     * 流程任务审批人 变量key
     */
    String TASK_ASSIGNEE_VAR_KEY = "assignee";

    /**
     * 是否需要指定审批人
     */
    String TASK_IS_APPOINT_APPROVE = "isAppoint";

    /**
     * 流程任务 各节点审批人 历史记录
     * 数据类型 Map<String, List<Long>>
     *     {
     *         "nodeid":[userid1,userid2,userid3]
     *         "nodeid":[userid1,userid2,userid3]
     *     }
     */
    String TASK_ASSIGNEE_RECORD_VAR_KEY = ":assigneeOfNode";

    /**
     * 流程任务 各节点审批类型 历史记录
     */
    String TASK_APPROVE_TYPE_RECORD_VAR_KEY = ":approveType";

    /**
     * 流程任务传阅人 前缀 规则 circulated
     */
    String TASK_CIRCULATED_VAR_KEY = "circulated";

//    /**
//     * 流程任务 多实例用户节点 审批人 变量key
//     * 存储数据 是字符串 userid1,userid2,userid3
//     * 用于多实例根据列表数据生成多个用户任务  存几条数据 生成几条task 数据
//     */
//    String TASK_MULTI_ASSIGNEE_VAR_StRING_KEY = "assigneeListString";

    /**
     * 流程任务 多实例用户节点 审批人 参数变量 key  例 assigneeList_{nodeId}
     * 存储数据 [userid1,userid2,userid3]
     * 用于多实例根据列表数据生成多个用户任务  存几条数据 生成几条task 数据
     */
    String TASK_MULTI_ASSIGNEE_VAR_KEY = "assigneeList_";

    /**
     * 流程任务传阅人 前缀 规则 circulatedList_{nodeId}
     */
    String TASK_MULTI_CIRCULATED_VAR_KEY = "circulatedList_";


    /**
     * 各节点配置 统一id 的key 默认都使用id
     */
    String NODE_CONFIG_ID_KEY = "id";


    /**
     * 开始节点 id 默认
     */
    String START_NODE_DEFAULT_ID = "Event_start_node";

    /**
     * 流程线 id 前缀
     */
    String FLOW_ID_PREFIX_STRING = "Flow";


    /**
     * 流程任务模板配置 前缀:{schemaId}
     */
    String SCHEMA_CACHE_PREFIX = "schema:";


    /**
     * 多实例外部任务缓存前缀 前缀:{callActivity}
     */
    String CALLACTIVITY_CACHE_PREFIX = "callActivity:";

    /**
     * 外部任务 审批结果 关键词
     */
    String CALLACTIVITY_KEYWORD = "finish";

    /**
     * 外部任务 审批结果 后缀
     */
    String CALLACTIVITY_RESULT_SUF = "count";


    /**
     * 模板编号占位符
     */
    String TEMPLATE_CODE_PLACEHOLDER = "#{template_code}#";

    /**
     * 模板名称占位符
     */
    String TEMPLATE_NAME_PLACEHOLDER = "#{template_name}#";

    /**
     * 模板分类占位符
     */
    String TEMPLATE_CATEGORY_PLACEHOLDER = "#{template_category}#";

    /**
     * 模板备注占位符
     */
    String TEMPLATE_REMARK_PLACEHOLDER = "#{template_remark}#";


    /**
     * 流程任务id占位符
     */
    String TASK_ID_PLACEHOLDER = "#{task_id}#";

    /**
     * 发起人id占位符
     */
    String INITIATOR_ID_PLACEHOLDER = "#{initiator_id}#";

    /**
     * 发起人名称占位符
     */
    String INITIATOR_USER_NAME_PLACEHOLDER = "#{initiator_user_name}#";


    /**
     * 发起人编码占位符
     */
    String INITIATOR_CODE_PLACEHOLDER = "#{initiator_code}#";

    /**
     * 发起人手机号占位符
     */
    String INITIATOR_MOBILE_PLACEHOLDER = "#{initiator_mobile}#";

    /**
     * 发起人组织架构名称占位符
     */
    String INITIATOR_DEPT_NAME_PLACEHOLDER = "#{initiator_dept_name}#";

    /**
     * 发起人岗位名称占位符
     */
    String INITIATOR_POST_NAME_PLACEHOLDER = "#{initiator_post_name}#";

    /**
     * yyyy-MM-dd HH:mm:ss 24小时制
     */
    String YYYYMMDDHHMMSS_24_PLACEHOLDER = "#{yyyy-MM-dd HH:mm:ss}#";

    /**
     * yyyy-MM-dd HH:mm:ss 12小时制
     */
    String YYYYMMDDHHMMSS_12_PLACEHOLDER = "#{yyyy-MM-dd hh:mm:ss}#";

    /**
     * yyyy-MM-dd
     */
    String YYYYMMDD_PLACEHOLDER = "#{yyyy-MM-dd}#";

    /**
     * HH:mm:ss   24小时制
     */
    String HHMMSS_24_PLACEHOLDER = "#{HH:mm:ss}#";

    /**
     * hh:mm:ss   12小时制
     */
    String HHMMSS_12_PLACEHOLDER = "#{hh:mm:ss}#";

    /**
     * 2位随机数 占位符
     */
    String RANDOM_2_PLACEHOLDER = "#{random_2}#";

    /**
     * 2位随机数+字母 占位符
     */
    String RANDOM_2_MIX_PLACEHOLDER = "#{random_2_mix}#";

    /**
     * 4位随机数 占位符
     */
    String RANDOM_4_PLACEHOLDER = "#{random_4}#";

    /**
     * 4位随机数+字母 占位符
     */
    String RANDOM_4_MIX_PLACEHOLDER = "#{random_4_mix}#";

    /**
     * 6位随机数 占位符
     */
    String RANDOM_6_PLACEHOLDER = "#{random_6}#";

    /**
     * 6位随机数+字母 占位符
     */
    String RANDOM_6_MIX_PLACEHOLDER = "#{random_6_mix}#";

    /**
     * 8位随机数 占位符
     */
    String RANDOM_8_PLACEHOLDER = "#{random_8}#";

    /**
     * 8位随机数+字母 占位符
     */
    String RANDOM_8_MIX_PLACEHOLDER = "#{random_8_mix}#";

    /**
     *    全局流程流水号 占位符
     */
    String SERIAL_NUBMER_PLACEHOLDER = "#{serial_number}#";

    String RANDOM_DASH = "#{random_-}#";

    /**
     * 4位数 流水号 占位符
     */
    String SERIAL_NUBMER_FOUR_PLACEHOLDER = "#{serial_number_four}#";

    /**
     * 5位数 流水号 占位符
     */
    String SERIAL_NUBMER_FIVE_PLACEHOLDER = "#{serial_number_five}#";

    /**
     * 6位数 流水号 占位符
     */
    String SERIAL_NUBMER_SIX_PLACEHOLDER = "#{serial_number_six}#";

    /**
     * 7位数 流水号 占位符
     */
    String SERIAL_NUBMER_SEVEN_PLACEHOLDER = "#{serial_number_seven}#";

    /**
     * 8位数 流水号 占位符
     */
    String SERIAL_NUBMER_EIGHT_PLACEHOLDER = "#{serial_number_eight}#";


    /**
     * bpmn 开始节点 的类型名称
     */
    String BPMN_START_EVENT_TYPE_NAME = "startEvent";

    /**
     *  开始节点 的类型名称
     */
    String START_EVENT_TYPE_NAME = "开始节点";

    /**
     * bpmn 前端 用户任务节点 的类型名称
     */
    String BPMN_XML_START_EVENT_TYPE_NAME = "bpmn:StartEvent";


    /**
     * bpmn 后端 用户任务节点 的类型名称
     */
    String BPMN_USER_TASK_TYPE_NAME = "userTask";

    /**
     * bpmn 前端 用户任务节点 的类型名称
     */
    String BPMN_XML_USER_TASK_TYPE_NAME = "bpmn:UserTask";

    /**
     *  用户任务节点 的类型名称
     */
    String USER_TASK_TYPE_NAME = "用户任务";

    /**
     * bpmn 脚本任务节点 的类型名称
     */
    String BPMN_SCRIPT_TASK_TYPE_NAME = "scriptTask";


    /**
     * 用户任务节点 的类型名称
     */
    String SCRIPT_TASK_TYPE_NAME = "脚本任务";

    /**
     *  用户任务节点 的类型名称
     */
    String CALL_ACTIVITY_TYPE_NAME = "外部流程";

    /**
     * bpmn 脚本任务节点 的类型名称
     */
    String BPMN_CALL_ACTIVITY_TYPE_NAME = "callActivity";

    /**
     * bpmn xml 外部流程节点 的类型名称
     */
    String BPMN_XML_CALL_ACTIVITY_TYPE_NAME = "bpmn:CallActivity";


    /**
     *  子流程节点 的类型名称
     */
    String SUB_PROCESS_TYPE_NAME = "子流程";

    /**
     * bpmn 子流程 的类型名称
     */
    String BPMN_SUB_PROCESS_TYPE_NAME = "subProcess";

    /**
     * bpmn xml 子流程节点 的类型名称
     */
    String BPMN_XML_SUB_PROCESS_TYPE_NAME = "bpmn:SubProcess";

    /**
     * bpmn 结束节点 的类型名称 noneEndEvent
     */
    String BPMN_END_NODE_TYPE_NAME = "noneEndEvent";


    /**
     * bpmn 互斥网关（排他）节点 的类型名称
     */
    String BPMN_EXCLUSIVE_GATEWAY_TYPE_NAME = "exclusiveGateway";


    /**
     * 结束节点 的类型名称
     */
    String END_NODE_TYPE_NAME = "结束节点";

    /**
     * bpmn 从 Execution 取  Property 的中的  节点类型值 的key
     */
    String BPMN_EVENT_SOURCE_TYPE_KEY = "type";



    /**
     * 超时提醒 key 前缀  格式为  timeout:{taskId}:{hit}:{noticePolicyConfigString}
     */
    String TIMEOUT_REMID_KEY = "timeout:";

    /**
     * camunda 多实例 节点 会将activityid 加上multiInstanceBody 例如：  Activity_14k881u#multiInstanceBody
     */
    String MULTI_INSTANCE_STRING = "#multiInstanceBody";

    /**
     * 多实例设置审批人key 浅醉
     */
    String MULTI_ASSIGNEE_CACHE_PREFIX = "multiAssignee:";


    /**
     * camunda 多实例 节点存储 所有实例数 的key
     */
    String MULTI_INSTANCE_VAR_KEY = "nrOfInstances";

    /**
     * camunda 多实例 节点存储 所有已完成实例数 的key
     */
    String MULTI_COMPLETED_INSTANCE_VAR_KEY = "nrOfCompletedInstances";


    /**
     * camunda 多实例 节点存储 所有活动实例数 的key
     */
    String MULTI_ACTIVE_INSTANCE_VAR_KEY = "nrOfActiveInstances";


    /**
     * 专项菜单 待办任务列表 的专项菜单id （specialMenuId）
     */
    String SPECIAL_MENU_ID_PARAM_NAME = "id";

    /**
     * 专项菜单 开始时间查询key （工作流开始时间）
     */
    String SPECIAL_MENU_START_TIME_PARAM_NAME = "startTime";

    /**
     * 专项菜单 开始时间查询key （工作流开始时间）
     */
    String SPECIAL_MENU_END_TIME_PARAM_NAME = "endTime";

    /**
     * 专项菜单 流水号key （工作流流水号）
     */
    String SPECIAL_MENU_SERIAL_NUMBER_PARAM_NAME = "serialNumber";

    /**
     * 专项菜单 流程任务名key （任务名）
     */
    String SPECIAL_MENU_TASK_NAME_PARAM_NAME = "taskName";

    /**
     * 专项菜单 流程模板名key （任务名）
     */
    String SPECIAL_MENU_SCHEMA_NAME_PARAM_NAME = "schemaName";

    /**
     * 专项菜单 流程发起人key （发起人）
     */
    String SPECIAL_MENU_ORIGINATOR_PARAM_NAME = "originator";

    /**
     * 会签用户任务节点 点击按钮审批结果关键词
     */
    String BUTTON_APPROVE_VAR_FLAG = "button";

    /**
     * 会签用户任务节点 点击按钮同意总和
     */
    String BUTTON_AGREE_APPROVE_VAR_FLAG = "agreeCount";

    /**
     * 会签用户任务节点 点击按钮拒绝总和
     */
    String BUTTON_REJECT_APPROVE_VAR_FLAG = "rejectCount";


    /**
     * 会签结果
     */
    String BUTTON_APPROVE_RESULT_VAR_FLAG = "result";


    /**
     * 开始事件 缓存 前缀
     */
    String START_EVENT_CACHE_PRE = "startEvent:";

    /**
     * 结束 缓存 前缀
     */
    String END_EVENT_CACHE_PRE = "endEvent:";


    /**
     * 按钮事件
     */
    String BUTTON_EVENT_CACHE_PRE = "buttonEvent:";



}
