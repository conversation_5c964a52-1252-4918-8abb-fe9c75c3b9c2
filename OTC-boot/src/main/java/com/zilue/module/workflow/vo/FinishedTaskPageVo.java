package com.zilue.module.workflow.vo;

import lombok.Data;

import java.util.Date;

/**
 * @Author: zilue
 * @Date: 2023/10/31 15:16
 */
@Data
public class FinishedTaskPageVo {

    /**
     * 流水号
     */
    private Long id;
    /**
     * 流水号
     */
    private Long serialNumber;

    /**
     * 模板id
     */
    private Long schemaId;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 流程id
     */
    private String processId;

    private String processName;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 流程模板名称
     */
    private String schemaName;

    /**
     * 当前进度
     */
    private Integer currentProgress;

    /**
     * 发起人
     */
    private String originator;

    /**
     * 当前任务
     */
    private String currentTaskName;

    /**
     * 时间
     */
    private Date createTime;

    /**
     * 催办状态
     */
    private Integer urgeStatus;

    /**
     * 流程状态
     */
    private String status;

    /**
     * 当前任务耗时
     */
    private String consumingTime;

    /**
     * 摘要信息
     */
    private String summaryInfo;

}
