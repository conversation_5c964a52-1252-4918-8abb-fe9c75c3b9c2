package com.zilue.module.workflow.model;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/29 9:49
 */
@Data
public class ReceiverConfiguration {
    /**
     * 所选类型的 name
     */
    private String name;


    /**
     * 所选类型 的 id (用户 角色  岗位 指定节点审批人 )
     */
    private String id;

    /**
     * 通知人员类型
     */
    private Integer memberType;


    /**
     * 表单字段值
     */
    private String value;

    /**
     * 工作流使用表单字段配置
     */
    private FormFieldConfig formFieldConfig;
}
