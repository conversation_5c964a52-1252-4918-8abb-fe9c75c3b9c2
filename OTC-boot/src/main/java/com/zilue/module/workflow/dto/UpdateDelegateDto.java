package com.zilue.module.workflow.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class UpdateDelegateDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("被委托人")
    @NotNull(message = "必须选择被委托人")
    private String delegateUserIds;

    @ApiModelProperty("开始时间")
    @NotNull(message = "开始时间必须选择")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @ApiModelProperty("结束时间")
    @NotNull(message = "结束时间必须选择")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @ApiModelProperty("说明备注")
    private String remark;

    @ApiModelProperty("所委托模板ids")
    @NotNull(message = "必须选择所委托的工作流模板")
    private String schemaIds;
}
