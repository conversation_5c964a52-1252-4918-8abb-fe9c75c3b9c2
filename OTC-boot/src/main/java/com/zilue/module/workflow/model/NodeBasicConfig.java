package com.zilue.module.workflow.model;

import lombok.Data;

import java.util.List;

/**
 * 各个节点基础配置
 * @Author: zilue
 * @Date: 2023/10/13 15:10
 */
@Data
public class NodeBasicConfig {
    /**
     * 节点id 前端生成  开始节点 会以Start开头
     */
    private String id;

    /**
     * 节点类型
     */
    private String type;

    /**
     * 节点名称
     */
    private String name;

    /**
     * 备注
     */
    private String remark;

    /**
     * 节点开始事件
     */
    private List<NodeEventConfig> startEventConfigs;


    /**
     * 节点结束事件
     */
    private List<NodeEventConfig> endEventConfigs;

    /**
     * 同意事件配置
     */
    private List<NodeEventConfig> agreeEventConfigs;

    /**
     * 驳回事件配置
     */
    private List<NodeEventConfig> rejectEventConfigs;

    /**
     * 撤回事件配置
     */
    private List<NodeEventConfig> withdrawEventConfigs;


    /**
     * 超时事件配置
     */
    private List<NodeEventConfig> timeoutEventConfigs;
}
