package com.zilue.module.workflow.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * @Author: zilue
 * @Date: 2023/11/4 10:03
 */
@Data
public class SaveDraftDto {

    @ApiModelProperty("工作流模板id")
    private Long schemaId;

    @ApiModelProperty("流程任务id")
    private String taskId;

    @ApiModelProperty("数据id")
    private String dataId;

    @ApiModelProperty("表单数据")
    @NotNull(message = "表单数据不能为空")
    private Map<String, Map<String, Object>> formData;
}
