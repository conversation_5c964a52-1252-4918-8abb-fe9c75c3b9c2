package com.zilue.module.workflow.dto;

import com.zilue.common.page.PageInput;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Author: zilue
 * @Date: 2023/11/3 10:09
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RecycleProcessPageDto extends PageInput {
    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 发起人
     */
    private String initiator;

    /**
     * 流水号
     */
    private String serialNumber;
}
