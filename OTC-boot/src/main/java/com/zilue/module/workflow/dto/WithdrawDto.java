package com.zilue.module.workflow.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author: zilue
 * @Date: 2023/2/15 9:02
 */
@Data
public class WithdrawDto {

    @ApiModelProperty("活动Id")
    private String activityId;


    @ApiModelProperty("任务Id")
    @NotBlank(message = "任务id 不能为空！")
    private String processId;


}
