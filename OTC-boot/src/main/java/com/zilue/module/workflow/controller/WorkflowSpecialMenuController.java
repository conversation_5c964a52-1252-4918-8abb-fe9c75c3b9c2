package com.zilue.module.workflow.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.yulichang.toolkit.MPJWrappers;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.model.result.R;
import com.zilue.common.page.ConventPage;
import com.zilue.common.page.PageOutput;
import com.zilue.common.utils.VoToColumnUtil;
import com.zilue.module.system.entity.Menu;
import com.zilue.module.workflow.dto.*;
import com.zilue.module.workflow.entity.WorkflowSpecialMenu;
import com.zilue.module.workflow.service.IWorkflowSpecialMenuService;
import com.zilue.module.workflow.vo.SpecialMenuPageVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 专项菜单表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@RestController
@RequestMapping(GlobalConstant.WORKFLOW_MODULE_PREFIX + "/special-menu")
@Api(value = GlobalConstant.WORKFLOW_MODULE_PREFIX + "/special-menu", tags = "流程专项菜单接口")
@AllArgsConstructor
public class WorkflowSpecialMenuController {

    private final IWorkflowSpecialMenuService specialMenuService;

    @GetMapping("/page")
    @ApiOperation(value = "专项菜单（分页）")
    public R page(SpecialMenuPageDto dto){
        //因为多表关联 会有多个表都使用了id字段，  所以必须专门指定主表的Id
        IPage<SpecialMenuPageVo> page = specialMenuService.selectJoinListPage(ConventPage.getPage(dto), SpecialMenuPageVo.class,
                MPJWrappers.<WorkflowSpecialMenu>lambdaJoin()
                        .orderByDesc(WorkflowSpecialMenu::getId)
                        .like(StrUtil.isNotBlank(dto.getKeyword()), SpecialMenuPageVo::getName, dto.getKeyword())
                        .or()
                        .like(StrUtil.isNotBlank(dto.getKeyword()), SpecialMenuPageVo::getCode, dto.getKeyword())
                        .or()
                        .like(StrUtil.isNotBlank(dto.getKeyword()), SpecialMenuPageVo::getMenuCode, dto.getKeyword())
                        .or()
                        .like(StrUtil.isNotBlank(dto.getKeyword()), SpecialMenuPageVo::getMenuName, dto.getKeyword())
                        .select(WorkflowSpecialMenu::getId)
                        .select(WorkflowSpecialMenu.class, x -> VoToColumnUtil.fieldsToColumns(SpecialMenuPageVo.class).contains(x.getProperty()))
                        .selectAs(Menu::getName, SpecialMenuPageVo::getMenuName)
                        .selectAs(Menu::getCode, SpecialMenuPageVo::getMenuCode)
                        .leftJoin(Menu.class, Menu::getId, WorkflowSpecialMenu::getMenuId));

        PageOutput<SpecialMenuPageVo> pageOutput = ConventPage.getPageOutput(page);
        return R.ok(pageOutput);
    }

    @GetMapping("/info")
    @ApiOperation(value = "查询单个详情")
    public R info(@RequestParam Long id) {
        return R.ok(specialMenuService.info(id));
    }

    @PostMapping
    @ApiOperation(value = "新增专项菜单")
    public R add(@Valid @RequestBody AddSpecialMenuDto dto) {
        return R.ok(specialMenuService.add(dto));
    }

    @PutMapping
    @ApiOperation(value = "修改专项菜单")
    public R update(@Valid @RequestBody UpdateSpecialMenuDto dto) {
        return R.ok(specialMenuService.update(dto));
    }

    @DeleteMapping
    @ApiOperation(value = "删除专项菜单(可批量)")
    public R delete(@RequestBody List<Long> ids) {
        return R.ok(specialMenuService.delete(ids));
    }

    @GetMapping("/pending")
    @ApiOperation(value = "待办任务")
    public R pending(Map<String,Object> param) {
        return R.ok(specialMenuService.pending(param));
    }
}
