package com.zilue.module.workflow.model;

import lombok.Data;

/**
 * @Author: zilue
 * @Date: 2023/12/15 11:06
 */
@Data
public class ApiRequestParamsConfig {

    /**
     * 唯一标识
     */
    private String id;

    /**
     * 名称
     */
    private String name;

    /**
     * 参数类型 0 value 1 流程数据
     */
    private String dataType;

    /**
     * 赋值类型 value |  processParameter | originator | formData
     */
    private String assignmentType;

    /**
     * 值
     */
    private String value;

    /**
     * 默认值
     */
    private String defaultValue;

    /**
     * 赋值配置
     */
    private String config;


}
