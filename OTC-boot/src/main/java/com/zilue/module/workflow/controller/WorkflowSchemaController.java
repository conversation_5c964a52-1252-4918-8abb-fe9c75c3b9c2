package com.zilue.module.workflow.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.enums.EnabledMark;
import com.zilue.common.model.result.R;
import com.zilue.module.workflow.dto.*;
import com.zilue.module.workflow.entity.WorkflowSchema;
import com.zilue.module.workflow.model.WorkflowSchemaConfig;
import com.zilue.module.workflow.service.IWorkflowSchemaService;
import com.zilue.module.workflow.vo.WorkflowSchemaInfoVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 流程模板表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-04
 */
@RestController
@RequestMapping(GlobalConstant.WORKFLOW_MODULE_PREFIX + "/schema")
@Api(value = GlobalConstant.WORKFLOW_MODULE_PREFIX + "/schema", tags = "流程模板")
@AllArgsConstructor
public class WorkflowSchemaController {

    private final IWorkflowSchemaService workflowSchemaService;


    @GetMapping("/page")
    @ApiOperation(value = "流程列表（分页）")
    public R page(@Valid WorkflowSchemaPageDto dto) {
        return R.ok(workflowSchemaService.getPage(dto));
    }


    @PostMapping
    @ApiOperation(value = "新增流程模板")
    public R add(@Valid @RequestBody AddWorkflowSchemaDto dto) {
        return R.ok(workflowSchemaService.add(dto));
    }

    @PostMapping("/draft")
    @ApiOperation(value = "新增流程模板草稿")
    public R draft(@Valid @RequestBody AddWorkflowSchemaDraftDto dto) {
        return R.ok(workflowSchemaService.addDraft(dto));
    }

    @GetMapping("/draft")
    @ApiOperation(value = "流程模板草稿")
    public R draftPage(@Valid WorkflowSchemaDraftPageDto dto) {
        return R.ok(workflowSchemaService.getDraftPage(dto));
    }


    @PutMapping
    @ApiOperation(value = "修改流程模板")
    public R update(@Valid @RequestBody UpdateWorkflowSchemaDto dto) {
        return R.ok(workflowSchemaService.update(dto));
    }


    @GetMapping("/info")
    @ApiOperation(value = "获取流程详细信息")
    public R info(@RequestParam Long id) {
        WorkflowSchema workflowSchema = workflowSchemaService.getById(id);
        if (workflowSchema == null) {
            return R.error("找不到此模板信息！");
        }
        return R.ok(BeanUtil.toBean(workflowSchema, WorkflowSchemaInfoVo.class));
    }

    @GetMapping("/multi/info")
    @ApiOperation(value = "获取批量流程详细信息")
    public R multiInfo(@RequestParam String ids) {
        List<String> idList = Arrays.stream(ids.split(StringPool.COMMA)).collect(Collectors.toList());

        List<WorkflowSchema> workflowSchemas = workflowSchemaService.listByIds(idList);

        return R.ok(BeanUtil.copyToList(workflowSchemas, WorkflowSchemaInfoVo.class));
    }


    @DeleteMapping
    @ApiOperation(value = "删除模板(可批量)")
    public R delete(@RequestBody List<Long> ids) {

        return R.ok(workflowSchemaService.delete(ids));
    }

    @PutMapping("/enabled")
    @ApiOperation(value = "流程启用")
    public R enabled(@Valid @RequestBody EnabledDto dto) {
        WorkflowSchema schema = new WorkflowSchema();
        schema.setId(dto.getSchemaId());
        schema.setEnabledMark(EnabledMark.ENABLED.getCode());
        return R.ok(workflowSchemaService.updateById(schema));
    }

    @PutMapping("/disabled")
    @ApiOperation(value = "流程禁用")
    public R disabled(@Valid @RequestBody EnabledDto dto) {
        WorkflowSchema schema = new WorkflowSchema();
        schema.setId(dto.getSchemaId());
        schema.setEnabledMark(EnabledMark.DISABLED.getCode());
        return R.ok(workflowSchemaService.updateById(schema));
    }

    @GetMapping("/export")
    @ApiOperation(value = "导出流程")
    @SneakyThrows
    public R export(@RequestParam Long id) {
        WorkflowSchema workflowSchema = workflowSchemaService.getById(id);
        if (workflowSchema == null) {
            return R.error("找不到此模板信息！");
        }
        WorkflowSchemaConfig workflowSchemaConfig = JSONUtil.toBean(workflowSchema.getJsonContent(), WorkflowSchemaConfig.class);
        workflowSchemaConfig.getProcessConfig().setXmlContent(workflowSchema.getXmlContent());

        return R.ok(JSONUtil.toJsonStr(workflowSchemaConfig));
    }

    @PostMapping("/import")
    @ApiOperation(value = "导入流程")
    @SneakyThrows
    public R importSchema(@RequestParam(value = "file") MultipartFile multipartFile) {
        return R.ok(workflowSchemaService.importSchema(multipartFile));
    }


}
