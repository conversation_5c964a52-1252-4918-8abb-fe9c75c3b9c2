package com.zilue.module.workflow.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 指定下一节点审批人参数
 * @Author: zilue
 * @Date: 2023/10/26 9:42
 */
@Data
public class ApproveUserDto {

    /**
     * 任务id
     */
    @NotBlank(message = "任务id不能为空")
    private String taskId;

    /**
     * 指定审批人id  可空  使用无对应处理人
     */
    private List<Long> approvedUsers;
}
