package com.zilue.module.workflow.vo;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @Author: zilue
 * @Date: 2023/12/28 14:44
 */
@Data
public class StartProcessInfoVo {

    /**
     * 所绑定的表单信息
     */
    private List<StartNodeFormInfoVo> formInfos;

    /**
     * 模板信息
     */
    private WorkflowSchemaInfoVo schemaInfo;

    /**
     * 所有的关联任务信息
     */
    private List<StartProcessRelationTaskVo> relationTasks;



    /**
     * 表单赋值 的formData
     */
    private Map<String,Map<String, Object>> formAssignmentData;

    /**
     * 流程图
     */
    private String workflowChat;

}
