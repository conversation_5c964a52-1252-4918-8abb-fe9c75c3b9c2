package com.zilue.module.workflow.vo;

import com.zilue.module.workflow.entity.WorkflowApproveRecord;
import com.zilue.module.workflow.model.OpinionConfig;
import lombok.Data;

import java.util.List;

/**
 * @Author: zilue
 * @Date: 2023/10/27 18:28
 */
@Data
public class TaskInfoVo {
    /**
     * 当前任务的表单信息
     */
    private List<RelationFormInfoVo> formInfos;

    /**
     * 模板信息
     */
    private WorkflowSchemaInfoVo schemaInfo;

    /**
     * 流程的流转记录
     */
    private List<ProcessRecordListVo> taskRecords;

    /**
     * 关联任务的信息
     */
    private List<TaskInfoRelationTaskVo> relationTaskInfo;

    /**
     * 是否可以加减签
     */
    private Boolean isAddOrSubSign;

    /**
     * 意见框配置
     */
    private OpinionConfig opinionConfig;

    /**
     * 用户任务审批意见
     */
    private List<WorkflowApproveRecord> taskApproveOpinions;
}
