package com.zilue.module.workflow.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.zilue.common.annotation.XjrLog;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.model.result.R;
import com.zilue.module.workflow.dto.*;
import com.zilue.module.workflow.service.IWorkflowExecuteService;
import com.zilue.module.workflow.dto.DraftPageDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 工作流操作接口
 *
 * @Author: zilue
 * @Date: 2023/9/8 14:15
 */
@RestController
@RequestMapping(GlobalConstant.WORKFLOW_MODULE_PREFIX + "/execute")
@Api(value = GlobalConstant.WORKFLOW_MODULE_PREFIX + "/execute", tags = "工作流操作接口")
@AllArgsConstructor
public class WorkflowExecuteController {

    private final IWorkflowExecuteService workflowExecuteService;

    @PostMapping("/deploy")
    @ApiOperation(value = "部署流程")
    public R deploy(@Valid @RequestBody DeployDto dto) {
        return R.ok(workflowExecuteService.deploy(dto));
    }

    @GetMapping("/preview")
    @ApiOperation(value = "预览流程")
    public R preview(@RequestParam Long schemaId) {
        return R.ok(workflowExecuteService.preview(schemaId));
    }

    @GetMapping("/start-process-info")
    @ApiOperation(value = "发起流程所需要的信息")
    public R startProcessInfo(@RequestParam Long schemaId) {
        return R.ok(workflowExecuteService.getStartProcessInfo(schemaId));
    }


    @GetMapping("/withdraw-process-info")
    @ApiOperation(value = "撤回流程所需要的信息")
    public R withdrawProcessInfo(@RequestParam String taskId,@RequestParam(required = false) String withdrawNodeId) {
        return R.ok(workflowExecuteService.getWithdrawProcessInfo(taskId,withdrawNodeId));
    }

    @GetMapping("/approve-process-info")
    @ApiOperation(value = "审批流程所需要的信息")
    public R approveProcessInfo(@RequestParam String taskId) {
        return R.ok(workflowExecuteService.getApproveProcessInfo(taskId));
    }


    @GetMapping("/get-approve-state")
    @ApiOperation(value = "获取当前task是不是在审批中")
    public R getApproveState(@RequestParam String taskId) {
        return R.ok(workflowExecuteService.getApproveState(taskId));
    }

    @GetMapping("/view-process-info")
    @ApiOperation(value = "查看流程所需要的信息（processId获取）")
    public R approveProcessInfoByProcessId(@RequestParam String processId) {
        return R.ok(workflowExecuteService.getApproveProcessInfoByProcessId(processId));
    }

    @GetMapping("/process/all-record")
    @ApiOperation(value = "查看流程所需要的所有流转信息（processId获取，包括父子流程）")
    public R allRecordInfoByProcessId(@RequestParam String processId,@RequestParam(required = false,defaultValue = "1") Integer onlySelf) {
        return R.ok(workflowExecuteService.getAllRecordInfoByProcessId(processId,onlySelf));
    }

    @GetMapping("/recycle-process-info")
    @ApiOperation(value = "获取回收站的数据流程数据")
    public R recycleProcessInfo(@RequestParam String processId) {
        return R.ok(workflowExecuteService.getRecycleProcessInfo(processId));
    }


    @PostMapping("/new-launch")
    @ApiOperation(value = "重构后发起流程")
    public R newLaunch(@Valid @RequestBody LaunchDto dto) {
        return R.ok(workflowExecuteService.newLaunch(dto));
    }

    @PostMapping("/relaunch")
    @ApiOperation(value = "重新发起")
    public R reLaunch(@Valid @RequestBody ReLaunchDto dto) {
        return R.ok(workflowExecuteService.reLaunch(dto));
    }


    @GetMapping("/pending")
    @ApiOperation(value = "待办任务")
    public R pending(@Valid PendingTaskPageDto dto) {
        return R.ok(workflowExecuteService.pending(dto));
    }

    @GetMapping("/task-info")
    @ApiOperation(value = "查询任务详情")
    public R taskInfo(@RequestParam String taskId) {
        return R.ok(workflowExecuteService.getTaskInfo(taskId));
    }

    @GetMapping("/process-info")
    @ApiOperation(value = "查询流程详情")
    public R processInfo(@RequestParam String processId) {
        return R.ok(workflowExecuteService.getProcessInfo(processId));
    }

    @GetMapping("/circulated-task-info")
    @ApiOperation(value = "查询传阅任务详情")
    public R circulatedTaskInfo(@RequestParam String taskId) {
        return R.ok(workflowExecuteService.getCirculatedTaskInfo(taskId));
    }


    @GetMapping("/process/finished-task")
    @ApiOperation(value = "查询当前流程已完成的任务")
    public R finishedTask(@RequestParam String processInstanceId){
        return R.ok(workflowExecuteService.getFinishedTask(processInstanceId));
    }

    @GetMapping("/process/record")
    @ApiOperation(value = "查询当前流程所有流转记录")
    public R processRecord(@RequestParam String processInstanceId){
        return R.ok(workflowExecuteService.getProcessRecord(processInstanceId));
    }


    @PostMapping("/new-approve")
    @ApiOperation(value = "重构后发审批")
    public R newApprove(@Valid @RequestBody ApproveDto dto) {
        return R.ok(workflowExecuteService.newApprove(dto));
    }


    @GetMapping("/approve/multi-info")
    @ApiOperation(value = "批量审批获取流程信息")
    public R approveMultiInfo(@Valid ApproveMultiInfoDto dto) {
        return R.ok(workflowExecuteService.approveMultiInfo(dto));
    }
    @PostMapping("/approve/multi")
    @ApiOperation(value = "批量审批")
    public R approveMulti(@Valid @RequestBody ApproveMultiDto dto) {
        return R.ok(workflowExecuteService.approveMulti(dto));
    }


    @PostMapping("/set-approve")
    @ApiOperation(value = "指定下一节点审批人(覆盖)")
    public R setApproveUser(@Valid @RequestBody ApproveUserDto dto) {
        return R.ok(workflowExecuteService.setApproveUser(dto));
    }

    @PostMapping("/set-approve-multi")
    @ApiOperation(value = "指定下一节点审批人(批量覆盖)")
    public R setApproveUserMulti(@Valid @RequestBody ApproveUserMultiDto dto) {
        return R.ok(workflowExecuteService.setApproveUserMulti(dto));
    }


    @GetMapping("/relation-task/page")
    @ApiOperation(value = "查询流程模板关联的任务分页")
    public R relationTaskPage(@Valid RelationTaskPageDto dto) {
        return R.ok(workflowExecuteService.getRelationTaskPage(dto));
    }

    @GetMapping("/relation-task/info")
    @ApiOperation(value = "查询流程模板关联的任务 详情")
    public R relationTaskPage(@Valid RelationTaskInfoDto dto) {
        return R.ok(workflowExecuteService.getRelationTaskInfo(dto));
    }



    @GetMapping("/circulated/page")
    @ApiOperation(value = "查询我的传阅")
    public R circulatedTaskPage(@Valid CirculatedTaskPageDto dto) {
        return R.ok(workflowExecuteService.getCirculatedTaskPage(dto));
    }

    @GetMapping("/finished/page")
    @ApiOperation(value = "查询我的已办任务分页")
    public R finishedTaskPage(@Valid FinishedTaskPageDto dto) {
        return R.ok(workflowExecuteService.getFinishedTaskPage(dto));
    }

    @GetMapping("/my-process/page")
    @ApiOperation(value = "查询我的流程")
    public R myProcessPage(@Valid MyProcessPageDto dto) {
        return R.ok(workflowExecuteService.getMyProcessPage(dto));
    }

    @PostMapping("/my-process/move-recycle")
    @ApiOperation(value = "我的流程 移入回收站")
    public R moveRecycle(@Valid @RequestBody MoveRecycleDto dto) {
        return R.ok(workflowExecuteService.moveRecycle(dto));
    }

    @GetMapping("/my-process/recycle/page")
    @ApiOperation(value = "回收站列表")
    public R recycleProcessPage(@Valid RecycleProcessPageDto dto) {
        return R.ok(workflowExecuteService.getRecycleProcessPage(dto));
    }

    @DeleteMapping("/my-process/recycle")
    @ApiOperation(value = "回收站 删除")
    public R recycleDelete(@Valid @RequestBody RecycleDeleteDto dto) {
        return R.ok(workflowExecuteService.recycleDelete(dto));
    }


    @PostMapping("/my-process/recycle/restart")
    @ApiOperation(value = "回收站 重新发起")
    public R restart(@Valid @RequestBody RestartDto dto) {
        return R.ok(workflowExecuteService.restart(dto));
    }


    @GetMapping("/my-task/history-task")
    @ApiOperation(value = "我的流程 已经完成的任务（不包括 外部流程  子流程  会签流程）")
    public R historyTask(@RequestParam String schemaId, @RequestParam String processInstanceId) {
        return R.ok(workflowExecuteService.getHistoryTask(schemaId, processInstanceId));
    }

    @PostMapping("/my-task/withdraw")
    @ApiOperation(value = "我的流程 撤回")
    public R withdraw(@Valid @RequestBody WithdrawDto dto) {
        return R.ok(workflowExecuteService.withdraw(dto));
    }


    @PostMapping("/draft")
    @ApiOperation(value = "保存草稿")
    public R addDraft(@Valid @RequestBody SaveDraftDto dto) throws JsonProcessingException {
        return R.ok(workflowExecuteService.saveDraft(dto));
    }


    @PutMapping("/draft")
    @ApiOperation(value = "修改草稿")
    public R updateDraft(@Valid @RequestBody UpdateDraftDto dto) {
        return R.ok(workflowExecuteService.updateDraft(dto));
    }

    @GetMapping("/draft/page")
    @ApiOperation(value = "查询草稿列表分页")
    public R draftPage(@Valid DraftPageDto dto) {
        return R.ok(workflowExecuteService.draftPage(dto));
    }

    @GetMapping("/draft/info")
    @ApiOperation(value = "查询草稿详情")
    public R draftInfo(@RequestParam Long id) {
        return R.ok(workflowExecuteService.draftData(id));
    }

    @DeleteMapping("/draft")
    @ApiOperation(value = "删除草稿")
    public R deleteDraft(@RequestBody List<Long> ids) {
        return R.ok(workflowExecuteService.deleteDraft(ids));
    }

    @GetMapping("/process-monitor/page")
    @ApiOperation(value = "流程监控分页列表")
    public R processMonitorPage(@Valid MonitorPageDto dto) {
        return R.ok(workflowExecuteService.getProcessMonitorPage(dto));
    }

    @DeleteMapping("/process-monitor/delete")
    @ApiOperation(value = "删除流程（流程监控使用）")
    public R deleteProcessMonitor(@RequestBody DeleteMonitorDto dto) {
        return R.ok(workflowExecuteService.deleteProcessMonitor(dto));
    }

    @PostMapping("/set-assignee")
    @ApiOperation(value = "指派审核人（给任务添加审批人）")
    public R setAssignee(@Valid @RequestBody SetAssigneeDto dto) {
        return R.ok(workflowExecuteService.setAssignee(dto));
    }

    @PostMapping("/set-suspended")
    @ApiOperation(value = "将流程挂起")
    public R setSuspended(@Valid @RequestBody SetSuspendedDto dto) {
        return R.ok(workflowExecuteService.setSuspended(dto));
    }

    @GetMapping("/approve-user")
    @ApiOperation(value = "获取节点审批人（获取下一节点审批人也是这个接口）")
    public R getAssignee(@Valid GetAssigneeDto dto){
        return R.ok(workflowExecuteService.getAssignee(dto));
    }

    @PostMapping("/set-sign")
    @ApiOperation(value = "加签/减签")
    public R addOrSubSign(@Valid @RequestBody AddOrSubSignDto dto) {
        return R.ok(workflowExecuteService.addOrSubSign(dto));
    }

    @GetMapping("/reject-node")
    @ApiOperation(value = "获取可以驳回的节点")
    public R rejectNode(@Valid RejectNodeDto dto){
        return R.ok(workflowExecuteService.getRejectNode(dto));
    }

    @PostMapping("/transfer")
    @ApiOperation(value = "转办")
    public R transfer(@Valid @RequestBody TransferDto dto){
        return R.ok(workflowExecuteService.transfer(dto));
    }


    @GetMapping("/process/form-finished-task")
    @ApiOperation(value = "根据formId  查询流程图 以及  当前流程已完成的任务")
    public R formFinishedTask(FormFinishedTaskDto dto){
        return R.ok(workflowExecuteService.getFormFinishedTask(dto));
    }


    @GetMapping("/count")
    @ApiOperation(value = "合计")
    public R count() {
        return R.ok(workflowExecuteService.getCount());
    }

    @PostMapping("/message-send")
    @ApiOperation(value = "消息推送接口")
    @XjrLog("发送了消息推送")
    public R messageSend(@Valid @RequestBody MessageSendDto dto){
        return R.ok(workflowExecuteService.messageSend(dto));
    }

    @GetMapping("/process/deal/record")
    @ApiOperation(value = "流程信息--流程处理信息")
    public R processDealRecord(@RequestParam String processInstanceId){
        return R.ok(workflowExecuteService.getProcessDealRecord(processInstanceId));
    }

    @PutMapping("/process-urge")
    @ApiOperation(value = "催办")
    @XjrLog(value = "催办")
    public R processUrge(@Valid @RequestBody TaskUrgeDto dto) {
        return R.ok(workflowExecuteService.processUrge(dto));
    }

    @PostMapping("/cancel-process")
    @ApiOperation(value = "流程终止")
    public R cancelProcess(@Valid @RequestBody SetSuspendedDto dto) {
        return R.ok(workflowExecuteService.cancelProcess(dto));
    }

    @PutMapping("/process-change")
    @ApiOperation(value = "流程监控-流程变更")
    @XjrLog(value = "流程监控-流程变更")
    public R processChange(@Valid @RequestBody ProcessChangeDto dto) {
        return R.ok(workflowExecuteService.processChange(dto));
    }

    @GetMapping("/all-process-node")
    @ApiOperation(value = "获取可以变更的节点")
    public R allProcessNode(@Valid RejectNodeDto dto){
        return R.ok(workflowExecuteService.getAllProcessNode(dto));
    }

    @PostMapping("/process-staging")
    @ApiOperation(value = "流程审批-暂存")
    public R processStaging(@Valid @RequestBody ApproveDto dto) {
        return R.ok(workflowExecuteService.processStaging(dto));
    }
}
