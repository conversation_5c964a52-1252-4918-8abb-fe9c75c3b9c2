package com.zilue.module.workflow.listener;

import lombok.AllArgsConstructor;
import org.camunda.bpm.engine.impl.bpmn.parser.BpmnParseListener;
import org.camunda.bpm.engine.impl.cfg.AbstractProcessEnginePlugin;
import org.camunda.bpm.engine.impl.cfg.ProcessEngineConfigurationImpl;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: zilue
 * @Date: 2023/10/11 14:27
 */
@Component
@AllArgsConstructor
public class CamundaGlobalPlugin extends AbstractProcessEnginePlugin {

    private final NewListener newListener;

    @Override
    public void preInit(ProcessEngineConfigurationImpl processEngineConfiguration) {
        List<BpmnParseListener> listeners = processEngineConfiguration.getCustomPreBPMNParseListeners();
        if(listeners == null){
            listeners = new ArrayList<>();
            processEngineConfiguration.setCustomPreBPMNParseListeners(listeners);
        }
//        listeners.add(new CamundaGlobalListener());
        listeners.add(newListener);

    }
}
