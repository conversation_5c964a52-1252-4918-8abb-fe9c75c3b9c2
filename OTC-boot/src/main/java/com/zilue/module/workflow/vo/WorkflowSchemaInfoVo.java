package com.zilue.module.workflow.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: zilue
 * @Date: 2023/9/21 15:05
 */
@Data
public class WorkflowSchemaInfoVo {
    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("流程编码")
    private String code;

    @ApiModelProperty("流程模板名称")
    private String name;

    @ApiModelProperty("流程分类")
    private Long category;

    @ApiModelProperty("部署ID")
    private String deploymentId;

    @ApiModelProperty("流程定义id")
    private String definitionId;

    @ApiModelProperty("是否在App上允许发起 1允许 2不允许")
    private Integer appShow;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("模板内容")
    private String xmlContent;

    @ApiModelProperty("模板Json")
    private String jsonContent;
}
