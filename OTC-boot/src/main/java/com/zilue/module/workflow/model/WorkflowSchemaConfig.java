package com.zilue.module.workflow.model;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 工作流模板配置
 * @Author: zilue
 * @Date: 2023/9/26 15:34
 */
@Data
public class WorkflowSchemaConfig {
    @NotNull(message = "流程配置不能为空")
    private ProcessConfig processConfig;

    @NotNull(message = "子节点配置不能为空")
    private List<Map<String, Object>> childNodeConfig;
}
