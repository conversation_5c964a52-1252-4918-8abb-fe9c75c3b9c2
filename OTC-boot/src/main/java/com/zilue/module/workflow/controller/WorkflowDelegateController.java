package com.zilue.module.workflow.controller;

import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.model.result.R;
import com.zilue.module.workflow.dto.AddDelegateDto;
import com.zilue.module.workflow.dto.DelegatePageDto;
import com.zilue.module.workflow.dto.UpdateDelegateDto;
import com.zilue.module.workflow.service.IWorkflowDelegateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 流程委托 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-05
 */
@RestController
@RequestMapping(GlobalConstant.WORKFLOW_MODULE_PREFIX + "/delegate")
@Api(value = GlobalConstant.WORKFLOW_MODULE_PREFIX + "/delegate", tags = "流程委托接口")
@AllArgsConstructor
public class WorkflowDelegateController {

    private final IWorkflowDelegateService workflowDelegateService;

    @GetMapping("/page")
    @ApiOperation(value = "流程委托（分页）")
    public R page(@Valid DelegatePageDto dto) {
        return R.ok(workflowDelegateService.page(dto));
    }

    @PostMapping
    @ApiOperation(value = "新增流程委托")
    public R add(@Valid @RequestBody AddDelegateDto dto){
        return R.ok(workflowDelegateService.add(dto));
    }

    @PutMapping
    @ApiOperation(value = "修改流程委托")
    public R update(@Valid @RequestBody UpdateDelegateDto dto){
        return R.ok(workflowDelegateService.update(dto));
    }


    @DeleteMapping
    @ApiOperation(value = "删除流程委托")
    public R delete(@Valid @RequestBody List<Long> ids){
        return R.ok(workflowDelegateService.delete(ids));
    }

    @GetMapping("/info")
    @ApiOperation(value = "获取流程委托信息")
    public R info(@RequestParam Long id){
        return R.ok(workflowDelegateService.getById(id));
    }
}
