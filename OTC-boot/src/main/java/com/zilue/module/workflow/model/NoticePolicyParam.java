package com.zilue.module.workflow.model;

import lombok.Data;

import java.util.List;

/**
 * @Author: zilue
 * @Date: 2023/4/19 18:46
 */
@Data
public class NoticePolicyParam {

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 用户名称
     */
    private String taskName;

    /**
     * 流程id
     */
    private String processId;

    /**
     * 模板id
     */
    private Long schemaId;

    /**
     * 模板名称
     */
    private String schemaName;

    /**
     * 发起人
     */
    private String startUserName;
    /**
     * 通知策略
     */
    private List<Integer> noticePolicyConfigs;
    /**
     * 需要通知的人员id
     */
    private List<Long> noticeUserIds;
}
