package com.zilue.module.workflow.service.impl;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.zilue.module.workflow.entity.WorkflowApproveRecord;
import com.zilue.module.workflow.mapper.WorkflowApproveRecordMapper;
import com.zilue.module.workflow.service.IWorkflowApproveRecordService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-10
 */
@Service
public class WorkflowApproveRecordServiceImpl extends MPJBaseServiceImpl<WorkflowApproveRecordMapper, WorkflowApproveRecord> implements IWorkflowApproveRecordService {

}
