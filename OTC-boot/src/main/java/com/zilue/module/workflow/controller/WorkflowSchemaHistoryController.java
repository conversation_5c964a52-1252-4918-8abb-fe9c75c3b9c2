package com.zilue.module.workflow.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.toolkit.MPJWrappers;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.exception.MyException;
import com.zilue.common.model.result.R;
import com.zilue.common.utils.VoToColumnUtil;
import com.zilue.module.organization.entity.User;
import com.zilue.module.workflow.dto.HistoryChangeDto;
import com.zilue.module.workflow.entity.WorkflowSchema;
import com.zilue.module.workflow.entity.WorkflowSchemaHistory;
import com.zilue.module.workflow.service.IWorkflowSchemaHistoryService;
import com.zilue.module.workflow.service.IWorkflowSchemaService;
import com.zilue.module.workflow.vo.SchemaHistoryListVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 流程模板历史记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-16
 */
@RestController
@RequestMapping(GlobalConstant.WORKFLOW_MODULE_PREFIX + "/schema-history")
@Api(value = GlobalConstant.WORKFLOW_MODULE_PREFIX + "/schema-history", tags = "流程模板历史记录接口")
@AllArgsConstructor
public class WorkflowSchemaHistoryController {

    private final IWorkflowSchemaHistoryService workflowSchemaHistoryService;

    private final IWorkflowSchemaService workflowSchemaService;


    @GetMapping("/list")
    @ApiOperation(value = "流程列表（分页）")
    public R list(@RequestParam Long schemaId) {
        WorkflowSchema workflowSchema = workflowSchemaService.getOne(Wrappers.<WorkflowSchema>lambdaQuery().eq(WorkflowSchema::getId, schemaId));
        if (workflowSchema == null){
            throw new MyException("找不到对应的模板数据");
        }
        //分为两种情况，一种是按原本的逻辑走，一种是按新逻辑走
        //不为空表示按新逻辑走，拿firstSchemaId去查询数据
        List<SchemaHistoryListVo> schemaHistoryListVos = workflowSchemaHistoryService.selectJoinList(SchemaHistoryListVo.class,
                MPJWrappers.<WorkflowSchemaHistory>lambdaJoin()
                        .eq(ObjectUtil.isNotNull(workflowSchema.getFirstSchemaId()), WorkflowSchemaHistory::getSchemaId, workflowSchema.getFirstSchemaId()) //新逻辑
                        .eq(!ObjectUtil.isNotNull(workflowSchema.getFirstSchemaId()), WorkflowSchemaHistory::getSchemaId, schemaId) //老逻辑
                        .select(WorkflowSchemaHistory::getId)
                        .select(WorkflowSchemaHistory.class, x -> VoToColumnUtil.fieldsToColumns(SchemaHistoryListVo.class).contains(x.getProperty()))
                        .selectAs(User::getName, SchemaHistoryListVo::getCreateUserName)
                        .leftJoin(User.class, User::getId, WorkflowSchemaHistory::getCreateUserId)
                        .orderByDesc(WorkflowSchemaHistory::getVersion)
        );

        List<SchemaHistoryListVo> listVos = BeanUtil.copyToList(schemaHistoryListVos, SchemaHistoryListVo.class);
        return R.ok(listVos);
    }

    @PutMapping("/set-current")
    @ApiOperation(value = "更新到此版本")
    public R change(@Valid @RequestBody HistoryChangeDto dto) {

        return R.ok(workflowSchemaHistoryService.change(dto));
    }

    @PutMapping("/processChanges")
    @ApiOperation(value = "新流程变更")
    public R processChanges(@Valid @RequestBody HistoryChangeDto dto) {
        return R.ok(workflowSchemaHistoryService.change(dto));
    }

    @GetMapping("/preview")
    @ApiOperation(value = "历史记录的xml")
    public R preview(@RequestParam String historyId){
        WorkflowSchemaHistory history = workflowSchemaHistoryService.getById(historyId);
        return R.ok(history.getXmlContent());
    }
}
