package com.zilue.module.workflow.model;

import lombok.Data;

import java.util.List;

/**
 * sql脚配置
 * @Author: zilue
 * @Date: 2023/9/23 10:17
 */
@Data
public class SqlConfig {
    /**
     * 是否启用
     */
    private Boolean enabled;


    /**
     * 数据库链接id
     */
    private String databaseId;

    /**
     * sql脚本内容
     */
    private String sqlContent;

    /**
     * 表单字段
     */
    private String formField;


    /**
     * 绑定字段
     */
    private String bindField;

    /**
     * sql参数配置
     */
    private List<String> sqlParamConfig;
}
