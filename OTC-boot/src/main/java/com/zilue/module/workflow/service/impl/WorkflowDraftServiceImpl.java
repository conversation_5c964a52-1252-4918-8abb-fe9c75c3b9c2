package com.zilue.module.workflow.service.impl;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.zilue.module.workflow.entity.WorkflowDraft;
import com.zilue.module.workflow.mapper.WorkflowDraftMapper;
import com.zilue.module.workflow.service.IWorkflowDraftService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Service
public class WorkflowDraftServiceImpl extends MPJBaseServiceImpl<WorkflowDraftMapper, WorkflowDraft> implements IWorkflowDraftService {

}
