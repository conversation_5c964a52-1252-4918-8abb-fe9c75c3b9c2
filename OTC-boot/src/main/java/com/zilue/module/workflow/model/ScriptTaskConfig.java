package com.zilue.module.workflow.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: zilue
 * @Date: 2023/9/23 10:03
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ScriptTaskConfig extends NodeBasicConfig  {

    /**
     * 是否记录流程信息
     */
    private Integer recordInfo;

    /**
     * 推送类型
     */
    private Integer pushType;

    /**
     * 脚本配置
     */
    private ScriptConfig script;

    /**
     * api配置
     */
    private Api api;


    /**
     * 参数操作
     */
    private AssignmentConfig assignmentConfig;

}
