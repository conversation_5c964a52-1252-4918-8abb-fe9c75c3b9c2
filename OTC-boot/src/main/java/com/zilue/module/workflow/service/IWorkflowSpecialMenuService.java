package com.zilue.module.workflow.service;

import com.github.yulichang.base.MPJBaseService;
import com.zilue.module.workflow.dto.AddSpecialMenuDto;
import com.zilue.module.workflow.dto.UpdateSpecialMenuDto;
import com.zilue.module.workflow.entity.WorkflowSpecialMenu;
import com.zilue.module.workflow.vo.SpecialMenuInfoVo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 专项菜单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
public interface IWorkflowSpecialMenuService extends MPJBaseService<WorkflowSpecialMenu> {

    /**
     * 新增专项菜单
     * @param dto
     * @return
     */
    boolean add(AddSpecialMenuDto dto);

    /**
     * 修改专项菜单
     * @param dto
     * @return
     */
    boolean update(UpdateSpecialMenuDto dto);

    /**
     * 删除专项菜单
     * @param ids
     * @return
     */
    boolean delete(List<Long> ids);

    /**
     * 获取详情
     * @param id
     * @return
     */
    SpecialMenuInfoVo info(Long id);

    /**
     * 专项菜单查询列表
     * @param param
     * @return
     */
    List<Map<String,Object>> pending(Map<String,Object> param);
}
