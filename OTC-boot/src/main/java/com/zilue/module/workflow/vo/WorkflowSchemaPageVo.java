package com.zilue.module.workflow.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: zilue
 * @Date: 2023/9/6 14:48
 */
@Data
public class WorkflowSchemaPageVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("流程编码")
    private String code;

    @ApiModelProperty("流程模板名称")
    private String name;

    @ApiModelProperty("流程分类")
    private Long category;

    @ApiModelProperty("流程分类")
    private String categoryName;

    @ApiModelProperty("流程定义id")
    private String definitionId;

    @ApiModelProperty("流程定义id")
    private String definitionKey;

    @ApiModelProperty("部署ID")
    private String deploymentId;

    @ApiModelProperty("是否在App上允许发起 1允许 2不允许")
    private Integer appShow;

    @ApiModelProperty("流程状态")
    private Integer enabledMark;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("是否为活动版本")
    private Integer activityFlag;

    @ApiModelProperty("第一个版本的模板id")
    private Long firstSchemaId;
}
