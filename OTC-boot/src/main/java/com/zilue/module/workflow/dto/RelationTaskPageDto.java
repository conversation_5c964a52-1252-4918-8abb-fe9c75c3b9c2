package com.zilue.module.workflow.dto;

import com.zilue.common.page.PageInput;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @Author: zilue
 * @Date: 2023/10/27 14:26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RelationTaskPageDto extends PageInput {

    /**
     * 模板id
     */
    @NotNull(message = "模板id 不能为空")
    private Long schemaId;

    /**
     * 关联模板id
     */
    @NotNull(message = "关联模板id 不能为空")
    private Long relationSchemaId;



    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

}
