package com.zilue.module.workflow.dto;

import com.zilue.module.workflow.model.ProcessConfig;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * @Author: zilue
 * @Date: 2023/9/8 10:15
 */
@Data
public class AddDesignDto {

    @NotBlank(message = "resourceId不能为空！")
    private String resourceId;

    @NotNull(message = "流程配置不能为空")
    private ProcessConfig processConfig;

    @NotNull(message = "子节点配置不能为空")
    private List<Map<String, Object>> childNodeConfig;
}
