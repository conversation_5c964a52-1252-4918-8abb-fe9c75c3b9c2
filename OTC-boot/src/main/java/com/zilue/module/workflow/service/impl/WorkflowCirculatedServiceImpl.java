package com.zilue.module.workflow.service.impl;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.zilue.module.workflow.entity.WorkflowCirculated;
import com.zilue.module.workflow.mapper.WorkflowCirculatedMapper;
import com.zilue.module.workflow.service.IWorkflowCirculatedService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 流程传阅信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-09
 */
@Service
public class WorkflowCirculatedServiceImpl extends MPJBaseServiceImpl<WorkflowCirculatedMapper, WorkflowCirculated> implements IWorkflowCirculatedService {

}
