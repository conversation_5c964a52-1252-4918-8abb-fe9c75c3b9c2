package com.zilue.module.workflow.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: zilue
 * @Date: 2023/1/29 16:35
 */
@Data
public class ApproveUserMultiDto {

    /**
     * 模板id
     */
    @NotBlank(message = "模板id 不能为空！")
    private String schemaId;

    /**
     * 所有需要设置的审批人信息
     */
    @NotNull(message = "审批人不能为空！")
    private List<ApproveUserDto> approveUserList;
}
