package com.zilue.module.workflow.vo;

import com.zilue.module.workflow.model.FormConfig;
import lombok.Data;

import java.util.Map;

/**
 * @Author: zilue
 * @Date: 2023/1/30 14:53
 */
@Data
public class UserTaskFormInfoVo {
    /**
     * 表单类型  0 系统表单 1 自定义表单
     */
    private Integer formType;

    /**
     * 如果是自定义表单 返回formJson  用于渲染表单
     */
    private String formJson;

    /**
     * form的表单配置
     */
    private FormConfig formConfig;

    /**
     * 表单数据
     */
    private Map<String,Object> formData;

    /**
     * 功能模块名称
     */
    private String functionalModule;

    /**
     * 功能名称
     */
    private String functionName;

    /**
     * Form页面名称
     */
    private String functionFormName;
}
