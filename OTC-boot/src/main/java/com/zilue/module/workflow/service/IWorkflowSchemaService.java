package com.zilue.module.workflow.service;

import com.github.yulichang.base.MPJBaseService;
import com.zilue.common.page.PageOutput;
import com.zilue.module.workflow.dto.*;
import com.zilue.module.workflow.entity.WorkflowSchema;
import com.zilue.module.workflow.vo.WorkflowSchemaDraftPageVo;
import com.zilue.module.workflow.vo.WorkflowSchemaPageVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 流程模板表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-04
 */
public interface IWorkflowSchemaService extends MPJBaseService<WorkflowSchema> {

    /**
     * 新增流程模板草稿
     * @param dto
     * @return
     */
    PageOutput<WorkflowSchemaPageVo>  getPage(WorkflowSchemaPageDto dto);
    /**
     * 新增流程模板设计
     * @param dto
     * @return
     */
    boolean add(AddWorkflowSchemaDto dto);

    /**
     * 新增流程模板草稿
     * @param dto
     * @return
     */
    boolean addDraft(AddWorkflowSchemaDraftDto dto);


    /**
     * 新增流程模板草稿
     * @param dto
     * @return
     */
    PageOutput<WorkflowSchemaDraftPageVo> getDraftPage(WorkflowSchemaDraftPageDto dto);


    /**
     * 修改流程模板设计
     * @param dto
     * @return
     */
    boolean update(UpdateWorkflowSchemaDto dto);


    /**
     * 删除流程模板设计
     * @param ids
     * @return
     */
    boolean delete(List<Long> ids);


    /**
     * 删除流程模板设计
     * @param multipartFile
     * @return
     */
    boolean importSchema(MultipartFile multipartFile);


}
