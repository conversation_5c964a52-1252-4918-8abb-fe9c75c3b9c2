package com.zilue.module.workflow.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 工作流 流转记录信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-10
 */
@TableName("xjr_workflow_record")
@ApiModel(value = "流转记录对象", description = "工作流 流转记录信息")
@Data
public class WorkflowRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("节点id")
    private String nodeId;

    @ApiModelProperty("节点类型")
    private String nodeType;

    @ApiModelProperty("节点名称")
    private String nodeName;

    @ApiModelProperty("节点多实例类型（节点审批类型）")
    private Integer nodeMultiType;

    @ApiModelProperty("模板id")
    private Long schemaId;

    @ApiModelProperty("流程id")
    private String processId;

    @ApiModelProperty("审批信息")
    private String message;

    @ApiModelProperty("记录时间")
    private LocalDateTime recordTime;

    @ApiModelProperty("传阅信息")
    private String circulateMessage;

    @TableField(fill = FieldFill.INSERT)
    private Long createUserId;

}
