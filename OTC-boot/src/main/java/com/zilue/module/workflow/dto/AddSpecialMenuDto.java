package com.zilue.module.workflow.dto;

import com.zilue.module.system.dto.AddMenuDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author: zilue
 * @Date: 2023/3/20 10:16
 */
@Data
public class AddSpecialMenuDto {

    @ApiModelProperty("功能名称")
    @NotBlank(message = "功能名称不能为空！")
    @Length(min = 1, max = 50, message = "功能名称不能超过50个字符！")
    private String name;

    @ApiModelProperty("功能编码")
    @NotBlank(message = "功能编码不能为空！")
    @Length(min = 1, max = 50, message = "功能编码不能超过50个字符！")
    private String code;

    @ApiModelProperty("菜单配置")
    @NotNull(message = "菜单配置不能为空！")
    @Valid
    private AddMenuDto menuInfo;

    @ApiModelProperty("模板配置 ")
    @NotBlank(message = "模板配置不能为空！")
    private String schemaId;

    @ApiModelProperty("模板权限类型 0 所有 1 指定")
    private Integer schemaAuthType;

    @ApiModelProperty("专项菜单使用人 如果 schema_auth_type == 1 才会有")
    private String schemaAuthUserId;

    @ApiModelProperty("字段权限 配置 0 所有 1指定")
    private Integer fieldAuthType;

    @ApiModelProperty("专项菜单字段权限 指定成员 如果 field_auth_type == 1 才会有")
    private String fieldAuthUserId;

    @ApiModelProperty("表单字段配置")
    private String fieldConfig;

    @ApiModelProperty("查询配置")
    private String queryConfig;
}
