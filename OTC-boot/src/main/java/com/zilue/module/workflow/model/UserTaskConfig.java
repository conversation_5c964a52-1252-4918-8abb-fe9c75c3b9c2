package com.zilue.module.workflow.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Author: zilue
 * @Date: 2023/9/22 16:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserTaskConfig extends NodeBasicConfig {

    /**
     * 自动同意规则  如果此处配置  无法选择下一节点审批人
     */
    private List<Integer> autoAgreeRule;

    /**
     *   是否由上一届点审批人指定下一节点审批人 如果此处配置  无法选择下一节点审批人
     */
    private Integer isPrevChooseNext;

    /**
     * 无对应处理人 多实例无法使用
     */
    private Integer noHandler;

    /**
     * 当前进度
     */
    private Integer currentProgress;

    /**
     * 是否可以配置临时宙批人
     */
    private Boolean provisionalApprover;

    /**
     * 审批人配置
     */
    private List<MemberConfig> approverConfigs;

    /**
     * 传阅人配置
     */
    private List<MemberConfig> circulateConfigs;


    /**
     * 表单配置
     */
    private List<FormConfig> formConfigs;

    /**
     * 会签配置
     */
    private CountersignConfig countersignConfig;

    /**
     * 按钮配置
     */
    private List<ButtonConfig> buttonConfigs;


    /**
     * 意见框配置
     */
    private OpinionConfig opinionConfig;

    /**
     * 参数操作
     */
    private AssignmentConfig assignmentConfig;

    /**
     * 通知策略
     */
    private List<Integer> noticePolicyConfigs;

    private Boolean allowUpload;
    /**
     * 用户节点消息推送配置，借用事件监听的配置，这里只使用type和messageConfig参数
     */
    private List<NodeEventConfig> noticeMessageConfigs;


}
