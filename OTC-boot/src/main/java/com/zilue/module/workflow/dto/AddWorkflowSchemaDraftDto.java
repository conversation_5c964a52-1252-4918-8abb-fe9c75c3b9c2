package com.zilue.module.workflow.dto;

import com.zilue.module.workflow.model.ProcessConfig;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @Author: zilue
 * @Date: 2023/11/3 16:13
 */
@Data
public class AddWorkflowSchemaDraftDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "流程配置不能为空")
    private ProcessConfig processConfig;

    @NotNull(message = "子节点配置不能为空")
    private List<Map<String, Object>> childNodeConfig;
}
