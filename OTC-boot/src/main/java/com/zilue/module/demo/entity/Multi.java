package com.zilue.module.demo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.github.yulichang.annotation.EntityMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: zilue
 * @Date: 2023/4/26 14:57
 */
@Data
@TableName("xjr_multi")
@ApiModel(value = "multi对象", description = "")
public class Multi {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty("字符串")
    private String fieldString;

    @ApiModelProperty("整型")
    private String fieldInt;

    @ApiModelProperty("时间")
    private LocalDateTime fieldDatetime;

    @ApiModelProperty("浮点")
    private Double fieldDouble;

    @ApiModelProperty("长整型")
    private Long fieldLong;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("排序")
    private Integer sortCode;

    /**
     * 查询下级 一对多
     */
    @TableField(exist = false)
    @EntityMapping(thisField = "id", joinField = "parentId")
    private List<MultiChild> childList;
}
