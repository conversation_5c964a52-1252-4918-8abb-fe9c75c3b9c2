package com.zilue.module.demo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: zilue
 * @Date: 2023/4/26 14:50
 */
@Data
public class MultiListDto {
    @ApiModelProperty("字符串")
    private String fieldString;

    @ApiModelProperty("整型")
    private String fieldInt;

    @ApiModelProperty("开始时间")
    private LocalDateTime fieldDatetimeStart;

    @ApiModelProperty("结束时间")
    private LocalDateTime fieldDatetimeEnd;

    @ApiModelProperty("浮点")
    private Double fieldDouble;

    @ApiModelProperty("长整型")
    private Long fieldLong;

    @ApiModelProperty("备注")
    private String remark;
}
