package com.zilue.module.demo.vo;

import com.zilue.module.demo.entity.MultiChild;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: zilue
 * @Date: 2023/4/26 15:03
 */
@Data
public class MultiVo {

    @ApiModelProperty("字符串")
    private String fieldString;

    @ApiModelProperty("整型")
    private String fieldInt;

    @ApiModelProperty("时间")
    private LocalDateTime fieldDatetime;

    @ApiModelProperty("浮点")
    private Double fieldDouble;

    @ApiModelProperty("长整型")
    private Long fieldLong;

    @ApiModelProperty("备注")
    private String remark;


    @ApiModelProperty("multi子表")
    private List<MultiChild> childList;
}
