package com.zilue.module.demo.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.model.result.R;
import com.zilue.common.page.ConventPage;
import com.zilue.common.page.PageOutput;
import com.zilue.common.utils.VoToColumnUtil;
import com.zilue.module.demo.dto.AddMultiDto;
import com.zilue.module.demo.dto.MultiListDto;
import com.zilue.module.demo.dto.MultiPageDto;
import com.zilue.module.demo.dto.UpdateMultiDto;
import com.zilue.module.demo.entity.Multi;
import com.zilue.module.demo.service.IMultiService;
import com.zilue.module.demo.vo.MultiPageVo;
import com.zilue.module.demo.vo.MultiVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @Author: zilue
 * @Date: 2023/4/26 14:48
 */

@RestController
@RequestMapping(GlobalConstant.SYSTEM_MODULE_PREFIX + "/multi")
@Api(value = GlobalConstant.SYSTEM_MODULE_PREFIX + "/multi",tags = "代码生产案例（多表）代码")
@AllArgsConstructor
public class MultiController {

    private final IMultiService multiService;

    @GetMapping(value = "/list")
    @ApiOperation(value="Multi列表(不分页)")
    public R list(@Valid MultiListDto dto){
        List<Multi> list = multiService.listDeep(Wrappers.lambdaQuery(Multi.class)
                .like(StrUtil.isNotBlank(dto.getFieldString()),Multi::getFieldString,dto.getFieldString())
                .eq(ObjectUtil.isNotNull(dto.getFieldInt()),Multi::getFieldInt,dto.getFieldInt())
                .eq(ObjectUtil.isNotNull(dto.getFieldLong()),Multi::getFieldLong,dto.getFieldLong())
                .eq(ObjectUtil.isNotNull(dto.getFieldDouble()),Multi::getFieldDouble,dto.getFieldDouble())
                .between(ObjectUtil.isNotNull(dto.getFieldDatetimeStart()) && ObjectUtil.isNotNull(dto.getFieldDatetimeEnd()),Multi::getFieldDatetime,dto.getFieldDatetimeStart(),dto.getFieldDatetimeEnd())
                .select(Multi.class, x -> VoToColumnUtil.fieldsToColumns(MultiPageVo.class).contains(x.getProperty())));

        List<MultiPageVo> multiListVos = BeanUtil.copyToList(list, MultiPageVo.class);
        return R.ok(multiListVos);
    }

    @GetMapping(value = "/page")
    @ApiOperation(value="Multi列表(分页)")
    public R page(@Valid MultiPageDto dto){

        LambdaQueryWrapper<Multi> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(dto.getFieldString()),Multi::getFieldString,dto.getFieldString())
                .eq(ObjectUtil.isNotNull(dto.getFieldInt()),Multi::getFieldInt,dto.getFieldInt())
                .eq(ObjectUtil.isNotNull(dto.getFieldLong()),Multi::getFieldLong,dto.getFieldLong())
                .eq(ObjectUtil.isNotNull(dto.getFieldDouble()),Multi::getFieldDouble,dto.getFieldDouble())
                .between(ObjectUtil.isNotNull(dto.getFieldDatetimeStart()) && ObjectUtil.isNotNull(dto.getFieldDatetimeEnd()),Multi::getFieldDatetime,dto.getFieldDatetimeStart(),dto.getFieldDatetimeEnd())
                .select(Multi.class,x -> VoToColumnUtil.fieldsToColumns(MultiPageVo.class).contains(x.getProperty()));
        IPage<Multi> page = multiService.pageDeep(ConventPage.getPage(dto), queryWrapper);
        PageOutput<MultiPageVo> pageOutput = ConventPage.getPageOutput(page, MultiPageVo.class);
        return R.ok(pageOutput);
    }

    @GetMapping(value = "/info")
    @ApiOperation(value="根据id查询multi信息")
    public R info(@RequestParam Long id){
        Multi multi = multiService.getByIdDeep(id);
        if (multi == null) {
            return R.error("找不到此数据！");
        }
        return R.ok(BeanUtil.toBean(multi, MultiVo.class));
    }

    @PostMapping
    @ApiOperation(value = "新增multi")
    public R add(@Valid @RequestBody AddMultiDto dto){
        Multi multi = BeanUtil.toBean(dto, Multi.class);
        return R.ok(multiService.add(multi));
    }

    @PutMapping
    @ApiOperation(value = "修改multi")
    public R update(@Valid @RequestBody UpdateMultiDto dto){

        Multi multi = BeanUtil.toBean(dto, Multi.class);
        return R.ok(multiService.update(multi));
    }

    @DeleteMapping
    @ApiOperation(value = "删除")
    public R delete(@Valid @RequestBody List<Long> ids){
        return R.ok(multiService.delete(ids));
    }


}
