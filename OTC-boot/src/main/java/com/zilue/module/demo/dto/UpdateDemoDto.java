package com.zilue.module.demo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @title: UpdateDemoDto
 * <AUTHOR>
 * @Date: 2023/4/17 0:10
 * @Version 1.0
 */
@Data
public class UpdateDemoDto {

    private Long id;

    @ApiModelProperty("字符串")
    private String fieldString;

    @ApiModelProperty("整型")
    private String fieldInt;

    @ApiModelProperty("时间")
    private LocalDateTime fieldDatetime;

    @ApiModelProperty("浮点")
    private Double fieldDouble;

    @ApiModelProperty("长整型")
    private Long fieldLong;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("排序")
    private Integer sortCode;
}
