package com.zilue.module.demo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zilue.common.annotation.Trans;
import com.zilue.common.enums.TransType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @title: DemoPageVo
 * <AUTHOR>
 * @Date: 2023/4/16 23:27
 * @Version 1.0
 */
@Data
public class DemoPageVo {

    private String id;

    @ApiModelProperty("字符串")
    @Trans(type = TransType.DIC)
    private String fieldString;

    @ApiModelProperty("整型")
    private String fieldInt;

    @ApiModelProperty("时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime fieldDatetime;

    @ApiModelProperty("浮点")
    private Double fieldDouble;

    @ApiModelProperty("长整型")
    private Long fieldLong;

    @ApiModelProperty("备注")
    @Trans(type = TransType.DIC)
    private String remark;


}
