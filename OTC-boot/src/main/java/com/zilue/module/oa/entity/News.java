package com.zilue.module.oa.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zilue.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 新闻中心表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-16
 */
@TableName("xjr_oa_news")
@ApiModel(value = "OaNews对象", description = "新闻中心表")
@Data
@EqualsAndHashCode(callSuper = false)
public class News extends AuditEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("新闻主键")
    private Long id;

    @ApiModelProperty("类型（1-新闻2-公告）")
    private Integer typeId;

    @ApiModelProperty("所属类别主键")
    private Long categoryId;

    @ApiModelProperty("所属类别")
    private String category;

    @ApiModelProperty("完整标题")
    private String fullHead;

    @ApiModelProperty("标题颜色")
    private String fullHeadColor;

    @ApiModelProperty("简略标题")
    private String briefHead;

    @ApiModelProperty("作者")
    private String authorName;

    @ApiModelProperty("编辑")
    private String compileName;

    @ApiModelProperty("Tag词")
    private String tagWord;

    @ApiModelProperty("关键字")
    private String keyword;

    @ApiModelProperty("来源")
    private String sourceName;

    @ApiModelProperty("来源地址")
    private String sourceAddress;

    @ApiModelProperty("新闻内容")
    private String newsContent;

    @ApiModelProperty("浏览量")
    private Integer pv;

    @ApiModelProperty("发布时间")
    private LocalDateTime releaseTime;

    @ApiModelProperty("排序码")
    private Integer sortCode;

    @ApiModelProperty("备注")
    private String remark;

}
