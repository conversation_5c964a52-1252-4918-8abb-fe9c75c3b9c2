package com.zilue.module.oa.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: zilue
 * @Date: 2023/3/8 14:35
 */
@Data
public class ScheduleVo {
    @ApiModelProperty("日程名称")
    private String scheduleName;

    @ApiModelProperty("日程内容")
    private String scheduleContent;

    @ApiModelProperty("日程分类")
    private String category;

    @ApiModelProperty("开始日期")
    private LocalDateTime startDate;

    @ApiModelProperty("开始时间")
    private String startTime;

    @ApiModelProperty("结束日期")
    private LocalDateTime endDate;

    @ApiModelProperty("结束时间")
    private String endTime;

    @ApiModelProperty("提前提醒")
    private Integer early;

    @ApiModelProperty("邮件提醒")
    private Integer isMailAlert;

    @ApiModelProperty("手机提醒")
    private Integer isMobileAlert;

    @ApiModelProperty("微信提醒")
    private Integer isWechatAlert;

    @ApiModelProperty("日程状态")
    private Integer scheduleState;

    @ApiModelProperty("排序码")
    private Integer sortCode;

    @ApiModelProperty("备注")
    private String description;
}
