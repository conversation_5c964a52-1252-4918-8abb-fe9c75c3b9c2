package com.zilue.module.oa.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: zilue
 * @Date: 2023/3/8 14:34
 */
@Data
public class NewsVo {

    @ApiModelProperty("所属类别主键")
    private Long categoryId;

    @ApiModelProperty("所属类别")
    private String category;

    @ApiModelProperty("完整标题")
    private String fullHead;

    @ApiModelProperty("标题颜色")
    private String fullHeadColor;

    @ApiModelProperty("简略标题")
    private String briefHead;

    @ApiModelProperty("作者")
    private String authorName;

    @ApiModelProperty("编辑")
    private String compileName;

    @ApiModelProperty("Tag词")
    private String tagWord;

    @ApiModelProperty("关键字")
    private String keyword;

    @ApiModelProperty("来源")
    private String sourceName;

    @ApiModelProperty("来源地址")
    private String sourceAddress;

    @ApiModelProperty("新闻内容")
    private String newsContent;

    @ApiModelProperty("浏览量")
    private Integer pv;

    @ApiModelProperty("发布时间")
    private LocalDateTime releaseTime;

    @ApiModelProperty("排序码")
    private Integer sortCode;

    @ApiModelProperty("备注")
    private String remark;

}
