package com.zilue.module.oa.service;

import com.zilue.module.oa.entity.Schedule;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 日程管理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-16
 */
public interface IScheduleService extends IService<Schedule> {

    /**
     * 删除日程管理 以及 消息推送
     *
     * @param ids
     * @return
     */
    boolean delete(List<Long> ids);

}
