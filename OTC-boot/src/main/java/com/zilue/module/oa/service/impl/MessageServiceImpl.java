package com.zilue.module.oa.service.impl;

import com.zilue.module.oa.entity.Message;
import com.zilue.module.oa.mapper.MessageMapper;
import com.zilue.module.oa.service.IMessageService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 消息状态 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-16
 */
@Service
@AllArgsConstructor
public class MessageServiceImpl extends ServiceImpl<MessageMapper, Message> implements IMessageService {

}
