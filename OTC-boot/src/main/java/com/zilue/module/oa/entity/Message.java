package com.zilue.module.oa.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 消息状态
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-16
 */
@TableName("xjr_message")
@ApiModel(value = "消息", description = "消息")
@Data
@EqualsAndHashCode(callSuper = false)
public class Message implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("消息主键")
    private Long id;

    @ApiModelProperty("消息类型")
    private Integer messageType;

    @ApiModelProperty("接收消息用户id")
    private Long userId;

    @ApiModelProperty("是否已读")
    private Integer isRead;

    @ApiModelProperty("发布时间")
    private LocalDateTime sendTime;

    @ApiModelProperty("已读时间")
    private LocalDateTime readTime;

    @ApiModelProperty("消息内容")
    private String messageContent;

    @ApiModelProperty("对象id  日程id 或者 taskId  ")
    private String objectId;

    @ApiModelProperty("processId  ")
    private String processId;

    @ApiModelProperty("schemaId  ")
    private Long schemaId;

}
