package com.zilue.module.oa.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class AddNewsDto {

    private static final long serialVersionUID = 1L;

    /**
     * 类型（1-新闻2-公告）
     */
    @ApiModelProperty("类型（1-新闻2-公告）")
    @NotNull(message = "类型（1-新闻2-公告）！")
    @Min(value = 1, message = "类型必须大于0")
    private Integer typeId;

    /**
     * 父级主键
     */
    @ApiModelProperty("父级主键")
    private Long categoryId;

    /**
     * 所属类别
     */
    @ApiModelProperty("所属类别")
    private String category;

    /**
     * 完整标题
     */
    @ApiModelProperty("完整标题")
    @Length(min = 1, max = 250, message = "完整标题最多250个字符！")
    private String fullHead;

    /**
     * 标题颜色
     */
    @ApiModelProperty("标题颜色")
    @Length(min = 1, max = 50, message = "标题颜色最多50个字符！")
    private String fullHeadColor;

    /**
     * 简略标题
     */
    @ApiModelProperty("简略标题")
    @Length(min = 1, max = 50, message = "简略标题最多50个字符！")
    private String briefHead;

    /**
     * 作者
     */
    @ApiModelProperty("作者")
    @Length(min = 1, max = 50, message = "作者最多50个字符！")
    private String authorName;

    /**
     * 编辑
     */
    @ApiModelProperty("编辑")
    @Length(min = 1, max = 50, message = "编辑最多50个字符！")
    private String compileName;

    /**
     * Tag词
     */
    @ApiModelProperty("Tag词")
    @Length(min = 1, max = 255, message = "Tag词最多255个字符！")
    private String tagWord;

    /**
     * 关键字
     */
    @ApiModelProperty("关键字")
    @Length(min = 1, max = 250, message = "关键字最多250个字符！")
    private String keyword;

    /**
     * 来源
     */
    @ApiModelProperty("来源")
    @Length(min = 1, max = 250, message = "来源最多250个字符！")
    private String sourceName;

    /**
     * 来源地址
     */
    @ApiModelProperty("来源地址")
    @Length(min = 1, max = 250, message = "来源地址最多250个字符！")
    private String sourceAddress;

    /**
     * 新闻内容
     */
    @ApiModelProperty("新闻内容")
    private String newsContent;

    /**
     * 发布时间
     */
    @ApiModelProperty("发布时间")
    private LocalDateTime releaseTime;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;
}
