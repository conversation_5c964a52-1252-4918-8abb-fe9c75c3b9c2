package com.zilue.module.oa.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class ScheduleListVo {

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 日程标题
     */
    @ApiModelProperty("日程标题")
    private String title;

    /**
     * 是否全天
     */
    @ApiModelProperty("是否全天")
    private Boolean allDay;

    @ApiModelProperty("日程名称")
    private String scheduleName;

    @ApiModelProperty("日程内容")
    private String scheduleContent;

    /**
     * 开始日期
     */
    @ApiModelProperty("开始日期")
    private LocalDateTime start;

    /**
     * 结束日期
     */
    @ApiModelProperty("结束日期")
    private LocalDateTime end;

}
