package com.zilue.module.oa.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zilue.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 日程管理
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-16
 */
@TableName("xjr_oa_schedule")
@ApiModel(value = "OaSchedule对象", description = "日程管理")
@Data
@EqualsAndHashCode(callSuper = false)
public class Schedule extends AuditEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("日程主键")
    private Long id;

    @ApiModelProperty("日程名称")
    private String scheduleName;

    @ApiModelProperty("日程内容")
    private String scheduleContent;

//    @ApiModelProperty("日程分类")
//    private String category;

    @ApiModelProperty("开始日期")
    private LocalDateTime startDate;

    @ApiModelProperty("开始时间")
    private String startTime;

    @ApiModelProperty("结束日期")
    private LocalDateTime endDate;

    @ApiModelProperty("结束时间")
    private String endTime;

    @ApiModelProperty("提前提醒")
    private Integer early;

    @ApiModelProperty("邮件提醒")
    private Integer isMailAlert;

    @ApiModelProperty("手机提醒")
    private Integer isMobileAlert;

    @ApiModelProperty("微信提醒")
    private Integer isWechatAlert;

    @ApiModelProperty("日程状态(0-未读，1-已读)")
    private Integer scheduleState;

    @ApiModelProperty("排序码")
    private Integer sortCode;

    @ApiModelProperty("备注")
    private String description;

}
