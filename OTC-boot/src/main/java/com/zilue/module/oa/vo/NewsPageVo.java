package com.zilue.module.oa.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: zilue
 * @Date: 2023/6/27 15:37
 */
@Data
public class NewsPageVo {
    /**
     * 新闻主键
     */
    @ApiModelProperty("新闻主键")
    private Long id;

    /**
     * 类型（1-新闻2-公告）
     */
    @ApiModelProperty("类型（1-新闻2-公告）")
    private Integer typeId;

    /**
     * 父级主键
     */
    @ApiModelProperty("父级主键")
    private Long categoryId;

    /**
     * 所属类别
     */
    @ApiModelProperty("所属类别")
    private String category;

    /**
     * 所属类别
     */
    @ApiModelProperty("所属类别名称")
    private String categoryName;

    /**
     * 完整标题
     */
    @ApiModelProperty("完整标题")
    private String fullHead;

    /**
     * 标题颜色
     */
    @ApiModelProperty("标题颜色")
    private String fullHeadColor;

    /**
     * 简略标题
     */
    @ApiModelProperty("简略标题")
    private String briefHead;

    /**
     * 作者
     */
    @ApiModelProperty("作者")
    private String authorName;

    /**
     * 编辑
     */
    @ApiModelProperty("编辑")
    private String compileName;

    /**
     * Tag词
     */
    @ApiModelProperty("Tag词")
    private String tagWord;

    /**
     * 关键字
     */
    @ApiModelProperty("关键字")
    private String keyword;

    /**
     * 来源
     */
    @ApiModelProperty("来源")
    private String sourceName;

    /**
     * 来源地址
     */
    @ApiModelProperty("来源地址")
    private String sourceAddress;

    /**
     * 新闻内容
     */
    @ApiModelProperty("新闻内容")
    private String newsContent;

    /**
     * 发布时间
     */
    @ApiModelProperty("发布时间")
    private LocalDateTime releaseTime;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String description;

    /**
     * 是否有效
     */
    @ApiModelProperty("是否有效")
    private Integer enabledMark;

    /**
     * 已读数据id，该字段有值说明已读
     */
    @ApiModelProperty("已读数据id，该字段有值说明已读")
    private Long readId;

    @ApiModelProperty("已读未读")
    private Boolean isRead;
}
