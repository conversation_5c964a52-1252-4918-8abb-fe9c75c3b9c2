package com.zilue.module.wechat.lineplan.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalTime;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.util.List;



/**
* @title: 路线规划模块
* <AUTHOR>
* @Date: 2024-12-27
* @Version 1.0
*/
@Data
public class AddOtcSalesmanLinePlanDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 客户ID
    */
    @ApiModelProperty("客户ID")
    private Long purMerchantId;
    /**
    * 业务员ID
    */
    @ApiModelProperty("业务员ID")
    private Long salesmanId;
    /**
    * 纬度

    */
    @ApiModelProperty("纬度 ")
    private BigDecimal latitude;
    /**
    * 经度

    */
    @ApiModelProperty("经度 ")
    private BigDecimal longitude;
    /**
    * 路线节点名称
    */
    @ApiModelProperty("路线节点名称")
    private String lineName;
    /**
    * 拜访的顺序
    */
    @ApiModelProperty("拜访的顺序")
    private Integer planOrder;
    /**
    * 业务日期
    */
    @ApiModelProperty("业务日期")
    private String planDay;
    /**
    * 注册地址
    */
    @ApiModelProperty("注册地址")
    private String registerAddress;

}
