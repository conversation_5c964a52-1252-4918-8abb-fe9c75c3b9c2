package com.zilue.module.wechat.sign.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zilue.common.page.PageOutput;
import com.zilue.module.business.punchset.entity.OtcPunchSet;
import com.zilue.module.business.sign.vo.OtcSignInAdminPageVo;
import com.zilue.module.wechat.sign.dto.AddOtcSignInDto;
import com.zilue.module.wechat.sign.dto.CoachedOtcSignInDto;
import com.zilue.module.wechat.sign.dto.OtcSignInPageDto;
import com.zilue.module.wechat.sign.dto.QueryOtcSignInDto;
import com.zilue.module.wechat.sign.entity.OtcSignIn;
import com.zilue.module.wechat.sign.vo.CheckOtcSignVo;
import com.zilue.module.wechat.sign.vo.OtcSignPurMernchantVo;
import com.zilue.module.wechat.sign.vo.OtcSimpleSignPurMernchantVo;
import io.swagger.models.auth.In;

import java.util.List;

/**
 * @title: service
 * <AUTHOR>
 * @Date: 2024-12-25
 * @Version 1.0
 */
public interface ISignService extends IService<OtcSignIn> {
    List<OtcSignPurMernchantVo> listByDate(QueryOtcSignInDto queryOtcSignInDto);

    PageOutput<OtcSignPurMernchantVo> coachedpage(CoachedOtcSignInDto dto);

    CheckOtcSignVo checkOtcSignIn(AddOtcSignInDto addOtcSignInDto, OtcPunchSet otcPunchSet);

    OtcSimpleSignPurMernchantVo getByCurrentTime(QueryOtcSignInDto queryOtcSignInDto);

    OtcSignIn saveSignIn(AddOtcSignInDto addOtcSignInDto, OtcPunchSet otcPunchSet);

    boolean saveCoachedEvaluation(AddOtcSignInDto addOtcSignInDto);

    PageOutput<OtcSignInAdminPageVo> getPageList(OtcSignInPageDto pageDto);

    Integer modifySignHistoryData();


}
