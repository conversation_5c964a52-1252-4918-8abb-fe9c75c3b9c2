package com.zilue.module.wechat.sign.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalTime;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.util.List;

/**
* @title: 表单出参
* <AUTHOR>
* @Date: 2024-12-25
* @Version 1.0
*/
@Data
public class OtcSignInVo {

    /**
    * 
    */
    @ApiModelProperty("")
    private Long id;
    /**
    * 打卡人id
    */
    @ApiModelProperty("打卡人id")
    private Long personId;
    /**
    * erp编码
    */
    @ApiModelProperty("erp编码")
    private String personCode;
    /**
    * 打卡人姓名
    */
    @ApiModelProperty("打卡人姓名")
    private String personName;
    /**
    * 打卡类型(1=签到 2=签退)
    */
    @ApiModelProperty("打卡类型(1=签到 2=签退)")
    private Integer siginType;
    /**
    * 客户id
    */
    @ApiModelProperty("客户id")
    private Long purMerchantId;
    /**
    * 客户姓名
    */
    @ApiModelProperty("客户姓名")
    private String purMerchantName;
    /**
    * 目的地(客户地址)
    */
    @ApiModelProperty("目的地(客户地址)")
    private String destination;
    /**
    * 客户纬度

    */
    @ApiModelProperty("客户纬度")
    private BigDecimal purMerchantLatitude;
    /**
    * 客户经度

    */
    @ApiModelProperty("客户经度 ")
    private BigDecimal purMerchantLongitude;
    /**
    * 打卡纬度
    */
    @ApiModelProperty("打卡纬度")
    private BigDecimal latitude;
    /**
    * 打卡经度
    */
    @ApiModelProperty("打卡经度")
    private BigDecimal longitude;
    /**
    * 打卡地址
    */
    @ApiModelProperty("打卡地址")
    private String address;
    /**
    * 打卡偏差
    */
    @ApiModelProperty("打卡偏差")
    private Integer offset;
    /**
    * 照片路径(英文逗号分隔,)
    */
    @ApiModelProperty("照片路径(英文逗号分隔,)")
    private String imageIds;
    /**
    * SignExceptionStatusEnum：
#是否为异常记录（异常：2,正常：1,默认：1）
    */
    @ApiModelProperty("SignExceptionStatusEnum：#是否为异常记录（异常：2,正常：1,默认：1）")
    private Integer exceptionStatus;
    /**
    * 异常原因(0=正常 1=超范围打卡 2=未打卡 3=迟到打卡 4=早退)
    */
    @ApiModelProperty("异常原因(0=正常 1=超范围打卡 2=未打卡 3=迟到打卡 4=早退)")
    private Integer exceptionReason;
    /**
    * 序列号(同一组签到、签退记录的序列号相同)
    */
    @ApiModelProperty("序列号(同一组签到、签退记录的序列号相同)")
    private String serialNumber;
    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remarks;
    /**
    * 超范围打卡补充说明
    */
    @ApiModelProperty("超范围打卡补充说明")
    private String superableScopeRemarks;
    /**
    * 打卡类别，1门店拜访，2连锁分部拜访,3连锁总部拜访
    */
    @ApiModelProperty("打卡类别，1门店拜访，2连锁分部拜访,3连锁总部拜访")
    private Integer category;
    /**
    * 协访类型：0无协访，1跟下属一起协防
    */
    @ApiModelProperty("协访类型：0无协访，1跟下属一起协防")
    private Integer coachedType;
    /**
    * 协访人id
    */
    @ApiModelProperty("协访人id")
    private String coachedPersonId;
    /**
    * 协访人姓名
    */
    @ApiModelProperty("协访人姓名")
    private String coachedPersonName;
    /**
    * 在店时间，单位是秒
    */
    @ApiModelProperty("在店时间，单位是秒")
    private Long durationTime;
    /**
    * 协访者编码
    */
    @ApiModelProperty("协访者编码")
    private String coachedErpCode;
    /**
     * 访前计划
     */
    @ApiModelProperty("访前计划")
    private double preVisitPlan;
    /**
     * 工作态度
     */
    @ApiModelProperty("工作态度")
    private double workAttitude;
    /**
     * 客情管理
     */
    @ApiModelProperty("客情管理")
    private double cusRelationshipManagement;
    /**
     * 陈述利益
     */
    @ApiModelProperty("陈述利益")
    private double statementInterests;
    /**
     * 客户满意度
     */
    @ApiModelProperty("综合评价")
    private double evaluate;
    /**
     * 综合评价
     */
    @ApiModelProperty("协防建议")
    private String suggestion;

    @ApiModelProperty("签到次数")
    private Long signCount;
    /**
     * 创建日期
     */
    @ApiModelProperty("创建日期")
    private LocalDateTime createDate;
}
