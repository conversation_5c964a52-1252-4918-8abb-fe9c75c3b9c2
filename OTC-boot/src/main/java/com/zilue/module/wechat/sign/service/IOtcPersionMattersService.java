package com.zilue.module.wechat.sign.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.yulichang.base.MPJBaseService;
import com.zilue.module.wechat.sign.dto.OtcPersionMattersDto;
import com.zilue.module.wechat.sign.entity.OtcPersionMatters;
import com.zilue.module.wechat.sign.vo.OtcPersionMattersVo;

import java.util.List;

public interface IOtcPersionMattersService extends MPJBaseService<OtcPersionMatters> {

    void saveOtcPersionMatters(OtcPersionMattersDto dto);
    List<OtcPersionMattersVo> getPersionMattersList(String serialNumber);
}
