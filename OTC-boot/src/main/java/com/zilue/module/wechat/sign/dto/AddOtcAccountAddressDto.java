package com.zilue.module.wechat.sign.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
@Data
public class AddOtcAccountAddressDto {
    /**
     * 客户ID
     */
    @ApiModelProperty("客户ID")
    @NotNull(message="客户id不能为空")
    private Long accountId;
    /**
     * 业务员ID
     */
    @ApiModelProperty("纬度")
    @NotBlank(message="纬度不能为空")
    private String latitude;
    /**
     * 纬度

     */
    @ApiModelProperty("经度")
    @NotBlank(message="经度不能为空")
    private String longitude;
    /**
     * 经度

     */
    @ApiModelProperty("注册地址")
    @NotBlank(message="注册地址不能为空")
    private String registerAddress;
    /**
     * 路线节点名称
     */
    @ApiModelProperty("说明")
    private String remark;
    /**
     * 拜访的顺序
     */
    @ApiModelProperty("图片ids")
    @NotBlank(message="图片ids不能为空")
    private String imageIds;

    @ApiModelProperty("当前时间时间日期格式为 yyyy-mm-dd")
    @NotBlank(message="图片ids不能为空")
    private String currentTime;
}
