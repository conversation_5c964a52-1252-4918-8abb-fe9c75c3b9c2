package com.zilue.module.wechat.sign.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class OtcWeekReportDetailVo {
    @ApiModelProperty("员工id")
    private String userId;

    @ApiModelProperty("员工code")
    private String systemUserId;

    @ApiModelProperty("机构code")
    private String businessUnitId;

    @ApiModelProperty("岗位code")
    private String postCode;

    @ApiModelProperty("开始时间")
    private String startDay;

    @ApiModelProperty("结束时间")
    private String endDay;

    @ApiModelProperty("周报内容")
    private String comment;

    @ApiModelProperty("员工姓名")
    private String userName;

    @ApiModelProperty("岗位名称")
    private String postName;

    @ApiModelProperty("机构名称")
    private String departmentName;

    @ApiModelProperty("完成任务情况")
    private List<OtcWeekReportTaskDetailVo> OtcWeekReportTaskDetailVos;
}
