package com.zilue.module.wechat.sign.dto;

import com.zilue.common.page.PageInput;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @title: 查询当天的打卡记录
 * <AUTHOR>
 * @Date: 2024-12-25
 * @Version 1.0
 */
@Data
public class QueryOtcSignInDto extends PageInput {
    /**
     * 签到日期
     */
    @ApiModelProperty("签到日期")
    @NotNull(message="签到日期不能为空 yyyy-mm-dd")
    private String createDate;
    /**
     * 打卡人id
     */
    @ApiModelProperty("打卡人id")
    @NotNull(message="打卡人id不能为空")
    private Long personId;
    /**
     * 客户id
     */
    @ApiModelProperty("客户id")
    private Long purMerchantId;
    @ApiModelProperty("客户名称")
    private String name;
    @ApiModelProperty("本人经度")
    private BigDecimal longitude;
    @ApiModelProperty("本人纬度")
    private BigDecimal latitude;
    @ApiModelProperty("协访类型：0无协访，1跟下属一起协防")
    private Integer coachedType;

    @ApiModelProperty("序列号(同一组签到、签退记录的序列号相同)")
    private String serialNumber;


}
