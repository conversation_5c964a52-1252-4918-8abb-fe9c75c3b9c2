package com.zilue.module.wechat.sign.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zilue.common.model.result.R;
import com.zilue.common.page.PageOutput;
import com.zilue.module.wechat.sign.dto.OtcAccountEmployeeDto;
import com.zilue.module.wechat.sign.entity.OtcAccountEmployee;
import com.zilue.module.wechat.sign.entity.OtcSignIn;
import com.zilue.module.wechat.sign.vo.OtcAccountEmployeeVo;
import com.zilue.module.wechat.sign.vo.OtcAccountStatisticsEmployeeVo;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotBlank;
import java.util.List;

public interface IOtcAccountEmployeeService extends IService<OtcAccountEmployee> {
    List<OtcAccountEmployeeVo> list(OtcAccountEmployeeDto dto);

    Integer otcAccountEmployeeAdd(OtcAccountEmployeeDto dto);

    Integer otcAccountEmployeeEdit(OtcAccountEmployeeDto dto);

    /**
     * 店员管理查询
     */

    PageOutput<OtcAccountEmployeeVo> getPageList(OtcAccountEmployeeDto pageDto);
    //查询人员统计信息 （手机端）
    OtcAccountStatisticsEmployeeVo getOtcAccountStatisticsEmployee(String serialNumber);
    //查询个人信息详情
    OtcAccountEmployeeVo getOtcAccountEmployee(String id);

    Integer deleteotcAccountEmployee(String serialNumber, Long id);

    List<OtcAccountEmployeeVo> getOtcAccountStatisticsEmployeeList(@NotBlank(message = "序列号不能为空") String serialNumber);
}
