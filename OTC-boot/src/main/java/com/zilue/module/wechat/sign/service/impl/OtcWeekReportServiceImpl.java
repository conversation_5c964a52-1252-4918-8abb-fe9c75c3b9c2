package com.zilue.module.wechat.sign.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zilue.common.enums.*;
import com.zilue.common.page.ConventPage;
import com.zilue.common.page.PageOutput;
import com.zilue.common.utils.DateUtils;
import com.zilue.common.utils.StringUtil;
import com.zilue.common.utils.VoToColumnUtil;
import com.zilue.module.business.account.entity.OtcAccount;
import com.zilue.module.business.salesreport.entity.OtcSalesReportMonth;
import com.zilue.module.business.taskManagement.entity.OtcSalesmanTask;
import com.zilue.module.business.taskManagement.service.ITaskManagementService;
import com.zilue.module.business.taskManagement.vo.OtcSalesmanTaskVo;
import com.zilue.module.organization.dto.UserRoleInfoDto;
import com.zilue.module.organization.entity.Department;
import com.zilue.module.organization.entity.User;
import com.zilue.module.organization.entity.UserDeptRelation;
import com.zilue.module.organization.mapper.UserPostRelationMapper;
import com.zilue.module.organization.service.IDepartmentService;
import com.zilue.module.organization.service.IUserDeptRelationService;
import com.zilue.module.organization.service.IUserService;
import com.zilue.module.organization.service.impl.PostServiceImpl;
import com.zilue.module.organization.vo.DepartmentListVo;
import com.zilue.module.wechat.sign.dto.*;
import com.zilue.module.wechat.sign.entity.*;
import com.zilue.module.wechat.sign.mapper.OtcSignInMapper;
import com.zilue.module.wechat.sign.mapper.OtcWeekReportMapper;
import com.zilue.module.wechat.sign.service.IOtcPersionMattersService;
import com.zilue.module.wechat.sign.service.IOtcWeekReportService;
import com.zilue.module.wechat.sign.service.IOtcWeekReportTaskService;
import com.zilue.module.wechat.sign.service.ISignService;
import com.zilue.module.wechat.sign.vo.OtcWeekReportDetailVo;
import com.zilue.module.wechat.sign.vo.OtcWeekReportPageVo;
import com.zilue.module.wechat.sign.vo.OtcWeekReportTaskDetailVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class OtcWeekReportServiceImpl extends ServiceImpl<OtcWeekReportMapper, OtcWeekReport> implements IOtcWeekReportService {
    private final IOtcWeekReportTaskService otcWeekReportTaskService;
    private final OtcSignInMapper otcSignInMapper;
    private final ISignService signService;
    private final UserPostRelationMapper userPostRelationMapper;
    private final ITaskManagementService taskManagementService;
    private final IUserService userService;
    private final IUserDeptRelationService userDeptRelationService;
    private final IDepartmentService departmentService;
    private final PostServiceImpl postServiceImpl;
    private final IOtcPersionMattersService otcPersionMattersService;

    /**
     * 新增周报
     *
     * @param addOtcWeekReportDto
     */
    @Override
    public void saveOtcWeekReport(AddOtcWeekReportDto addOtcWeekReportDto) {
        String userCode = addOtcWeekReportDto.getSystemUserId();
        String startDay = addOtcWeekReportDto.getStartDay();
        String endDay = addOtcWeekReportDto.getEndDay();
        String userId = addOtcWeekReportDto.getUserId();
        List<OtcWeekReport> otcWeekReports = this.list(Wrappers.<OtcWeekReport>query().lambda()
                .eq(OtcWeekReport::getUserId, userId)
                .eq(OtcWeekReport::getStartDay, startDay));
        List<AddOtcWeekReportTaskDetailDto> otcWeekReportTaskDetailList = addOtcWeekReportDto.getOtcWeekReportTaskDetailList();
        OtcWeekReport otcWeekReport = new OtcWeekReport();
        BeanUtil.copyProperties(addOtcWeekReportDto, otcWeekReport);
        otcWeekReport.setCreateDate(LocalDateTime.now());
        if (CollectionUtil.isNotEmpty(otcWeekReports)) {
            OtcWeekReport otcWeekReportExist = otcWeekReports.get(0);
            otcWeekReport.setId(otcWeekReportExist.getId());
        }
        this.saveOrUpdate(otcWeekReport);
        List<OtcWeekReportTask> otcWeekReportTasks = BeanUtil.copyToList(otcWeekReportTaskDetailList, OtcWeekReportTask.class);

        Boolean otcWeekReportTasksExist = otcWeekReportTaskService.remove(Wrappers.<OtcWeekReportTask>query().lambda()
                .eq(OtcWeekReportTask::getWeekReportId, otcWeekReport.getId()));

        otcWeekReportTasks.forEach(o -> o.setWeekReportId(otcWeekReport.getId()));
        otcWeekReportTaskService.saveOrUpdateBatch(otcWeekReportTasks);
    }

    private OtcWeekReportTask getGoalAndCompleteNum(AddOtcWeekReportDto addOtcWeekReportDto, Long otcWeekReportId, String taskType, String taskTypeName, String frequency) {
        OtcWeekReportTask otcWeekReportTask = new OtcWeekReportTask();
        BeanUtil.copyProperties(addOtcWeekReportDto, otcWeekReportTask);
        String userCode = addOtcWeekReportDto.getSystemUserId();
        String startDay = addOtcWeekReportDto.getStartDay();
        String endDay = addOtcWeekReportDto.getEndDay();
        //目标值
        OtcSalesmanTaskVo otcSalesmanTaskVo = new OtcSalesmanTaskVo();
        otcSalesmanTaskVo.setTaskType(taskType);
        otcSalesmanTaskVo.setFrequency(frequency);
        Integer goal = otcSignInMapper.selectTaskGoal(otcSalesmanTaskVo);

        //实际完成值
        List<OtcSignIn> otcSignInLists = signService.list(Wrappers.<OtcSignIn>query().lambda()
                .eq(OtcSignIn::getSiginType, 2)
                .eq(OtcSignIn::getPersonCode, userCode)
                .eq(OtcSignIn::getCategory, taskType)
                .between(OtcSignIn::getCreateDate, startDay, endDay)
                .orderByDesc(OtcSignIn::getCreateDate));
        Integer signSum = otcSignInLists.size();
        otcWeekReportTask.setWeekReportId(otcWeekReportId);
        otcWeekReportTask.setTaskType(taskType);
        otcWeekReportTask.setTaskTypeName(taskTypeName);
        otcWeekReportTask.setTargetNum(goal.toString());
        otcWeekReportTask.setCompleteNum(signSum.toString());
        return otcWeekReportTask;
    }

    /**
     * 获得新增周报获得填写项
     *
     * @param addOtcWeekReportDto
     * @return
     */
    @Override
    public List<OtcWeekReportTaskDetailVo> getOtcWeekReportTaskDetail(AddOtcWeekReportDto addOtcWeekReportDto) {
        List<OtcWeekReportTaskDetailVo> otcWeekReportTaskDetailVos = new ArrayList<>();
        String userCode = addOtcWeekReportDto.getSystemUserId();
        String userId = addOtcWeekReportDto.getUserId();
        String monthDayBegin = addOtcWeekReportDto.getStartDay();
        String monthDayEnd = addOtcWeekReportDto.getEndDay();
        Date monthDayBeginDate = DateUtils.stringToDate(monthDayBegin, DateUtils.DATE_PATTERN);
        Date monthDayEndDate = DateUtils.addDateDays(DateUtils.stringToDate(monthDayEnd, DateUtils.DATE_PATTERN), 1);

        List<UserRoleInfoDto> userRoleInfoDtoList = userPostRelationMapper.getUserPostInfo(Arrays.asList(Long.parseLong(userId)));
        UserRoleInfoDto userRoleInfoDto = userRoleInfoDtoList.get(0);
        List<OtcSalesmanTask> otcSalesmanTasks = taskManagementService.list(Wrappers.lambdaQuery(OtcSalesmanTask.class)
                .eq(OtcSalesmanTask::getEnabledMark, EnabledMark.ENABLED.getCode())
                .eq(OtcSalesmanTask::getIsEnabled, 1)
                .eq(OtcSalesmanTask::getDeleteMark, DeleteMark.NODELETE.getCode())
                .eq(OtcSalesmanTask::getTaskStatus, 2)
                .orderByDesc(OtcSalesmanTask::getCreateDate));
        otcSalesmanTasks = otcSalesmanTasks.stream().filter(otcSalesmanTask -> !otcSalesmanTask.getTaskType().equals(TaskTypeEnum.SALEPERFORMANCE.getCode())).collect(Collectors.toList());
        otcSalesmanTasks = otcSalesmanTasks.stream().filter(otcSalesmanTask -> Arrays.asList(otcSalesmanTask.getPostId().split(",")).contains(userRoleInfoDto.getId().toString())).collect(Collectors.toList());
        List<OtcSignIn> signIns = signService.list(Wrappers.<OtcSignIn>query().lambda()
                .eq(OtcSignIn::getSiginType, 2)
                .eq(OtcSignIn::getPersonCode, userCode)
                .in(ObjectUtil.isNotEmpty(otcSalesmanTasks.stream().map(OtcSalesmanTask::getTaskType).collect(Collectors.toList())), OtcSignIn::getCategory, otcSalesmanTasks.stream().map(OtcSalesmanTask::getTaskType).collect(Collectors.toList()))
                .between(OtcSignIn::getCreateDate, monthDayBeginDate, monthDayEndDate)
                .orderByDesc(OtcSignIn::getCreateDate));
        // 按照 Category 字段分组并计算 signCount 求和
        Map<String, Long> categoryToSignCountMap = signIns.stream()
                .collect(Collectors.groupingBy(OtcSignIn::getCategory, Collectors.summingLong(OtcSignIn::getSignCount)));

        List<String> serialNumbers = signIns.stream().filter(signIn -> TaskTypeEnum.VISITSHOP.getCode().equals(signIn.getCategory())).map(OtcSignIn::getSerialNumber).collect(Collectors.toList());
        Map<Integer, Long> persionMatterCountMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(serialNumbers)) {
            List<OtcPersionMatters> otcPersionMatters = otcPersionMattersService.list(Wrappers.<OtcPersionMatters>query().lambda()
                    .eq(OtcPersionMatters::getPersonCode, userCode)
                    .in(OtcPersionMatters::getSerialNumber, serialNumbers)
                    .orderByDesc(OtcPersionMatters::getCreateDate));
            persionMatterCountMap = otcPersionMatters.stream()
                    .collect(Collectors.groupingBy(OtcPersionMatters::getMattersType, Collectors.counting()));

        }
        for (OtcSalesmanTask otcSalesmanTask : otcSalesmanTasks) {
            String category = otcSalesmanTask.getTaskType();
            String mattersType = TaskTypeEnum.getMattersTypeCode(category);
            Long signCount = categoryToSignCountMap.getOrDefault(category, 0L);
            if (signCount.equals(0L)) {
                if (TaskTypeEnum.getParentCodeByCode(category).equals(TaskTypeEnum.VISITSHOP.getCode())) {
                    signCount = persionMatterCountMap.getOrDefault(Integer.parseInt(mattersType), 0L);
                }
            }
            OtcWeekReportTaskDetailVo otcWeekReportTaskDetailVo = new OtcWeekReportTaskDetailVo();
            otcWeekReportTaskDetailVo.setTaskType(otcSalesmanTask.getTaskType());
            otcWeekReportTaskDetailVo.setTaskTypeName(TaskTypeEnum.getMessageByCode(otcSalesmanTask.getTaskType()));
            otcWeekReportTaskDetailVo.setCompleteNum(signCount.toString());
            otcWeekReportTaskDetailVos.add(otcWeekReportTaskDetailVo);
        }
        return otcWeekReportTaskDetailVos;
    }

    /**
     * 个人查看周报分页
     *
     * @param pageDto
     * @return
     */
    @Override
    public PageOutput<OtcWeekReportPageVo> getPageList(OtcWeekReportPageDto pageDto) {
        LambdaQueryWrapper<OtcWeekReport> queryWrapper = Wrappers.lambdaQuery(OtcWeekReport.class)
                .eq(OtcWeekReport::getEnabledMark, EnabledMark.ENABLED.getCode())
                .eq(OtcWeekReport::getDeleteMark, DeleteMark.NODELETE.getCode())
                .eq(StringUtil.isNotBlank(pageDto.getUserId()), OtcWeekReport::getUserId, pageDto.getUserId())
                .orderByDesc(OtcWeekReport::getCreateDate);
        IPage<OtcWeekReport> page = this.page(ConventPage.getPage(pageDto), queryWrapper);
        PageOutput<OtcWeekReportPageVo> pageOutput = ConventPage.getPageOutput(page, OtcWeekReportPageVo.class);
        return pageOutput;
    }


   /* @Override
    public List<OtcWeekReport> getUserOtcWeekReport(QueryOtcWeekReportDto dto) {
        List<OtcWeekReport> otcWeekReportList = this.list(new LambdaQueryWrapper<OtcWeekReport>().eq(OtcWeekReport::getUserId, dto.getUserId()))
                .stream().sorted(Comparator.comparing(OtcWeekReport::getCreateDate).reversed())
                .collect(Collectors.toList());
        return otcWeekReportList;
    }*/

    /**
     * 个人周报详情
     *
     * @param dto
     * @return
     */
    @Override
    public OtcWeekReportDetailVo getOtcWeekReportDetail(QueryOtcWeekReportDto dto) {
        OtcWeekReport otcWeekReport = this.getById(dto.getId());
        List<OtcWeekReportTask> otcWeekReportTaskList = otcWeekReportTaskService.list(new LambdaQueryWrapper<OtcWeekReportTask>().eq(OtcWeekReportTask::getWeekReportId, dto.getId()));
        OtcWeekReportDetailVo otcWeekReportDetailVo = new OtcWeekReportDetailVo();

        BeanUtil.copyProperties(otcWeekReport, otcWeekReportDetailVo);
        List<OtcWeekReportTaskDetailVo> otcWeekReportTaskDetailVos = BeanUtil.copyToList(otcWeekReportTaskList, OtcWeekReportTaskDetailVo.class);
        otcWeekReportDetailVo.setOtcWeekReportTaskDetailVos(otcWeekReportTaskDetailVos);

        userService.list().forEach(user -> {
            if (user.getId().toString().equals(otcWeekReport.getUserId())) {
                otcWeekReportDetailVo.setUserName(user.getName());
            }
        });
        userPostRelationMapper.getUserPostInfo(Arrays.asList(Long.parseLong(otcWeekReport.getUserId()))).forEach(userRoleInfoDto -> {
            if (userRoleInfoDto.getUserId().toString().equals(otcWeekReport.getUserId())) {
                otcWeekReportDetailVo.setPostName(userRoleInfoDto.getName());
            }
        });
        List<Department> departments = departmentService.list();
        Map<Long, Department> departmentMap = departments.stream().collect(Collectors.toMap(
                Department::getId,
                Department -> Department,
                (existing, replacement) -> existing
        ));
        Department currentDepartment = departmentMap.getOrDefault(Long.valueOf(otcWeekReport.getBusinessUnitId()), null);

        String[] deparIds = currentDepartment.getHierarchy().split("-");
        StringBuffer departNames = new StringBuffer();
        for (int i = 0; i < deparIds.length; i++) {
            if (departmentMap.containsKey(Long.valueOf(deparIds[i]))) {
                if (ObjectUtil.isNull(departNames)) {
                    departNames.append(departmentMap.get(Long.valueOf(deparIds[i])).getName());
                } else {
                    departNames.append("/").append(departmentMap.get(Long.valueOf(deparIds[i])).getName());
                }
            }
        }
        otcWeekReportDetailVo.setDepartmentName(departNames.toString());
        return otcWeekReportDetailVo;
    }

    /**
     * 后台查看日志列表
     *
     * @param
     * @return
     */
    @Override
    public PageOutput<OtcWeekReportPageVo> getOtcWeekReportList(OtcWeekReportPageDto pageDto) {
        String deptId = pageDto.getDepartmentId();
        String leaderUserName = pageDto.getLeaderUserName();
        String monthDayBegin = pageDto.getStartDay();
        String monthDayEnd = pageDto.getEndDay();
        Date monthDayBeginDate = null;
        Date monthDayEndDate = null;
        if (ObjectUtil.isNotEmpty(monthDayBegin)) {
            monthDayBeginDate = DateUtils.stringToDate(monthDayBegin, DateUtils.DATE_PATTERN);
            monthDayEndDate = DateUtils.addDateDays(DateUtils.stringToDate(monthDayEnd, DateUtils.DATE_PATTERN), 1);
        }

        List<User> allUser = userService.list();
        Map<String, Long> allUserMap = allUser.stream().collect(Collectors.toMap(User::getName, User::getId));
        Map<Long, String> allUserIdMap = allUser.stream().collect(Collectors.toMap(User::getId, User::getName));
        List<UserDeptRelation> userDeptRelationList = userDeptRelationService.list();

        /*if (ObjectUtil.isNotEmpty(leaderUserName)) {
            List<UserDeptRelation> currentUserDeptRelationList = userDeptRelationList.stream().filter(x -> x.getUserId().equals(allUserMap.getOrDefault(leaderUserName, 0l))).collect(Collectors.toList());
            if (currentUserDeptRelationList.size() > 0) {
                deptId = currentUserDeptRelationList.get(0).getDeptId();
            }
        }*/
        List<Department> departments = departmentService.list();
        Map<Long, Department> departmentMap = departments.stream().collect(Collectors.toMap(
                Department::getId,
                Department -> Department,
                (existing, replacement) -> existing
        ));
        List<Department> departmentList = departmentService.list(Wrappers.lambdaQuery(Department.class)
                .like(ObjectUtil.isNotEmpty(deptId), Department::getHierarchy, deptId)
                .orderByAsc(Department::getSortCode)
                .select(Department.class, x -> VoToColumnUtil.fieldsToColumns(DepartmentListVo.class).contains(x.getProperty())));

        Map<Long, String> allDepartmentMap = departmentList.stream().collect(Collectors.toMap(Department::getId, Department::getName));
        List<Long> deptIdList = departmentList.stream().map(Department::getId).collect(Collectors.toList());
        List<UserDeptRelation> deptUserDeptRelationList = userDeptRelationList.stream().filter(x -> deptIdList.contains(x.getDeptId())).collect(Collectors.toList());
        List<Long> deptUserIdList = deptUserDeptRelationList.stream().map(x -> x.getUserId()).collect(Collectors.toList());
        List<String> deptUserIdStringList = deptUserIdList.stream().map(String::valueOf).collect(Collectors.toList());

        //List<OtcWeekReport> otcWeekReportTaskList = this.list(new LambdaQueryWrapper<OtcWeekReport>().in(OtcWeekReport::getUserId, deptUserIdStringList));
        LambdaQueryWrapper<OtcWeekReport> queryWrapper = Wrappers.lambdaQuery(OtcWeekReport.class)
                .eq(OtcWeekReport::getEnabledMark, EnabledMark.ENABLED.getCode())
                .eq(OtcWeekReport::getDeleteMark, DeleteMark.NODELETE.getCode())
                .eq(ObjectUtil.isNotEmpty(leaderUserName), OtcWeekReport::getUserId, allUserMap.getOrDefault(leaderUserName, 0l))
                .between(ObjectUtil.isNotEmpty(monthDayBegin), OtcWeekReport::getCreateDate, monthDayBeginDate, monthDayEndDate)
                .in(OtcWeekReport::getUserId, deptUserIdStringList)
                .orderByDesc(OtcWeekReport::getCreateDate);
        IPage<OtcWeekReport> page = this.page(ConventPage.getPage(pageDto), queryWrapper);
        PageOutput<OtcWeekReportPageVo> pageOutput = ConventPage.getPageOutput(page, OtcWeekReportPageVo.class);
        pageOutput.getList().forEach(x -> {
            x.setUserName(allUserIdMap.getOrDefault(Long.valueOf(x.getUserId()), ""));
            Department currentDepartment = departmentList.stream().filter(a -> a.getId().equals(Long.valueOf(x.getBusinessUnitId()))).collect(Collectors.toList()).get(0);
            String[] deparIds = currentDepartment.getHierarchy().split("-");
            StringBuffer departNames = new StringBuffer();
            for (int i = 0; i < deparIds.length; i++) {
                if (departmentMap.containsKey(Long.valueOf(deparIds[i]))) {
                    if (ObjectUtil.isNull(departNames)) {
                        departNames.append(departmentMap.get(Long.valueOf(deparIds[i])).getName());
                    } else {
                        departNames.append("/").append(departmentMap.get(Long.valueOf(deparIds[i])).getName());
                    }
                }
            }
            x.setBusinessUnitName(departNames.toString());
        });
        return pageOutput;

    }

    @Override
    public void delOtcWeekReport(QueryOtcWeekReportDto dto) {
        this.remove(new LambdaQueryWrapper<OtcWeekReport>().eq(OtcWeekReport::getId, dto.getId()));
        otcWeekReportTaskService.remove(new LambdaQueryWrapper<OtcWeekReportTask>().eq(OtcWeekReportTask::getWeekReportId, dto.getId()));
        return;
    }


    private void genOtcWeekReportDetail(AddOtcWeekReportDto addOtcWeekReportDto) {
        OtcWeekReport otcWeekReport = new OtcWeekReport();
        BeanUtil.copyProperties(addOtcWeekReportDto, otcWeekReport);
        if (addOtcWeekReportDto.getPostCode().equals(PostCodeEnum.EMPLOYEE.getCode())) {
            //员工门店拜访目标值
           /* OtcSalesmanTaskVo otcSalesmanTaskVo = new OtcSalesmanTaskVo();
            otcSalesmanTaskVo.setTaskType(TaskTypeEnum.VISITSHOP.getCode());
            otcSalesmanTaskVo.setFrequency(TaskFrequencyEnum.DAY.getCode());
            Integer goal = otcSignInMapper.selectTaskGoal(otcSalesmanTaskVo);

            //员工实际完成门店拜访
            List<OtcSignIn> otcSignInLists = signService.list(Wrappers.<OtcSignIn>query().lambda()
                    .eq(OtcSignIn::getSiginType, 2)
                    .eq(OtcSignIn::getPersonCode, userCode)
                    .eq(OtcSignIn::getCategory, TaskTypeEnum.VISITSHOP.getCode())
                    .between(OtcSignIn::getCreateDate, startDay, endDay)
                    .orderByDesc(OtcSignIn::getCreateDate));
            Integer signSum = otcSignInLists.size();

            OtcWeekReportTask otcWeekReportTask = new OtcWeekReportTask();
            BeanUtil.copyProperties(addOtcWeekReportDto, otcWeekReportTask);
            otcWeekReportTask.setWeekReportId(otcWeekReport.getId());
            otcWeekReportTask.setTaskType(TaskTypeEnum.VISITSHOP.getCode());
            otcWeekReportTask.setTaskTypeName(TaskTypeEnum.VISITSHOP.getValue());
            otcWeekReportTask.setTargetNum(goal.toString());
            otcWeekReportTask.setCompleteNum(signSum.toString());*/
            OtcWeekReportTask otcWeekReportTask = getGoalAndCompleteNum(addOtcWeekReportDto, otcWeekReport.getId(), CategoryEnum.VISITSHOP.getCode(), CategoryEnum.VISITSHOP.getValue(), TaskFrequencyEnum.DAY.getCode());
            otcWeekReportTaskService.save(otcWeekReportTask);
        } else if (addOtcWeekReportDto.getPostCode().equals(PostCodeEnum.LEADER.getCode())) {
            List<OtcWeekReportTask> otcWeekReportTaskList = new ArrayList<>();
            //主管协访门店
            OtcWeekReportTask otcWeekReportTaskShop = getGoalAndCompleteNum(addOtcWeekReportDto, otcWeekReport.getId(), CategoryEnum.COACHSHOP.getCode(), CategoryEnum.COACHSHOP.getValue(), TaskFrequencyEnum.MOUTH.getCode());
            //主管拜访分部
            OtcWeekReportTask otcWeekReportTaskSegment = getGoalAndCompleteNum(addOtcWeekReportDto, otcWeekReport.getId(), CategoryEnum.VISITSEGMENT.getCode(), CategoryEnum.VISITSEGMENT.getValue(), TaskFrequencyEnum.MOUTH.getCode());
            otcWeekReportTaskList.add(otcWeekReportTaskShop);
            otcWeekReportTaskList.add(otcWeekReportTaskSegment);
            otcWeekReportTaskService.saveBatch(otcWeekReportTaskList);
        } else if (addOtcWeekReportDto.getPostCode().equals(PostCodeEnum.MANAGER.getCode())) {
            //省区经理拜访总部
            OtcWeekReportTask otcWeekReportTaskHead = getGoalAndCompleteNum(addOtcWeekReportDto, otcWeekReport.getId(), CategoryEnum.VISITHEAD.getCode(), CategoryEnum.VISITHEAD.getValue(), TaskFrequencyEnum.MOUTH.getCode());
            otcWeekReportTaskService.save(otcWeekReportTaskHead);
        } else if (addOtcWeekReportDto.getPostCode().equals(PostCodeEnum.KAMANAGER.getCode())) {
            List<OtcWeekReportTask> otcWeekReportTaskList = new ArrayList<>();
            //KA经理拜访总部
            OtcWeekReportTask otcWeekReportTaskHead = getGoalAndCompleteNum(addOtcWeekReportDto, otcWeekReport.getId(), CategoryEnum.VISITHEAD.getCode(), CategoryEnum.VISITHEAD.getValue(), TaskFrequencyEnum.MOUTH.getCode());
            //KA经理协访分部
            OtcWeekReportTask otcWeekReportTaskSegment = getGoalAndCompleteNum(addOtcWeekReportDto, otcWeekReport.getId(), CategoryEnum.COACHSEGMENT.getCode(), CategoryEnum.COACHSEGMENT.getValue(), TaskFrequencyEnum.MOUTH.getCode());
            otcWeekReportTaskList.add(otcWeekReportTaskHead);
            otcWeekReportTaskList.add(otcWeekReportTaskSegment);
            otcWeekReportTaskService.saveBatch(otcWeekReportTaskList);
        } else if (addOtcWeekReportDto.getPostCode().equals(PostCodeEnum.DIRECTOR.getCode())) {

        }
    }
}
