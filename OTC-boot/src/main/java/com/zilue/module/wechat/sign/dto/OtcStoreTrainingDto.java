package com.zilue.module.wechat.sign.dto;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class OtcStoreTrainingDto implements Serializable {
    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("打卡人id")
    @NotNull(message="打卡人id不能为空")
    private Long personId;

    @ApiModelProperty("edp编码")
    @NotBlank(message="edp编码不能为空")
    private String personCode;

    @ApiModelProperty("打卡人姓名")
    @NotBlank(message="打卡人姓名不能为空")
    private String personName;
    @ApiModelProperty("培训前文件")
    private String preFilePath;

    @ApiModelProperty("edp编码")
    private String happenFilePath;

    @ApiModelProperty("培训后文件")
    private String afterFilePath;

    @ApiModelProperty("活动总结")
    private String comment;
    @ApiModelProperty("序列号(同一组签到、签退记录的序列号相同)")
    @NotBlank(message="序列号不能为空")
    private String serialNumber;
}
