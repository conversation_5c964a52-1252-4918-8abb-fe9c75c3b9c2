package com.zilue.module.wechat.sign.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ProductInventoryDto implements Serializable {


    @ApiModelProperty("盘点编号")
    private String inventoryNo;

    @ApiModelProperty("产品品规编号")
    private String productFormatId;
    @ApiModelProperty("产品品规名称")
    private String productFormatName;
    @ApiModelProperty("产品批次编号")
    private String productBatchNo;

    @ApiModelProperty("产品售价")
    private BigDecimal sellPrice;

    @ApiModelProperty("盘点数量")
    private String inventoryNum;
    @ApiModelProperty("序列号(同一组签到、签退记录的序列号相同)")
    private String serialNumber;
}
