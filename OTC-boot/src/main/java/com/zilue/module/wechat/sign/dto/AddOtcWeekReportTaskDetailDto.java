package com.zilue.module.wechat.sign.dto;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@ApiModel(value = "员工周报访问统计表", description = "员工周报访问统计对象")
@EqualsAndHashCode(callSuper = false)
public class AddOtcWeekReportTaskDetailDto {


    @ApiModelProperty("员工周报ID")
    private Long weekReportId;

    @ApiModelProperty("员工id")
    private String userId;

    @ApiModelProperty("员工code")
    private String systemUserId;

    @ApiModelProperty("机构code")
    private String businessUnitId;

    @ApiModelProperty("周期目标数")
    private String targetNum;

    @ApiModelProperty("周期内完成数")
    private String completeNum;

    @ApiModelProperty("完成率")
    private BigDecimal rate;

    @ApiModelProperty("打卡类别，1门店拜访，2连锁分部拜访,3连锁总部拜访,4门店协防,5连锁分部协防")
    private String taskType;

    @ApiModelProperty("打卡类别，1门店拜访，2连锁分部拜访,3连锁总部拜访,4门店协防,5连锁分部协防")
    private String taskTypeName;
}
