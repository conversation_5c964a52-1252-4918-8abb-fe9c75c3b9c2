package com.zilue.module.wechat.sign.dto;

import com.zilue.common.page.PageInput;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
public class OtcWeekReportPageDto extends PageInput {
    @ApiModelProperty("员工id")
    private String userId;

    @ApiModelProperty("负责人")
    private String leaderUserName;

    @ApiModelProperty("机构Id")
    private String departmentId;

    @ApiModelProperty("开始时间")
    private String startDay;

    @ApiModelProperty("结束时间")
    private String endDay;

}
