package com.zilue.module.wechat.sign.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zilue.module.wechat.sign.entity.OtcProductInventory;
import com.zilue.module.wechat.sign.entity.OtcProductInventoryFile;
import com.zilue.module.wechat.sign.mapper.OtcProductInventoryFileMapper;
import com.zilue.module.wechat.sign.mapper.OtcProductInventoryMapper;
import com.zilue.module.wechat.sign.service.IOtcProductInventoryFileService;
import com.zilue.module.wechat.sign.service.IOtcProductInventoryService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class OtcProductInventoryFileServiceImpl  extends ServiceImpl<OtcProductInventoryFileMapper, OtcProductInventoryFile> implements IOtcProductInventoryFileService {
}
