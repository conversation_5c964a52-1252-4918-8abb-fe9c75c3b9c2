package com.zilue.module.wechat.sign.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zilue.common.enums.*;
import com.zilue.common.page.ConventPage;
import com.zilue.common.page.PageOutput;
import com.zilue.common.utils.StringUtil;
import com.zilue.common.utils.VoToColumnUtil;
import com.zilue.module.business.customer.entity.OtcCustomerTag;
import com.zilue.module.business.customer.vo.CustomerTagVo;
import com.zilue.module.business.punchset.dto.OtcVisitMattersUpDto;
import com.zilue.module.business.punchset.entity.OtcVisitMatters;
import com.zilue.module.business.punchset.service.IVisitMattersService;
import com.zilue.module.organization.entity.User;
import com.zilue.module.organization.service.IUserService;
import com.zilue.module.system.dto.DictionaryItemPageDto;
import com.zilue.module.system.entity.DictionaryDetail;
import com.zilue.module.system.mapper.DictionaryitemMapper;
import com.zilue.module.system.service.IDictionarydetailService;
import com.zilue.module.system.vo.DictionaryDetailListVo;
import com.zilue.module.system.vo.DictionaryItemVo;
import com.zilue.module.wechat.sign.dto.OtcAccountEmployeeDto;
import com.zilue.module.wechat.sign.dto.QueryOtcSignInDto;
import com.zilue.module.wechat.sign.entity.OtcAccountEmployee;
import com.zilue.module.wechat.sign.entity.OtcPersionMatters;
import com.zilue.module.wechat.sign.entity.OtcSignIn;
import com.zilue.module.wechat.sign.mapper.OtcAccountEmployeeMapper;
import com.zilue.module.wechat.sign.mapper.OtcSignInMapper;
import com.zilue.module.wechat.sign.service.IOtcAccountEmployeeService;
import com.zilue.module.wechat.sign.service.IOtcPersionMattersService;
import com.zilue.module.wechat.sign.service.ISignService;
import com.zilue.module.wechat.sign.vo.OtcAccountEmployeeVo;
import com.zilue.module.wechat.sign.vo.OtcAccountStatisticsEmployeeVo;
import com.zilue.module.wechat.sign.vo.OtcSignPurMernchantVo;
import io.swagger.models.auth.In;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class OtcAccountEmployeeServiceImpl extends ServiceImpl<OtcAccountEmployeeMapper, OtcAccountEmployee> implements IOtcAccountEmployeeService {
    private final OtcAccountEmployeeMapper otcAccountEmployeeMapper;
    private final IDictionarydetailService dictionarydetailService;
    private final DictionaryitemMapper dictionaryitemMapper;
    private final IUserService userService;
    private final IOtcPersionMattersService otcPersionMattersService;
    private final IVisitMattersService visitMattersService;

    /**
     * 门店员工列表
     *
     * @param dto
     * @return
     */
    @Override
    public List<OtcAccountEmployeeVo> list(OtcAccountEmployeeDto dto) {
        LambdaQueryWrapper<OtcAccountEmployee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OtcAccountEmployee::getEnabledMark, YesOrNoEnum.YES.getCode())
                .eq(ObjectUtil.isNotEmpty(dto.getSerialNumber()), OtcAccountEmployee::getSerialNumber, dto.getSerialNumber())
                .eq(ObjectUtil.isNotEmpty(dto.getAccountId()), OtcAccountEmployee::getAccountId, dto.getAccountId())
                .select();
        List<OtcAccountEmployee> otcAccountEmployeeList = otcAccountEmployeeMapper.selectList(queryWrapper);

        if (CollectionUtil.isEmpty(otcAccountEmployeeList)) {
            return null;
        } else {
            DictionaryItemPageDto dtoDic = new DictionaryItemPageDto();
            dtoDic.setName("门店员工");
            List<DictionaryItemVo> dicList = dictionaryitemMapper.dicItemList(dtoDic);
          /*   LambdaQueryWrapper<DictionaryDetail> queryWrapperDic = new LambdaQueryWrapper<>();
            queryWrapperDic.eq(ObjectUtil.isNotEmpty(dto.getEmployeePostDicItem()), DictionaryDetail::getItemId, dto.getEmployeePostDicItem());
            queryWrapperDic.select(DictionaryDetail.class, x -> VoToColumnUtil.fieldsToColumns(DictionaryDetailListVo.class).contains(x.getProperty()));
            queryWrapperDic.orderByAsc(DictionaryDetail::getSortCode);
            List<DictionaryDetail> listDic = dictionarydetailService.list(queryWrapperDic);*/
            Map<String, String> dicMap = dicList.stream()
                    .collect(Collectors.toMap(DictionaryItemVo::getCode, DictionaryItemVo::getName));
            List<OtcAccountEmployeeVo> OtcAccountEmployeeVoList = new ArrayList<>();
            otcAccountEmployeeList.forEach(stringListEntry -> {
                OtcAccountEmployeeVo OtcSignPurMernchantvo = BeanUtil.toBean(stringListEntry, OtcAccountEmployeeVo.class);
                OtcSignPurMernchantvo.setEmployeePostDicItem(dicMap.get(OtcSignPurMernchantvo.getEmployeePost()));
                OtcAccountEmployeeVoList.add(OtcSignPurMernchantvo);
            });
            return OtcAccountEmployeeVoList;
        }
    }

    /**
     * 新增门店员工
     *
     * @param dto
     * @return
     */
    @Override
    public Integer otcAccountEmployeeAdd(OtcAccountEmployeeDto dto) {
        OtcAccountEmployee otcAccountEmployee = BeanUtil.toBean(dto, OtcAccountEmployee.class);
        Integer resi = otcAccountEmployeeMapper.insert(otcAccountEmployee);
        String serialNumber = dto.getSerialNumber();
        LambdaQueryWrapper<OtcVisitMatters> queryViMattersWrapper = new LambdaQueryWrapper<>();
        queryViMattersWrapper.eq(OtcVisitMatters::getMattersType, MattersType.EMPLOYEEFILE.getCode());
        OtcVisitMatters otcVisitMatters = visitMattersService.getOne(queryViMattersWrapper);
        OtcVisitMattersUpDto otc=BeanUtil.copyProperties(otcVisitMatters,OtcVisitMattersUpDto.class);
        OtcPersionMatters otcPersionMatters=BeanUtil.copyProperties(otc,OtcPersionMatters.class);
        otcPersionMatters.setPersonId(dto.getPersonId());
        otcPersionMatters.setPersonCode(dto.getPersonCode());
        otcPersionMatters.setPersonName(dto.getPersonName());
        otcPersionMatters.setSerialNumber(serialNumber);
        otcPersionMattersService.save(otcPersionMatters);
        return resi;
    }

    @Override
    public Integer deleteotcAccountEmployee(String serialNumber, Long id) {
        LambdaQueryWrapper<OtcAccountEmployee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OtcAccountEmployee::getSerialNumber, serialNumber);
        queryWrapper.eq(OtcAccountEmployee::getDeleteMark, 0);
        List<OtcAccountEmployee> list = this.list(queryWrapper);
        if (list.size() == 1) {
            remove(Wrappers.<OtcAccountEmployee>lambdaQuery().eq(OtcAccountEmployee::getId, id));
            //删除状态录表记录表
            LambdaQueryWrapper<OtcPersionMatters> queryMattersWrapper = new LambdaQueryWrapper<>();
            queryMattersWrapper.eq(OtcPersionMatters::getSerialNumber, serialNumber)
                    .eq(OtcPersionMatters::getMattersType, MattersType.EMPLOYEEFILE.getCode());
            otcPersionMattersService.remove(queryMattersWrapper);
        } else {
            remove(Wrappers.<OtcAccountEmployee>lambdaQuery().eq(OtcAccountEmployee::getId, id));
        }
        // 查询剩余的记录数量
        queryWrapper.clear(); // 清除之前的条件
        queryWrapper.eq(OtcAccountEmployee::getSerialNumber, serialNumber);
        queryWrapper.eq(OtcAccountEmployee::getDeleteMark, 0);
        Integer i = (int) this.count(queryWrapper);
        return i;
    }




    /**
     * 门店员工编辑
     *
     * @param dto
     * @return
     */
    @Override
    public Integer otcAccountEmployeeEdit(OtcAccountEmployeeDto dto) {
        OtcAccountEmployee otcAccountEmployee = BeanUtil.toBean(dto, OtcAccountEmployee.class);
        Integer resi = otcAccountEmployeeMapper.updateById(otcAccountEmployee);
//        String serialNumber = dto.getSerialNumber();
        //删除相关记录表记录表
//        LambdaQueryWrapper<OtcPersionMatters> queryMattersWrapper = new LambdaQueryWrapper<>();
//        queryMattersWrapper.eq(OtcPersionMatters::getSerialNumber, serialNumber)
//                .eq(OtcPersionMatters::getMattersType, MattersType.EMPLOYEEFILE.getCode());
//        otcPersionMattersService.remove(queryMattersWrapper);
        //保存相关记录表记录表
//        LambdaQueryWrapper<OtcVisitMatters> queryViMattersWrapper = new LambdaQueryWrapper<>();
//        queryViMattersWrapper.eq(OtcVisitMatters::getMattersType, MattersType.EMPLOYEEFILE.getCode());
//        OtcVisitMatters otcVisitMatters = visitMattersService.getOne(queryViMattersWrapper);
//        OtcVisitMattersUpDto otc=BeanUtil.copyProperties(otcVisitMatters,OtcVisitMattersUpDto.class);
//        OtcPersionMatters otcPersionMatters=BeanUtil.copyProperties(otc,OtcPersionMatters.class);
//        otcPersionMatters.setPersonId(dto.getPersonId());
//        otcPersionMatters.setPersonCode(dto.getPersonCode());
//        otcPersionMatters.setPersonName(dto.getPersonName());
//        otcPersionMatters.setSerialNumber(serialNumber);
//        otcPersionMattersService.save(otcPersionMatters);
        return resi;
    }

    @Override
    public PageOutput<OtcAccountEmployeeVo> getPageList(OtcAccountEmployeeDto pageDto) {

        LambdaQueryWrapper<OtcAccountEmployee> queryWrapper = Wrappers.lambdaQuery(OtcAccountEmployee.class)
                .eq(OtcAccountEmployee::getEnabledMark, EnabledMark.ENABLED.getCode())
                .eq(OtcAccountEmployee::getDeleteMark, DeleteMark.NODELETE.getCode())
                .eq(StringUtil.isNotBlank(pageDto.getMobile()), OtcAccountEmployee::getMobile, pageDto.getMobile())
                .like(StringUtil.isNotBlank(pageDto.getAccountName()), OtcAccountEmployee::getAccountName, pageDto.getAccountName())
                .like(StringUtil.isNotBlank(pageDto.getEmployeeName()), OtcAccountEmployee::getEmployeeName, pageDto.getEmployeeName())
                .orderByDesc(OtcAccountEmployee::getCreateDate);
        IPage<OtcAccountEmployee> page = this.page(ConventPage.getPage(pageDto), queryWrapper);


        PageOutput<OtcAccountEmployeeVo> pageOutput = ConventPage.getPageOutput(page, OtcAccountEmployeeVo.class);
        List<OtcAccountEmployeeVo> employeeVos = pageOutput.getList().stream().filter(tag -> tag.getId() != null).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(employeeVos)) {
            //获取创建人信息
            List<Long> userIdList = employeeVos.stream().map(OtcAccountEmployeeVo::getCreateUserId).collect(Collectors.toList());
            List<User> userList = userService.listByIds(userIdList);
            Map<Long, User> userMap = userList.stream().collect(Collectors.toMap(User::getId, user -> user));

            //岗位信息
            List<DictionaryDetail> dictionaryDetailList = dictionarydetailService.list(new LambdaQueryWrapper<DictionaryDetail>().eq(DictionaryDetail::getItemId, DictionaryIdEnum.POST.getCode())
                    .eq(DictionaryDetail::getEnabledMark, EnabledMark.ENABLED.getCode()).eq(DictionaryDetail::getDeleteMark, DeleteMark.NODELETE.getCode()));
            Map<String, String> dictionaryMap = dictionaryDetailList.stream().collect(Collectors.toMap(DictionaryDetail::getCode, DictionaryDetail::getName));

            employeeVos.forEach(employeeVo -> {
                User user = userMap.get(employeeVo.getCreateUserId());
                if (user != null) {
                    // 将 gender 数字转换为中文名称
                    String genderName = user.getGender() == 1 ? "男" : "女";
                    employeeVo.setCreateUserName(user.getName());
                    employeeVo.setGenderName(genderName);
                    employeeVo.setEmployeePost(dictionaryMap.get(employeeVo.getEmployeePost()));
                } else {
                    employeeVo.setCreateUserName("管理员");
                    employeeVo.setGenderName("男");
                }
            });
        }
        pageOutput.setList(employeeVos);

        return pageOutput;
    }

    @Override
    public OtcAccountStatisticsEmployeeVo getOtcAccountStatisticsEmployee(String serialNumber) {
        LambdaQueryWrapper<OtcAccountEmployee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OtcAccountEmployee::getSerialNumber, serialNumber);
        queryWrapper.eq(OtcAccountEmployee::getDeleteMark, 0);
        queryWrapper.orderByDesc(OtcAccountEmployee::getCreateDate);
        List<OtcAccountEmployee> accountEmployeeList = this.list(queryWrapper);
        OtcAccountStatisticsEmployeeVo vo = new OtcAccountStatisticsEmployeeVo();
        vo.setEmployeeNum(accountEmployeeList.size());

        //岗位信息
        List<DictionaryDetail> dictionaryDetailList = dictionarydetailService.list(new LambdaQueryWrapper<DictionaryDetail>().eq(DictionaryDetail::getItemId, DictionaryIdEnum.POST.getCode())
                .eq(DictionaryDetail::getEnabledMark, EnabledMark.ENABLED.getCode()).eq(DictionaryDetail::getDeleteMark, DeleteMark.NODELETE.getCode()));
        Map<String, String> dictionaryMap = dictionaryDetailList.stream().collect(Collectors.toMap(DictionaryDetail::getCode, DictionaryDetail::getName));

        List<OtcAccountEmployeeVo> accountEmployeeVoList = BeanUtil.copyToList(accountEmployeeList, OtcAccountEmployeeVo.class);

        accountEmployeeVoList.forEach(employeeVo -> {
            employeeVo.setPostName(dictionaryMap.get(employeeVo.getEmployeePost()));
        });
        vo.setAccountEmployeeList(accountEmployeeVoList);
        return vo;
    }
    @Override
    public List<OtcAccountEmployeeVo> getOtcAccountStatisticsEmployeeList(String serialNumber) {
        LambdaQueryWrapper<OtcAccountEmployee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OtcAccountEmployee::getSerialNumber, serialNumber);
        queryWrapper.eq(OtcAccountEmployee::getDeleteMark, 0);
        queryWrapper.orderByDesc(OtcAccountEmployee::getCreateDate);
        List<OtcAccountEmployee> accountEmployeeList = this.list(queryWrapper);
        //岗位信息
        List<DictionaryDetail> dictionaryDetailList = dictionarydetailService.list(new LambdaQueryWrapper<DictionaryDetail>().eq(DictionaryDetail::getItemId, DictionaryIdEnum.POST.getCode())
                .eq(DictionaryDetail::getEnabledMark, EnabledMark.ENABLED.getCode()).eq(DictionaryDetail::getDeleteMark, DeleteMark.NODELETE.getCode()));
        Map<String, String> dictionaryMap = dictionaryDetailList.stream().collect(Collectors.toMap(DictionaryDetail::getCode, DictionaryDetail::getName));

        List<OtcAccountEmployeeVo> accountEmployeeVoList = BeanUtil.copyToList(accountEmployeeList, OtcAccountEmployeeVo.class);

        accountEmployeeVoList.forEach(employeeVo -> {
            employeeVo.setPostName(dictionaryMap.get(employeeVo.getEmployeePost()));
            employeeVo.setGenderName(employeeVo.getGender() == 1 ? "男" : employeeVo.getGender() == 2 ? "女" : "未知");
            employeeVo.setEmploying(Objects.equals(employeeVo.getEmploying(), "1") ? "在职" : Objects.equals(employeeVo.getEmploying(), "2") ? "离职" : "未知");
        });
        return accountEmployeeVoList;
    }


    @Override
    public OtcAccountEmployeeVo getOtcAccountEmployee(String id) {
        OtcAccountEmployee accountEmployee = this.getById(id);

        //岗位信息
        List<DictionaryDetail> dictionaryDetailList = dictionarydetailService.list(new LambdaQueryWrapper<DictionaryDetail>().eq(DictionaryDetail::getItemId, DictionaryIdEnum.POST.getCode())
                .eq(DictionaryDetail::getEnabledMark, EnabledMark.ENABLED.getCode()).eq(DictionaryDetail::getDeleteMark, DeleteMark.NODELETE.getCode()));
        Map<String, String> dictionaryMap = dictionaryDetailList.stream().collect(Collectors.toMap(DictionaryDetail::getCode, DictionaryDetail::getName));
        OtcAccountEmployeeVo vo = BeanUtil.toBean(accountEmployee, OtcAccountEmployeeVo.class);
        vo.setPostName(dictionaryMap.get(accountEmployee.getEmployeePost()));
        return vo;
    }


}
