package com.zilue.module.wechat.sign.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zilue.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@TableName("otc_patient_record")
@ApiModel(value = "患者档案表", description = "患者档案对象")
@EqualsAndHashCode(callSuper = false)
public class OtcPatientRecord  extends AuditEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;
    @ApiModelProperty("打卡人id")
    private Long personId;

    @ApiModelProperty("edp编码")
    private String personCode;

    @ApiModelProperty("打卡人姓名")
    private String personName;
    @ApiModelProperty("收集档案编号")
    private String recordNo;

    @ApiModelProperty("患者名称")
    private String patientName;

    @ApiModelProperty("联系电话")
    private String mobile;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("性别")
    private Integer gender;

    @ApiModelProperty("年龄")
    private Integer age;

    @ApiModelProperty("购药时间")
    private LocalDateTime buyTime;
    @ApiModelProperty("序列号(同一组签到、签退记录的序列号相同)")
    private String serialNumber;
}
