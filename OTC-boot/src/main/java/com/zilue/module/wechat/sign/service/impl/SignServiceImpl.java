package com.zilue.module.wechat.sign.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zilue.common.enums.MattersType;
import com.zilue.common.enums.YesOrNoEnum;
import com.zilue.common.exception.MyException;
import com.zilue.common.page.ConventPage;
import com.zilue.common.page.PageOutput;
import com.zilue.common.utils.DistanceUtils;
import com.zilue.common.utils.StringUtil;
import com.zilue.common.utils.TimeUtils;
import com.zilue.common.utils.VoToColumnUtil;
import com.zilue.module.business.punchset.dto.OtcVisitMattersUpDto;
import com.zilue.common.utils.*;
import com.zilue.module.business.punchset.entity.OtcPunchSet;
import com.zilue.module.business.punchset.entity.OtcVisitMatters;
import com.zilue.module.business.punchset.service.IVisitMattersService;
import com.zilue.module.business.punchset.vo.DistanceVo;
import com.zilue.module.business.sign.vo.OtcSignInAdminPageVo;
import com.zilue.module.organization.dto.UserListDto;
import com.zilue.module.organization.service.IUserService;
import com.zilue.module.organization.vo.UserDepartNameInfo;
import com.zilue.module.system.entity.File;
import com.zilue.module.system.service.IFileService;
import com.zilue.module.wechat.sign.dto.AddOtcSignInDto;
import com.zilue.module.wechat.sign.dto.CoachedOtcSignInDto;
import com.zilue.module.wechat.sign.dto.OtcSignInPageDto;
import com.zilue.module.wechat.sign.dto.QueryOtcSignInDto;
import com.zilue.module.wechat.sign.entity.OtcPersionMatters;
import com.zilue.module.wechat.sign.entity.OtcSignIn;
import com.zilue.module.wechat.sign.mapper.OtcSignInMapper;
import com.zilue.module.wechat.sign.service.IOtcPersionMattersService;
import com.zilue.module.wechat.sign.service.ISignService;
import com.zilue.module.wechat.sign.vo.CheckOtcSignVo;
import com.zilue.module.wechat.sign.vo.OtcSignPurMernchantVo;
import com.zilue.module.wechat.sign.vo.OtcSimpleSignPurMernchantVo;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @title: service
 * <AUTHOR>
 * @Date: 2024-12-25
 * @Version 1.0
 */
@Service
@AllArgsConstructor
public class SignServiceImpl extends ServiceImpl<OtcSignInMapper, OtcSignIn> implements ISignService {
    private final OtcSignInMapper otcSignInMapper;
    private final IFileService fileService;
    private final IUserService userService;
    private static Integer SIGININ = 1;//签到
    private static Integer SIGNOUT = 2;//签退
    private static Integer VISITTYPE = 0;//拜访
    private static Integer COACHEDTYPE = 1;//协防
    private final IVisitMattersService visitMattersService;
    private final IOtcPersionMattersService otcPersionMattersService;


    @Override
    public List<OtcSignPurMernchantVo> listByDate(QueryOtcSignInDto queryOtcSignInDto) {
        Map<Long, List<OtcSignIn>> coachedMap = new HashMap<>();
        LambdaQueryWrapper<OtcSignIn> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(OtcSignIn::getEnabledMark, YesOrNoEnum.YES.getCode())
                .eq(OtcSignIn::getPersonId, queryOtcSignInDto.getPersonId())
                .eq(ObjectUtil.isNotEmpty(queryOtcSignInDto.getSerialNumber()), OtcSignIn::getSerialNumber, queryOtcSignInDto.getSerialNumber())
                .eq(ObjectUtil.isNotNull(queryOtcSignInDto.getCoachedType()), OtcSignIn::getCoachedType, queryOtcSignInDto.getCoachedType())
                .apply("DATE_FORMAT(create_date, '%Y-%m-%d') = {0}", queryOtcSignInDto.getCreateDate()).orderByDesc(OtcSignIn::getCreateDate)
                .select();
        List<OtcSignIn> otcSignInList = otcSignInMapper.selectList(queryWrapper);
        //对于个人拜访记录这块的查询 做一定的特殊处理要判断他是不是被协防的记录,首先查询可能有的相关的协防他的记录
        if (ObjectUtil.isNull(queryOtcSignInDto.getCoachedType()) || queryOtcSignInDto.getCoachedType() != 1) {
            LambdaQueryWrapper<OtcSignIn> coachedQueryWrapper = new LambdaQueryWrapper<>();
            coachedQueryWrapper
                    .eq(OtcSignIn::getEnabledMark, YesOrNoEnum.YES.getCode())
                    .eq(OtcSignIn::getCoachedPersonId, queryOtcSignInDto.getPersonId())
                    .eq(ObjectUtil.isNotEmpty(queryOtcSignInDto.getSerialNumber()), OtcSignIn::getSerialNumber, queryOtcSignInDto.getSerialNumber())
                    .eq(OtcSignIn::getCoachedType, COACHEDTYPE)
                    .eq(OtcSignIn::getSiginType, SIGNOUT)
                    .apply("DATE_FORMAT(create_date, '%Y-%m-%d') = {0}", queryOtcSignInDto.getCreateDate()).orderByDesc(OtcSignIn::getCreateDate)
                    .select();
            List<OtcSignIn> coachedList = otcSignInMapper.selectList(coachedQueryWrapper);
            if (CollectionUtil.isNotEmpty(coachedList)) {
                coachedMap = coachedList.stream().collect(Collectors.groupingBy(obj -> obj.getPurMerchantId()));
            }
        }
        if (CollectionUtil.isEmpty(otcSignInList)) {
            return null;
        } else {
            List<OtcSignPurMernchantVo> otcSignPurMernchantVoList = new ArrayList<>();
            final Map<Long, List<OtcSignIn>> finalCoachedMap = coachedMap;
            //针对统一序列号进行分组
            Map<String, List<OtcSignIn>> serialNumberMap = otcSignInList.stream().collect(Collectors.groupingBy(OtcSignIn::getSerialNumber));
            serialNumberMap.entrySet().stream().forEach(stringListEntry -> {
                List<OtcSignIn> serialNumberList = stringListEntry.getValue();
                serialNumberList.stream().sorted(Comparator.comparing(OtcSignIn::getSiginType)).collect(Collectors.toList());
                OtcSignPurMernchantVo otcSignPurMernchantvo = BeanUtil.toBean(serialNumberList.get(0), OtcSignPurMernchantVo.class);
                List<OtcSignPurMernchantVo.OtcSignInWechatVo> otcSignInWechatVoList = new ArrayList<>();
                if (serialNumberList.size() == 2) {
                    otcSignPurMernchantvo.setDurationTime(Math.abs(serialNumberList.get(1).getCreateDate().toEpochSecond(ZoneOffset.UTC) - serialNumberList.get(0).getCreateDate().toEpochSecond(ZoneOffset.UTC)) / 60);
                }
                serialNumberList.stream().forEach(otcSignIn -> {
                    OtcSignPurMernchantVo.OtcSignInWechatVo otcSignInWechatVo = BeanUtil.toBean(otcSignIn, OtcSignPurMernchantVo.OtcSignInWechatVo.class);
                    if (StringUtil.isNotBlank(otcSignInWechatVo.getImageIds())) {
                        LambdaQueryWrapper<File> queryWrapperFile = new LambdaQueryWrapper<>();
                        queryWrapperFile.in(File::getId, Arrays.asList(otcSignInWechatVo.getImageIds().split(",")));
                        List<File> listFile = fileService.list(queryWrapperFile);
                        otcSignInWechatVo.setListFile(listFile);
                    }
                    //需要判断是协防记录的查询
                    if (otcSignIn.getSiginType() == SIGNOUT && ObjectUtil.isNotNull(queryOtcSignInDto.getCoachedType()) && queryOtcSignInDto.getCoachedType() == 1) {
                        otcSignPurMernchantvo.setSignId(otcSignIn.getId());
                        otcSignPurMernchantvo.setIsExist(YesOrNoEnum.YES.getCode());
                    }
                    //如果是非协防记录而是作为个人拜访记录来查询的，则要加上下面的判断看看是不是有协防他的领导记录存在如果有则走下面的记录
                    if (ObjectUtil.isNotNull(finalCoachedMap) && finalCoachedMap.containsKey(otcSignIn.getPurMerchantId())) {
                        otcSignPurMernchantvo.setSignId(finalCoachedMap.get(otcSignIn.getPurMerchantId()).get(0).getId());
                        otcSignPurMernchantvo.setIsExist(YesOrNoEnum.YES.getCode());
                    }
                    otcSignInWechatVoList.add(otcSignInWechatVo);
                });
                otcSignPurMernchantvo.setOtcSignInWechatVo(otcSignInWechatVoList);
                //排序
                List<OtcSignPurMernchantVo.OtcSignInWechatVo> sortedList = otcSignPurMernchantvo.getOtcSignInWechatVo().stream().sorted(Comparator.comparing(OtcSignPurMernchantVo.OtcSignInWechatVo::getSiginType)).collect(Collectors.toList());
                otcSignPurMernchantvo.setOtcSignInWechatVo(sortedList);

                otcSignPurMernchantVoList.add(otcSignPurMernchantvo);
            });
            List<OtcSignPurMernchantVo> finalOtcSignPurMernchantVoList = otcSignPurMernchantVoList.stream().sorted(Comparator.comparing(OtcSignPurMernchantVo::getCreateDate).reversed()).collect(Collectors.toList());
            return finalOtcSignPurMernchantVoList;
        }
    }

    @Override
    public PageOutput<OtcSignPurMernchantVo> coachedpage(CoachedOtcSignInDto dto) {
        UserListDto userListDto = BeanUtil.toBean(dto, UserListDto.class);
        List<UserDepartNameInfo> userDepartNameInfoList = userService.coachedUserAll(userListDto);
        List<Long> listUserId = userDepartNameInfoList.stream().map(UserDepartNameInfo::getUserId).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(listUserId)) {
            return null;
        }
        LambdaQueryWrapper<OtcSignIn> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(OtcSignIn::getEnabledMark, YesOrNoEnum.YES.getCode())
                .eq(OtcSignIn::getSiginType, SIGININ)
                .eq(OtcSignIn::getCoachedType, VISITTYPE)
                .in(OtcSignIn::getPersonId, listUserId)
                .apply("DATE_FORMAT(create_date, '%Y-%m-%d') = {0}", dto.getCreateDate()).orderByDesc(OtcSignIn::getCreateDate)
                .select(OtcSignIn.class, x -> VoToColumnUtil.fieldsToColumns(OtcSignPurMernchantVo.class).contains(x.getProperty()));
        IPage<OtcSignIn> otcSignInPage = otcSignInMapper.selectPage(ConventPage.getPage(dto), queryWrapper);
        PageOutput<OtcSignPurMernchantVo> pageOutput = ConventPage.getPageOutput(otcSignInPage, OtcSignPurMernchantVo.class);
        List<OtcSignPurMernchantVo> otcSignPurMernchantVoList = pageOutput.getList();
        if (CollectionUtil.isEmpty(otcSignPurMernchantVoList)) {
            return pageOutput;
        } else {
            //查找完整的数据
            LambdaQueryWrapper<OtcSignIn> queryWrapperNew = new LambdaQueryWrapper<>();
            queryWrapperNew
                    .in(OtcSignIn::getSerialNumber, otcSignPurMernchantVoList.stream().map(OtcSignPurMernchantVo::getSerialNumber).collect(Collectors.toList()))
                    .select();
            List<OtcSignIn> otcSignInList = otcSignInMapper.selectList(queryWrapperNew);
            //针对统一序列号进行分组
            Map<String, List<OtcSignIn>> serialNumberMap = otcSignInList.stream().collect(Collectors.groupingBy(OtcSignIn::getSerialNumber));
            otcSignPurMernchantVoList.stream().forEach(otcSignPurMernchantVo -> {
                if (serialNumberMap.containsKey(otcSignPurMernchantVo.getSerialNumber())) {
                    List<OtcSignIn> serialNumberList = serialNumberMap.get(otcSignPurMernchantVo.getSerialNumber());
                    List<OtcSignPurMernchantVo.OtcSignInWechatVo> otcSignInWechatVoList = new ArrayList<>();
                    if (serialNumberList.size() == 2) {
                        otcSignPurMernchantVo.setDurationTime(Math.abs(serialNumberList.get(1).getCreateDate().toEpochSecond(ZoneOffset.UTC) - serialNumberList.get(0).getCreateDate().toEpochSecond(ZoneOffset.UTC)) / 60);
                    }
                    serialNumberList.stream().forEach(otcSignIn -> {
                        OtcSignPurMernchantVo.OtcSignInWechatVo otcSignInWechatVo = BeanUtil.toBean(otcSignIn, OtcSignPurMernchantVo.OtcSignInWechatVo.class);
                        if (StringUtil.isNotBlank(otcSignInWechatVo.getImageIds())) {
                            LambdaQueryWrapper<File> queryWrapperFile = new LambdaQueryWrapper<>();
                            queryWrapperFile.in(File::getId, Arrays.asList(otcSignInWechatVo.getImageIds().split(",")));
                            List<File> listFile = fileService.list(queryWrapperFile);
                            otcSignInWechatVo.setListFile(listFile);
                        }
                        otcSignInWechatVoList.add(otcSignInWechatVo);
                    });
                    otcSignPurMernchantVo.setOtcSignInWechatVo(otcSignInWechatVoList);
                    //排序
                    List<OtcSignPurMernchantVo.OtcSignInWechatVo> sortedList = otcSignPurMernchantVo.getOtcSignInWechatVo().stream().sorted(Comparator.comparing(OtcSignPurMernchantVo.OtcSignInWechatVo::getSiginType)).collect(Collectors.toList());
                    otcSignPurMernchantVo.setOtcSignInWechatVo(sortedList);
                }
            });
            pageOutput.setList(otcSignPurMernchantVoList);
            return pageOutput;
        }
    }

    @Override
    public CheckOtcSignVo checkOtcSignIn(AddOtcSignInDto addOtcSignInDto, OtcPunchSet otcPunchSet) {
        //先计算距离
        DistanceVo gpsSource = new DistanceVo(addOtcSignInDto.getLongitude(), addOtcSignInDto.getLatitude());
        DistanceVo gpsTarget = new DistanceVo(addOtcSignInDto.getPurMerchantLongitude(), addOtcSignInDto.getPurMerchantLatitude());
        int offset = DistanceUtils.getDistance(gpsSource, gpsTarget).intValue() - otcPunchSet.getOuterScope();
        CheckOtcSignVo checkOtcSignVo = new CheckOtcSignVo();
        checkOtcSignVo.setOffset(offset);
        checkOtcSignVo.setSuperableScope(otcPunchSet.getSuperableScope());
        //判断是否允许超范围打卡
        if (otcPunchSet.getSuperableScope() == 0 && offset > 0) {
            checkOtcSignVo.setIsSignIn(YesOrNoEnum.NO);
        } else {
            checkOtcSignVo.setIsSignIn(YesOrNoEnum.YES);
        }
        return checkOtcSignVo;
    }

    @Override
    public OtcSimpleSignPurMernchantVo getByCurrentTime(QueryOtcSignInDto queryOtcSignInDto) {
        if (ObjectUtil.isNull(queryOtcSignInDto.getPersonId()) || StringUtil.isBlank(queryOtcSignInDto.getCreateDate())) {
            throw new MyException("业务员id或者签到日期不能为空");
        }
        LambdaQueryWrapper<OtcSignIn> queryWrapper = new LambdaQueryWrapper<>();
        OtcSimpleSignPurMernchantVo otcSimpleSignPurMernchantVo = new OtcSimpleSignPurMernchantVo();
        //分为人工选择商家/初始化商家
        if (ObjectUtil.isNotEmpty(queryOtcSignInDto.getPurMerchantId())) {
            queryWrapper
                    .eq(OtcSignIn::getEnabledMark, YesOrNoEnum.YES.getCode())
                    .eq(OtcSignIn::getPersonId, queryOtcSignInDto.getPersonId())
                    .eq(OtcSignIn::getPurMerchantId, queryOtcSignInDto.getPurMerchantId())
                    .eq(ObjectUtil.isNotNull(queryOtcSignInDto.getCoachedType()), OtcSignIn::getCoachedType, queryOtcSignInDto.getCoachedType())
                    .eq(ObjectUtil.isNotEmpty(queryOtcSignInDto.getCoachedType()), OtcSignIn::getCoachedType, queryOtcSignInDto.getCoachedType())
                    .apply("DATE_FORMAT(create_date, '%Y-%m-%d') = {0}", queryOtcSignInDto.getCreateDate()).orderByDesc(OtcSignIn::getId)
                    .select();
            List<OtcSignIn> otcSignInList = otcSignInMapper.selectList(queryWrapper);
            //看一下是不是有相应的签退数据
            if (CollectionUtil.isNotEmpty(otcSignInList)) {
                otcSimpleSignPurMernchantVo = BeanUtil.copyProperties(otcSignInList.get(0), OtcSimpleSignPurMernchantVo.class);
                if (otcSignInList.size() == 2) {
                    otcSimpleSignPurMernchantVo.setSiginType(2);
                } else if (otcSignInList.size() == 1) {
                    otcSimpleSignPurMernchantVo.setSiginType(1);
                }
                return otcSimpleSignPurMernchantVo;
            } else {
                return null;
            }
        } else {
            queryWrapper
                    .eq(OtcSignIn::getEnabledMark, YesOrNoEnum.YES.getCode())
                    .eq(OtcSignIn::getPersonId, queryOtcSignInDto.getPersonId())
                    .eq(ObjectUtil.isNotNull(queryOtcSignInDto.getCoachedType()), OtcSignIn::getCoachedType, queryOtcSignInDto.getCoachedType())
                    .apply("DATE_FORMAT(create_date, '%Y-%m-%d') = {0}", queryOtcSignInDto.getCreateDate()).orderByDesc(OtcSignIn::getId)
                    .select();
            List<OtcSignIn> otcSignInList = otcSignInMapper.selectList(queryWrapper);
            if (CollectionUtil.isNotEmpty(otcSignInList)) {
                List<String> serialNumberList = otcSignInList.stream().filter(entry -> entry.getSiginType() == 2).map(OtcSignIn::getSerialNumber).collect(Collectors.toList());
                //排除所有的签退数据
                otcSignInList = otcSignInList.stream().filter(entry -> !serialNumberList.stream().anyMatch(serialNumber -> entry.getSerialNumber().equals(serialNumber))).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(otcSignInList)) {
                    OtcSignIn otcSignIn = otcSignInList.stream().sorted(Comparator.comparing(OtcSignIn::getCreateDate).reversed()).collect(Collectors.toList()).get(0);
                    otcSimpleSignPurMernchantVo = BeanUtil.copyProperties(otcSignIn, OtcSimpleSignPurMernchantVo.class);
                    return otcSimpleSignPurMernchantVo;
                } else {
                    return null;
                }
            } else {
                return null;
            }
        }
    }

    @Override
    public OtcSignIn saveSignIn(AddOtcSignInDto addOtcSignInDto, OtcPunchSet otcPunchSet) {
        OtcSignIn otcSignIn = BeanUtil.copyProperties(addOtcSignInDto, OtcSignIn.class);
        //偏差多少米
        if (otcSignIn.getSiginType() == 1) {
            //如果是签到则生成uuid
            otcSignIn.setSerialNumber(IdUtil.simpleUUID());
            //保存相关记录表记录表
            LambdaQueryWrapper<OtcVisitMatters> queryViMattersWrapper = new LambdaQueryWrapper<>();
            queryViMattersWrapper.eq(OtcVisitMatters::getMattersType, MattersType.SIGNPHOTO.getCode());
            OtcVisitMatters otcVisitMatters = visitMattersService.getOne(queryViMattersWrapper);
            OtcVisitMattersUpDto otc = BeanUtil.copyProperties(otcVisitMatters, OtcVisitMattersUpDto.class);
            OtcPersionMatters otcPersionMatters = BeanUtil.copyProperties(otc, OtcPersionMatters.class);
            otcPersionMatters.setPersonId(addOtcSignInDto.getPersonId());
            otcPersionMatters.setPersonCode(addOtcSignInDto.getPersonCode());
            otcPersionMatters.setPersonName(addOtcSignInDto.getPersonName());
            otcPersionMatters.setSerialNumber(otcSignIn.getSerialNumber());
            otcPersionMattersService.save(otcPersionMatters);
        } else if (otcSignIn.getSiginType() == 2) {
            //如果是签退记录则需要获取相应的签到记录
            LambdaQueryWrapper<OtcSignIn> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper
                    .eq(OtcSignIn::getEnabledMark, YesOrNoEnum.YES.getCode())
                    .eq(OtcSignIn::getPersonId, addOtcSignInDto.getPersonId())
                    .eq(OtcSignIn::getPurMerchantId, addOtcSignInDto.getPurMerchantId())
                    //.eq(OtcSignIn::getSiginType, 1)
                    .eq(OtcSignIn::getCategory, addOtcSignInDto.getCategory())
                    .apply("DATE_FORMAT(create_date, '%Y-%m-%d') = {0}", addOtcSignInDto.getCurrentTime()).orderByDesc(OtcSignIn::getId)
                    .select();
            List<OtcSignIn> otcSignInList = otcSignInMapper.selectList(queryWrapper);
            // 统计每个 serialNumber 出现的次数
            Map<String, Long> serialNumberCountMap = otcSignInList.stream()
                    .map(OtcSignIn::getSerialNumber)
                    .filter(Objects::nonNull) // 过滤掉 null 值（可选）
                    .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

            // 筛选出 serialNumber 只出现一次的记录
            Optional<OtcSignIn> uniqueSerialNumberRecord = otcSignInList.stream()
                    .filter(signIn -> serialNumberCountMap.getOrDefault(signIn.getSerialNumber(), 0L) == 1)
                    .findFirst(); // 或者用 findAny() 如果只需要任意一个
            if (uniqueSerialNumberRecord.isPresent()) {
                OtcSignIn otcSignInOld =  uniqueSerialNumberRecord.get();
                DistanceVo gpsSource = new DistanceVo(addOtcSignInDto.getLongitude(), addOtcSignInDto.getLatitude());
                DistanceVo gpsTarget = new DistanceVo(addOtcSignInDto.getPurMerchantLongitude(), addOtcSignInDto.getPurMerchantLatitude());
                //偏差多少米
                int offset = DistanceUtils.getDistance(gpsSource, gpsTarget).intValue() - otcPunchSet.getOuterScope();
                otcSignIn.setSerialNumber(otcSignInOld.getSerialNumber());
                otcSignIn.setOffset(offset);
                LambdaQueryWrapper<OtcPersionMatters> queryPMattersWrapper = new LambdaQueryWrapper<>();
                queryPMattersWrapper.eq(OtcPersionMatters::getSerialNumber, otcSignInOld.getSerialNumber());
                List<OtcPersionMatters> listPersion = otcPersionMattersService.list(queryPMattersWrapper);

                Long signCount = listPersion.stream().mapToLong(item -> item.getSignCount())
                        .sum();
                //只做了牌照打卡则次数为1  有做了其他项则提出牌照打卡这一次
                if (CollectionUtil.isNotEmpty(listPersion) && listPersion.size() == 1) {
                    signCount = 1l;
                } else {
                    signCount = signCount - 1;
                }
                otcSignIn.setSignCount(signCount);
            } else {
                throw new MyException("未找到签到数据，无法签退");
            }
        }
        otcSignInMapper.insert(otcSignIn);
        return otcSignIn;
    }

    @Override
    public boolean saveCoachedEvaluation(AddOtcSignInDto addOtcSignInDto) {
        if (ObjectUtil.isNotEmpty(addOtcSignInDto) && ObjectUtil.isNotEmpty(addOtcSignInDto.getId())) {
            OtcSignIn otcSignIn = otcSignInMapper.selectById(addOtcSignInDto.getId());
            otcSignIn.setPreVisitPlan(ObjectUtil.isNotEmpty(addOtcSignInDto.getPreVisitPlan()) ? addOtcSignInDto.getPreVisitPlan() : 0);
            otcSignIn.setWorkAttitude(ObjectUtil.isNotEmpty(addOtcSignInDto.getWorkAttitude()) ? addOtcSignInDto.getWorkAttitude() : 0);
            otcSignIn.setCusRelationshipManagement(ObjectUtil.isNotEmpty(addOtcSignInDto.getCusRelationshipManagement()) ? addOtcSignInDto.getCusRelationshipManagement() : 0);
            otcSignIn.setStatementInterests(ObjectUtil.isNotEmpty(addOtcSignInDto.getStatementInterests()) ? addOtcSignInDto.getStatementInterests() : 0);
            otcSignIn.setSuggestion(ObjectUtil.isNotEmpty(addOtcSignInDto.getSuggestion()) ? addOtcSignInDto.getSuggestion() : "");
            otcSignIn.setEvaluate(ObjectUtil.isNotEmpty(addOtcSignInDto.getEvaluate()) ? addOtcSignInDto.getEvaluate() : 0);
            otcSignIn.setId(addOtcSignInDto.getId());
            otcSignInMapper.updateById(otcSignIn);
        } else {
            throw new MyException("未找签退主键,无法评价");
        }
        return Boolean.TRUE;
    }

    @Override
    public PageOutput<OtcSignInAdminPageVo> getPageList(OtcSignInPageDto pageDto) {
        IPage<OtcSignInAdminPageVo> signInIPage = otcSignInMapper.pageList(ConventPage.getPage(pageDto), pageDto);
        PageOutput<OtcSignInAdminPageVo> pageOutput = ConventPage.getPageOutput(signInIPage, OtcSignInAdminPageVo.class);
        List<OtcSignInAdminPageVo> pageOutputList = pageOutput.getList();
        if (CollectionUtils.isNotEmpty(pageOutputList)) {
            pageOutputList.forEach(oo -> {
                String imageIds = oo.getImageIds();
                if (StringUtils.isNotBlank(imageIds)) {
                    // 使用流将字符串数组转换为List<Long>
                    List<Long> imageIdList = Arrays.stream(imageIds.split(","))
                            .map(String::trim) // 去除前后空格
                            .filter(s -> !s.isEmpty()) // 过滤掉空字符串
                            .map(Long::parseLong) // 将字符串转换为Long
                            .collect(Collectors.toList()); // 收集结果为List
                    List<File> fileList = fileService.lambdaQuery().select(File::getId, File::getFileUrl).in(File::getId, imageIdList).list();
                    List<String> fileUrlList = fileList.stream().map(File::getFileUrl).collect(Collectors.toList());
                    oo.setImageUrls(fileUrlList);
                }
                if (Objects.nonNull(oo.getSignDate()) && Objects.nonNull(oo.getSignOutDate())) {
                    // 转换为 LocalDateTime
                    long betweenDates = Math.abs(LocalDateTimeUtil.between(oo.getSignDate(), oo.getSignOutDate(), ChronoUnit.MINUTES));
                    oo.setBetWeenMinus(betweenDates);
                }
            });
            pageOutput.setList(pageOutputList);
        }
        return pageOutput;
    }

    /**
     * 修补签到行为改版前的数据
     */
    @Transactional
    public Integer modifySignHistoryData() {
        LambdaQueryWrapper<OtcSignIn> querySignWrapper = new LambdaQueryWrapper<>();
        querySignWrapper.le(OtcSignIn::getCreateDate, "2025-04-04");
        querySignWrapper.eq(OtcSignIn::getSiginType, 1);
        List<OtcSignIn> otcSignIns = this.list(querySignWrapper);
        List<OtcPersionMatters> otcPersionMattersExist = otcPersionMattersService.list();
        List<String> otcSerialNumberExist = otcPersionMattersExist.stream().map(OtcPersionMatters::getSerialNumber).collect(Collectors.toList());
        List<OtcPersionMatters> otcPersionMattersList = new ArrayList<>();
        for (OtcSignIn otcSignIn : otcSignIns) {
            if (!otcSerialNumberExist.contains(otcSignIn.getSerialNumber())) {
                OtcPersionMatters otcPersionMatters = new OtcPersionMatters();
                otcPersionMatters.setPersonId(otcSignIn.getPersonId());
                otcPersionMatters.setPersonCode(otcSignIn.getPersonCode());
                otcPersionMatters.setPersonName(otcSignIn.getPersonName());
                otcPersionMatters.setSerialNumber(otcSignIn.getSerialNumber());
                otcPersionMatters.setMattersType(1);
                otcPersionMatters.setMattersName("签到拍照");
                otcPersionMatters.setMustDo(1);
                otcPersionMatters.setSignCount(1);
                otcPersionMattersList.add(otcPersionMatters);
            }
        }
        otcPersionMattersService.saveBatch(otcPersionMattersList);
        return otcPersionMattersList.size();
    }

}
