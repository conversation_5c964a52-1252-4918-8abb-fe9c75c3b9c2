package com.zilue.module.wechat.sign.dto;

import com.zilue.module.organization.vo.UserDeptVo;
import com.zilue.module.organization.vo.UserPostVo;
import com.zilue.module.organization.vo.UserRoleVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class WechatHomePageDto implements Serializable {

    /**
     * 账户
     */
    private Long id;
    /**
     * 账户
     */
    @ApiModelProperty("账户")
    private String userName;

    /**
     * 姓名
     */
    @ApiModelProperty("姓名")
    private String name;
    /**
     * 编号
     */
    @ApiModelProperty("编号")
    private String code;

    @ApiModelProperty("年-月份")
    private String month;
    /**
     * 角色名称
     */
    @ApiModelProperty("角色名称")
    private String postName;
    /**
     * 所有部门信息
     */
    @ApiModelProperty("用户所在部门")
    private Long departmentId;

    /**
     * 角色
     */
    @ApiModelProperty("角色")
    private List<UserRoleVo> roles;
    /**
     * 所有岗位信息
     */
    @ApiModelProperty("所有岗位信息")
    private List<UserPostVo> posts;

    /**
     * 负责的部门信息
     */
    @ApiModelProperty("负责的部门信息")
    private List<UserDeptVo> chargeDepartments;

    @ApiModelProperty("EDP部门名称")
    private String businessUnitIdName;
}
