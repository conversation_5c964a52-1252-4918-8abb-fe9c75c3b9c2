package com.zilue.module.wechat.sign.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class OtcWeekReportPageVo {

    private Long id;
    @ApiModelProperty("员工id")
    private String userId;

    @ApiModelProperty("员工code")
    private String systemUserId;

    @ApiModelProperty("机构code")
    private String businessUnitId;

    @ApiModelProperty("岗位code")
    private String postCode;

    @ApiModelProperty("开始时间")
    private String startDay;

    @ApiModelProperty("结束时间")
    private String endDay;

    @ApiModelProperty("周报内容")
    private String comment;

    @ApiModelProperty("员工名称")
    private String userName;

    @ApiModelProperty("机构名称")
    private String businessUnitName;

    @ApiModelProperty("提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createDate;
}
