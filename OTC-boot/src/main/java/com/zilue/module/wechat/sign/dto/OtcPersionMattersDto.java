package com.zilue.module.wechat.sign.dto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
@Data
public class OtcPersionMattersDto implements Serializable {
    @ApiModelProperty("打卡人id")
    @NotNull(message = "打卡人id不能为空")
    private Long personId;

    @ApiModelProperty("edp编码")
    @NotBlank(message = "edp编码不能为空")
    private String personCode;

    @ApiModelProperty("打卡人姓名")
    @NotBlank(message = "打卡人姓名不能为空")
    private String personName;

    @ApiModelProperty("事项类型 1 签到拍照 2产品盘点 3竞品采集 4驻店促销 5患者档案 6员工档案 7贴柜培训")
    @NotNull(message = "事项类型不能为空")
    private Integer mattersType;
    @ApiModelProperty("序列号(同一组签到、签退记录的序列号相同)")
    @NotBlank(message = "序列号不能为空")
    private String serialNumber;
}
