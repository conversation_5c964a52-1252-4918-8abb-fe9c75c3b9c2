package com.zilue.module.wechat.sign.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("otc_account_address")
@ApiModel(value = "客户地址更新表", description = "客户地址更新表")
public class OtcAccountAddress {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ApiModelProperty("")
    @TableId
    private Long id;
    /**
     * 客户ID
     */
    @ApiModelProperty("客户ID")
    private Long accountId;
    /**
     * 业务员ID
     */
    @ApiModelProperty("纬度")
    private String latitude;
    /**
     * 纬度

     */
    @ApiModelProperty("经度")
    private String longitude;
    /**
     * 经度

     */
    @ApiModelProperty("注册地址")
    private String registerAddress;
    /**
     * 路线节点名称
     */
    @ApiModelProperty("说明")
    private String remark;
    /**
     * 拜访的顺序
     */
    @ApiModelProperty("图片ids")
    private String imageIds;
    /**
     * 创建人ID
     */
    @ApiModelProperty("创建人ID")
    @TableField(fill = FieldFill.INSERT)
    private Long createUserId;
    /**
     * 创建日期
     */
    @ApiModelProperty("创建日期")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createDate;
    /**
     * 修改人ID
     */
    @ApiModelProperty("修改人ID")
    @TableField(fill = FieldFill.UPDATE)
    private Long modifyUserId;
    /**
     * 修改日期
     */
    @ApiModelProperty("修改日期")
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime modifyDate;
    /**
     * 删除标记
     */
    @ApiModelProperty("删除标记")
    @TableField(fill = FieldFill.INSERT)
    // @TableLogic
    private Integer deleteMark;
    /**
     * 有效标记
     */
    @ApiModelProperty("有效标记")
    @TableField(fill = FieldFill.INSERT)
    private Integer enabledMark;

}
