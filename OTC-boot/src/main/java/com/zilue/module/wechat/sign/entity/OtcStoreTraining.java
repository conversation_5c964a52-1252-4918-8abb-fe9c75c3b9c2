package com.zilue.module.wechat.sign.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zilue.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("otc_store_training")
@ApiModel(value = "贴柜培训表", description = "贴柜培训")
public class OtcStoreTraining  extends AuditEntity implements Serializable {
    @ApiModelProperty("主键")
    private Long id;
    @ApiModelProperty("打卡人id")
    private Long personId;

    @ApiModelProperty("edp编码")
    private String personCode;

    @ApiModelProperty("打卡人姓名")
    private String personName;
    @ApiModelProperty("培训前文件")
    private String preFilePath;

    @ApiModelProperty("培训中文件")
    private String happenFilePath;

    @ApiModelProperty("培训后文件")
    private String afterFilePath;

    @ApiModelProperty("活动总结")
    private String comment;
    @ApiModelProperty("序列号(同一组签到、签退记录的序列号相同)")
    private String serialNumber;
}