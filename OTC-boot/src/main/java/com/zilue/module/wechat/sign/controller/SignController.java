package com.zilue.module.wechat.sign.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.zilue.common.annotation.RepeatSubmit;
import com.zilue.common.annotation.XjrLog;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.exception.MyException;
import com.zilue.common.model.result.R;
import com.zilue.common.page.ConventPage;
import com.zilue.common.page.PageOutput;
import com.zilue.common.utils.UploadUtil;
import com.zilue.common.utils.VoToColumnUtil;
import com.zilue.config.DomainNameConfig;
import com.zilue.module.ai.bean.AiOcrData;
import com.zilue.module.ai.bean.AiOcrDataV2;
import com.zilue.module.ai.util.AiUtil;
import com.zilue.module.business.punchset.entity.OtcPunchSet;
import com.zilue.module.business.punchset.service.IPunchSetService;
import com.zilue.module.system.entity.File;
import com.zilue.module.system.service.IFileService;
import com.zilue.module.wechat.sign.dto.*;
import com.zilue.module.wechat.sign.entity.OtcAccountAddress;
import com.zilue.module.wechat.sign.entity.OtcSignIn;
import com.zilue.module.wechat.sign.service.*;
import com.zilue.module.wechat.sign.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * @title: 拜访打卡
 * <AUTHOR>
 * @Date: 2024-12-25
 * @Version 1.0
 */
@RestController
@RequestMapping(GlobalConstant.WECHAT_MODULE_PREFIX + "/sign")
@Api(value = GlobalConstant.WECHAT_MODULE_PREFIX + "/sign", tags = "拜访打卡代码")
@AllArgsConstructor
public class SignController {

    private static final Logger log = LoggerFactory.getLogger(SignController.class);
    private final ISignService signService;
    private final IPunchSetService punchSetService;
    private final IOtcAccountEmployeeService otcAccountEmployeeService;
    private final IOtcAccountAddressService otcAccountAddressService;
    private final IWechatHomePageService wechatHomePageService;
    private final IOtcProductInventoryService otcProductInventoryService;
    private final IOtcProductCompetitorService otcProductCompetitorService;
    private final IOtcStorePromotionService otcStorePromotionService;
    private final IOtcStoreTrainingService otcStoreTrainingService;
    private final IOtcPatientRecordService otcPatientRecordService;
    private final IOtcWeekReportService otcWeekReportService;
    private final IOtcPersionMattersService otcPersionMattersService;
    private final IOtcStoreDisplayCheckService otcStoreDisplayCheckService;
    private final DomainNameConfig domainNameConfig;
    private final IFileService fileService;


    private final static Long PUNCHSETID = 1l;

    @GetMapping("/page")
    @ApiOperation(value = "OtcSignIn列表(分页)")
    // @SaCheckPermission("sign:detail")
    public R<PageOutput<OtcSignInPageVo>> page(@Valid OtcSignInPageDto dto) {
        LambdaQueryWrapper<OtcSignIn> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .like(StrUtil.isNotBlank(dto.getPurMerchantName()), OtcSignIn::getPurMerchantName, dto.getPurMerchantName())
                .eq(ObjectUtil.isNull(dto.getCategory()), OtcSignIn::getCategory, dto.getCategory())
                .or()
                .eq(StrUtil.isNotBlank(dto.getSalesmanNameOrEdpCode()), OtcSignIn::getPersonName, dto.getSalesmanNameOrEdpCode())
                .eq(StrUtil.isNotBlank(dto.getSalesmanNameOrEdpCode()), OtcSignIn::getPersonCode, dto.getSalesmanNameOrEdpCode())
                .orderByDesc(OtcSignIn::getId)
                .select(OtcSignIn.class, x -> VoToColumnUtil.fieldsToColumns(OtcSignInPageVo.class).contains(x.getProperty()));
        IPage<OtcSignIn> page = signService.page(ConventPage.getPage(dto), queryWrapper);
        PageOutput<OtcSignInPageVo> pageOutput = ConventPage.getPageOutput(page, OtcSignInPageVo.class);
        return R.ok(pageOutput);
    }

    @GetMapping("/info")
    @ApiOperation(value = "根据id查询OtcSignIn信息")
    // @SaCheckPermission("sign:detail")
    public R<OtcSignInVo> info(@RequestParam Long id) {
        OtcSignIn otcSignIn = signService.getById(id);
        OtcSignInVo otcSignInVo = BeanUtil.toBean(otcSignIn, OtcSignInVo.class);
        if (otcSignInVo == null) {
            return R.error("找不到此数据！", otcSignInVo);
        }
        return R.ok(otcSignInVo);
    }

    @GetMapping("/getPunchingCardRecord")
    @ApiOperation(value = "根据id查询OtcSignIn信息")
    // @SaCheckPermission("sign:detail")
    public R<OtcSignInVo> getPunchingCardRecord(@RequestParam String serialNumber, @RequestParam String purMerchantId) {
        OtcSignIn otcSignIn = signService.getOne(new LambdaQueryWrapper<OtcSignIn>()
                .eq(OtcSignIn::getSerialNumber, serialNumber).eq(OtcSignIn::getPurMerchantId, purMerchantId));
        OtcSignInVo otcSignInVo = BeanUtil.toBean(otcSignIn, OtcSignInVo.class);
        if (otcSignInVo == null) {
            return R.error("找不到此数据！", otcSignInVo);
        }
        return R.ok(otcSignInVo);
    }


    @PostMapping("/getPersonIdByPurMerchantId")
    @ApiOperation(value = "根据id查询OtcSignIn信息")
    // @SaCheckPermission("sign:detail")
    public R<OtcSignInVo> getPersonIdByPurMerchantId(@Valid @RequestBody OtcSignInPageDto dto) {
        OtcSignIn otcSignIn = signService.getOne(new LambdaQueryWrapper<OtcSignIn>()
                .eq(OtcSignIn::getPurMerchantId, dto.getPurMerchantId()).eq(OtcSignIn::getSiginType, 1)
                .orderByDesc(OtcSignIn::getCreateDate), false);
        OtcSignInVo otcSignInVo = BeanUtil.toBean(otcSignIn, OtcSignInVo.class);
        if (otcSignInVo == null) {
            return R.error("找不到此数据！", otcSignInVo);
        }
        return R.ok(otcSignInVo);
    }

    @PostMapping("/coachedpage")
    @ApiOperation(value = "下属协防列表")
    // @SaCheckPermission("sign:detail")
    public R<PageOutput<OtcSignPurMernchantVo>> coachedpage(@Valid @RequestBody CoachedOtcSignInDto dto) {
        return R.ok(signService.coachedpage(dto));
    }

    @PostMapping("/GetByCurrentTime")
    @ApiOperation(value = "查询当前时间该用户的拜访记录(返回最近的未签退的记录)")
    // @SaCheckPermission("sign:detail")
    @XjrLog(value = "查询当前时间该用户的拜访记录(返回最近的未签退的记录)")
    public R<OtcSimpleSignPurMernchantVo> GetByCurrentTime(@Valid @RequestBody QueryOtcSignInDto queryOtcSignInDto) {
        return R.ok(signService.getByCurrentTime(queryOtcSignInDto));
    }

    @GetMapping("/listByDate")
    @ApiOperation(value = "查询当前时间该用户的拜访记录")
    //@SaCheckPermission("sign:detail")
    @XjrLog(value = "查询当前时间该用户的拜访记录")
    public R<List<OtcSignPurMernchantVo>> listByDate(@Valid QueryOtcSignInDto queryOtcSignInDto) {
        return R.ok(signService.listByDate(queryOtcSignInDto));
    }

    @GetMapping("/getPunchSetById")
    @ApiOperation(value = "查询打卡设置")
    //@SaCheckPermission("sign:detail")
    @XjrLog(value = "查询打卡设置")
    public R<OtcPunchSet> getPunchSetById() {
        return R.ok(punchSetService.getById(PUNCHSETID));
    }

    @GetMapping("/checkOtcSignIn")
    @ApiOperation(value = "校验打卡是否超范围")
    //@SaCheckPermission("sign:detail")
    @XjrLog(value = "校验打卡是否超范围")
    public R<CheckOtcSignVo> checkOtcSignIn(@Valid AddOtcSignInDto addOtcSignInDto) {
        OtcPunchSet otcPunchSet = punchSetService.getById(PUNCHSETID);
        //CheckOtcSignVo checkOtcSignVo=signService.checkOtcSignIn(addOtcSignInDto,otcPunchSet);
        if (BeanUtil.isEmpty(otcPunchSet)) {
            throw new MyException("请联系管理员，设置签到设置");
        }
        return R.ok(signService.checkOtcSignIn(addOtcSignInDto, otcPunchSet));
    }

    @PostMapping("/saveSignIn")
    @ApiOperation(value = "签到")
    // @SaCheckPermission("sign:add")
    @XjrLog(value = "签到")
    @RepeatSubmit
    public R<OtcSignIn> saveSignIn(@Valid @RequestBody AddOtcSignInDto dto) {
        // 根据PUNCHSETID获取OtcPunchSet对象
        OtcPunchSet otcPunchSet = punchSetService.getById(PUNCHSETID);
        // 调用signService的saveSignIn方法，传入dto和otcPunchSet对象，返回R<OtcSignIn>对象
        return R.ok(signService.saveSignIn(dto, otcPunchSet));
    }

    @PostMapping("/saveCoachedEvaluation")
    @ApiOperation(value = "协防评价")
    // @SaCheckPermission("sign:add")
    @XjrLog(value = "协防评价")
    @RepeatSubmit
    public R<Boolean> saveCoachedEvaluation(@Valid @RequestBody AddOtcSignInDto dto) {
        return R.ok(signService.saveCoachedEvaluation(dto));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除")
    //@SaCheckPermission("sign:delete")
    @RepeatSubmit
    public R<Boolean> delete(@Valid @RequestBody List<Long> ids) {
        return R.ok(signService.removeBatchByIds(ids));
    }

    @PostMapping("/otcAccountEmployeeList")
    @ApiOperation(value = "门店员工列表")
    public R<List<OtcAccountEmployeeVo>> otcAccountEmployeeList(@Valid @RequestBody OtcAccountEmployeeDto dto) {
        return R.ok(otcAccountEmployeeService.list(dto));
    }

    @PostMapping("/otcAccountEmployeeAdd")
    @ApiOperation(value = "新增门店员工")
    public R<Integer> otcAccountEmployeeAdd(@Valid @RequestBody OtcAccountEmployeeDto dto) {
        return R.ok(otcAccountEmployeeService.otcAccountEmployeeAdd(dto));
    }

    @PostMapping("/deleteotcAccountEmployee")
    @ApiOperation(value = "删除门店员工")
    public R<Integer> deleteotcAccountEmployee(@RequestParam String serialNumber, @RequestParam Long id) {
        return R.ok(otcAccountEmployeeService.deleteotcAccountEmployee(serialNumber, id));
    }


    @PostMapping("/otcAccountEmployeeEdit")
    @ApiOperation(value = "编辑门店员工")
    public R<Integer> otcAccountEmployeeEdit(@Valid @RequestBody OtcAccountEmployeeDto dto) {
        return R.ok(otcAccountEmployeeService.otcAccountEmployeeEdit(dto));
    }

    @PostMapping("/getWechatHomePage")
    @ApiOperation(value = "小程序首页")
    public R<WechatHomePageVo> getWechatHomePage(@Valid @RequestBody WechatHomePageDto dto) {
        return R.ok(wechatHomePageService.getWechatHomePage(dto));
    }


    @PostMapping("/saveOtcAccountAddress")
    @ApiOperation(value = "上报更新地址")
    // @SaCheckPermission("sign:add")
    @XjrLog(value = "上报更新地址")
    @RepeatSubmit
    public R<OtcAccountAddress> saveOtcAccountAddress(@Valid @RequestBody AddOtcAccountAddressDto dto) {
        OtcAccountAddress otcAccountAddress = otcAccountAddressService.saveOtcAccountAddress(dto);
        if (ObjectUtil.isNull(otcAccountAddress)) {
            return R.error("当天该客户已经上报过一次");
        }
        return R.ok(otcAccountAddress);
    }

    @PostMapping("/saveProductInventory")
    @ApiOperation(value = "保存产品品规盘点")
    public R saveProductInventory(@Valid @RequestBody AddProductInventoryDto dto) {
        otcProductInventoryService.saveProductInventory(dto);
        return R.ok();
    }

    @PostMapping("/deleteProductInventory")
    @ApiOperation(value = "删除产品品规盘点")
    public R<Integer> deleteProductInventory(@RequestParam String serialNumber, @RequestParam String inventoryNo) {
        return R.ok(otcProductInventoryService.deleteProductInventory(serialNumber, inventoryNo));
    }


    @PostMapping("/saveProductCompetitor")
    @ApiOperation(value = "保存产品竞品登记")
    public R saveProductCompetitor(@Valid @RequestBody List<AddOtcProductCompetitorDto> dto) {
        otcProductCompetitorService.saveProductCompetitor(dto);
        return R.ok();
    }

    @PostMapping("/deleteProductCompetitor")
    @ApiOperation(value = "删除产品竞品登记")
    public R<Integer> deleteProductCompetitor(@RequestParam String serialNumber, @RequestParam String productId) {

        return R.ok(otcProductCompetitorService.deleteProductCompetitor(serialNumber, productId));
    }

    @PostMapping("/saveOtcPersionMatters")
    @ApiOperation(value = "保存盘点/采集/患者档案数据")
    public R saveOtcPersionMatters(@Valid @RequestBody OtcPersionMattersDto dto) {
        otcPersionMattersService.saveOtcPersionMatters(dto);
        return R.ok();
    }


    @PostMapping("/saveOtcStorePromotion")
    @ApiOperation(value = "新增驻店活动促销")
    public R saveOtcStorePromotion(@Valid @RequestBody AddOtcStorePromotionDto dto) {
        otcStorePromotionService.saveOtcStorePromotion(dto);
        return R.ok();
    }

    @PostMapping("/saveOtcStoreTraining")
    @ApiOperation(value = "新增贴柜培训")
    public R saveOtcStoreTraining(@Valid @RequestBody OtcStoreTrainingDto dto) {
        otcStoreTrainingService.saveOtcStoreTraining(dto);
        return R.ok();
    }

    @PostMapping("/saveOtcPatientRecord")
    @ApiOperation(value = "新增患者档案")
    public R saveOtcPatientRecord(@Valid @RequestBody AddOtcPatientRecordDto dto) {
        otcPatientRecordService.saveOtcPatientRecord(dto);
        return R.ok();
    }

    @PostMapping("/deleteOtcPatientRecord")
    @ApiOperation(value = "删除患者档案")
    public R<Integer> deleteOtcPatientRecord(@RequestParam String serialNumber, @RequestParam String recordNo) {

        return R.ok(otcPatientRecordService.deleteOtcPatientRecord(serialNumber, recordNo));
    }

    @PostMapping("/saveOtcStoreDisplayCheck")
    @ApiOperation(value = "新增陈列检查")
    public R saveOtcStoreDisplayCheck(@Valid @RequestBody OtcStoreDisplayCheckDto otcStoreDisplayCheckDto) {
        otcStoreDisplayCheckService.saveOtcStoreDisplayCheck(otcStoreDisplayCheckDto);
        return R.ok();
    }

    @GetMapping("/getProductInventory")
    @ApiOperation(value = "查询产品品规盘点单批次详情")
    public R<ProductInventoryVo> getProductInventory(@RequestParam String inventoryNo) {
        ProductInventoryVo vo = otcProductInventoryService.getProductInventory(inventoryNo);
        return R.ok("查询成功", vo);
    }

    @GetMapping("/getProductStatisticsInventory")
    @ApiOperation(value = "查询产品品规盘点统计详情")
    public R<ProductInventoryStatisticsVo> getProductStatisticsInventory(@RequestParam String serialNumber) {
        ProductInventoryStatisticsVo vo = otcProductInventoryService.getProductStatisticsInventory(serialNumber);
        return R.ok("查询成功", vo);
    }

    @PostMapping("/getProductCompetitor")
    @ApiOperation(value = "查询产品竞品登记详情")
    public R<OtcProductCompetitorVo> getProductCompetitor(@Valid @RequestBody QueryOtcProductCompetitorDto dto) {
        OtcProductCompetitorVo vo = otcProductCompetitorService.getProductCompetitor(dto);
        return R.ok("查询成功", vo);
    }

    @GetMapping("/getProductStatisticsCompetitor")
    @ApiOperation(value = "查询产品竞品登记统计详情")
    public R<OtcProductStatisticsCompetitorVo> getProductStatisticsCompetitor(@RequestParam String serialNumber) {
        OtcProductStatisticsCompetitorVo otcVo = otcProductCompetitorService.getProductStatisticsCompetitor(serialNumber);
        return R.ok("查询成功", otcVo);
    }

    @GetMapping("/getOtcStorePromotion")
    @ApiOperation(value = "查询驻店活动促销详情")
    public R<OtcStorePromotionVo> getOtcStorePromotion(@RequestParam String serialNumber) {
        OtcStorePromotionVo vo = otcStorePromotionService.getOtcStorePromotion(serialNumber);
        return R.ok("查询成功", vo);
    }

    @GetMapping("/getOtcStoreTraining")
    @ApiOperation(value = "查询贴柜培训详情")
    public R<OtcStoreTrainingVo> getOtcStoreTraining(@RequestParam String serialNumber) {
        OtcStoreTrainingVo vo = otcStoreTrainingService.getOtcStoreTraining(serialNumber);
        return R.ok("查询成功", vo);
    }

    @GetMapping("/getOtcPatientStatisticsRecord")
    @ApiOperation(value = "查询患者档案统计信息详情")
    public R<OtcPatientStatisticsRecordVo> getOtcPatientStatisticsRecord(@RequestParam String serialNumber) {
        OtcPatientStatisticsRecordVo vo = otcPatientRecordService.getOtcPatientStatisticsRecord(serialNumber);
        return R.ok("查询成功", vo);
    }

    @GetMapping("/getOtcPatientRecord")
    @ApiOperation(value = "查询患者档案个人信息详情")
    public R<OtcPatientRecordVo> getOtcPatientRecord(@RequestParam String recordNo) {
        OtcPatientRecordVo vo = otcPatientRecordService.getOtcPatientRecord(recordNo);
        return R.ok("查询成功", vo);
    }

    @GetMapping("/getOtcAccountStatisticsEmployee")
    @ApiOperation(value = "查询员工档案统计信息")
    public R<OtcAccountStatisticsEmployeeVo> getOtcAccountStatisticsEmployee(@RequestParam String serialNumber) {
        return R.ok(otcAccountEmployeeService.getOtcAccountStatisticsEmployee(serialNumber));
    }

    @GetMapping("/getOtcAccountEmployee")
    @ApiOperation(value = "查询员工档案统计信息")
    public R<OtcAccountEmployeeVo> getOtcAccountEmployee(@RequestParam String id) {
        return R.ok(otcAccountEmployeeService.getOtcAccountEmployee(id));
    }

    @GetMapping("/getPersionMattersList")
    @ApiOperation(value = "查询员工完成事项信息")
    public R<List<OtcPersionMattersVo>> getPersionMattersList(@RequestParam String serialNumber) {
        return R.ok(otcPersionMattersService.getPersionMattersList(serialNumber));
    }

    @GetMapping("/getOtcStoreDisplayCheck")
    @ApiOperation(value = "查询陈列检查详情")
    public R<OtcStoreDisplayCheckVo> getOtcStoreDisplayCheck(@RequestParam String serialNumber) {
        OtcStoreDisplayCheckVo vo = otcStoreDisplayCheckService.getOtcStoreDisplayCheck(serialNumber);
        return R.ok("查询成功", vo);
    }


    @PostMapping("/getOtcWeekReportTaskDetail")
    @ApiOperation(value = "获得新增周报获得填写项")
    public R<List<OtcWeekReportTaskDetailVo>> getOtcWeekReportTaskDetail(@Valid @RequestBody AddOtcWeekReportDto dto) {
        return R.ok(otcWeekReportService.getOtcWeekReportTaskDetail(dto));
    }


    @PostMapping("/saveOtcWeekReport")
    @ApiOperation(value = "新增周报")
    public R saveOtcWeekReport(@Valid @RequestBody AddOtcWeekReportDto dto) {
        otcWeekReportService.saveOtcWeekReport(dto);
        return R.ok();
    }

   /* @PostMapping("/getUserOtcWeekReport")
    @ApiOperation(value = "员工周报列表详情")
    public R<List<OtcWeekReport>> getUserOtcWeekReport(@Valid @RequestBody QueryOtcWeekReportDto dto) {
        return R.ok(otcWeekReportService.getUserOtcWeekReport(dto));
    }*/

    @PostMapping("/getPageList")
    @ApiOperation(value = "个人查看周报分页")
    public R<PageOutput<OtcWeekReportPageVo>> getPageList(@Valid @RequestBody OtcWeekReportPageDto dto) {
        return R.ok(otcWeekReportService.getPageList(dto));
    }

    @PostMapping("/getOtcWeekReportDetail")
    @ApiOperation(value = "个人周报详情")
    public R<OtcWeekReportDetailVo> getOtcWeekReportDetail(@Valid @RequestBody QueryOtcWeekReportDto dto) {
        return R.ok(otcWeekReportService.getOtcWeekReportDetail(dto));
    }

    @PostMapping("/getOtcWeekReportList")
    @ApiOperation(value = "后台查看周报列表")
    public R<PageOutput<OtcWeekReportPageVo>> getOtcWeekReportList(@Valid @RequestBody OtcWeekReportPageDto pageDto) {
        return R.ok(otcWeekReportService.getOtcWeekReportList(pageDto));
    }

    @PostMapping("/delOtcWeekReport")
    @ApiOperation(value = "删除周报")
    public R delOtcWeekReport(@Valid @RequestBody QueryOtcWeekReportDto dto) {
        otcWeekReportService.delOtcWeekReport(dto);
        return R.ok();
    }

    @PostMapping(value = "/aiOcr", produces = "application/json;charset=utf-8")
    @ApiOperation(value = "aiocr识别")
    public R<List<AiOcrData>> visitMattersList(@RequestBody MultipartFile file) {
        List<AiOcrData> list = CollUtil.newArrayList();
        try {
            String filePath = UploadUtil.uploadFile(file);
            String[] stringsPath = filePath.split("/zilue");
            filePath = filePath.replace(stringsPath[0], domainNameConfig.getName());
            list = AiUtil.getOcrDataByUrl(filePath);
        } catch (Exception e) {
            log.error("上传文件失败", e);
        }
        return R.ok(list);
    }



    @PostMapping(value = "/aiOcrV2", produces = "application/json;charset=utf-8")
    @ApiOperation(value = "aiocr识别V2")
    public R<List<AiOcrDataV2>> visitMattersListV2(@RequestBody MultipartFile[] multipartFiles) {
        List<String> urlList = new ArrayList<>();
        if (multipartFiles != null && multipartFiles.length > 0) {
            Long folderId = IdWorker.getId();
            for (MultipartFile multipartFile : multipartFiles) {
                Long fileId = IdWorker.getId();
                File file = uploadFile(multipartFile, folderId, fileId);
                if (ObjectUtil.isNotNull(file) && StrUtil.isNotEmpty(file.getFileUrl())) {
                    urlList.add(file.getFileUrl());
                }
            }
        }
        if (CollUtil.isEmpty(urlList)) {
            return R.ok(CollUtil.newArrayList());
        }
        List<AiOcrDataV2> list = AiUtil.getOcrDataByUrlV2(urlList);
        return R.ok(list);
    }


    private File uploadFile(MultipartFile file, Long folderId, Long fileId)  {
        String filename = file.getOriginalFilename();
        String suffix = StringUtils.substringAfterLast(filename, StringPool.DOT);
        try {
            //保存到云服务器
            String filePath = UploadUtil.uploadFile(file);
            String[] stringsPath = filePath.split("/zilue");
            filePath = filePath.replace(stringsPath[0], domainNameConfig.getName());
            File fileEntity = new File();
            fileEntity.setId(fileId);
            fileEntity.setFolderId(folderId);
            fileEntity.setFileName(filename);
            fileEntity.setFileUrl(filePath);
            fileEntity.setFileSize(file.getSize());
            fileEntity.setFileSuffiex(StringPool.DOT + suffix);
            fileEntity.setFileType(suffix);
            fileService.save(fileEntity);
            return fileEntity;
        } catch (Exception e) {
            log.error("文件上传失败");
        }
        return null;
    }
}