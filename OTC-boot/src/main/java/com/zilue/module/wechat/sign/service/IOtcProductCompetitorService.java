package com.zilue.module.wechat.sign.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zilue.module.wechat.sign.dto.AddOtcProductCompetitorDto;
import com.zilue.module.wechat.sign.dto.ProductInventoryDto;
import com.zilue.module.wechat.sign.dto.QueryOtcProductCompetitorDto;
import com.zilue.module.wechat.sign.dto.QueryProductCompetitorDto;
import com.zilue.module.wechat.sign.entity.OtcProductCompetitor;
import com.zilue.module.wechat.sign.vo.OtcProductCompetitorListVo;
import com.zilue.module.wechat.sign.vo.OtcProductCompetitorVo;
import com.zilue.module.wechat.sign.vo.OtcProductStatisticsCompetitorVo;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

public interface IOtcProductCompetitorService extends IService<OtcProductCompetitor> {
    void saveProductCompetitor(List<AddOtcProductCompetitorDto> addOtcProductCompetitorDtos);

    OtcProductCompetitorVo getProductCompetitor(QueryOtcProductCompetitorDto dto);
    OtcProductStatisticsCompetitorVo getProductStatisticsCompetitor(String serialNumber);

    List<OtcProductCompetitorListVo> getProductCompetitorDetails(String serialNumber);

    List<OtcProductCompetitorListVo> getProductCompetitorDetailsGather(QueryProductCompetitorDto dto);

    void saveProductCompetitorDetails(OtcProductCompetitorVo dto);

    List<OtcProductCompetitorVo> getProductCompetitorList(String serialNumber);

    int deleteProductCompetitor(String serialNumber, String productId);

    List<OtcProductCompetitorListVo> getProductCompetitorListVo(@NotBlank(message = "序列号不能为空") String serialNumber);
}
