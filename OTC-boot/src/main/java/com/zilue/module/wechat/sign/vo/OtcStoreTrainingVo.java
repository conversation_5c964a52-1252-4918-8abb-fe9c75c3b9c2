package com.zilue.module.wechat.sign.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class OtcStoreTrainingVo {
    @ApiModelProperty("主键")
    private Long id;
    @ApiModelProperty("打卡人id")
    private Long personId;
    @ApiModelProperty("edp编码")
    private String personCode;
    @ApiModelProperty("打卡人姓名")
    private String personName;
    @ApiModelProperty("培训前文件")
    private String preFilePath;
    @ApiModelProperty("edp编码")
    private String happenFilePath;
    @ApiModelProperty("培训后文件")
    private String afterFilePath;
    @ApiModelProperty("活动总结")
    private String comment;
    @ApiModelProperty("序列号(同一组签到、签退记录的序列号相同)")
    private String serialNumber;
}
