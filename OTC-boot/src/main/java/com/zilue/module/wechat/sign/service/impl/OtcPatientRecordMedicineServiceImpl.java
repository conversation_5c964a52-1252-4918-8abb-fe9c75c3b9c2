package com.zilue.module.wechat.sign.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zilue.module.wechat.sign.entity.OtcPatientRecordMedicine;
import com.zilue.module.wechat.sign.mapper.OtcPatientRecordMedicineMapper;
import com.zilue.module.wechat.sign.service.IOtcPatientRecordMedicineService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class OtcPatientRecordMedicineServiceImpl  extends ServiceImpl<OtcPatientRecordMedicineMapper, OtcPatientRecordMedicine> implements IOtcPatientRecordMedicineService {
}
