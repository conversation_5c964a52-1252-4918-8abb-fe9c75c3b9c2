package com.zilue.module.wechat.sign.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zilue.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("otc_persion_matters")
@ApiModel(value = "个人到店事项表", description = "个人到店事项")
public class OtcPersionMatters extends AuditEntity implements Serializable {
    @ApiModelProperty("")
    @TableId
    private Long id;
    @ApiModelProperty("打卡人id")
    private Long personId;

    @ApiModelProperty("edp编码")
    private String personCode;

    @ApiModelProperty("打卡人姓名")
    private String personName;

    @ApiModelProperty("事项类型 1 签到拍照 2产品盘点 3竞品采集 4驻店促销 5患者档案 6员工档案 7贴柜培训")
    private Integer mattersType;

    @ApiModelProperty("事项名字")
    private String mattersName;

    @ApiModelProperty("'是否必做(1=是 2=否)'")
    private Integer mustDo;

    @ApiModelProperty("'等于签到次数'")
    private Integer signCount;
    @ApiModelProperty("序列号(同一组签到、签退记录的序列号相同)")
    private String serialNumber;
}
