package com.zilue.module.wechat.sign.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class OtcStoreDisplayCheckDto implements Serializable {
    @ApiModelProperty("id")
    private Long id;
    /**
     * 文件路径
     */
    @ApiModelProperty("文件路径")
    private String filePaths;

    /**
     * 序列号(同一组签到、签退记录的序列号相同)
     */
    @ApiModelProperty("序列号(同一组签到、签退记录的序列号相同)")
    private String serialNumber;

    /**
     * 打卡人id
     */
    @ApiModelProperty("打卡人id")
    private Long personId;

    /**
     * erp编码
     */
    @ApiModelProperty("erp编码")
    private String personCode;

    /**
     * 打卡人姓名
     */
    @ApiModelProperty("打卡人姓名")
    private String personName;
}
