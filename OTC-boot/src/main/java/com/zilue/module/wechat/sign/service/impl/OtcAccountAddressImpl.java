package com.zilue.module.wechat.sign.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zilue.common.exception.MyException;
import com.zilue.module.business.account.dto.UpdateOtcAccountDto;
import com.zilue.module.business.account.service.IAccountService;
import com.zilue.module.wechat.sign.dto.AddOtcAccountAddressDto;
import com.zilue.module.wechat.sign.entity.OtcAccountAddress;
import com.zilue.module.wechat.sign.entity.OtcAccountEmployee;
import com.zilue.module.wechat.sign.mapper.OtcAccountAddressMapper;
import com.zilue.module.wechat.sign.service.IOtcAccountAddressService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class OtcAccountAddressImpl extends ServiceImpl<OtcAccountAddressMapper, OtcAccountAddress> implements IOtcAccountAddressService {
    private final IAccountService accountService;
    @Override
    public OtcAccountAddress saveOtcAccountAddress(AddOtcAccountAddressDto dto) {
        LambdaQueryWrapper<OtcAccountAddress> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OtcAccountAddress::getAccountId, dto.getAccountId()).apply("DATE_FORMAT(create_date, '%Y-%m-%d') = {0}", dto.getCurrentTime());
        List<OtcAccountAddress> listOtcAccountAddress=this.list(queryWrapper);
        if(CollectionUtil.isNotEmpty(listOtcAccountAddress)){
            return null;
        }
        OtcAccountAddress otcAccountAddress= BeanUtil.copyProperties(dto,OtcAccountAddress.class);
        this.save(otcAccountAddress);
        UpdateOtcAccountDto updateOtcAccountDto=new UpdateOtcAccountDto();
        updateOtcAccountDto.setId(dto.getAccountId());
        updateOtcAccountDto.setAddress1Longitude(dto.getLongitude());
        updateOtcAccountDto.setAddress1Latitude(dto.getLatitude());
        updateOtcAccountDto.setAddress1Name(dto.getRegisterAddress());
        accountService.updateAccountById( updateOtcAccountDto);
        return otcAccountAddress;
    }
}
