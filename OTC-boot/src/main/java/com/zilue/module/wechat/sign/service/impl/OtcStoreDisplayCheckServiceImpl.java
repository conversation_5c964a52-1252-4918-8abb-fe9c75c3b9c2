package com.zilue.module.wechat.sign.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zilue.common.enums.MattersType;
import com.zilue.module.business.punchset.dto.OtcVisitMattersUpDto;
import com.zilue.module.business.punchset.entity.OtcVisitMatters;
import com.zilue.module.business.punchset.service.IVisitMattersService;
import com.zilue.module.wechat.sign.dto.AddOtcStorePromotionDto;
import com.zilue.module.wechat.sign.dto.OtcStoreDisplayCheckDto;
import com.zilue.module.wechat.sign.entity.OtcPersionMatters;
import com.zilue.module.wechat.sign.entity.OtcStoreDisplayCheck;
import com.zilue.module.wechat.sign.entity.OtcStorePromotion;
import com.zilue.module.wechat.sign.mapper.OtcStoreDisplayCheckMapper;
import com.zilue.module.wechat.sign.mapper.OtcStorePromotionMapper;
import com.zilue.module.wechat.sign.service.IOtcPersionMattersService;
import com.zilue.module.wechat.sign.service.IOtcStoreDisplayCheckService;
import com.zilue.module.wechat.sign.service.IOtcStorePromotionService;
import com.zilue.module.wechat.sign.vo.OtcStoreDisplayCheckVo;
import com.zilue.module.wechat.sign.vo.OtcStorePromotionVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;

@Service
@AllArgsConstructor
public class OtcStoreDisplayCheckServiceImpl extends ServiceImpl<OtcStoreDisplayCheckMapper, OtcStoreDisplayCheck> implements IOtcStoreDisplayCheckService {
    private final IOtcPersionMattersService otcPersionMattersService;
    private final IVisitMattersService visitMattersService;
    @Override
    public void saveOtcStoreDisplayCheck(OtcStoreDisplayCheckDto otcStoreDisplayCheckDto) {
        String serialNumber=otcStoreDisplayCheckDto.getSerialNumber();
        OtcStoreDisplayCheck otcStoreDisplayCheck = new OtcStoreDisplayCheck();
        BeanUtil.copyProperties(otcStoreDisplayCheckDto, otcStoreDisplayCheck);
        this.saveOrUpdate(otcStoreDisplayCheck);
        //删除相关记录表记录表
        LambdaQueryWrapper<OtcPersionMatters> queryMattersWrapper = new LambdaQueryWrapper<>();
        queryMattersWrapper.eq(OtcPersionMatters::getSerialNumber,serialNumber)
                .eq(OtcPersionMatters::getMattersType, MattersType.DISPLAYCHECK.getCode());
        otcPersionMattersService.remove(queryMattersWrapper);
        //保存相关记录表记录表
        LambdaQueryWrapper<OtcVisitMatters> queryViMattersWrapper = new LambdaQueryWrapper<>();
        queryViMattersWrapper.eq(OtcVisitMatters::getMattersType, MattersType.DISPLAYCHECK.getCode());
        OtcVisitMatters otcVisitMatters= visitMattersService.getOne(queryViMattersWrapper);

        OtcVisitMattersUpDto otc=BeanUtil.copyProperties(otcVisitMatters,OtcVisitMattersUpDto.class);
        OtcPersionMatters otcPersionMatters=BeanUtil.copyProperties(otc,OtcPersionMatters.class);
        otcPersionMatters.setPersonId(otcStoreDisplayCheckDto.getPersonId());
        otcPersionMatters.setPersonCode(otcStoreDisplayCheckDto.getPersonCode());
        otcPersionMatters.setPersonName(otcStoreDisplayCheckDto.getPersonName());
        otcPersionMatters.setSerialNumber(serialNumber);
        otcPersionMattersService.save(otcPersionMatters);
    }


    @Override
    public OtcStoreDisplayCheckVo getOtcStoreDisplayCheck(String serialNumber) {
        LambdaQueryWrapper<OtcStoreDisplayCheck> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OtcStoreDisplayCheck::getSerialNumber,serialNumber);
        OtcStoreDisplayCheck otcStoreDisplayCheck=this.getOne(queryWrapper);
        OtcStoreDisplayCheckVo vo=BeanUtil.copyProperties(otcStoreDisplayCheck, OtcStoreDisplayCheckVo.class);
        if (otcStoreDisplayCheck!= null){
            // 处理 filePaths 字段，将其从中文逗号分隔的字符串转换为 List<String>
            if (vo.getFilePaths() != null && !vo.getFilePaths().isEmpty()) {
                // 使用 split 方法按中文逗号分割字符串
                String[] filePathsArray = vo.getFilePaths().split(","); // 分割为数组
                vo.setFilePathsList(Arrays.asList(filePathsArray));
            } else {
                vo.setFilePathsList(new ArrayList<>());
            }
        }
        return vo;
    }
}
