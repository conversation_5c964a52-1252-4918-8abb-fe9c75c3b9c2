package com.zilue.module.wechat.sign.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.zilue.common.page.PageInput;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
public class OtcAccountEmployeeVo {

    private Long id;

    @ApiModelProperty("门店编号")
    private Long accountId;

    @ApiModelProperty("门店名称")
    private String accountName;

    @ApiModelProperty("员工名称")
    private String employeeName;

    @ApiModelProperty("联系电话")
    private String mobile;

    @ApiModelProperty("职位")
    private String employeePost;

    private String postName;

    @ApiModelProperty("性别")
    private Integer gender;
    @ApiModelProperty("性别名称")
    private String genderName;

    @ApiModelProperty("'年龄'")
    private Integer age;

    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("门店员工职位字段iD")
    private String employeePostDicItem;

    private String workTime;

    private String birthDay;

    @ApiModelProperty("是否在职")
    private String employing;


    /**
     * 创建人ID
     */
    @ApiModelProperty("创建人ID")
    @TableField(fill = FieldFill.INSERT)
    private Long createUserId;

    private String createUserName;
    /**
     * 创建日期
     */
    @ApiModelProperty("创建日期")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createDate;
    /**
     * 修改人ID
     */
    @ApiModelProperty("修改人ID")
    @TableField(fill = FieldFill.UPDATE)
    private Long modifyUserId;
    /**
     * 修改日期
     */
    @ApiModelProperty("修改日期")
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime modifyDate;
    /**
     * 删除标记
     */
    @ApiModelProperty("删除标记")
    @TableField(fill = FieldFill.INSERT)
    private Integer deleteMark;
    /**
     * 有效标记
     */
    @ApiModelProperty("有效标记")
    @TableField(fill = FieldFill.INSERT)
    private Integer enabledMark;

    @ApiModelProperty("家庭住址")
    private String address;

    @ApiModelProperty("爱好")
    private String hobby;

    @ApiModelProperty("月成交金额")
    private BigDecimal monthSalesMoney;
}
