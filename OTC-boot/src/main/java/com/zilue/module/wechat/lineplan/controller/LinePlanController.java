package com.zilue.module.wechat.lineplan.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.CoordinateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.dtflys.forest.annotation.Post;
import com.zilue.common.annotation.RepeatSubmit;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.exception.MyException;
import com.zilue.common.model.result.R;
import com.zilue.common.page.PageOutput;
import com.zilue.common.utils.SearchBaiDuMapApiUtil;
import com.zilue.module.system.entity.BaiduRoutingVO;
import com.zilue.module.system.entity.DiscoveryRouteQuery;
import com.zilue.module.wechat.lineplan.dto.AddOtcSalesmanLinePlanDto;
import com.zilue.module.wechat.lineplan.dto.OtcSalesmanLinePlanPageDto;
import com.zilue.module.wechat.lineplan.dto.Point;
import com.zilue.module.wechat.lineplan.entity.OtcSalesmanLinePlan;
import com.zilue.module.wechat.lineplan.service.ILinePlanService;
import com.zilue.module.wechat.lineplan.vo.MyOtcSalesmanLinePlanVo;
import com.zilue.module.wechat.lineplan.vo.OtcSalesmanLinePlanVo;
import com.zilue.module.wechat.sign.dto.QueryOtcSignInDto;
import com.zilue.module.wechat.lineplan.vo.OtcLineplanPurMernchantVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @title: 路线规划模块
 * <AUTHOR>
 * @Date: 2024-12-27
 * @Version 1.0
 */
@RestController
@RequestMapping(GlobalConstant.WECHAT_MODULE_PREFIX + "/lineplan")
@Api(value = GlobalConstant.WECHAT_MODULE_PREFIX + "/lineplan", tags = "路线规划模块代码")
@AllArgsConstructor
public class LinePlanController {
    private final ILinePlanService linePlanService;
    @PostMapping("/ListOtcSalesmanLinePlan")
    @ApiOperation(value = "OtcSalesmanLinePlan列表(不分页)")
    public R<List<OtcLineplanPurMernchantVo>> ListOtcSalesmanLinePlan(@Valid @RequestBody OtcSalesmanLinePlanPageDto dto) {
        return R.ok(linePlanService.ListOtcSalesmanLinePlan(dto));
    }
    @GetMapping("/info")
    @ApiOperation(value = "根据id查询OtcSalesmanLinePlan信息")
    // @SaCheckPermission("lineplan:detail")
    public R<OtcSalesmanLinePlanVo> info(@RequestParam Long id) {
        OtcSalesmanLinePlan otcSalesmanLinePlan = linePlanService.getById(id);
        OtcSalesmanLinePlanVo otcSalesmanLinePlanVo=BeanUtil.toBean(otcSalesmanLinePlan, OtcSalesmanLinePlanVo.class);
        if (otcSalesmanLinePlan == null) {
            return R.error("找不到此数据！",otcSalesmanLinePlanVo);
        }
        return R.ok(otcSalesmanLinePlanVo);
    }
    @PostMapping("/ListMyOtcSalesmanLinePlan")
    @ApiOperation(value = "获取我的路线")
    public R<MyOtcSalesmanLinePlanVo> ListMyOtcSalesmanLinePlan(@Valid @RequestBody OtcSalesmanLinePlanPageDto dto) {
        return R.ok(linePlanService.ListMyOtcSalesmanLinePlan(dto));
    }

    @PostMapping("/saveOtcSalesmanLinePlanDto")
    @ApiOperation(value = "保存OtcSalesmanLinePlan")
    //@SaCheckPermission("lineplan:add")
    @RepeatSubmit
    public R<Boolean> saveOtcSalesmanLinePlanDto(@Valid @RequestBody List<AddOtcSalesmanLinePlanDto> dto) {
        boolean isSuccess = linePlanService.saveOtcSalesmanLinePlanDto(dto);
        return R.ok(isSuccess);
    }
    @PostMapping("/delOtcSalesmanLinePlanDto")
    @ApiOperation(value = "删除相关记录")
    //@SaCheckPermission("lineplan:add")
    @RepeatSubmit
    public R<Boolean> delOtcSalesmanLinePlanDto(@Valid @RequestBody AddOtcSalesmanLinePlanDto dto) {
        boolean isSuccess = linePlanService.delOtcSalesmanLinePlanDto(dto);
        return R.ok(isSuccess);
    }
    @PostMapping("/listAccount")
    @ApiOperation(value="路线客户列表接口")
    // @SaCheckPermission("sign:detail")
    public R<PageOutput<OtcLineplanPurMernchantVo>> listOtcSignIn(@Valid @RequestBody QueryOtcSignInDto queryOtcSignInDto){
        return R.ok(linePlanService.listAccount(queryOtcSignInDto));
    }

    public static void main(String[] args) {
        CoordinateUtil.Coordinate cc=CoordinateUtil.bd09ToGcj02(118.**************,31.***************);
        System.out.println(cc.getLat()+";"+cc.getLng());
    }
    /**
     * 生成百度api路线图
     */
    @ApiOperation(value = "生成百度api路线图")
    @PostMapping(value = "/getBaiduRouting")
    @RepeatSubmit
    public R<BaiduRoutingVO> getBaiduRouting(@RequestBody DiscoveryRouteQuery discoveryRouteQuery) {
/*       Map<String, BigDecimal> originMap= SearchBaiDuMapApiUtil.gaoDeToBaiDu(new BigDecimal(discoveryRouteQuery.getOrigin().getLng()),new BigDecimal(discoveryRouteQuery.getOrigin().getLat()));
        Map<String, BigDecimal> destinationMap= SearchBaiDuMapApiUtil.gaoDeToBaiDu(new BigDecimal(discoveryRouteQuery.getDestination().getLng()),new BigDecimal(discoveryRouteQuery.getDestination().getLat()));
        discoveryRouteQuery.setOrigin(new DiscoveryRouteQuery.Location().setLat(originMap.get("lat").toString()).setLng(originMap.get("lon").toString()));
        discoveryRouteQuery.setDestination(new DiscoveryRouteQuery.Location().setLat(destinationMap.get("lat").toString()).setLng(destinationMap.get("lon").toString()));
        List<DiscoveryRouteQuery.Location> locationList=discoveryRouteQuery.getWaypoints();
        locationList.stream().forEach(location -> {
            Map<String, BigDecimal> locationMap =SearchBaiDuMapApiUtil.gaoDeToBaiDu(new BigDecimal(location.getLng()),new BigDecimal(location.getLat()));
            location.setLat(locationMap.get("lat").toString());
            location.setLng(locationMap.get("lon").toString());
        });*/
        BaiduRoutingVO bvo = SearchBaiDuMapApiUtil.getRouting(discoveryRouteQuery);
        if (bvo.getStatus() == 0) {
            List<BaiduRoutingVO.POI.Route> routeList=bvo.getResult().getRoutes();
            if(CollectionUtil.isNotEmpty(routeList)){
                routeList.stream().forEach(route -> {
                    StringBuffer builder=new StringBuffer();
                    route.getSteps().stream().forEach(step -> {
                        Arrays.stream(step.getPath().split(";")).forEach(stepLo->{
                            Map<String, BigDecimal> stepMap= SearchBaiDuMapApiUtil.baiduToGaoDe(new BigDecimal(stepLo.split(",")[1]),new BigDecimal(stepLo.split(",")[0]));
                            if(ObjectUtil.isNotEmpty(builder)){
                                builder.append(";").append(stepMap.get("lat").toString()+","+stepMap.get("lon").toString());
                            }else {
                                builder.append(stepMap.get("lat").toString()+","+stepMap.get("lon").toString());
                            }
                        });

                    });
                    route.setTotalpath(builder.toString());
                });
            }
            bvo.getResult().setRoutes(routeList);
        } else {
            return R.error("路线规划失败",bvo);
        }
        return R.ok(bvo);
    }
    @ApiOperation(value = "百度坐标转换高德坐标")
    @PostMapping(value = "/baiduToGaoDe")
    //@RepeatSubmit
    public R<Point> baiduToGaoDe(@RequestBody Point point) {
        if(ObjectUtil.isNull(point)){
            throw new MyException("当前坐标不能为空");
        }
        CoordinateUtil.Coordinate cc=CoordinateUtil.bd09ToGcj02(point.getLng(),point.getLat());
       // Map<String, BigDecimal> map=SearchBaiDuMapApiUtil.baiduToGaoDe(new BigDecimal(point.getLng()),new BigDecimal(point.getLat()));
        point.setLat(cc.getLat());
        point.setLng(cc.getLng());
        return R.ok(point);
    }


}