package com.zilue.module.wechat.sign.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zilue.module.wechat.sign.entity.OtcWeekReport;
import com.zilue.module.wechat.sign.entity.OtcWeekReportTask;
import com.zilue.module.wechat.sign.mapper.OtcWeekReportMapper;
import com.zilue.module.wechat.sign.mapper.OtcWeekReportTaskMapper;
import com.zilue.module.wechat.sign.service.IOtcWeekReportService;
import com.zilue.module.wechat.sign.service.IOtcWeekReportTaskService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class OtcWeekReportTaskServiceImpl extends ServiceImpl<OtcWeekReportTaskMapper, OtcWeekReportTask> implements IOtcWeekReportTaskService {
}
