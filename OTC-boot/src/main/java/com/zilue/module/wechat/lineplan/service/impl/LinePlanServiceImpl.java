package com.zilue.module.wechat.lineplan.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.toolkit.MPJWrappers;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.enums.DeleteMark;
import com.zilue.common.enums.EnabledMark;
import com.zilue.common.enums.StateCodeEnum;
import com.zilue.common.enums.YesOrNoEnum;
import com.zilue.common.exception.MyException;
import com.zilue.common.page.ConventPage;
import com.zilue.common.page.PageOutput;
import com.zilue.common.utils.*;
import com.zilue.module.business.account.entity.OtcAccount;
import com.zilue.module.business.account.entity.OtcAccountUserRelation;
import com.zilue.module.business.account.service.IAccountService;
import com.zilue.module.business.account.service.IAccountUserRelationService;
import com.zilue.module.business.account.vo.OtcAccountPageVo;
import com.zilue.module.business.customer.entity.OtcCustomerTag;
import com.zilue.module.business.customer.entity.OtcCustomerTagRel;
import com.zilue.module.business.customer.service.ICustomerTagService;
import com.zilue.module.business.customer.vo.CustomerTagVo;
import com.zilue.module.business.punchset.vo.DistanceVo;
import com.zilue.module.wechat.lineplan.dto.AddOtcSalesmanLinePlanDto;
import com.zilue.module.wechat.lineplan.dto.OtcSalesmanLinePlanPageDto;
import com.zilue.module.wechat.lineplan.entity.OtcSalesmanLinePlan;
import com.zilue.module.wechat.lineplan.mapper.OtcSalesmanLinePlanMapper;
import com.zilue.module.wechat.lineplan.service.ILinePlanService;
import com.zilue.module.wechat.sign.dto.QueryOtcSignInDto;
import com.zilue.module.wechat.sign.entity.OtcSignIn;
import com.zilue.module.wechat.sign.service.ISignService;
import com.zilue.module.wechat.lineplan.vo.OtcLineplanPurMernchantVo;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import com.zilue.module.wechat.lineplan.vo.MyOtcSalesmanLinePlanVo;

/**
* @title: service
* <AUTHOR>
* @Date: 2024-12-27
* @Version 1.0
*/
@Service
@AllArgsConstructor
public class LinePlanServiceImpl extends ServiceImpl<OtcSalesmanLinePlanMapper, OtcSalesmanLinePlan> implements ILinePlanService {
    private final IAccountService accountService;
    private final IAccountUserRelationService accountUserRelationService;
    private final ICustomerTagService customerTagService;
    private final ISignService signService;
    @Override
    public List<OtcLineplanPurMernchantVo> ListOtcSalesmanLinePlan(OtcSalesmanLinePlanPageDto dto) {
        LambdaQueryWrapper<OtcSalesmanLinePlan> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OtcSalesmanLinePlan::getPlanDay,dto.getPlanDay()).eq(OtcSalesmanLinePlan::getSalesmanId,dto.getSalesmanId())
                .orderByAsc(OtcSalesmanLinePlan::getPlanOrder);
        List<OtcSalesmanLinePlan> OtcSalesmanLinePlanList=this.list(queryWrapper);
        List<OtcLineplanPurMernchantVo> otcSalesmanLinePlanPageVoList=new ArrayList<OtcLineplanPurMernchantVo>();
        OtcSalesmanLinePlanList.stream().forEach(otcSalesmanLinePlan->{
            OtcLineplanPurMernchantVo otcLineplanPurMernchantVo= new OtcLineplanPurMernchantVo();
            otcLineplanPurMernchantVo.setId(otcSalesmanLinePlan.getPurMerchantId());
            otcLineplanPurMernchantVo.setName(otcSalesmanLinePlan.getLineName());
            otcLineplanPurMernchantVo.setAddress1Latitude(otcSalesmanLinePlan.getLatitude().toString());
            otcLineplanPurMernchantVo.setAddress1Longitude(otcSalesmanLinePlan.getLongitude().toString());
            if(ObjectUtil.isNotNull(otcSalesmanLinePlan.getLatitude())||ObjectUtil.isNotNull(otcSalesmanLinePlan.getLongitude())){
                Map<String, BigDecimal> map= SearchBaiDuMapApiUtil.baiduToGaoDe(new BigDecimal(otcSalesmanLinePlan.getLongitude().toString()),new BigDecimal(otcSalesmanLinePlan.getLatitude().toString()));
                otcLineplanPurMernchantVo.setAddress2Latitude(String.valueOf(map.get("lat")));
                otcLineplanPurMernchantVo.setAddress2Longitude(String.valueOf(map.get("lon")));
            }
            otcLineplanPurMernchantVo.setAddress1Name(otcSalesmanLinePlan.getRegisterAddress());
            otcLineplanPurMernchantVo.setPlanOrder(otcSalesmanLinePlan.getPlanOrder());
            otcSalesmanLinePlanPageVoList.add(otcLineplanPurMernchantVo);
        });
        //考虑给门店加上标签信息
        //CUSTOMERTAGID
        Set<Long> terminalIdList=otcSalesmanLinePlanPageVoList.stream().map(OtcLineplanPurMernchantVo::getId).collect(Collectors.toSet());
        List<CustomerTagVo> OtcCustomerTagList = customerTagService.selectJoinList(CustomerTagVo.class,
                MPJWrappers.<OtcCustomerTag>lambdaJoin()
                        .distinct()
                        .in(ObjectUtil.isNotNull(terminalIdList), OtcCustomerTagRel::getAccountId, terminalIdList)
                        .select(OtcCustomerTag::getId, OtcCustomerTag::getTagName,OtcCustomerTag::getTagColor,OtcCustomerTag::getShowStatus,OtcCustomerTag::getSort)
                        .select(OtcCustomerTagRel::getAccountId)
                        .select(OtcCustomerTag.class, x-> VoToColumnUtil.fieldsToColumns(CustomerTagVo.class).contains(x.getProperty()))
                        .leftJoin(OtcCustomerTagRel.class, OtcCustomerTagRel::getCustomerTagId, OtcCustomerTag::getId)
                        .orderByAsc(OtcCustomerTag::getSort)
                        .selectAs(OtcCustomerTagRel::getAccountId,"accountId"));
        Map<Long,List<CustomerTagVo>> mapOtcCustomerTag=OtcCustomerTagList.stream().collect(Collectors.groupingBy(CustomerTagVo::getAccountId));
        //统计拜访次数以及时间
        LambdaQueryWrapper<OtcSignIn> queryWrapperOtcSignIn = new LambdaQueryWrapper<>();
        queryWrapperOtcSignIn
                .eq(OtcSignIn::getEnabledMark, YesOrNoEnum.YES.getCode())
                .eq(OtcSignIn::getPersonId,dto.getSalesmanId())
                .eq(OtcSignIn::getSiginType,GlobalConstant.SIGINTYPE)
                .in(OtcSignIn::getPurMerchantId,terminalIdList)
                .apply("DATE_FORMAT(create_date, '%Y-%m') = {0}", DateUtil.format(DateUtil.parse(dto.getPlanDay()),DateUtils.MONTH_PATTERN)).orderByDesc(OtcSignIn::getCreateDate);
        List<OtcSignIn> otcSignInList=signService.list(queryWrapperOtcSignIn);
        Map<Long,List<OtcSignIn>> mapOtcSignIn=otcSignInList.stream().collect(Collectors.groupingBy(OtcSignIn::getPurMerchantId));
        otcSalesmanLinePlanPageVoList.forEach(oo -> {
            if(ObjectUtil.isNull(oo.getAddress1Latitude())||ObjectUtil.isNull(oo.getAddress1Longitude())||ObjectUtil.isNull(dto.getLongitude())||ObjectUtil.isNull(dto.getLatitude())){
                oo.setDistance(0);
            }else {
                DistanceVo gpsSource = new DistanceVo(dto.getLongitude(), dto.getLatitude());
                DistanceVo gpsTarget = new DistanceVo(new BigDecimal(oo.getAddress1Longitude()), new BigDecimal(oo.getAddress1Latitude()));
                oo.setDistance(DistanceUtils.getDistance(gpsSource, gpsTarget).intValue()/1000);
                Map<String, BigDecimal> map= SearchBaiDuMapApiUtil.baiduToGaoDe(new BigDecimal(oo.getAddress1Longitude()),new BigDecimal(oo.getAddress1Latitude()));
                oo.setAddress2Latitude(String.valueOf(map.get("lat")));
                oo.setAddress2Longitude(String.valueOf(map.get("lon")));
            }
            if(mapOtcCustomerTag.containsKey(oo.getId())){
                oo.setCustomerTagList(mapOtcCustomerTag.get(oo.getId()));
            }
            if(mapOtcSignIn.containsKey(oo.getId())){
                oo.setCount(mapOtcSignIn.get(oo.getId()).size());
                oo.setCreateDate(mapOtcSignIn.get(oo.getId()).get(0).getCreateDate());
            }
        });
        otcSalesmanLinePlanPageVoList.stream().sorted(Comparator.comparing(OtcLineplanPurMernchantVo::getPlanOrder))
                .collect(Collectors.toList());
        return otcSalesmanLinePlanPageVoList;
    }

    @Override
    public Boolean saveOtcSalesmanLinePlanDto(List<AddOtcSalesmanLinePlanDto> dto) {
        if(CollectionUtil.isEmpty(dto)){
            throw new MyException("数据不能为空");
        }
        LambdaQueryWrapper<OtcSalesmanLinePlan> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OtcSalesmanLinePlan::getSalesmanId,dto.get(0).getSalesmanId())
                    .eq(OtcSalesmanLinePlan::getPlanDay,dto.get(0).getPlanDay());
        //删除老旧记录添加新纪录
         this.remove(queryWrapper);
         List<OtcSalesmanLinePlan> OtcSalesmanLinePlanList=new ArrayList<>();
        dto.stream().forEach(addOtcSalesmanLinePlanDto -> {
            OtcSalesmanLinePlan otcSalesmanLinePlan=BeanUtil.copyProperties(addOtcSalesmanLinePlanDto,OtcSalesmanLinePlan.class);
            OtcSalesmanLinePlanList.add(otcSalesmanLinePlan);
        });
        return this.saveBatch(OtcSalesmanLinePlanList);
    }

    @Override
    public Boolean delOtcSalesmanLinePlanDto(AddOtcSalesmanLinePlanDto dto) {
        if(ObjectUtil.isNull(dto.getPlanDay())||ObjectUtil.isNull(dto.getSalesmanId())){
            throw new MyException("业务员数据以及业务日期不能为空");
        }
        LambdaQueryWrapper<OtcSalesmanLinePlan> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OtcSalesmanLinePlan::getSalesmanId,dto.getSalesmanId())
                .eq(OtcSalesmanLinePlan::getPlanDay,dto.getPlanDay());
        return this.remove(queryWrapper);
    }

    @Override
    public PageOutput<OtcLineplanPurMernchantVo> listAccount(QueryOtcSignInDto pageDto) {
        LambdaQueryWrapper queryWrapper = initializeQueryWrapper(pageDto);
        IPage<OtcAccount> page = accountService.page(ConventPage.getPage(pageDto), queryWrapper);
        PageOutput<OtcLineplanPurMernchantVo> pageOutput = ConventPage.getPageOutput(page, OtcLineplanPurMernchantVo.class);
        List<OtcLineplanPurMernchantVo> pageOutputList = pageOutput.getList();
        if(CollectionUtils.isNotEmpty(pageOutputList)) {
            List<Long> terminalIds = pageOutputList.stream().map(OtcLineplanPurMernchantVo::getId).collect(Collectors.toList());
            List<OtcAccountUserRelation> objectIdList = accountUserRelationService.list(Wrappers.lambdaQuery(OtcAccountUserRelation.class).in(OtcAccountUserRelation::getTerminalId, terminalIds));
            if(CollectionUtils.isNotEmpty(objectIdList)) {
                //考虑给门店加上标签信息
                LambdaQueryWrapper<OtcCustomerTag> OtcCustomerQueryWrapper = new LambdaQueryWrapper<>();
                //CUSTOMERTAGID
                Set<Long> terminalIdList=pageOutputList.stream().map(OtcLineplanPurMernchantVo::getId).collect(Collectors.toSet());
                List<CustomerTagVo> OtcCustomerTagList = customerTagService.selectJoinList(CustomerTagVo.class,
                        MPJWrappers.<OtcCustomerTag>lambdaJoin()
                                .distinct()
                                .in(ObjectUtil.isNotNull(terminalIdList), OtcCustomerTagRel::getAccountId, terminalIdList)
                                .select(OtcCustomerTag::getId, OtcCustomerTag::getTagName,OtcCustomerTag::getTagColor,OtcCustomerTag::getShowStatus,OtcCustomerTag::getSort)
                                .select(OtcCustomerTagRel::getAccountId)
                                .select(OtcCustomerTag.class, x-> VoToColumnUtil.fieldsToColumns(CustomerTagVo.class).contains(x.getProperty()))
                                .leftJoin(OtcCustomerTagRel.class, OtcCustomerTagRel::getCustomerTagId, OtcCustomerTag::getId)
                                .orderByAsc(OtcCustomerTag::getSort)
                                .selectAs(OtcCustomerTagRel::getAccountId,"accountId"));
                Map<Long,List<CustomerTagVo>> mapOtcCustomerTag=OtcCustomerTagList.stream().collect(Collectors.groupingBy(CustomerTagVo::getAccountId));
                //统计拜访次数以及时间
                LambdaQueryWrapper<OtcSignIn> queryWrapperOtcSignIn = new LambdaQueryWrapper<>();
                queryWrapperOtcSignIn
                        .eq(OtcSignIn::getEnabledMark, YesOrNoEnum.YES.getCode())
                        .eq(OtcSignIn::getPersonId,pageDto.getPersonId())
                        .eq(OtcSignIn::getSiginType,GlobalConstant.SIGINTYPE)
                        .in(OtcSignIn::getPurMerchantId,terminalIdList)
                        .apply("DATE_FORMAT(create_date, '%Y-%m') = {0}", DateUtil.format(DateUtil.parse(pageDto.getCreateDate()),DateUtils.MONTH_PATTERN)).orderByDesc(OtcSignIn::getCreateDate);
                List<OtcSignIn> otcSignInList=signService.list(queryWrapperOtcSignIn);
                Map<Long,List<OtcSignIn>> mapOtcSignIn=otcSignInList.stream().collect(Collectors.groupingBy(OtcSignIn::getPurMerchantId));
                pageOutputList.forEach(oo -> {
                    if(ObjectUtil.isNull(oo.getAddress1Latitude())||ObjectUtil.isNull(oo.getAddress1Longitude())||ObjectUtil.isNull(pageDto.getLongitude())||ObjectUtil.isNull(pageDto.getLatitude())){
                        oo.setDistance(0);
                    }else {
                        DistanceVo gpsSource = new DistanceVo(pageDto.getLongitude(), pageDto.getLatitude());
                        DistanceVo gpsTarget = new DistanceVo(new BigDecimal(oo.getAddress1Longitude()), new BigDecimal(oo.getAddress1Latitude()));
                        oo.setDistance(DistanceUtils.getDistance(gpsSource, gpsTarget).intValue()/1000);
                        Map<String, BigDecimal> map= SearchBaiDuMapApiUtil.baiduToGaoDe(new BigDecimal(oo.getAddress1Longitude()),new BigDecimal(oo.getAddress1Latitude()));
                        oo.setAddress2Latitude(String.valueOf(map.get("lat")));
                        oo.setAddress2Longitude(String.valueOf(map.get("lon")));
                    }
                    if(mapOtcCustomerTag.containsKey(oo.getId())){
                        oo.setCustomerTagList(mapOtcCustomerTag.get(oo.getId()));
                    }
                    if(mapOtcSignIn.containsKey(oo.getId())){
                        oo.setCount(mapOtcSignIn.get(oo.getId()).size());
                        oo.setCreateDate(mapOtcSignIn.get(oo.getId()).get(0).getCreateDate());
                    }
                });
            }
        }
        pageOutput.setList(pageOutputList);
        return pageOutput;
    }

    @Override
    public MyOtcSalesmanLinePlanVo ListMyOtcSalesmanLinePlan(OtcSalesmanLinePlanPageDto dto) {
            LambdaQueryWrapper<OtcSalesmanLinePlan> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(OtcSalesmanLinePlan::getPlanDay,dto.getPlanDay()).eq(OtcSalesmanLinePlan::getSalesmanId,dto.getSalesmanId()).ne(OtcSalesmanLinePlan::getPurMerchantId,-1L)
                    .orderByAsc(OtcSalesmanLinePlan::getPlanOrder);
            List<OtcSalesmanLinePlan> OtcSalesmanLinePlanList=this.list(queryWrapper);
            List<MyOtcSalesmanLinePlanVo.MyLinePlanVo> myLinePlanVoList=new ArrayList<>();
            OtcSalesmanLinePlanList.stream().forEach(otcSalesmanLinePlan->{
                MyOtcSalesmanLinePlanVo.MyLinePlanVo myLinePlanVo= new MyOtcSalesmanLinePlanVo.MyLinePlanVo();
                myLinePlanVo.setLineName(otcSalesmanLinePlan.getLineName());
               myLinePlanVo.setId(otcSalesmanLinePlan.getPurMerchantId());
                myLinePlanVo.setRegisterAddress(otcSalesmanLinePlan.getRegisterAddress());
                myLinePlanVoList.add(myLinePlanVo);
               // otcSalesmanLinePlanPageVoList.add(otcLineplanPurMernchantVo);
            });
            //考虑给门店加上标签信息
            //CUSTOMERTAGID
            Set<Long> terminalIdList=myLinePlanVoList.stream().map(MyOtcSalesmanLinePlanVo.MyLinePlanVo::getId).collect(Collectors.toSet());
            //统计拜访次数以及时间
            LambdaQueryWrapper<OtcSignIn> queryWrapperOtcSignIn = new LambdaQueryWrapper<>();
            queryWrapperOtcSignIn
                    .eq(OtcSignIn::getEnabledMark, YesOrNoEnum.YES.getCode())
                    .eq(OtcSignIn::getPersonId,dto.getSalesmanId())
                    .eq(OtcSignIn::getSiginType,GlobalConstant.SIGINTYPE)
                    .in(CollectionUtil.isNotEmpty(terminalIdList),OtcSignIn::getPurMerchantId,terminalIdList)
                    .apply("DATE_FORMAT(create_date, '%Y-%m-%d') = {0}", DateUtil.format(DateUtil.parse(dto.getPlanDay()),DateUtils.DATE_PATTERN)).orderByDesc(OtcSignIn::getCreateDate);
            List<OtcSignIn> otcSignInList=signService.list(queryWrapperOtcSignIn);
            Set<Long> set=otcSignInList.stream().map(OtcSignIn::getPurMerchantId).collect(Collectors.toSet());
             myLinePlanVoList.stream().forEach(my->{
                 if(set.contains(my.getId())){
                     my.setStatus("1");
                 }else {
                     my.setStatus("0");
                 }
               });
            MyOtcSalesmanLinePlanVo MyOtcSalesmanLinePlanVo=new MyOtcSalesmanLinePlanVo();
            MyOtcSalesmanLinePlanVo.setReal(otcSignInList.size());
            MyOtcSalesmanLinePlanVo.setTarget(myLinePlanVoList.size());
            MyOtcSalesmanLinePlanVo.setMyLinePlanVoList(myLinePlanVoList);
        return MyOtcSalesmanLinePlanVo;
    }

    private LambdaQueryWrapper initializeQueryWrapper(QueryOtcSignInDto pageDto) {
        List<Object>  terminalIdList = accountUserRelationService.listObjs(Wrappers.<OtcAccountUserRelation>query().lambda()
                .select(OtcAccountUserRelation::getTerminalId)
                .eq(OtcAccountUserRelation::getDeleteMark, DeleteMark.NODELETE.getCode())
                .eq(ObjectUtil.isNotNull(pageDto.getPersonId()),OtcAccountUserRelation::getUserId,pageDto.getPersonId())
        );
        LambdaQueryWrapper<OtcAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(OtcAccount::getStateCode, StateCodeEnum.ENABLED.getCode())
                .eq(OtcAccount::getDeleteMark, DeleteMark.NODELETE.getCode())
                .eq(OtcAccount::getEnabledMark, EnabledMark.ENABLED.getCode())
                .like(StringUtil.isNotBlank(pageDto.getName()),OtcAccount::getName,pageDto.getName())
                .orderByDesc(OtcAccount::getId)
                .select(OtcAccount.class,x -> VoToColumnUtil.fieldsToColumns(OtcAccountPageVo.class).contains(x.getProperty()));
        //其中核心门店要除外，另外计算固加上这个条件
        queryWrapper.in(CollectionUtils.isNotEmpty(terminalIdList),OtcAccount::getId, terminalIdList);
        return queryWrapper;
    }
}
