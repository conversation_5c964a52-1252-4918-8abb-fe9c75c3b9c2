package com.zilue.module.wechat.sign.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class QueryOtcProductCompetitorDto implements Serializable {
    @ApiModelProperty("打卡人id")

    private Long personId;

    @ApiModelProperty("edp编码")
    @NotBlank(message="edp编码不能为空")
    private String personCode;

    @ApiModelProperty("产品编号")
    private String productId;

    @ApiModelProperty("序列号(同一组签到、签退记录的序列号相同)")
    private String serialNumber;

}
