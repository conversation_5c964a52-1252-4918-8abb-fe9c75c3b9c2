package com.zilue.module.wechat.lineplan.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zilue.common.page.PageOutput;
import com.zilue.module.wechat.lineplan.dto.AddOtcSalesmanLinePlanDto;
import com.zilue.module.wechat.lineplan.dto.OtcSalesmanLinePlanPageDto;
import com.zilue.module.wechat.lineplan.entity.OtcSalesmanLinePlan;
import com.zilue.module.wechat.sign.dto.QueryOtcSignInDto;
import com.zilue.module.wechat.lineplan.vo.OtcLineplanPurMernchantVo;
import com.zilue.module.wechat.lineplan.vo.MyOtcSalesmanLinePlanVo;
import java.util.List;

/**
* @title: service
* <AUTHOR>
* @Date: 2024-12-27
* @Version 1.0
*/
public interface ILinePlanService extends IService<OtcSalesmanLinePlan> {
    List<OtcLineplanPurMernchantVo> ListOtcSalesmanLinePlan(OtcSalesmanLinePlanPageDto dto);
    Boolean saveOtcSalesmanLinePlanDto(List<AddOtcSalesmanLinePlanDto> dto);
    Boolean delOtcSalesmanLinePlanDto(AddOtcSalesmanLinePlanDto dto);
    PageOutput<OtcLineplanPurMernchantVo> listAccount(QueryOtcSignInDto queryOtcSignInDto);
    MyOtcSalesmanLinePlanVo ListMyOtcSalesmanLinePlan(OtcSalesmanLinePlanPageDto dto);
}
