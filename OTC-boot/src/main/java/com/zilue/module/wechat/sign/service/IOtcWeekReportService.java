package com.zilue.module.wechat.sign.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zilue.common.page.PageOutput;
import com.zilue.module.wechat.sign.dto.AddOtcWeekReportDto;
import com.zilue.module.wechat.sign.dto.OtcWeekReportPageDto;
import com.zilue.module.wechat.sign.dto.QueryOtcWeekReportDto;
import com.zilue.module.wechat.sign.entity.OtcStoreTraining;
import com.zilue.module.wechat.sign.entity.OtcWeekReport;
import com.zilue.module.wechat.sign.vo.OtcWeekReportDetailVo;
import com.zilue.module.wechat.sign.vo.OtcWeekReportPageVo;
import com.zilue.module.wechat.sign.vo.OtcWeekReportTaskDetailVo;

import java.util.List;

public interface IOtcWeekReportService extends IService<OtcWeekReport> {
    void saveOtcWeekReport(AddOtcWeekReportDto addOtcWeekReportDto);

    PageOutput<OtcWeekReportPageVo> getPageList(OtcWeekReportPageDto pageDto);

    OtcWeekReportDetailVo getOtcWeekReportDetail(QueryOtcWeekReportDto dto);

    List<OtcWeekReportTaskDetailVo> getOtcWeekReportTaskDetail(AddOtcWeekReportDto addOtcWeekReportDto);

    //List<OtcWeekReport> getUserOtcWeekReport(QueryOtcWeekReportDto dto);
    PageOutput<OtcWeekReportPageVo> getOtcWeekReportList(OtcWeekReportPageDto pageDto);

    void delOtcWeekReport(QueryOtcWeekReportDto dto);
}
