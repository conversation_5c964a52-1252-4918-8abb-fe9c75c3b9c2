package com.zilue.module.wechat.sign.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class OtcPatientRecordDto  implements Serializable {

    private Long id;
    @ApiModelProperty("打卡人id")
    @NotNull(message="打卡人id不能为空")
    private Long personId;

    @ApiModelProperty("edp编码")
    @NotBlank(message="edp编码不能为空")
    private String personCode;

    @ApiModelProperty("打卡人姓名")
    @NotBlank(message="打卡人姓名不能为空")
    private String personName;
    @ApiModelProperty("患者名称")
    private String patientName;

    @ApiModelProperty("联系电话")
    private String mobile;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("性别")
    private Integer gender;

    @ApiModelProperty("性别")
    private String genderName;

    @ApiModelProperty("年龄")
    private Integer age;

    @ApiModelProperty("购药时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDateTime buyTime;

    @ApiModelProperty("收集档案编号")
    private String recordNo;
    @ApiModelProperty("序列号(同一组签到、签退记录的序列号相同)")
    @NotBlank(message="edp编码不能为空")
    private String serialNumber;
}
