package com.zilue.module.wechat.sign.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zilue.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@TableName("otc_account_employee")
@ApiModel(value = "店员对象", description = "店员")
@EqualsAndHashCode(callSuper = false)
public class OtcAccountEmployee extends AuditEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("门店编号")
    private Long accountId;

    @ApiModelProperty("门店名称")
    private String accountName;

    @ApiModelProperty("员工名称")
    private String employeeName;

    @ApiModelProperty("联系电话")
    private String mobile;

    @ApiModelProperty("职位")
    private String employeePost;

    @ApiModelProperty("性别")
    private Integer gender;

    @ApiModelProperty("'年龄'")
    private Integer age;

    @ApiModelProperty("生日")
    private String birthDay;

    @ApiModelProperty("工作状态，是否在职 1在职 2离职")
    private String employing;

    @ApiModelProperty("上班时间")
    private String workTime;

    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("序列号(同一组签到、签退记录的序列号相同)")
    @NotBlank(message="序列号不能为空")
    private String serialNumber;


    @ApiModelProperty("家庭住址")
    private String address;

    @ApiModelProperty("爱好")
    private String hobby;

    @ApiModelProperty("月成交金额")
    private BigDecimal monthSalesMoney;
}
