package com.zilue.module.wechat.sign.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.List;

@Data
public class AddOtcWeekReportDto implements Serializable {
    private String id;

    @ApiModelProperty("员工id")
    private String userId;

    @ApiModelProperty("员工code")
    private String systemUserId;

    @ApiModelProperty("机构code")
    private String businessUnitId;

    @ApiModelProperty("岗位code")
    private String postCode;

    @ApiModelProperty("开始时间")
    private String startDay;

    @ApiModelProperty("结束时间")
    private String endDay;

    @ApiModelProperty("周报内容")
    private String comment;

    @ApiModelProperty("周行为")
    private List<AddOtcWeekReportTaskDetailDto> otcWeekReportTaskDetailList;
}
