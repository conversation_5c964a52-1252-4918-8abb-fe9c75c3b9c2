package com.zilue.module.authority.service;

import com.github.yulichang.base.MPJBaseService;
import com.github.yulichang.extension.mapping.base.MPJDeepService;
import com.zilue.module.authority.dto.AddDataAuthDto;
import com.zilue.module.authority.dto.UpdateDataAuthDto;
import com.zilue.module.authority.entity.DataAuth;
import com.zilue.module.authority.vo.AuthObjectVo;

import java.util.List;

/**
 * <p>
 * 数据权限表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-27
 */
public interface IDataAuthService extends MPJBaseService<DataAuth>, MPJDeepService<DataAuth> {

    Boolean add(AddDataAuthDto dto);

    Boolean edit(UpdateDataAuthDto dto);

    List<AuthObjectVo> getAuthObjectsInfo(Long id);
}
