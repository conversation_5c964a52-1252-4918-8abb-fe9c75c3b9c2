package com.zilue.module.authority.service.impl;

import com.zilue.module.authority.entity.DataAuthConfig;
import com.zilue.module.authority.mapper.DataAuthConfigMapper;
import com.zilue.module.authority.service.IDataAuthConfigService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 数据权限自定义配置详情表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-27
 */
@Service
public class DataAuthConfigServiceImpl extends ServiceImpl<DataAuthConfigMapper, DataAuthConfig> implements IDataAuthConfigService {

}
