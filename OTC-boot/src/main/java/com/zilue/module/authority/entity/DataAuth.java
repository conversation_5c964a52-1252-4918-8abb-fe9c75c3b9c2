package com.zilue.module.authority.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.List;

import com.github.yulichang.annotation.EntityMapping;
import com.zilue.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 数据权限表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-27
 */
@TableName("xjr_data_auth")
@ApiModel(value = "DataAuth对象", description = "数据权限表")
@Data
@EqualsAndHashCode(callSuper = true)
public class DataAuth extends AuditEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("类型 0 角色 1 用户")
    private Integer authType;

    @ApiModelProperty("授权方式 0 简易 1 自定义")
    private Integer authMethod;

    @ApiModelProperty("实现范围 如果是简易模式 就会有这个数据 ")
    private Integer authScope;

    @ApiModelProperty("授权公式 如果是自定义模式 就会有这个数据")
    private String authFormula;

    @ApiModelProperty("备注")
    private String remark;


    @ApiModelProperty("授权对象")
    @TableField(exist = false)
    @EntityMapping(thisField = "id", joinField = "dataAuthId")
    private List<DataAuthRelation> authObjectList;

    @ApiModelProperty("授权配置 授权方式为1  就会有")
    @TableField(exist = false)
    @EntityMapping(thisField = "id", joinField = "dataAuthId")
    private List<DataAuthConfig> authConfigList;



}
