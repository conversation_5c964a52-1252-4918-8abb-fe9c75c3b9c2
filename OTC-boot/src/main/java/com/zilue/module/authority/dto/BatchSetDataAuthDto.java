package com.zilue.module.authority.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class BatchSetDataAuthDto {

    @ApiModelProperty("用户id集合")
    @NotNull(message = "用户id集合")
    private List<Long> userIdList;

    @ApiModelProperty("数据id集合")
    @NotNull(message = "数据id集合")
    private List<String> dataIdList;
}
