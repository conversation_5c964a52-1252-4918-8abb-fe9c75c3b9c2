package com.zilue.module.authority.vo;

import com.zilue.module.authority.entity.DataAuthConfig;
import com.zilue.module.authority.entity.DataAuthRelation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: zilue
 * @Date: 2023/2/27 15:00
 */
@Data
public class DataAuthVo {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("类型 0 角色 1 用户")
    private Integer authType;

    @ApiModelProperty("授权方式 0 简易 1 自定义")
    private Integer authMethod;

    @ApiModelProperty("实现范围 如果是简易模式 就会有这个数据 ")
    private Integer authScope;

    @ApiModelProperty("授权公式 ")
    private String authFormula;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("授权对象")
    private List<DataAuthRelation> authObjectList;

    @ApiModelProperty("授权配置 授权方式为1  就会有")
    private List<DataAuthConfig> authConfigList;
}
