package com.zilue.module.authority.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 数据权限 对象类型关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-27
 */
@TableName("xjr_data_auth_relation")
@ApiModel(value = "DataAuthRelation对象", description = "数据权限 对象类型关联表")
@Data
public class DataAuthRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("data_auth 表id")
    private Long dataAuthId;

    @ApiModelProperty("对象id  如果data_auth 表中的类型是 角色  就是角色id  如果是用户 就是用户id")
    private Long objectId;


}
