package com.zilue.module.authority.service;

import com.zilue.module.authority.entity.DataAuthTableRelation;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 数据权限 与 表 关联关系表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-28
 */
public interface IDataAuthTableRelationService extends IService<DataAuthTableRelation> {

    Boolean saveDataAuthTableRelations(String tableName, List<Long> dataAuthIdList);
}
