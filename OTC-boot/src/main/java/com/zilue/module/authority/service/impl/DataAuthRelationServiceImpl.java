package com.zilue.module.authority.service.impl;

import com.zilue.module.authority.entity.DataAuthRelation;
import com.zilue.module.authority.mapper.DataAuthRelationMapper;
import com.zilue.module.authority.service.IDataAuthRelationService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 数据权限 对象类型关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-27
 */
@Service
public class DataAuthRelationServiceImpl extends ServiceImpl<DataAuthRelationMapper, DataAuthRelation> implements IDataAuthRelationService {

}
