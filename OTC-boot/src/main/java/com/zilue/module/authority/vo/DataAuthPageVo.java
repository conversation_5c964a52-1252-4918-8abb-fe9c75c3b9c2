package com.zilue.module.authority.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: zilue
 * @Date: 2023/2/27 14:56
 */
@Data
public class DataAuthPageVo {
    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("类型 0 角色 1 用户")
    private Integer authType;

    @ApiModelProperty("授权对象名字")
    private String authObjectName;

    @ApiModelProperty("授权方式 0 简易 1 自定义")
    private Integer authMethod;

    @ApiModelProperty("授权公式 ")
    private String authFormula;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("创建人")
    private String createUserName;

    @ApiModelProperty("创建时间")
    private LocalDateTime createDate;

    @ApiModelProperty("修改人")
    private String modifyUserName;

    @ApiModelProperty("修改时间")
    private LocalDateTime modifyDate;

}
