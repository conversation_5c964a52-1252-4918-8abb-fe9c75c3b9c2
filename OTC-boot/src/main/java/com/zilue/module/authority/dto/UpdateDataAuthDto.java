package com.zilue.module.authority.dto;

import com.zilue.module.authority.entity.DataAuthConfig;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: zilue
 * @Date: 2023/2/27 15:02
 */
@Data
public class UpdateDataAuthDto {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("名称")
    @NotBlank(message = "名称不能为空！")
    private String name;

    @ApiModelProperty("编码")
    @NotBlank(message = "编码不能为空！")
    private String code;

    @ApiModelProperty("类型 0 角色 1 用户")
    @NotNull(message = "类型不能为空！")
    private Integer authType;


    @ApiModelProperty("授权方式 0 简易 1 自定义")
    @NotNull(message = "类型不能为空！")
    private Integer authMethod;

    @ApiModelProperty("实现范围 如果是简易模式 就会有这个数据 ")
    private Integer authScope;

    @ApiModelProperty("授权公式 ")
    private String authFormula;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("授权对象 id 列表")
    private List<Long> objectIdList;

    @ApiModelProperty("授权配置 授权方式为1  就会有")
    private List<DataAuthConfig> authConfigList;
}
