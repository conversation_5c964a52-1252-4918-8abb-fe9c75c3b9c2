package com.zilue.module.organization.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class UserInfoVo {
    private static final long serialVersionUID = 1L;

    /**
     * 账户
     */
    private String id;
    /**
     * 账户
     */
    @ApiModelProperty("账户")
    private String userName;

    /**
     * 姓名
     */
    @ApiModelProperty("姓名")
    private String name;

    /**
     * 编号
     */
    @ApiModelProperty("编号")
    private String code;

    /**
     * 昵称
     */
    @ApiModelProperty("昵称")
    private String nickName;

    /**
     * 地址
     */
    @ApiModelProperty("地址")
    private String address;

    /**
     * 备注
     */
    private String remark;

    /**
     * 密码
     */
    private String password;

    /**
     * 性别
     */
    @ApiModelProperty("性别")
    private Integer gender;

    /**
     * 手机号
     */
    @ApiModelProperty("手机号")
    private String mobile;


    /**
     * 角色名称
     */
    @ApiModelProperty("角色名称")
    private String postName;

    /**
     * 头像
     */
    @ApiModelProperty("头像")
    private String avatar;

    /**
     * 邮箱
     */
    @ApiModelProperty("邮箱")
    private String email;


    /**
     * 所有部门信息
     */
    @ApiModelProperty("用户关联所有部门")
    private List<UserDeptVo> departments;

    /**
     * 角色
     */
    @ApiModelProperty("角色")
    private List<UserRoleVo> roles;

    /**
     * 所有岗位信息
     */
    @ApiModelProperty("所有岗位信息")
    private List<UserPostVo> posts;

    /**
     * 负责的部门信息
     */
    @ApiModelProperty("负责的部门信息")
    private List<UserDeptVo> chargeDepartments;

    @ApiModelProperty("EDP部门名称")
    private String businessUnitIdName;
}
