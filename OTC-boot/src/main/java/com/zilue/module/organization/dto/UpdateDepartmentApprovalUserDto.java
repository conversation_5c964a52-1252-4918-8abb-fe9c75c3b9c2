package com.zilue.module.organization.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/8 9:16
 */
@Data
public class UpdateDepartmentApprovalUserDto {
    private Long id;

    @ApiModelProperty("部门id")
    private Long departmentId;

    @ApiModelProperty("审批专员id")
    private Long approvalSpecialistId;

    @ApiModelProperty("用户id")
    private String userId;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("手机号码")
    private String phoneNumber;

    @ApiModelProperty("邮箱")
    private String emailCount;
}
