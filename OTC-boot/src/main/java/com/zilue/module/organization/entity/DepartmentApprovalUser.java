package com.zilue.module.organization.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.zilue.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 部门审批专员人员表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-07
 */
@TableName("xjr_department_approval_user")
@ApiModel(value = "DepartmentApprovalUser对象", description = "部门审批专员人员表")
@Data
public class DepartmentApprovalUser extends AuditEntity implements Serializable {

    private static final long serialVersionUID = 1L;

      private Long id;

      @ApiModelProperty("部门id")
      private Long departmentId;

      @ApiModelProperty("审批专员id")
      private Long approvalSpecialistId;

      @ApiModelProperty("用户id")
      private String userId;

      @ApiModelProperty("备注")
      private String remark;

      @ApiModelProperty("手机号码")
      private String phoneNumber;

      @ApiModelProperty("邮箱")
      private String emailCount;

}
