package com.zilue.module.organization.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zilue.common.enums.ProviderType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;

@TableName("otc_sms_template")
@ApiModel(value = "OtcSmsTemplate对象", description = "短信模版")
@Data
@EqualsAndHashCode(callSuper = false)
public class OtcSmsTemplate implements Serializable {
    @ApiModelProperty(value = "主键")
    protected Long id;

    /**
     * 供应商类型
     * #ProviderType{ALI:OK,阿里云短信;TENCENT:0,腾讯云短信;BAIDU:1000,百度云短信}
     */
    @ApiModelProperty(value = "供应商类型")
    private ProviderType providerType;

    /**
     * 应用ID
     */
    @ApiModelProperty(value = "应用ID")
    private String appId;

    /**
     * 应用密码
     */
    @ApiModelProperty(value = "应用密码")
    private String appSecret;

    /**
     * SMS服务域名
     * 百度、其他厂商会用
     */
    @ApiModelProperty(value = "SMS服务域名")
    private String url;

    /**
     * 模板编码
     * 用于api发送
     */
    @ApiModelProperty(value = "模板编码")
    private String customCode;

    /**
     * 模板名称
     */
    @ApiModelProperty(value = "模板名称")
    private String name;

    /**
     * 模板内容
     */
    @ApiModelProperty(value = "模板内容")
    private String content;

    /**
     * 模板参数
     */
    @ApiModelProperty(value = "模板参数")
    private String templateParams;

    /**
     * 模板CODE
     */
    @ApiModelProperty(value = "模板CODE")
    private String templateCode;

    /**
     * 模板签名
     */
    @ApiModelProperty(value = "模板签名名称")
    private String signName;

    /**
     * 模板描述
     */
    @ApiModelProperty(value = "模板描述")
    private String templateDescribe;
}
