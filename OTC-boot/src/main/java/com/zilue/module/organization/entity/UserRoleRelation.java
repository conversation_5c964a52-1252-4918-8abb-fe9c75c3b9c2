package com.zilue.module.organization.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 用户关联角色表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-02
 */
@TableName("xjr_user_role_relation")
@ApiModel(value = "UserRoleRelation对象", description = "用户关联角色表")
@Data
public class UserRoleRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("角色ID")
    private Long roleId;


}
