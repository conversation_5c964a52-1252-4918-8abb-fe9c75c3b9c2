package com.zilue.module.organization.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.zilue.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 角色
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-02
 */
@TableName("xjr_role")
@ApiModel(value = "Role对象", description = "角色")
@Data
@EqualsAndHashCode(callSuper = false)
public class Role extends AuditEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("名字")
    private String name;

    @ApiModelProperty("编码")
    private String code;

//    @ApiModelProperty("数据权限")
//    private Integer dataAuthType;

    @ApiModelProperty("排序号")
    private Integer sortCode;

    @ApiModelProperty("备注")
    private String remark;



}
