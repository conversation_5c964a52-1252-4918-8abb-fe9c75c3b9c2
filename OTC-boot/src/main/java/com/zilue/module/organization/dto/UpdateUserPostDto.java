package com.zilue.module.organization.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class UpdateUserPostDto {
    @NotNull(message = "岗位id不能为空")
    private Long postId;//岗位id

    private List<Long> userIds;//接收用户id的列表

    @NotNull(message = "部门id不能为空")
    private Long deptId;//部门id

    @ApiModelProperty("类型：0：人员选择，1：按组织架构选择")
    @NotNull(message = "类型不能为空")
    private Integer type;//

    private List<Long> departmentIds;//按组织架构选择人员的组织id
}
