package com.zilue.module.organization.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @title: UserVo
 * <AUTHOR>
 * @Date: 2022/4/4 17:10
 * @Version 1.0
 */
@Data
public class UserVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 账户
     */
    @ApiModelProperty("账户")
    private String userName;

    /**
     * 姓名
     */
    @ApiModelProperty("姓名")
    private String name;

    /**
     * 编号
     */
    @ApiModelProperty("编号")
    private String code;

    /**
     * 昵称
     */
    @ApiModelProperty("昵称")
    private String nickName;

    /**
     * 密码
     */
    @ApiModelProperty("密码")
    private String password;

    /**
     * 性别
     */
    @ApiModelProperty("性别")
    private Integer gender;

    /**
     * 手机号
     */
    @ApiModelProperty("手机号")
    private String mobile;

    /**
     * 角色Id
     */
    private String postId;

    /**
     * 头像
     */
    @ApiModelProperty("头像")
    private String avatar;

    /**
     * 邮箱
     */
    @ApiModelProperty("邮箱")
    private String email;

    /**
     * 地址
     */
    @ApiModelProperty("地址")
    private String address;

    /**
     * 排序码
     */
    private Integer sortCode;

    /**
     * 排序码
     */
    private String remark;

//    private String departmentId;

    private String departmentIds;

    private Integer enabledMark;

    private LocalDateTime createDate;

    private LocalDateTime modifyDate;


    @ApiModelProperty("生日")
    private LocalDateTime birthDate;
    @ApiModelProperty("是否开启密码验证")
    private Integer passwordAuthentication;
    @ApiModelProperty("身份证号")
    private String identityCardNumber;

    @ApiModelProperty("EDP用户ID")
    private String systemUserId;

    @ApiModelProperty("EDP部门ID")
    private String businessUnitId;

    @ApiModelProperty("EDP部门名称")
    private String businessUnitIdName;

    @ApiModelProperty("EDP职务")
    private String title;

    @ApiModelProperty("EDP用户类型")
    private String userType;

    @ApiModelProperty("EDP用户是否禁用 0在用 1停用")
    private String isDisabled;

    @ApiModelProperty("数据事业部标识 1otc ; 2招商代理")
    private String businessGroupType;

    /**
     * 角色
     */
    @ApiModelProperty("角色")
    private List<UserRoleVo> roles;

    /**
     * 所有岗位信息
     */
    @ApiModelProperty("岗位")
    private List<UserPostVo> posts;

    /**
     * 负责的部门信息
     */
    @ApiModelProperty("部门/辖区")
    private List<UserDeptVo> chargeDepartments;
}

