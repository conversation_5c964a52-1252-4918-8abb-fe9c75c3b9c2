package com.zilue.module.organization.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zilue.module.organization.dto.UserRoleInfoDto;
import com.zilue.module.organization.entity.UserPostRelation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface UserPostRelationMapper extends BaseMapper<UserPostRelation> {


    List<UserRoleInfoDto> getUserPostInfo(@Param("userIds") List<Long> userIds);
}
