package com.zilue.module.organization.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/11 9:39
 */
@Data
public class UserExportVo {

    @ApiModelProperty("账号")
    @ExcelProperty("账号(*)")
    private String userName;

    @ApiModelProperty("用户姓名")
    @ExcelProperty("用户姓名(*)")
    private String name;

    @ApiModelProperty("工号")
    @ExcelProperty("工号(*)")
    private String code;

    @ApiModelProperty("登录密码")
    @ExcelProperty("登录密码(*)")
    private String password;

    @NotNull(message = "所属部门不能为空！")
    @ExcelProperty("所属部门ids，使用,隔开(*)")
    @ApiModelProperty("所属部门ids,使用,隔开")
    private String departmentIds;

    @ApiModelProperty("性别")
    @ExcelProperty("性别(*)")
    private Integer gender;

    @ApiModelProperty("手机号码")
    @ExcelProperty("手机号码(*)")
    private String mobile;

    @ApiModelProperty("邮箱")
    @ExcelProperty("邮箱(*)")
    private String email;

    @ApiModelProperty("联系地址")
    @ExcelProperty("联系地址")
    private String address;

    @ApiModelProperty("排序码")
    @ExcelProperty("排序码")
    private Integer sortCode;

    @ApiModelProperty("备注")
    @ExcelProperty("备注")
    private String remark;

}
