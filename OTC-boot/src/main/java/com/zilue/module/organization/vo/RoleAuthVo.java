package com.zilue.module.organization.vo;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @title: RoleAuthVo
 * <AUTHOR>
 * @Date: 2023/4/10 16:25
 * @Version 1.0
 */
@Data
public class RoleAuthVo {

    private List<Long> menuIds;

    private List<Long> buttonIds;

    private List<Long> columnIds;

    private List<Long> formIds;

    public List<Long> getMenuIds() {
        if (this.menuIds == null) {
            this.menuIds = new ArrayList<>();
        }
        return this.menuIds;
    }

    public List<Long> getButtonIds() {
        if (this.buttonIds == null) {
            this.buttonIds = new ArrayList<>();
        }
        return this.buttonIds;
    }

    public List<Long> getColumnIds() {
        if (this.columnIds == null) {
            this.columnIds = new ArrayList<>();
        }
        return this.columnIds;
    }

    public List<Long> getFormIds() {
        if (this.formIds == null) {
            this.formIds = new ArrayList<>();
        }
        return this.formIds;
    }
}
