package com.zilue.module.organization.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/11 9:39
 */
@Data
public class DepartmentExportVo {

    @ApiModelProperty("组织名称")
    @NotNull(message = "组织名称不能为空！")
    @Length(min = 1,max = 20,message = "组织名称最少2个字符，最多20个字符！")
    @ExcelProperty("组织名称(*)")
    private String name;

    @ApiModelProperty("上级组织id")
    @ExcelProperty("上级组织id(*)")
    private Long parentId;

    @ApiModelProperty("组织编码")
    @NotNull(message = "组织编码不能为空！")
    @Length(min = 1,max = 10,message = "组织编码最少2个字符，最多10个字符！")
    @ExcelProperty("组织编码(*)")
    private String code;

    @ApiModelProperty("组织简称")
    @ExcelProperty("组织简称")
    private String shortName;

    @ApiModelProperty("是否启用")
    @ExcelProperty("是否启用(*)，0-禁用，1-启用")
    private Integer enabledMark;

    @ApiModelProperty("组织类别，1：公司，0：部门")
    @ExcelProperty("组织类别(*)，0-部门，1-公司")
    private Integer departmentType;

    @ApiModelProperty("排序号")
    private Integer sortCode;

    @ApiModelProperty("备注")
    @Length(max = 250,message = "备注最多50个字符！")
    private String remark;
}
