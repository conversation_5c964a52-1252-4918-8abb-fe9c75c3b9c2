package com.zilue.module.organization.consumer;

import cn.dev33.satoken.secure.SaSecureUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.rabbitmq.MqConstant;
import com.zilue.common.utils.JsonUtil;
import com.zilue.config.CommonPropertiesConfig;
import com.zilue.config.XjrSmsConfig;
import com.zilue.module.organization.entity.*;
import com.zilue.module.organization.service.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 定义接收者（可以定义N个接受者，消息会均匀的发送到N个接收者中）
 * <p>
 * RabbitMq接受者1
 * （@RabbitListener声明类上，一个类只能监听一个队列）
 *
 * @author: admin
 * @date: 2025/01/15
 */
@Slf4j
@Component
@AllArgsConstructor
public class UserConsumerListenerProcess {

    private final IDepartmentService departmentService;

    private final IUserPostRelationService userPostRelationService;

    private final IUserDeptRelationService userDeptRelationService;

    private final IPostService postService;

    private final IUserService userService;

    private final CommonPropertiesConfig propertiesConfig;

    @RabbitListener(bindings = {
            @QueueBinding(
                    value = @Queue,
                    exchange = @Exchange(type = "topic", name = MqConstant.TOPIC_OTC),
                    key = {MqConstant.ROUTING_KEY_USER}
            )
    })
    public void onMessage(String message) {
        if (StringUtils.isNotBlank(message)) {
            List<User> userList = JsonUtil.convertJsonToList(message, User.class);
            Map<String, User> userMap = userList.stream()
                    .collect(Collectors.toMap(User::getSystemUserId, user -> user));
            List<Department> departmentList = departmentService.list(Wrappers.lambdaQuery(Department.class).select(Department::getId, Department::getBusinessUnitId, Department::getNewMgrId));
            // 使用Collectors.toMap方法将List转换为Map
            Map<String, Department> departmentMap = departmentList.stream().collect(Collectors.toMap(Department::getBusinessUnitId, p -> p));
            List<Post> postList = postService.list(Wrappers.lambdaQuery(Post.class).select(Post::getId, Post::getName));
            // 使用Collectors.toMap方法将List转换为Map
            Map<String, Post> postMap = postList.stream().collect(Collectors.toMap(Post::getName, p -> p));
            List<User> existUsers = userService.list(Wrappers.lambdaQuery(User.class).select(User::getId, User::getSystemUserId));
            List<String> existSystemUserIds = existUsers.stream().map(User::getSystemUserId).distinct().collect(Collectors.toList());
            // 新增用户
            List<User> newUserList = userList.stream().filter(oo -> !existSystemUserIds.contains(oo.getSystemUserId())).distinct().collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(newUserList)) {
                dealNewUser(newUserList, departmentMap, postMap);
            }
            // 更新用户
            if(CollectionUtil.isNotEmpty(existUsers)) {
                existUsers.forEach(oo -> {
                    User user = userMap.get(oo.getSystemUserId());
                    if (Objects.nonNull(user)) {
                        BeanUtil.copyProperties(user, oo);
                    }
                });
                dealOldUser(existUsers, departmentMap);
            }
        }
    }

    /**
     * 更新用户的信息
     * @param userList
     * @param departmentMap
     */
    private void dealOldUser(List<User> userList, Map<String, Department> departmentMap) {
        for (User systemUser : userList) {
            if(Objects.nonNull(systemUser.getId())) {
                userService.updateById(systemUser);
                Long deptId = 1L;
                if(departmentMap.containsKey(systemUser.getBusinessUnitId())) {
                    deptId = departmentMap.get(systemUser.getBusinessUnitId()).getId();
                }
                userDeptRelationService.lambdaUpdate().set(UserDeptRelation::getDeptId, deptId).eq(UserDeptRelation::getUserId, systemUser.getId());
            }
        }
    }

    /**
     * 新增用的信息和关联的其他信息
     * @param userList
     * @param departmentMap
     * @param postMap
     */
    private void dealNewUser(List<User> userList, Map<String, Department> departmentMap, Map<String, Post> postMap) {
        List<UserDeptRelation> deptRelations = new ArrayList<>(userList.size());
        List<UserPostRelation> postRelations = new ArrayList<>(userList.size());
        XjrSmsConfig smsConfig = SpringUtil.getBean(XjrSmsConfig.class);//短信配置
        for (User systemUser : userList) {
            User user = BeanUtil.copyProperties(systemUser, User.class);
            String password = StringUtils.EMPTY;
            if(StringUtils.isNotBlank(user.getMobile())) {
                String phoneNumber = user.getMobile();
                user.setUserName(phoneNumber);
//                if(phoneNumber.length() >= 6) {
//                    password = phoneNumber.substring(phoneNumber.length() - 6);
//                }
            } else {
                user.setUserName(user.getCode());
            }
            password = propertiesConfig.getDefaultPassword();
            user.setPassword(SaSecureUtil.md5BySalt(password, GlobalConstant.SECRET_KEY));
            //插入user用户表
            userService.save(user);
            // 短信通知用户
//            if(StringUtils.isNotBlank(user.getMobile())) {
//                JSONObject templateParam = new JSONObject();
//                templateParam.put("account", user.getUserName());
//                templateParam.put("password", password);
//                String templateParamStr = templateParam.toJSONString();
//                userService.sendUserSms(user.getMobile(), templateParamStr, smsConfig.getRegisterTemplateId());
//            }

            //将用户所选部门保存到关联表中
            UserDeptRelation userDeptRelation = new UserDeptRelation();
            userDeptRelation.setUserId(user.getId());
            if (ObjectUtil.isNotEmpty(departmentMap.get(user.getBusinessUnitId()))) {
                userDeptRelation.setDeptId(departmentMap.get(user.getBusinessUnitId()).getId());
            } else {
                userDeptRelation.setDeptId(1l);
            }
            deptRelations.add(userDeptRelation);
            //将用户所选岗位保存到关联表中
            UserPostRelation userPostRelation = new UserPostRelation();
            userPostRelation.setUserId(user.getId());
            userPostRelation.setPostId(postMap.get("员工").getId());
            postRelations.add(userPostRelation);
        }
        if(CollectionUtil.isNotEmpty(deptRelations)) {
            userDeptRelationService.saveBatch(deptRelations);
        }
        if(CollectionUtil.isNotEmpty(postRelations)) {
            userPostRelationService.saveBatch(postRelations);
        }
    }
}
