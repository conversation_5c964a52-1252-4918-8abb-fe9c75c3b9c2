package com.zilue.module.organization.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/8 9:40
 */
@Data
public class DepartmentApprovalUserVo {
    private Long id;

    @ApiModelProperty("部门id")
    private Long departmentId;

    @ApiModelProperty("审批专员id")
    private Long approvalSpecialistId;

    @ApiModelProperty("审批专员名称")
    private String approvalSpecialistName;

    @ApiModelProperty("用户id")
    private String userId;

    @ApiModelProperty("用户名称")
    private String userName;

    @ApiModelProperty("手机号码")
    private String phoneNumber;

    @ApiModelProperty("邮箱")
    private String emailCount;
}
