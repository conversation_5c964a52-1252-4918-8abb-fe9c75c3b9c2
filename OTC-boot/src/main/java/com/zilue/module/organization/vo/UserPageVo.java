package com.zilue.module.organization.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @title: UserPageVo
 * <AUTHOR>
 * @Date: 2022/4/4 17:02
 * @Version 1.0
 */
@Data
public class UserPageVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;
    /**
     * 账户
     */
    private String userName;

    /**
     * 姓名
     */
    private String name;

    /**
     * 编号
     */
    private String code;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 密码
     */
    private String password;

    /**
     * 性别
     */
    private Integer gender;

    /**
     * 手机号
     */
    private String mobile;


    /**
     * 头像
     */
    private String avatar;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 地址
     */
    private String address;

    /**
     * 排序码
     */
    private Integer sortCode;

    /**
     * 排序码
     */
    private String remark;



    private Integer enabledMark;

    private LocalDateTime createDate;

    private LocalDateTime modifyDate;

    private String departmentName;

    /**
     * 对应企业微信用户id
     */
    private String wechatUserId;

    /**
     * 对应钉钉用户id
     */
    private String dingtalkUserId;

    //岗位名称
    private List<String> postNames;

    //角色名称
    private List<String> roleNames;

    @ApiModelProperty("EDP部门ID")
    private String businessUnitId;

    @ApiModelProperty("EDP部门名称")
    private String businessUnitIdName;


}