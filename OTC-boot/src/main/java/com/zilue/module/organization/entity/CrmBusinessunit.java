package com.zilue.module.organization.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@TableName("crm_businessunit")
@ApiModel(value = "CrmBusinessunit对象", description = "机构")
@Data
@EqualsAndHashCode(callSuper = false)
public class CrmBusinessunit {
    @ApiModelProperty("EDP部门id")
    private String businessUnitId;


    @ApiModelProperty("简称")
    private String newShortName;
    @ApiModelProperty("EDP部门全称")
    private String name;

    @ApiModelProperty("EDP上级部门id")
    private String parentBusinessUnitId;

    @ApiModelProperty("EDP上级部门全称")
    private String parentBusinessUnitIdName;

    @ApiModelProperty("EDP部门层级")
    private String newKyLevel;

    @ApiModelProperty("EDP部门经理id")
    private String newMgrId;

    @ApiModelProperty("EDP部门经理姓名")
    private String newMgrIdName;

    @ApiModelProperty("EDP部门类别")
    private String businessUnitType;

    @ApiModelProperty("EDP部门状态0在用 1停用")
    private String isDisabled;

    @ApiModelProperty("数据事业部标识 1otc 2招商代理")
    private String businessGroupType;
}
