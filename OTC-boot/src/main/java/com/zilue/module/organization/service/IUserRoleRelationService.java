package com.zilue.module.organization.service;

import com.zilue.module.organization.entity.UserRoleRelation;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 用户关联角色表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-02
 */
public interface IUserRoleRelationService extends IService<UserRoleRelation> {

    boolean addRoleUser(Long roleId, List<Long> userIds);
}
