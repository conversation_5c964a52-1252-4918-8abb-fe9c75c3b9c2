package com.zilue.module.organization.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * @title: UpdateUserDto
 * <AUTHOR>
 * @Date: 2023/4/4 17:14
 * @Version 1.0
 */
@Data
public class UpdateUserDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "主键不能为空！")
    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("账户")
    //@NotNull(message = "账号不能为空")
    //@Length(min = 3,max = 25,message = "账号最少3个字符，最多25个字符！")
    private String userName;

    @ApiModelProperty("姓名")
   // @NotNull(message = "姓名不能为空")
   // @Length(min = 2,max = 20,message = "姓名最少2个字符，最多20个字符！")
    private String name;

    @ApiModelProperty("编号")
    //@NotNull(message = "编号不能为空")
   // @Length(min = 2,max = 20,message = "编号最少2个字符，最多10个字符！")
    private String code;

    @ApiModelProperty("昵称")
    //@Length(max = 50,message = "昵称最多50个字符！")
    private String nickName;

    @ApiModelProperty("性别")
    //@NotNull(message = "性别必须选择")
    //@Range(min = -1,max = 2,message = "性别参数不正确！")
    private Integer gender = -1;

    @ApiModelProperty("手机号")
    //@NotNull(message = "手机不能为空")
    //@Pattern(regexp = "1[3-9][0-9]\\d{8}",message = "手机号格式不正确！")
    private String mobile;


    @ApiModelProperty("头像")
    private String avatar;

    @ApiModelProperty("邮箱")
   // @Email(message = "邮箱格式不正确！")
   // @Length(max = 60,message = "邮箱字符不能超过60字符！")
    private String email;

    @ApiModelProperty("地址")
   // @Length(max = 200,message = "地址字符不能超过200字符！")
    private String address;

    @ApiModelProperty("经度")
    private Double longitude;

    @ApiModelProperty("纬度")
    private Double latitude;

    @ApiModelProperty("排序码")
    private Integer sortCode;

    @ApiModelProperty("备注")
   // @Length(max = 255,message = "备注字符不能超过255字符！")
    private String remark;

   // @NotNull(message = "部门不能为空！")
    @ApiModelProperty("部门id")
    private String departmentIds;

    @ApiModelProperty("微信号码")
    private String wechatNumber;

    @ApiModelProperty("qq号码")
    private String qqNumber;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("生日")
    private Timestamp birthDate;

    @ApiModelProperty("是否开启密码验证")
    private Integer passwordAuthentication;

    @ApiModelProperty("电话号码")
    private String phoneNumber;

    @ApiModelProperty("身份证号")
    private String identityCardNumber;

    @ApiModelProperty("政治面貌-数据字典id")
    private Long politicsStatus;

    @ApiModelProperty("行政职务-数据字典id")
    private Long administrativePost;

    @ApiModelProperty("行政职级-数据字典id")
    private Long administrativeRank;

    @ApiModelProperty("用户密级-数据字典id")
    private Long secretLevel;

    @ApiModelProperty("职称等级-数据字典id")
    private Long professionalTitleGrade;

    @ApiModelProperty("技术职务-数据字典id")
    private Long technicalPosition;

    @ApiModelProperty("管理职务-数据字典id")
    private Long managerialPosition;

    @ApiModelProperty("职业技能-数据字典id")
    private Long vocationalSkill;

    @ApiModelProperty("所有角色信息")
    private List<Long> roleIds;

    @ApiModelProperty("所有岗位信息")
    private List<Long> postIds;

    @ApiModelProperty("负责的部门信息")
    private List<Long> chargeDepartmentIds;

    @ApiModelProperty("绑定ip")
    private String bindIp;
}
