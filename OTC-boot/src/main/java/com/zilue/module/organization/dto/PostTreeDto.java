package com.zilue.module.organization.dto;

import com.zilue.common.page.ListInput;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;
@Data
@EqualsAndHashCode(callSuper = true)
public class PostTreeDto extends ListInput {

    @Length(min = 1,max = 20,message = "机构名称最少2个字符，最多20个字符！")
    private String name;

    @Length(min = 1,max = 10,message = "机构编码最少2个字符，最多10个字符！")
    private String code;

    private Integer enabledMark;

    @ApiModelProperty("岗位所属组织id")
    private Long deptId;
}
