package com.zilue.module.organization.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zilue.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 机构
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-02
 */
@TableName("xjr_department")
@ApiModel(value = "Department对象", description = "机构")
@Data
@EqualsAndHashCode(callSuper = false)
public class Department extends AuditEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("组织部门名称-EDP部门简称")
    private String name;

    @ApiModelProperty("父组织部门ID")
    private Long parentId;

    @ApiModelProperty("组织编码")
    private String code;

    @ApiModelProperty("部门层级")
    private String hierarchy;

    @ApiModelProperty("排序号")
    private Integer sortCode;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("简称")
    private String shortName;

    @ApiModelProperty("管理人-用户id")
    private Long custodian;

    @ApiModelProperty("组织类别，1：公司，0：部门")
    private Integer departmentType;

    @ApiModelProperty("对应企业微信部门id")
    private Long wechatDeptId;

    @ApiModelProperty("对应钉钉部门id")
    private Long dingtalkDeptId;

    private String dingAgentId;

    private String dingAppKey;

    private String dingAppSecret;

    @ApiModelProperty("EDP部门id")
    private String businessUnitId;

    @ApiModelProperty("EDP部门全称")
    private String businessUnitName;

    @ApiModelProperty("EDP上级部门id")
    private String parentBusinessUnitId;

    @ApiModelProperty("EDP上级部门全称")
    private String parentBusinessUnitName;

    @ApiModelProperty("EDP部门层级")
    private String newKyLevel;

    @ApiModelProperty("EDP部门经理id")
    private String newMgrId;

    @ApiModelProperty("EDP部门经理姓名")
    private String newMgrIdName;

    @ApiModelProperty("EDP部门类别")
    private String businessUnitType;

    @ApiModelProperty("EDP部门状态0在用 1停用")
    private String isDisabled;

    @ApiModelProperty("数据事业部标识 1otc 2招商代理")
    private String businessGroupType;

}
