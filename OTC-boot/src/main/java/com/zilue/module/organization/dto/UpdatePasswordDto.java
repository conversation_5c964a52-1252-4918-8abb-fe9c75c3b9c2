package com.zilue.module.organization.dto;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Author: zilue
 * @Date:2022/3/28 16:43
 */
@Data
public class UpdatePasswordDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull
    @Length(min = 6,max = 32,message = "当前密码长度不得小于6个字符，不得大于32个字符！")
    private String oldPassword;

    @NotNull
    @Length(min = 6,max = 32,message = "新密码长度不得小于6个字符，不得大于32个字符！")
    private String newPassword;

    @NotNull
    @Length(min = 6,max = 32,message = "确认密码长度不得小于6个字符，不得大于32个字符！")
    private String confirmPassword;



}

