package com.zilue.module.organization.dto;

import com.zilue.common.page.PageInput;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

/**
 * @title: RolePageDto
 * <AUTHOR>
 * @Date: 2023/4/4 18:26
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RolePageDto extends PageInput {
    @Length(max = 20,message = "岗位名称不能大于10个字符！")
    private String name;

    @Length(max = 10,message = "岗位编码不能大于10个字符！")
    private String code;

    private Integer enabledMark;
}
