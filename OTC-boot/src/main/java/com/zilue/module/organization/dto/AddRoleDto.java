package com.zilue.module.organization.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @title: AddRoleDto
 * <AUTHOR>
 * @Date: 2023/4/4 18:27
 * @Version 1.0
 */
@Data
public class AddRoleDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "角色名不能为空")
    @Length(min = 1,max = 20,message = "角色名最多20个字符！")
    @ApiModelProperty("名字")
    private String name;

    @NotNull(message = "角色编码不能为空")
    @Length(min = 1,max = 10,message = "编码最多20个字符！")
    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("数据权限")
    private Integer dataAuthType;

    @ApiModelProperty("排序号")
    private Integer sortCode;

    @Length(max = 255,message = "备注最多20个字符！")
    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("启用状态")
    private Integer enabledMark;



}
