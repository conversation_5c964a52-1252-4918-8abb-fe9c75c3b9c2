package com.zilue.module.organization.mapper;

import com.zilue.module.organization.dto.UserRoleInfoDto;
import com.zilue.module.organization.entity.UserRoleRelation;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户关联角色表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-02
 */
@Mapper
public interface UserRoleRelationMapper extends BaseMapper<UserRoleRelation> {
    List<UserRoleInfoDto> getUserRoleInfo(@Param("userIds") List<Long> userIds);

}
