package com.zilue.module.organization.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zilue.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-02
 */
@TableName("xjr_user")
@ApiModel(value = "User对象", description = "用户")
@Data
@EqualsAndHashCode(callSuper = false)
public class User extends AuditEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("账户")
    private String userName;

    @ApiModelProperty("姓名(edp-FullName)")
    private String name;

    @ApiModelProperty("用户工号(edp-new_bm)")
    private String code;

    @ApiModelProperty("昵称")
    private String nickName;

    @ApiModelProperty("密码")
    private String password;

    @ApiModelProperty("性别 1男2女(edp-new_gender)")
    private Integer gender;

    @ApiModelProperty("手机号(edp-address1_telephone1)")
    private String mobile;

    @ApiModelProperty("角色Id")
    @TableField(exist = false)
    private Long postId;

    @ApiModelProperty("头像")
    private String avatar;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("排序码")
    private Integer sortCode;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("部门")
    @TableField(exist = false)
    private Long departmentId;

    @ApiModelProperty("出生日期")
    private LocalDateTime birthDate;

    @ApiModelProperty("对应企业微信用户id")
    private String wechatUserId;

    @ApiModelProperty("对应钉钉用户id")
    private String dingtalkUserId;

    @ApiModelProperty("是否开启密码验证")
    private Integer passwordAuthentication;

    @ApiModelProperty("身份证号(edp-new_sfzh)")
    private String identityCardNumber;

    @ApiModelProperty("对应微信公众号用户的openId")
    private String wechatOpenId;

    @ApiModelProperty("EDP用户ID")
    private String systemUserId;

    @ApiModelProperty("EDP部门ID")
    private String businessUnitId;

    @ApiModelProperty("EDP部门名称")
    private String businessUnitIdName;

    @ApiModelProperty("EDP职务")
    private String title;

    @ApiModelProperty("EDP用户类型")
    private String userType;

    @ApiModelProperty("EDP用户是否禁用 0在用 1停用")
    private Integer isDisabled;

    @ApiModelProperty("数据事业部标识 1otc ; 2招商代理")
    private Integer businessGroupType;

}
