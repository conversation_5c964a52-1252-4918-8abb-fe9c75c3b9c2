package com.zilue.module.organization.dto;

import com.zilue.common.page.PageInput;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

/**
 * @title: DepartmentPageDto
 * <AUTHOR>
 * @Date: 2023/4/4 16:35
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DepartmentPageDto extends PageInput {

    @Length(min = 1,max = 20,message = "机构名称最少2个字符，最多20个字符！")
    private String name;

    @Length(min = 1,max = 10,message = "机构编码最少2个字符，最多10个字符！")
    private String code;

    private Integer enabledMark;
}
