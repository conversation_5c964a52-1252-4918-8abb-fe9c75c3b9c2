package com.zilue.module.organization.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(value = "Department与用户关联对象", description = "机构")
@Data
@EqualsAndHashCode(callSuper = false)
public class DepartmentUserRelVo {

    @ApiModelProperty("组织部门名称-EDP部门简称")
    private String name;

    @ApiModelProperty("组织编码")
    private String code;

    @ApiModelProperty("部门层级")
    private String hierarchy;

    @ApiModelProperty("父组织部门ID")
    private Long parentId;

    @ApiModelProperty("用户ID")
    private Long userId;

}
