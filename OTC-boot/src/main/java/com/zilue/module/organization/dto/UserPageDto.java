package com.zilue.module.organization.dto;

import com.zilue.common.page.PageInput;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

/**
 * @title: UserPageDto
 * <AUTHOR>
 * @Date: 2023/4/4 17:06
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UserPageDto extends PageInput {

    private Long departmentId;

    @Length(max = 20, message = "用户名长度不能超过20")
    private String userName;

    @Length(max = 20, message = "姓名长度不能超过20")
    private String name;

    @Length(max = 10, message = "编码长度不能超过10")
    private String code;

    @Length(max = 20, message = "手机号长度不能超过20")
    private String mobile;

    private String roleId;

    //锁定状态 0 锁定 1 正常
    private String enabledMark;
}
