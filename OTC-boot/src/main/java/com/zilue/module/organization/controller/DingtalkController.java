package com.zilue.module.organization.controller;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zilue.common.annotation.XjrLog;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.enums.EnabledMark;
import com.zilue.common.model.result.R;
import com.zilue.common.page.PageOutput;
import com.zilue.common.utils.ListUtils;
import com.zilue.common.utils.RedisUtil;
import com.zilue.common.utils.TreeUtil;
import com.zilue.module.organization.dto.DingDeptSettingDto;
import com.zilue.module.organization.dto.UserPageDto;
import com.zilue.module.organization.dto.WeChatDepartPageDto;
import com.zilue.module.organization.entity.Department;
import com.zilue.module.organization.entity.User;
import com.zilue.module.organization.entity.UserDeptRelation;
import com.zilue.module.organization.service.DingtalkService;
import com.zilue.module.organization.service.IDepartmentService;
import com.zilue.module.organization.service.IUserDeptRelationService;
import com.zilue.module.organization.service.IUserService;
import com.zilue.module.organization.vo.DepartmentTreeVo;
import com.zilue.module.organization.vo.UserPageVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.CompletableFuture;

@RestController
@RequestMapping(value = GlobalConstant.ORGANIZATION_MODULE_PREFIX + "/ding-talk")
@Api(value = GlobalConstant.ORGANIZATION_MODULE_PREFIX + "/ding-talk", tags = "钉钉信息")
@AllArgsConstructor
public class DingtalkController {

    private final IUserService userService;

    private final IUserDeptRelationService userDeptRelationService;

    private final IDepartmentService departmentService;

    private DingtalkService dingtalkService;

    private RedisUtil redisUtil;

    @PutMapping("/sync-user/to-dingtalk")
    @ApiOperation(value="微信信息更新")
    public R syncUsersToDingTalk(@RequestParam Long departmentId) {

        dingtalkService.syncUsersToDingTalk(departmentId);
        CompletableFuture.runAsync(() -> {
            List<Department> depList = departmentService.list();
            redisUtil.set(GlobalConstant.DEP_CACHE_KEY, depList);

            List<UserDeptRelation> userDeptRelationList = userDeptRelationService.list();
            redisUtil.set(GlobalConstant.USER_DEPT_RELATION_CACHE_KEY, userDeptRelationList);
        });
        return R.ok();
    }

    @PutMapping("/sync-user/to-system")
    @ApiOperation(value="微信信息更新")
    public R syncUsersToSystem(@RequestParam Long departmentId) {

        dingtalkService.syncUsersToSystem(departmentId);
        CompletableFuture.runAsync(() -> {
            List<Department> depList = departmentService.list();
            redisUtil.set(GlobalConstant.DEP_CACHE_KEY, depList);

            List<UserDeptRelation> userDeptRelationList = userDeptRelationService.list();
            redisUtil.set(GlobalConstant.USER_DEPT_RELATION_CACHE_KEY, userDeptRelationList);
        });
        return R.ok();
    }

    @PutMapping("/sync-departments")
    @ApiOperation(value="微信信息更新")
    public R syncDepartments(@RequestParam Long departmentId) {

        dingtalkService.syncDepartments(departmentId);
        CompletableFuture.runAsync(() -> {
            List<User> userList = userService.list();
            redisUtil.set(GlobalConstant.USER_CACHE_KEY, userList);
        });
        return R.ok();
    }

    @GetMapping(value = "/page")
    @ApiOperation(value="获取员工分页信息")
    @XjrLog(value = "获取员工分页信息")
    public R page(UserPageDto dto){
        PageOutput<UserPageVo> page = userService.page(dto);
        if (dto.getDepartmentId() != null) {
            Department department = departmentService.getById(dto.getDepartmentId());
            for (UserPageVo userPageVo : page.getList()) {
                userPageVo.setDepartmentName(department.getName());
            }
        }
        return  R.ok(page);
    }

    @GetMapping(value = "/departments")
    @ApiOperation(value="获取部门分页信息")
    @XjrLog(value = "获取部门分页信息")
    public R page(WeChatDepartPageDto dto){
        List<Department> departmentList = departmentService.list(Wrappers.lambdaQuery(Department.class)
                .eq(Department::getEnabledMark, EnabledMark.ENABLED.getCode())
                .and(StrUtil.isNotEmpty(dto.getKeyword()),
                        wrapper -> wrapper.like(Department::getName, dto.getKeyword())
                                .or().like(Department::getCode, dto.getKeyword())));
        List<DepartmentTreeVo> departmentTreeVoList = TreeUtil.build(BeanUtil.copyToList(departmentList, DepartmentTreeVo.class));

        return  R.ok(ListUtils.Pager(dto.getSize(), dto.getLimit(), departmentTreeVoList));
    }

    @PutMapping(value = "/department")
    @ApiOperation(value = "设置钉钉公司秘钥配置")
    public R setDingTalkInfo(@RequestBody DingDeptSettingDto dto) {
        Department department = BeanUtil.toBean(dto, Department.class);
        return R.ok(departmentService.updateById(department));
    }
}
