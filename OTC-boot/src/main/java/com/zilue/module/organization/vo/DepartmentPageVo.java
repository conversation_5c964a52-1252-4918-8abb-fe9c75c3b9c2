package com.zilue.module.organization.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @title: DepartmentPageVo
 * <AUTHOR>
 * @Date: 2022/4/4 16:38
 * @Version 1.0
 */
@Data
public class DepartmentPageVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    @ApiModelProperty("机构名称")
    private String name;

    @ApiModelProperty("机构名称")
    private Long parentId;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("排序号")
    private Integer sortCode;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("组织类别，1：公司，0：部门")
    private Integer departmentType;

}