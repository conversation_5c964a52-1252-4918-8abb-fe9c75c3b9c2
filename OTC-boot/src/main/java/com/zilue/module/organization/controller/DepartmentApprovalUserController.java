package com.zilue.module.organization.controller;

import cn.hutool.core.bean.BeanUtil;
import com.zilue.common.annotation.XjrLog;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.model.result.R;
import com.zilue.module.organization.dto.UpdateDepartmentApprovalUserDto;
import com.zilue.module.organization.entity.DepartmentApprovalUser;
import com.zilue.module.organization.service.IDepartmentApprovalUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 部门审批专员人员表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-07
 */
@RestController
@RequestMapping(GlobalConstant.ORGANIZATION_MODULE_PREFIX +"/departmentApprovalUser")
@Api(value = GlobalConstant.ORGANIZATION_MODULE_PREFIX +"/departmentApprovalUser", tags = "部门审批专员人员")
@AllArgsConstructor
public class DepartmentApprovalUserController {
    private final IDepartmentApprovalUserService departmentApprovalUserService;


    @PutMapping
    @ApiOperation(value = "修改部门审批专员人员")
    @XjrLog(value = "修改部门审批专员人员")
    public R update(@Valid @RequestBody List<UpdateDepartmentApprovalUserDto> updateDepartmentApprovalUserDtos) {
        //新增或者编辑部门审批专员人员
        List<DepartmentApprovalUser> departmentApprovalUsers = BeanUtil.copyToList(updateDepartmentApprovalUserDtos, DepartmentApprovalUser.class);
        departmentApprovalUserService.saveOrUpdateBatch(departmentApprovalUsers);
        return R.ok();
    }


}
