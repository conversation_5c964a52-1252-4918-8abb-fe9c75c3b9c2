package com.zilue.module.organization.vo;

import com.zilue.common.model.tree.ITreeNode;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @title: DepartmentTreeVo
 * <AUTHOR>
 * @Date: 2022/4/5 21:47
 * @Version 1.0
 */
@Data
public class DepartmentTreeVo implements ITreeNode<DepartmentTreeVo,Long>, Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String name;

    private Long parentId;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("排序号")
    private Integer sortCode;

    @ApiModelProperty("备注")
    private String remark;

    private Integer enabledMark;

    private Boolean disabled;

    public Boolean getDisabled() {
        return this.enabledMark != null && this.enabledMark == 1;
    }

    private List<DepartmentTreeVo> children;


    @ApiModelProperty("组织类别，1：公司，0：部门")
    private Integer departmentType;

    @ApiModelProperty("对应企业微信部门id")
    private Long wechatDeptId;

    @ApiModelProperty("对应钉钉部门id")
    private Long dingtalkDeptId;

    private String dingAgentId;

    private String dingAppKey;

    private String dingAppSecret;
}
