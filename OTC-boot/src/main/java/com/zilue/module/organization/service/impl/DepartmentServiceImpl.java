package com.zilue.module.organization.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.enums.YesOrNoEnum;
import com.zilue.common.page.PageInput;
import com.zilue.common.utils.ListUtils;
import com.zilue.common.utils.RedisUtil;
import com.zilue.common.utils.TreeUtil;
import com.zilue.common.utils.VoToColumnUtil;
import com.zilue.module.organization.dto.DepartmentTreeDto;
import com.zilue.module.organization.entity.CrmBusinessunit;
import com.zilue.module.organization.entity.CrmSystemUser;
import com.zilue.module.organization.entity.Department;
import com.zilue.module.organization.mapper.CrmBusinessunitMapper;
import com.zilue.module.organization.mapper.CrmSystemUserMapper;
import com.zilue.module.organization.mapper.DepartmentMapper;
import com.zilue.module.organization.service.IDepartmentService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zilue.module.organization.vo.DepartmentExportVo;
import com.zilue.module.organization.vo.DepartmentTreeVo;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <p>
 * 机构 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-02
 */
@Service
@AllArgsConstructor
public class DepartmentServiceImpl extends ServiceImpl<DepartmentMapper, Department> implements IDepartmentService {

    private final DepartmentMapper departmentMapper;

    private final CrmBusinessunitMapper crmBusinessunitMapper;


    private final RedisUtil redisUtil;

    @Override
    public Object page(PageInput pageInput) {
        LambdaQueryWrapper<Department> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(pageInput.getKeyword()), Department::getName, pageInput.getKeyword())
                .select(Department.class, x -> VoToColumnUtil.fieldsToColumns(DepartmentTreeVo.class).contains(x.getProperty()));
        List<Department> departmentList = departmentMapper.selectList(queryWrapper);
        List<DepartmentTreeVo> build = TreeUtil.build(BeanUtil.copyToList(departmentList, DepartmentTreeVo.class));
        return ListUtils.Pager(pageInput.getSize(), pageInput.getLimit(), build);
    }

    /**
     * 获取所有的子集id
     *
     * @param deptIds
     * @param voList
     * @return
     */
    @Override
    public List<Long> getAllChildIds(List<Long> deptIds, List<DepartmentTreeVo> voList) {
        List<Long> ids = new ArrayList<>();
        for (Long deptId : deptIds) {
            if (voList.stream().anyMatch(x -> x.getParentId().equals(deptId))) {
                List<DepartmentTreeVo> child = voList.stream().filter(x -> x.getParentId().equals(deptId)).collect(Collectors.toList());
                List<Long> allChildId = child.stream().map(DepartmentTreeVo::getId).collect(Collectors.toList());
                List<Long> allChildId1 = getAllChildIds(allChildId, voList);
                ids.addAll(allChildId);
                ids.addAll(allChildId1);
            }
        }
        return ids;
    }


    @Override
    public void dealWithDepartment() {
        QueryWrapper<CrmBusinessunit> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("BusinessUnitID", "new_shortname", "Name", "new_mgrid", "new_mgridName", "businessunittype", "new_ky_level", "ParentBusinessUnitId", "ParentBusinessUnitIdName")
                .groupBy("BusinessUnitID").orderByDesc("CreatedDate"); // 假设User实体类对应的表有这些字段，需根据实际情况填写所有字段名
        //Object o1 = crmBusinessunitMapper.selectList(queryWrapper);
        List<CrmBusinessunit> crmBusinessunit1 = crmBusinessunitMapper.selectList(queryWrapper);
        // List<Department> departments = BeanUtil.copyToList(crmBusinessunit1, Department.class);
        Map<String, Long> idMap = new HashMap<>();
     /*   List<CrmBusinessunit> crmBusinessunit3 = crmBusinessunit1.stream().filter(x -> x.getNewKyLevel().equals("3")).collect(Collectors.toList());
         insertDepartment(Arrays.asList(crmBusinessunit3.get(0)), "3", idMap);*/
        List<CrmBusinessunit> crmBusinessunit4 = crmBusinessunit1.stream().filter(x -> x.getNewKyLevel().equals("4")).collect(Collectors.toList());
        insertDepartment(crmBusinessunit4, "4", idMap);
        List<CrmBusinessunit> crmBusinessunit5 = crmBusinessunit1.stream().filter(x -> x.getNewKyLevel().equals("5")).collect(Collectors.toList());
        insertDepartment(crmBusinessunit5, "5", idMap);
        List<CrmBusinessunit> crmBusinessunit6 = crmBusinessunit1.stream().filter(x -> x.getNewKyLevel().equals("6")).collect(Collectors.toList());
        insertDepartment(crmBusinessunit6, "6", idMap);
        int s = 0;
    }

    private void insertDepartment(List<CrmBusinessunit> crmBusinessunits, String level, Map<String, Long> idMap) {
        for (CrmBusinessunit crmBusinessunit : crmBusinessunits) {
            Department department = BeanUtil.copyProperties(crmBusinessunit, Department.class);
            department.setName(crmBusinessunit.getNewShortName());
            department.setBusinessUnitName(crmBusinessunit.getName());
            department.setParentBusinessUnitName(crmBusinessunit.getParentBusinessUnitIdName());
            if (level.equals("4")) {
                department.setId(1l);
                department.setParentId(0l);
            } else {
                if (ObjectUtil.isNotEmpty(idMap.get(crmBusinessunit.getParentBusinessUnitId()))) {
                    department.setParentId(idMap.get(crmBusinessunit.getParentBusinessUnitId()));
                } else {
                    department.setParentId(1l);
                }
            }
            departmentMapper.insert(department);
            idMap.put(crmBusinessunit.getBusinessUnitId(), department.getId());
            if (level.equals("4")) {
                department.setHierarchy(department.getId().toString());
            } else {
                List<Department> list = redisUtil.get(GlobalConstant.DEP_CACHE_KEY, new TypeReference<List<Department>>() {
                });
                //获取父级的层级
                String hierarchy = list.stream().filter(x -> x.getId().equals(department.getParentId())).findFirst().orElse(new Department()).getHierarchy();
                //在父级的基础上添加层级
                department.setHierarchy(hierarchy + StringPool.DASH + department.getId());
            }
            departmentMapper.updateById(department);
            CompletableFuture.runAsync(() -> {
                LambdaQueryWrapper<Department> queryWrapper = new LambdaQueryWrapper<>();
                List<Department> list = departmentMapper.selectList(queryWrapper);
                redisUtil.set(GlobalConstant.DEP_CACHE_KEY, list);
            });
        }

    }


    @Override
    public List<DepartmentTreeVo> treeEnabled(DepartmentTreeDto dto) {
        boolean isLikeQuery = StrUtil.isNotBlank(dto.getKeyword());
        LambdaQueryWrapper<Department> wrapper = Wrappers.<Department>lambdaQuery()
                .eq(ObjectUtil.isNotEmpty(dto.getEnabledMark()), Department::getEnabledMark, dto.getEnabledMark())
                .orderByAsc(Department::getSortCode);
        if (isLikeQuery) {
            List<Department> hierarchyList = list(wrapper.clone()
                    .and(likeWrapper -> likeWrapper.like(Department::getName, dto.getKeyword())
                            .or().like(Department::getCode, dto.getKeyword())).select(Department::getHierarchy));
            if (CollectionUtils.isNotEmpty(hierarchyList)) {
                List<Long> idList = new ArrayList<>();
                for (Department hierarchyD : hierarchyList) {
                    String hierarchy = hierarchyD.getHierarchy();
                    if (StringUtils.isBlank(hierarchy)) continue;
                    String[] ids = StringUtils.split(hierarchy, StringPool.DASH);
                    idList.addAll(Arrays.stream(ids).map(Long::parseLong).collect(Collectors.toList()));
                }
                wrapper.in(Department::getId, idList);
            }
        }
        List<Department> list = list(wrapper
                .select(Department.class, x -> VoToColumnUtil.fieldsToColumns(DepartmentTreeVo.class).contains(x.getProperty())));

        List<DepartmentTreeVo> voList = BeanUtil.copyToList(list, DepartmentTreeVo.class);

        //所有需要过滤掉下级节点的deptIds
        List<Long> deptIds = new ArrayList<>();
        for (DepartmentTreeVo departmentTreeVo : voList) {
            if (departmentTreeVo.getEnabledMark() == YesOrNoEnum.NO.getCode()) {
                //如果有下级，下级进行过滤，不显示到前端去
                deptIds.add(departmentTreeVo.getId());
                departmentTreeVo.setDisabled(true);
            } else {
                departmentTreeVo.setDisabled(false);
            }
        }

        //所有需要过滤的ids的集合
        List<Long> allChildIds = getAllChildIds(deptIds, voList);

        //不是组织管理模块请求过滤父级,如果是组织管理isOrg不为空,不用过滤
        if (ObjectUtil.isEmpty(dto.getIsOrg())) {
            //将父级节点也过滤掉
            allChildIds.addAll(deptIds);
        }

        List<DepartmentTreeVo> vo = voList.stream().filter(u -> !allChildIds.contains(u.getId())).collect(Collectors.toList());

        List<DepartmentTreeVo> treeVoList = TreeUtil.build(vo);
        for (DepartmentTreeVo departmentTreeVo : treeVoList) {
            if (departmentTreeVo.getParentId() == 0) {
                departmentTreeVo.setParentId(null);
            }
        }
        return treeVoList;
    }
}
