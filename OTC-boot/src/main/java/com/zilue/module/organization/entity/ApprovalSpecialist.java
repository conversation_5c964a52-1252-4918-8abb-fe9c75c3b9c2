package com.zilue.module.organization.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.zilue.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 审批专员表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-07
 */
@TableName("xjr_approval_specialist")
@ApiModel(value = "ApprovalSpecialist对象", description = "审批专员表")
@Data
public class ApprovalSpecialist  extends AuditEntity implements Serializable {

    private static final long serialVersionUID = 1L;

      private Long id;

      @ApiModelProperty("名字")
      private String name;

      @ApiModelProperty("排序号")
      private Integer sortCode;

      @ApiModelProperty("备注")
      private String remark;
}
