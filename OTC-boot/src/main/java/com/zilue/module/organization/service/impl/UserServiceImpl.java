package com.zilue.module.organization.service.impl;

import cn.dev33.satoken.secure.SaSecureUtil;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.toolkit.MPJWrappers;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.enums.DeleteMark;
import com.zilue.common.enums.ProviderType;
import com.zilue.common.enums.TemplateCodeTypeEnum;
import com.zilue.common.exception.MyException;
import com.zilue.common.page.ConventPage;
import com.zilue.common.page.PageOutput;
import com.zilue.common.utils.RedisUtil;
import com.zilue.common.utils.VoToColumnUtil;
import com.zilue.module.business.taskManagement.entity.OtcSalesmanTask;
import com.zilue.module.organization.dto.*;
import com.zilue.module.organization.entity.*;
import com.zilue.module.organization.mapper.CrmSystemUserMapper;
import com.zilue.module.organization.mapper.UserMapper;
import com.zilue.module.organization.mapper.UserPostRelationMapper;
import com.zilue.module.organization.mapper.UserRoleRelationMapper;
import com.zilue.module.organization.service.*;
import com.zilue.module.organization.vo.*;
import com.zilue.module.workflow.entity.WorkflowExtra;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static ch.qos.logback.core.spi.ComponentTracker.DEFAULT_TIMEOUT;

/**
 * <p>
 * 用户 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-02
 */
@Service
@AllArgsConstructor
public class UserServiceImpl extends MPJBaseServiceImpl<UserMapper, User> implements IUserService {

    private final UserMapper userMapper;

    private final CrmSystemUserMapper crmSystemUserMapper;

    private final UserRoleRelationMapper userRoleRelationMapper;

    private final UserPostRelationMapper userPostRelationMapper;

    private final IUserDeptRelationService userDeptRelationService;

    private final IUserRoleRelationService userRoleRelationService;

    private final IUserPostRelationService userPostRelationService;

    private final IUserChargeDeptService userChargeDeptService;

    private final IDepartmentService departmentService;

    private final IPostService postService;

    private final IRoleService roleService;

    private final ISmsTaskService smsTaskService;

    private final RedisUtil redisUtil;
    private final String KACODE = "10004";//KA经理
    private final String REGIONALDIRECTORCODE = "10003";//地区主管
    private final String PROVINCIALMANAGERCODE = "10002";//省区经理
    private final String STAFFCODE = "10005";//员工
    private final String DIVISIONDIRECTORCODE = "10001";//事业部总监

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean add(AddUserDto dto) {
        long count = count(Wrappers.<User>query().lambda().eq(User::getUserName, dto.getUserName()).or().eq(User::getCode, dto.getCode()));
        if (count > 0) {
            throw new MyException("用户名称或编码已存在");
        }
        User user = BeanUtil.toBean(dto, User.class);

        //密码加密加盐存储到数据库
        user.setPassword(SaSecureUtil.md5BySalt(dto.getPassword(), GlobalConstant.SECRET_KEY));

        save(user);

        List<UserDeptRelation> userDeptRelationList = new ArrayList<>();
        if (StrUtil.isNotBlank(dto.getDepartmentIds())) {
            String allDeptIdStr = StrUtil.join(StringPool.COMMA, dto.getDepartmentIds());
            List<Long> departmentIds = Arrays.stream(allDeptIdStr.split(StringPool.COMMA)).map(Convert::toLong).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(departmentIds)) {
                for (Long deptId : departmentIds) {
                    //将用户所选部门保存到关联表中
                    UserDeptRelation userDeptRelation = new UserDeptRelation();
                    userDeptRelation.setUserId(user.getId());
                    userDeptRelation.setDeptId(deptId);
                    userDeptRelationList.add(userDeptRelation);
                }
            }
            userDeptRelationService.saveBatch(userDeptRelationList);
        }

        //将用户所选角色保存到关联表中
        List<UserRoleRelation> userRoleRelationList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(dto.getRoleIds()) && dto.getRoleIds().size() > 0) {
            for (Long id : dto.getRoleIds()) {
                UserRoleRelation userRoleRelation = new UserRoleRelation();
                userRoleRelation.setRoleId(id);
                userRoleRelation.setUserId(user.getId());
                userRoleRelationList.add(userRoleRelation);
            }
            userRoleRelationService.saveBatch(userRoleRelationList);
        }

        //将用户所选岗位保存到关联表中
        List<UserPostRelation> userPostRelationList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(dto.getPostIds()) && dto.getPostIds().size() > 0) {
            for (Long id : dto.getPostIds()) {
                UserPostRelation userPostRelation = new UserPostRelation();
                userPostRelation.setPostId(id);
                userPostRelation.setUserId(user.getId());
                userPostRelationList.add(userPostRelation);
            }
            userPostRelationService.saveBatch(userPostRelationList);
        }

        //将用户所负责的部门保存到关联表中
        List<UserChargeDept> userChargeDeptList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(dto.getChargeDepartmentIds()) && dto.getChargeDepartmentIds().size() > 0) {
            for (Long id : dto.getChargeDepartmentIds()) {
                UserChargeDept userChargeDept = new UserChargeDept();
                userChargeDept.setDeptId(id);
                userChargeDept.setUserId(user.getId());
                userChargeDeptList.add(userChargeDept);
            }
            userChargeDeptService.saveBatch(userChargeDeptList);
        }

        //异步更新用户表、用户部门表、用户角色表、用户岗位表数据
        CompletableFuture.runAsync(() -> {
            List<User> list = list();
            redisUtil.set(GlobalConstant.USER_CACHE_KEY, list);

            List<UserDeptRelation> deptRelationList = userDeptRelationService.list(Wrappers.lambdaQuery(UserDeptRelation.class));
            redisUtil.set(GlobalConstant.USER_DEPT_RELATION_CACHE_KEY, deptRelationList);

            List<UserRoleRelation> roleRelationList = userRoleRelationService.list(Wrappers.lambdaQuery(UserRoleRelation.class));
            redisUtil.set(GlobalConstant.USER_ROLE_RELATION_CACHE_KEY, roleRelationList);

            List<UserPostRelation> postRelationList = userPostRelationService.list(Wrappers.lambdaQuery(UserPostRelation.class));
            redisUtil.set(GlobalConstant.USER_POST_RELATION_CACHE_KEY, postRelationList);

        });

        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(UpdateUserDto dto) {
        long count = count(Wrappers.<User>query().lambda()
                .eq(User::getCode, dto.getCode())
                .ne(User::getId, dto.getId()));

        if (count > 0) {
            throw new MyException("用户名称或编码已存在");
        }

        User user = BeanUtil.toBean(dto, User.class);

        updateById(user);

        //先删除再新增
        userDeptRelationService.remove(Wrappers.<UserDeptRelation>query().lambda().eq(UserDeptRelation::getUserId, user.getId()));
        userRoleRelationService.remove(Wrappers.<UserRoleRelation>query().lambda().eq(UserRoleRelation::getUserId, user.getId()));
        userPostRelationService.remove(Wrappers.<UserPostRelation>query().lambda().eq(UserPostRelation::getUserId, user.getId()));
        userChargeDeptService.remove(Wrappers.<UserChargeDept>query().lambda().eq(UserChargeDept::getUserId, user.getId()));

        List<UserDeptRelation> userDeptRelationList = new ArrayList<>();
        if (StrUtil.isNotBlank(dto.getDepartmentIds())) {
            String allDeptIdStr = StrUtil.join(StringPool.COMMA, dto.getDepartmentIds());
            List<Long> departmentIds = Arrays.stream(allDeptIdStr.split(StringPool.COMMA)).map(Convert::toLong).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(departmentIds)) {
                for (Long deptId : departmentIds) {
                    //将用户所选部门保存到关联表中
                    UserDeptRelation userDeptRelation = new UserDeptRelation();
                    userDeptRelation.setUserId(user.getId());
                    userDeptRelation.setDeptId(deptId);
                    userDeptRelationList.add(userDeptRelation);
                }
            }
            userDeptRelationService.saveBatch(userDeptRelationList);
        }

        //将用户所选角色保存到关联表中
        List<UserRoleRelation> userRoleRelationList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(dto.getRoleIds()) && dto.getRoleIds().size() > 0) {
            for (Long id : dto.getRoleIds()) {
                UserRoleRelation userRoleRelation = new UserRoleRelation();
                userRoleRelation.setRoleId(id);
                userRoleRelation.setUserId(user.getId());
                userRoleRelationList.add(userRoleRelation);
            }
            userRoleRelationService.saveBatch(userRoleRelationList);
        }

        //将用户所选岗位保存到关联表中
        List<UserPostRelation> userPostRelationList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(dto.getPostIds()) && dto.getPostIds().size() > 0) {
            for (Long id : dto.getPostIds()) {
                UserPostRelation userPostRelation = new UserPostRelation();
                userPostRelation.setPostId(id);
                userPostRelation.setUserId(user.getId());
                userPostRelationList.add(userPostRelation);
            }
            userPostRelationService.saveBatch(userPostRelationList);
        }

        //将用户所负责的部门保存到关联表中
        List<UserChargeDept> userChargeDeptList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(dto.getChargeDepartmentIds()) && dto.getChargeDepartmentIds().size() > 0) {
            for (Long id : dto.getChargeDepartmentIds()) {
                UserChargeDept userChargeDept = new UserChargeDept();
                userChargeDept.setDeptId(id);
                userChargeDept.setUserId(user.getId());
                userChargeDeptList.add(userChargeDept);
            }
            userChargeDeptService.saveBatch(userChargeDeptList);
        }

        //异步更新用户表、用户部门表、用户角色表、用户岗位表数据
        CompletableFuture.runAsync(() -> {
            List<User> list = list();
            redisUtil.set(GlobalConstant.USER_CACHE_KEY, list);

            List<UserDeptRelation> deptRelationList = userDeptRelationService.list(Wrappers.lambdaQuery(UserDeptRelation.class));
            redisUtil.set(GlobalConstant.USER_DEPT_RELATION_CACHE_KEY, deptRelationList);

            List<UserRoleRelation> roleRelationList = userRoleRelationService.list(Wrappers.lambdaQuery(UserRoleRelation.class));
            redisUtil.set(GlobalConstant.USER_ROLE_RELATION_CACHE_KEY, roleRelationList);

            List<UserPostRelation> postRelationList = userPostRelationService.list(Wrappers.lambdaQuery(UserPostRelation.class));
            redisUtil.set(GlobalConstant.USER_POST_RELATION_CACHE_KEY, postRelationList);
        });
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteBatch(List<Long> ids) {
        //删除时需要同时删除用户部门关联表和用户角色关联表和用户岗位关系表和用户负责部门关联表的数据。
        this.removeBatchByIds(ids);
        //根据用户ids去缓存中查询到对应的三个表的数据
        List<UserDeptRelation> userDeptRelationList = redisUtil.get(GlobalConstant.USER_DEPT_RELATION_CACHE_KEY, new TypeReference<List<UserDeptRelation>>() {
        });
        List<UserPostRelation> userPostRelationList = redisUtil.get(GlobalConstant.USER_POST_RELATION_CACHE_KEY, new TypeReference<List<UserPostRelation>>() {
        });
        List<UserRoleRelation> userRoleRelationList = redisUtil.get(GlobalConstant.USER_ROLE_RELATION_CACHE_KEY, new TypeReference<List<UserRoleRelation>>() {
        });
        List<UserChargeDept> userChargeDeptList = userChargeDeptService.list();
        //拿用户ids进行过滤，如果存在，就删除
        List<Long> deptRelationIds = userDeptRelationList.stream().filter(u -> ids.contains(u.getUserId())).map(UserDeptRelation::getId).collect(Collectors.toList());
        List<Long> postRelationIds = userPostRelationList.stream().filter(u -> ids.contains(u.getUserId())).map(UserPostRelation::getId).collect(Collectors.toList());
        List<Long> roleRelationIds = userRoleRelationList.stream().filter(u -> ids.contains(u.getUserId())).map(UserRoleRelation::getId).collect(Collectors.toList());
        List<Long> userChargeIds = userChargeDeptList.stream().filter(u -> ids.contains(u.getUserId())).map(UserChargeDept::getId).collect(Collectors.toList());
        //调用四个表的删除
        if (CollectionUtil.isNotEmpty(deptRelationIds)) {
            userDeptRelationService.removeBatchByIds(deptRelationIds);
        }
        if (CollectionUtil.isNotEmpty(postRelationIds)) {
            userPostRelationService.removeBatchByIds(postRelationIds);
        }
        if (CollectionUtil.isNotEmpty(roleRelationIds)) {
            userRoleRelationService.removeBatchByIds(roleRelationIds);
        }
        if (CollectionUtil.isNotEmpty(userChargeIds)) {
            userChargeDeptService.removeBatchByIds(userChargeIds);
        }
        //更新缓存
        CompletableFuture.runAsync(() -> {
            List<User> list = this.list();
            redisUtil.set(GlobalConstant.USER_CACHE_KEY, list);

            List<UserDeptRelation> deptRelationList = userDeptRelationService.list(Wrappers.lambdaQuery(UserDeptRelation.class));
            redisUtil.set(GlobalConstant.USER_DEPT_RELATION_CACHE_KEY, deptRelationList);

            List<UserPostRelation> postRelationList = userPostRelationService.list(Wrappers.lambdaQuery(UserPostRelation.class));
            redisUtil.set(GlobalConstant.USER_POST_RELATION_CACHE_KEY, postRelationList);

            List<UserRoleRelation> roleRelationList = userRoleRelationService.list(Wrappers.lambdaQuery(UserRoleRelation.class));
            redisUtil.set(GlobalConstant.USER_ROLE_RELATION_CACHE_KEY, roleRelationList);
        });
        return Boolean.TRUE;
    }

    @Override
    public List<UserInfoVo> getUsersInfo(List<Long> ids) {

        if (CollectionUtils.isEmpty(ids) || ObjectUtil.isNull(ids.get(0))) {
            return new ArrayList<>();
        }

        List<User> list = userMapper.selectList(Wrappers.lambdaQuery(User.class)
                .in(User::getId, ids)
                .select(User.class, x -> VoToColumnUtil.fieldsToColumns(UserInfoVo.class).contains(x.getProperty())));

        return BeanUtil.copyToList(list, UserInfoVo.class);
    }

    @Override
    public PageOutput<WeChatPageVO> getPage(WeChatPageDto dto) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(dto.getKeyword()), User::getName, dto.getKeyword())
                .select(User.class, x -> VoToColumnUtil.fieldsToColumns(WeChatPageVO.class).contains(x.getProperty()));
        IPage<User> page = userMapper.selectPage(ConventPage.getPage(dto), queryWrapper);
        List<User> records = page.getRecords();
        for (User record : records) {
            record.setRemark("员工更新成功");
            record.setSortCode(1);
        }
        return ConventPage.getPageOutput(page, WeChatPageVO.class);

    }

    public PageOutput<UserPageVo> page(UserPageDto dto) {
        List<Long> deptIds = new ArrayList<>();
        if (ObjectUtil.isNotNull(dto.getDepartmentId())) {
            List<Department> list = redisUtil.get(GlobalConstant.DEP_CACHE_KEY, new TypeReference<List<Department>>() {
            });
            //当前部门的层级
            String hierarchy = list.stream().filter(x -> x.getId().equals(dto.getDepartmentId())).findFirst().orElse(new Department()).getHierarchy();
            if (StrUtil.isNotBlank(hierarchy)) {
                //层级里面包含当前部门层级的就是它的子集，如1-1，下面包含了1-1、1-1-2这种
                deptIds = list.stream().filter(x -> StrUtil.isNotBlank(x.getHierarchy()) && x.getHierarchy().contains(hierarchy)).map(Department::getId).collect(Collectors.toList());
            } else {
                //如果不存在层级就查询自己的数据
                deptIds.add(dto.getDepartmentId());
            }
        }

        //因为多表关联 会有多个表都使用了id字段，  所以必须专门指定主表的Id
        if (ObjectUtil.isNotNull(dto.getDepartmentId())) {//不为空联合查询
            IPage<UserPageVo> page = null;
            if (ObjectUtil.isNotNull(dto.getRoleId())) {
                page = this.selectJoinListPage(ConventPage.getPage(dto), UserPageVo.class,
                        MPJWrappers.<User>lambdaJoin()
                                .distinct()
                                .and(StrUtil.isNotBlank(dto.getKeyword()),
                                        x -> x.like(User::getCode, dto.getKeyword()).or().like(User::getName, dto.getKeyword()))
                                .eq(StrUtil.isNotBlank(dto.getRoleId()), Role::getId, dto.getRoleId())
                                .eq(StrUtil.isNotBlank(dto.getEnabledMark()), User::getDeleteMark, dto.getEnabledMark())
                                .like(StrUtil.isNotBlank(dto.getUserName()), User::getUserName, dto.getUserName())
                                .like(StrUtil.isNotBlank(dto.getCode()), User::getCode, dto.getCode())
                                .like(StrUtil.isNotBlank(dto.getName()), User::getName, dto.getName())
                                .like(StrUtil.isNotBlank(dto.getMobile()), User::getMobile, dto.getMobile())
                                .in(ObjectUtil.isNotNull(dto.getDepartmentId()), UserDeptRelation::getDeptId, deptIds)
                                .orderByDesc(User::getCreateDate)
                                .select(User::getId)
                                .select(User.class, x -> VoToColumnUtil.fieldsToColumns(UserPageVo.class).contains(x.getProperty()))
                                .leftJoin(UserDeptRelation.class, UserDeptRelation::getUserId, User::getId)
                                .leftJoin(UserRoleRelation.class, UserRoleRelation::getUserId, User::getId)
                                .leftJoin(Role.class, Role::getId, UserRoleRelation::getRoleId));
            } else {
                page = this.selectJoinListPage(ConventPage.getPage(dto), UserPageVo.class,
                        MPJWrappers.<User>lambdaJoin()
                                .distinct()
                                .and(StrUtil.isNotBlank(dto.getKeyword()),
                                        x -> x.like(User::getCode, dto.getKeyword()).or().like(User::getName, dto.getKeyword()))
                                .eq(StrUtil.isNotBlank(dto.getEnabledMark()), User::getDeleteMark, dto.getEnabledMark())
                                .like(StrUtil.isNotBlank(dto.getUserName()), User::getUserName, dto.getUserName())
                                .like(StrUtil.isNotBlank(dto.getCode()), User::getCode, dto.getCode())
                                .like(StrUtil.isNotBlank(dto.getName()), User::getName, dto.getName())
                                .like(StrUtil.isNotBlank(dto.getMobile()), User::getMobile, dto.getMobile())
                                .in(ObjectUtil.isNotNull(dto.getDepartmentId()), UserDeptRelation::getDeptId, deptIds)
                                .orderByDesc(User::getCreateDate)
                                .select(User::getId)
                                .select(User.class, x -> VoToColumnUtil.fieldsToColumns(UserPageVo.class).contains(x.getProperty()))
                                .leftJoin(UserDeptRelation.class, UserDeptRelation::getUserId, User::getId));
            }
            List<UserPageVo> userPageVos = page.getRecords();
            List<Long> userIds = userPageVos.stream().map(UserPageVo::getId).collect(Collectors.toList());
            List<UserRoleInfoDto> userPostInfoDtos = userPostRelationMapper.getUserPostInfo(userIds);
            Map<Long, List<UserRoleInfoDto>> userPostInfoMap = userPostInfoDtos.stream().collect(Collectors.groupingBy(UserRoleInfoDto::getUserId));
            List<UserRoleInfoDto> userRoleInfoDtos = userRoleRelationMapper.getUserRoleInfo(userIds);
            Map<Long, List<UserRoleInfoDto>> userRoleInfoMap = userRoleInfoDtos.stream().collect(Collectors.groupingBy(UserRoleInfoDto::getUserId));
            userPageVos.forEach(x -> x.setPostNames(null != userPostInfoMap.get(x.getId()) ? userPostInfoMap.get(x.getId()).stream().map(UserRoleInfoDto::getName).collect(Collectors.toList()) : new ArrayList<>()));
            userPageVos.forEach(x -> x.setRoleNames(null != userRoleInfoMap.get(x.getId()) ? userRoleInfoMap.get(x.getId()).stream().map(UserRoleInfoDto::getName).collect(Collectors.toList()) : new ArrayList<>()));
            return ConventPage.getPageOutput(page);
        } else {
            LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.like(StrUtil.isNotBlank(dto.getKeyword()), User::getName, dto.getKeyword())
                    .or(StrUtil.isNotBlank(dto.getKeyword()), x -> x.like(StrUtil.isNotBlank(dto.getKeyword()), User::getCode, dto.getKeyword()))
                    .like(StrUtil.isNotBlank(dto.getUserName()), User::getUserName, dto.getUserName())
                    .like(StrUtil.isNotBlank(dto.getCode()), User::getCode, dto.getCode())
                    .like(StrUtil.isNotBlank(dto.getName()), User::getName, dto.getName())
                    .like(StrUtil.isNotBlank(dto.getMobile()), User::getMobile, dto.getMobile())
                    .orderByDesc(User::getCreateDate)
                    .select(User.class, x -> VoToColumnUtil.fieldsToColumns(UserPageVo.class).contains(x.getProperty()));

            IPage<User> page = this.page(ConventPage.getPage(dto), queryWrapper);
            return ConventPage.getPageOutput(page, UserPageVo.class);
        }
    }

    @Override
    public PageOutput<UserDepartNameInfo> coachedUserList(UserListDto dto) {
        List<Long> deptIds = new ArrayList<>();
        List<Department> list = redisUtil.get(GlobalConstant.DEP_CACHE_KEY, new TypeReference<List<Department>>() {
        });
        //当前部门的层级
        String hierarchy = list.stream().filter(x -> x.getId().equals(dto.getDepartmentId())).findFirst().orElse(new Department()).getHierarchy();
        if (StrUtil.isNotBlank(hierarchy)) {
            //层级里面包含当前部门层级的就是它的子集，如1-1，下面包含了1-1、1-1-2这种
            deptIds = list.stream().filter(x -> StrUtil.isNotBlank(x.getHierarchy()) && x.getHierarchy().contains(hierarchy)).map(Department::getId).collect(Collectors.toList());
        } else {
            //如果不存在层级就查询自己的数据
            deptIds.add(dto.getDepartmentId());
        }
        //1.地区主管协防代表 2.KA经理协防地区主管
        Post post = postService.getById(dto.getPostId());
        if (!post.getCode().equals(KACODE) && !post.getCode().equals(REGIONALDIRECTORCODE)) {
            throw new MyException("该角色岗位 既不是KA经理 也不是地区主管无法协防");
        }
        boolean deptIdCondition=ObjectUtil.isNotNull(dto.getDepartmentId())&&!post.getCode().equals(KACODE);
        if (post.getCode().equals(KACODE)) {
            //如果是KA经理  则他访问的是地区主管
            LambdaQueryWrapper<Post> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Post::getCode, REGIONALDIRECTORCODE).eq(Post::getDeleteMark, DeleteMark.NODELETE.getCode());
            post = postService.getOne(queryWrapper);
        }else if (post.getCode().equals(REGIONALDIRECTORCODE)) {
            //如果是地区主管  则他访问的是员工
            LambdaQueryWrapper<Post> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Post::getCode, STAFFCODE).eq(Post::getDeleteMark, DeleteMark.NODELETE.getCode());
            post = postService.getOne(queryWrapper);
        }

        IPage<UserDepartNameInfo> page = this.selectJoinListPage(ConventPage.getPage(dto), UserDepartNameInfo.class,
                MPJWrappers.<User>lambdaJoin()
                        .distinct()
                        .like(StrUtil.isNotBlank(dto.getUserName()), User::getName, dto.getUserName())
                        .in(deptIdCondition, UserDeptRelation::getDeptId, deptIds)
                        .eq(ObjectUtil.isNotNull(post.getId()), UserPostRelation::getPostId, post.getId())
                        .orderByDesc(User::getCreateDate)
                        .select(User::getName)
                        .select(User::getCreateDate)
                        .select(Department::getHierarchy)
                        .select(UserPostRelation::getUserId)
                        .leftJoin(UserPostRelation.class, UserPostRelation::getUserId, User::getId)
                        .leftJoin(UserDeptRelation.class, UserDeptRelation::getUserId, User::getId)
                        .leftJoin(Department.class, Department::getId, UserDeptRelation::getDeptId)
        );
        List<UserDepartNameInfo> userDepartNameInfoList = page.getRecords();
        Map<Long, List<Department>> mapDepartment = list.stream().collect(Collectors.groupingBy(Department::getId));
        //名称转化
        if (CollectionUtil.isNotEmpty(userDepartNameInfoList)) {
            userDepartNameInfoList.stream().forEach(userDepartNameInfo -> {
                String[] deparIds = userDepartNameInfo.getHierarchy().split("-");
                StringBuffer departNames = new StringBuffer();
                for (int i = 0; i < deparIds.length; i++) {
                    if (mapDepartment.containsKey(Long.valueOf(deparIds[i]))) {
                        if (ObjectUtil.isNull(departNames)) {
                            departNames.append(mapDepartment.get(Long.valueOf(deparIds[i])).get(0).getName());
                        } else {
                            departNames.append("/").append(mapDepartment.get(Long.valueOf(deparIds[i])).get(0).getName());
                        }
                    }
                }
                userDepartNameInfo.setDepartNames(departNames.toString());
            });
        }
        page.setRecords(userDepartNameInfoList);

        return ConventPage.getPageOutput(page);
    }

    @Override
    public List<UserDepartNameInfo> coachedUserAll(UserListDto dto) {
            List<Long> deptIds = new ArrayList<>();
            List<Department> list = redisUtil.get(GlobalConstant.DEP_CACHE_KEY, new TypeReference<List<Department>>() {
            });
            //当前部门的层级
            String hierarchy = list.stream().filter(x -> x.getId().equals(dto.getDepartmentId())).findFirst().orElse(new Department()).getHierarchy();
            if (StrUtil.isNotBlank(hierarchy)) {
                //层级里面包含当前部门层级的就是它的子集，如1-1，下面包含了1-1、1-1-2这种
                deptIds = list.stream().filter(x -> StrUtil.isNotBlank(x.getHierarchy()) && x.getHierarchy().contains(hierarchy)).map(Department::getId).collect(Collectors.toList());
            } else {
                //如果不存在层级就查询自己的数据
                deptIds.add(dto.getDepartmentId());
            }
            //1.地区主管协防代表 2.省区经理
            Post post = postService.getById(dto.getPostId());
           /* if (!post.getCode().equals(KACODE) && !post.getCode().equals(REGIONALDIRECTORCODE)) {
                throw new MyException("该角色岗位 既不是KA经理 也不是地区主管无法协防");
            }*/
            List<Post> postList=new ArrayList<>();
            if (post.getCode().equals(REGIONALDIRECTORCODE)) {
            //如果是KA经理  则他访问的是地区主管
                LambdaQueryWrapper<Post> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(Post::getCode, STAFFCODE).eq(Post::getDeleteMark, DeleteMark.NODELETE.getCode());
                postList = postService.list(queryWrapper);
            }
            if (post.getCode().equals(PROVINCIALMANAGERCODE)) {
                //如果是省区经理 则他访问的是地区主管
                LambdaQueryWrapper<Post> queryWrapper = new LambdaQueryWrapper<>();
                List<String> listStr=new ArrayList<>();
                listStr.add(REGIONALDIRECTORCODE);
                listStr.add(STAFFCODE);
                queryWrapper.in(Post::getCode, listStr).eq(Post::getDeleteMark, DeleteMark.NODELETE.getCode());
                postList = postService.list(queryWrapper);
            }
            //因为多表关联 会有多个表都使用了id字段，  所以必须专门指定主表的Id
            List<UserDepartNameInfo> userDepartNameInfoList = this.selectJoinList(UserDepartNameInfo.class,
                    MPJWrappers.<User>lambdaJoin()
                            .distinct()
                            .like(StrUtil.isNotBlank(dto.getUserName()), User::getName, dto.getUserName())
                            .in(ObjectUtil.isNotNull(dto.getDepartmentId()), UserDeptRelation::getDeptId, deptIds)
                            .in(CollectionUtil.isNotEmpty(postList), UserPostRelation::getPostId, postList.stream().map(Post::getId).collect(Collectors.toList()))
                            .orderByDesc(User::getCreateDate)
                            .select(User::getName)
                            .select(UserPostRelation::getUserId)
                            .select(Department::getHierarchy)
                           // .select(User.class, x -> VoToColumnUtil.fieldsToColumns(UserDepartNameInfo.class).contains(x.getProperty()))
                            .leftJoin(UserPostRelation.class, UserPostRelation::getUserId, User::getId)
                            .leftJoin(UserDeptRelation.class, UserDeptRelation::getUserId, User::getId)
                            .leftJoin(Department.class, Department::getId, UserDeptRelation::getDeptId)
            );
            return userDepartNameInfoList;
    }

    /**
     * 根据id获取用户信息
     *
     * @param id
     * @return
     */
    @Override
    public UserVo getInfo(Long id) {
        User user = this.getById(id);
        if (user == null) {
            throw new RuntimeException("找不到此用户！");
        }
        UserInfoVo currentInfo = this.getCurrentInfo(user);
        UserVo userVo = BeanUtil.toBean(user, UserVo.class);
        List<Long> deptIds = userDeptRelationService.list(Wrappers.lambdaQuery(UserDeptRelation.class)
                        .eq(UserDeptRelation::getUserId, user.getId()))
                .stream().map(UserDeptRelation::getDeptId).collect(Collectors.toList());
        String allDeptIdStr = StrUtil.join(StringPool.COMMA, deptIds);
        userVo.setDepartmentIds(allDeptIdStr);
        userVo.setRoles(currentInfo.getRoles());
        userVo.setPosts(currentInfo.getPosts());
        userVo.setChargeDepartments(currentInfo.getChargeDepartments());
        return userVo;
    }

    @Override
    public UserInfoVo getCurrentInfo(User user) {

        List<Long> roleIds = userRoleRelationService.list(Wrappers.lambdaQuery(UserRoleRelation.class)
                        .eq(UserRoleRelation::getUserId, user.getId()))
                .stream().map(UserRoleRelation::getRoleId).collect(Collectors.toList());

        List<Long> deptIds = userDeptRelationService.list(Wrappers.lambdaQuery(UserDeptRelation.class)
                        .eq(UserDeptRelation::getUserId, user.getId()))
                .stream().map(UserDeptRelation::getDeptId).collect(Collectors.toList());

        List<Long> postIds = userPostRelationService.list(Wrappers.lambdaQuery(UserPostRelation.class)
                        .eq(UserPostRelation::getUserId, user.getId()))
                .stream().map(UserPostRelation::getPostId).collect(Collectors.toList());

        List<Long> chargeDeptIds = userChargeDeptService.list(Wrappers.lambdaQuery(UserChargeDept.class)
                        .eq(UserChargeDept::getUserId, user.getId()))
                .stream().map(UserChargeDept::getDeptId).collect(Collectors.toList());

        UserInfoVo vo = BeanUtil.toBean(user, UserInfoVo.class);
        if (StringUtils.isNotBlank(vo.getBusinessUnitIdName())) {
            String[] parts = vo.getBusinessUnitIdName().split(" ");
            String targetElement = "OTC事业部"; // 指定的元素
            List<String> partList = Arrays.asList(parts);
            int index = partList.indexOf(targetElement);
            String businessUnitIdName = "";
            if (index != -1) { // 如果找到目标元素
                businessUnitIdName = String.join("/", partList.subList(index, partList.size()));
            } else {
                businessUnitIdName = String.join("/", partList);
            }
            vo.setBusinessUnitIdName(businessUnitIdName);
        }
        if (roleIds.size() > 0) {

            List<Role> list = roleService.list(Wrappers.lambdaQuery(Role.class).in(Role::getId, roleIds).orderByAsc(Role::getSortCode));
            List<UserRoleVo> userRoleVoList = BeanUtil.copyToList(list, UserRoleVo.class);
            vo.setRoles(userRoleVoList);

        }

        if (deptIds.size() > 0) {
            List<Department> list = departmentService.list(Wrappers.lambdaQuery(Department.class).in(Department::getId, deptIds).orderByAsc(Department::getSortCode));
            List<UserDeptVo> userDeptVoList = BeanUtil.copyToList(list, UserDeptVo.class);
            vo.setDepartments(userDeptVoList);

        }

        if (postIds.size() > 0) {
            List<Post> list = postService.list(Wrappers.lambdaQuery(Post.class).in(Post::getId, postIds).orderByAsc(Post::getSortCode));
            List<UserPostVo> userPostVoList = BeanUtil.copyToList(list, UserPostVo.class);
            vo.setPosts(userPostVoList);

        }

        if (chargeDeptIds.size() > 0) {
            List<Department> list = departmentService.list(Wrappers.lambdaQuery(Department.class).in(Department::getId, chargeDeptIds).orderByAsc(Department::getSortCode));
            List<UserDeptVo> userDeptVoList = BeanUtil.copyToList(list, UserDeptVo.class);
            vo.setChargeDepartments(userDeptVoList);
        }
        return vo;
    }

    @Override
    public Boolean importData(MultipartFile file) throws IOException {
        List<UserExportVo> userExportVos = EasyExcel.read(file.getInputStream()).head(UserExportVo.class).sheet().doReadSync();
        List<User> users = BeanUtil.copyToList(userExportVos, User.class);
        //获取所有的字典项
        List<User> list = list();
        for (User user : users) {
            List<User> count = list.stream().filter(x -> x.getCode().equals(user.getCode())
                    || x.getName().equals(user.getName())).collect(Collectors.toList());
            if (count.size() > 0) {
                throw new MyException("导入的项目名或者编码已经存在！项目名称为：" + user.getName());
            }
        }
        //批量保存用户信息
        boolean isSuccess = saveBatch(users);
        //因为不想在循环里面写插入语句，所以需重新获取所有的用户项，主要获取创建人为登录人，并且创建时间为前后五分钟内的数据
        LocalDateTime nowTime = LocalDateTime.now();
        List<User> saveUserData = list(Wrappers.<User>lambdaQuery().eq(User::getCreateUserId, StpUtil.getLoginIdAsLong())
                .between(User::getCreateDate, nowTime.minusMinutes(5), nowTime.plusMinutes(5)));
        List<UserDeptRelation> userDeptRelationList = new ArrayList<>();
        for (UserExportVo userExportVo : userExportVos) {//保存用户部门数据
            //找到之前保存的用户信息的数据，需要使用到userId
            Optional<User> first = saveUserData.stream().filter(x -> x.getName().equals(userExportVo.getName()) && x.getCode().equals(userExportVo.getCode())).findFirst();
            if (first.isPresent()) {
                Long userId = first.get().getId();
                if (StrUtil.isNotBlank(userExportVo.getDepartmentIds())) {
                    //分割deptId
                    List<Long> deptIds = Arrays.stream(userExportVo.getDepartmentIds().split(StringPool.COMMA)).map(Convert::toLong).collect(Collectors.toList());
                    for (Long deptId : deptIds) {
                        UserDeptRelation userDeptRelation = new UserDeptRelation();
                        userDeptRelation.setDeptId(deptId);
                        userDeptRelation.setUserId(userId);
                        userDeptRelationList.add(userDeptRelation);
                    }
                }
            }
        }
        //批量保存用户部门数据
        userDeptRelationService.saveBatch(userDeptRelationList);
        return isSuccess;
    }

    @Override
    @Transactional
    public void dealWithUser() {
        QueryWrapper<CrmSystemUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("SystemUserId ,new_bm as code,FullName as name,BusinessUnitId,BusinessUnitIdName," +
                "Title,address1_telephone1 as mobile,new_sfzh as identityCardNumber,new_gender as gender,new_usertypedictionaryidName as userType,InternalEMailAddress as email,IsDisabled,businessgrouptype ,rowId")
                .groupBy("SystemUserId").orderByDesc("CreatedDate"); // 假设User实体类对应的表有这些字段，需根据实际情况填写所有字段名
        //Object o1 = crmBusinessunitMapper.selectList(queryWrapper);
        List<CrmSystemUser> crmSystemUser = crmSystemUserMapper.selectList(queryWrapper);
        List<Department> departmentList = departmentService.list(Wrappers.lambdaQuery(Department.class).select(Department::getId, Department::getBusinessUnitId, Department::getNewMgrId).groupBy(Department::getBusinessUnitId));
        // 使用Collectors.toMap方法将List转换为Map
        Map<String, Department> departmentMap = departmentList.stream().collect(Collectors.toMap(Department::getBusinessUnitId, p -> p));
        List<Post> postList = postService.list(Wrappers.lambdaQuery(Post.class).select(Post::getId, Post::getName));
        // 使用Collectors.toMap方法将List转换为Map
        Map<String, Post> postMap = postList.stream().collect(Collectors.toMap(Post::getName, p -> p));
        Map<String, Long> userBusinessMap = new HashMap<>();
        for (CrmSystemUser systemUser : crmSystemUser) {
            User user = BeanUtil.copyProperties(systemUser, User.class);
            user.setUserName(systemUser.getMobile());
            user.setPassword("04f221a5b4f013702ab4694a64fd0af9");
            //插入user用户表
            int s = userMapper.insert(user);
            userBusinessMap.put(user.getSystemUserId(), user.getId());
            //将用户所选部门保存到关联表中
            UserDeptRelation userDeptRelation = new UserDeptRelation();
            userDeptRelation.setUserId(user.getId());
            if (ObjectUtil.isNotEmpty(departmentMap.get(user.getBusinessUnitId()))) {
                userDeptRelation.setDeptId(departmentMap.get(user.getBusinessUnitId()).getId());
            } else {
                userDeptRelation.setDeptId(1l);
            }
            userDeptRelationService.save(userDeptRelation);


            //将用户所选岗位保存到关联表中
            UserPostRelation userPostRelation = new UserPostRelation();
            userPostRelation.setUserId(user.getId());
            userPostRelation.setPostId(postMap.get("员工").getId());
            userPostRelationService.save(userPostRelation);
        }
        //将用户所负责的部门保存到关联表中
        List<UserChargeDept> userChargeDeptList = new ArrayList<>();
        for (Department department : departmentList) {
            if (ObjectUtil.isNotEmpty(department.getNewMgrId())) {
                UserChargeDept userChargeDept = new UserChargeDept();
                userChargeDept.setDeptId(department.getId());
                userChargeDept.setUserId(null != userBusinessMap.get(department.getNewMgrId()) ? userBusinessMap.get(department.getNewMgrId()) : 1000000000000000000l);
                userChargeDeptList.add(userChargeDept);
            }
        }
        userChargeDeptService.saveBatch(userChargeDeptList);
        //异步更新用户表、用户部门表、用户角色表、用户岗位表数据
        CompletableFuture.runAsync(() -> {
            List<User> list = list();
            redisUtil.set(GlobalConstant.USER_CACHE_KEY, list);

            List<UserDeptRelation> deptRelationList = userDeptRelationService.list(Wrappers.lambdaQuery(UserDeptRelation.class));
            redisUtil.set(GlobalConstant.USER_DEPT_RELATION_CACHE_KEY, deptRelationList);

            //List<UserRoleRelation> roleRelationList = userRoleRelationService.list(Wrappers.lambdaQuery(UserRoleRelation.class));
            //redisUtil.set(GlobalConstant.USER_ROLE_RELATION_CACHE_KEY, roleRelationList);

            List<UserPostRelation> postRelationList = userPostRelationService.list(Wrappers.lambdaQuery(UserPostRelation.class));
            redisUtil.set(GlobalConstant.USER_POST_RELATION_CACHE_KEY, postRelationList);

        });
        int s = 0;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUserPostAndRole(UpdateUserDto dto) {
        long count = count(Wrappers.<User>query().lambda()
                .eq(User::getCode, dto.getCode())
                .ne(User::getId, dto.getId()));

        if (count > 0) {
            throw new MyException("用户名称或编码已存在");
        }

        User user = BeanUtil.toBean(dto, User.class);

        //先删除再新增
        userRoleRelationService.remove(Wrappers.<UserRoleRelation>query().lambda().eq(UserRoleRelation::getUserId, user.getId()));
        userPostRelationService.remove(Wrappers.<UserPostRelation>query().lambda().eq(UserPostRelation::getUserId, user.getId()));

        //将用户所选角色保存到关联表中
        List<UserRoleRelation> userRoleRelationList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(dto.getRoleIds()) && dto.getRoleIds().size() > 0) {
            for (Long id : dto.getRoleIds()) {
                UserRoleRelation userRoleRelation = new UserRoleRelation();
                userRoleRelation.setRoleId(id);
                userRoleRelation.setUserId(user.getId());
                userRoleRelationList.add(userRoleRelation);
            }
            userRoleRelationService.saveBatch(userRoleRelationList);
        }

        //将用户所选岗位保存到关联表中
        List<UserPostRelation> userPostRelationList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(dto.getPostIds()) && dto.getPostIds().size() > 0) {
            for (Long id : dto.getPostIds()) {
                UserPostRelation userPostRelation = new UserPostRelation();
                userPostRelation.setPostId(id);
                userPostRelation.setUserId(user.getId());
                userPostRelationList.add(userPostRelation);
            }
            userPostRelationService.saveBatch(userPostRelationList);
        }


        //异步更新用户表、用户部门表、用户角色表、用户岗位表数据
        CompletableFuture.runAsync(() -> {
            List<User> list = list();
            redisUtil.set(GlobalConstant.USER_CACHE_KEY, list);

            List<UserRoleRelation> roleRelationList = userRoleRelationService.list(Wrappers.lambdaQuery(UserRoleRelation.class));
            redisUtil.set(GlobalConstant.USER_ROLE_RELATION_CACHE_KEY, roleRelationList);

            List<UserPostRelation> postRelationList = userPostRelationService.list(Wrappers.lambdaQuery(UserPostRelation.class));
            redisUtil.set(GlobalConstant.USER_POST_RELATION_CACHE_KEY, postRelationList);
        });
        return Boolean.TRUE;
    }

    @Override
    public void sendMessage(SmsSendTaskDTO smsTaskDTO) {
        OtcSmsTemplate template = smsTaskService.getOne(Wrappers.<OtcSmsTemplate>lambdaQuery()
                .eq(OtcSmsTemplate::getCustomCode, "ACCOUNT_PASSWORD"));
        OtcSmsTask smsTask = BeanUtil.toBean(smsTaskDTO, OtcSmsTask.class);
        //smsTask.setSourceType(SourceType.SERVICE);
        smsTask.setTemplateParams(smsTaskDTO.getTemplateParam().toString());
        saveTask(smsTask, smsTaskDTO.getCustomCode(), template);

    }

    @Override
    public void sendUserSms(String userPhone, String templateParam, String templateId) {
        OtcSmsTemplate template = smsTaskService.getById(templateId);
        //发送
        send(SmsDO.builder()
                .phone(userPhone).appId(template.getAppId()).appSecret(template.getAppSecret())
                .signName(template.getSignName()).templateCode(template.getTemplateCode()).endPoint(template.getUrl()).templateParams(templateParam).content(template.getContent())
                .build());
    }

    private void saveTask(OtcSmsTask smsTask, TemplateCodeTypeEnum type, OtcSmsTemplate otcTemplate) {
        validAndInit(smsTask, type, otcTemplate);
        sendMsg(smsTask, otcTemplate);
    }


    public void validAndInit(OtcSmsTask smsTask, TemplateCodeTypeEnum type, OtcSmsTemplate otcTemplate) {
        OtcSmsTemplate template = null;
        if (type != null) {
            template = otcTemplate;

            smsTask.setTemplateId(template.getId());

            if (StrUtil.isEmpty(smsTask.getTopic())) {
                smsTask.setTopic(template.getSignName());
            }
        }


        String templateParams = smsTask.getTemplateParams();
        JSONObject obj = JSONObject.parseObject(templateParams, Feature.OrderedField);


        if (StrUtil.isEmpty(smsTask.getContent())) {
            smsTask.setContent(content(template.getProviderType(), template.getContent(), smsTask.getTemplateParams()));
        }

    }

    private static String content(ProviderType providerType, String templateContent, String templateParams) {
        try {
            if (StrUtil.isNotEmpty(templateParams)) {
                JSONObject param = JSONObject.parseObject(templateParams, Feature.OrderedField);
                return processTemplate(templateContent, providerType.getRegex(), param);
            }
            return "";
        } catch (Exception e) {
            return "";
        }
    }

    private static String processTemplate(String template, String regex, JSONObject params) {
        StringBuffer sb = new StringBuffer();
        Matcher m = Pattern.compile(regex).matcher(template);
        while (m.find()) {
            String key = m.group(1);
            String value = params.getString(key);
            value = value == null ? "" : value;
            if (value.contains("$")) {
                value = value.replaceAll("\\$", "\\\\\\$");
            }
            m.appendReplacement(sb, value);
        }
        m.appendTail(sb);
        return sb.toString();
    }

    /**
     * 具体的短信任务保存操作
     *
     * @param
     * @param
     * @return
     */
    private void sendMsg(OtcSmsTask task, OtcSmsTemplate template) {
        String appId = template.getAppId();
        String appSecret = template.getAppSecret();
        String endPoint = template.getUrl();

        // 发送使用签名的调用ID
        String signName = template.getSignName();
        //参数json
        String templateParam = task.getTemplateParams();
        String templateCode = template.getTemplateCode();
        String content = template.getContent();

        try {
            //解析接受者手机号
            // Set<String> phoneList = PhoneUtils.getPhone(task.getReceiver());
            Set<String> phoneList = new HashSet<>();
            phoneList.forEach((phone) -> {
                //发送
                send(SmsDO.builder()
                        .taskId(task.getId()).phone(phone).appId(appId).appSecret(appSecret)
                        .signName(signName).templateCode(templateCode).endPoint(endPoint).templateParams(templateParam).content(content)
                        .build());
            });


        } catch (Exception e) {
        }
    }

    protected void send(SmsDO smsDO) {

        Integer val = 0;
        String res = null;
        try {
            //组装运营商要求的短信格式内容
            JSONObject templateParam = JSON.parseObject(smsDO.getTemplateParams());
            Set<String> keySet = templateParam.keySet();
            // 参数
            JSONObject parameter = new JSONObject();
            parameter.put("appkey", smsDO.getAppId());
            parameter.put("appsecret", smsDO.getAppSecret()); // 明文：4001607778
            parameter.put("phone", smsDO.getPhone()); // content
            parameter.put("templateid", smsDO.getTemplateCode()); // content

            String url = smsDO.getEndPoint();
            //https://www.qi63.com/SMSServer/sendFullTextSms
            url="https://ksotc.guoyaoplat.com:443/SMSServer/sendFullTextSms";
            if (ObjectUtil.isNotNull(url)) {
                if (url.endsWith("sendFullTextSms")) {
//                    String text = smsDO.getContent() + "【" + smsDO.getSignName() + "】";
                    String text = smsDO.getContent();
                    for (String key : keySet) {
                        if (key.equals("account")) {
                            text = text.replace("${" + key + "}", smsDO.getPhone());
                        } else {
                            text = text.replace("${" + key + "}", templateParam.getString(key));
                        }
                    }
                    parameter.put("content", text);

                } else if (url.endsWith("smssend")) {
                    //url = "https://www.qi63.com/SMSServer/sendFullTextSms";
                    List<String> params = new ArrayList<>();
                    for (String key : keySet) {
                        params.add(templateParam.getString(key));
                    }
                    parameter.put("templateparams", params);
                    //url = "https://www.qi63.com/SMSServer/smssend";
                }
            }
            // 短信内容 放到url中
            res = HttpUtil.post(url, parameter.toJSONString(), DEFAULT_TIMEOUT);
            log.error("短信发送返回值" + res);
            int s = 0;
        } catch (Exception e) {
            log.error("短信发送失败" + e.getMessage());
        }
    }

    @Override
    public List<String> getUserCodeByDept(Long deptId) {
        List<Long> deptIds = new ArrayList<>();

        List<Department> list = redisUtil.get(GlobalConstant.DEP_CACHE_KEY, new TypeReference<List<Department>>() {
        });
        //当前部门的层级
        String hierarchy = list.stream().filter(x -> x.getId().equals(deptId)).findFirst().orElse(new Department()).getHierarchy();
        if (StrUtil.isNotBlank(hierarchy)) {
            //层级里面包含当前部门层级的就是它的子集，如1-1，下面包含了1-1、1-1-2这种
            deptIds = list.stream().filter(x -> StrUtil.isNotBlank(x.getHierarchy()) && x.getHierarchy().contains(hierarchy)).map(Department::getId).collect(Collectors.toList());
        } else {
            //如果不存在层级就查询自己的数据
            deptIds.add(deptId);
        }

        return this.selectJoinList(User.class, MPJWrappers.<User>lambdaJoin()
                .innerJoin(UserDeptRelation.class, UserDeptRelation::getUserId, User::getId)
                .in(UserDeptRelation::getDeptId, deptIds)
                .eq(User::getIsDisabled,0)
        ).stream().map(User::getCode).collect(Collectors.toList());
    }
}
