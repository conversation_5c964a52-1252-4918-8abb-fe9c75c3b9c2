package com.zilue.module.organization.service;

import com.github.yulichang.base.MPJBaseService;
import com.zilue.common.page.PageOutput;
import com.zilue.module.organization.dto.*;
import com.zilue.module.organization.entity.User;
import com.zilue.module.organization.vo.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * <p>
 * 用户 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-02
 */
public interface IUserService extends MPJBaseService<User> {

    boolean add(AddUserDto dto);

    boolean update(UpdateUserDto dto);

    boolean deleteBatch(List<Long> ids);

    /**
     * 批量获取用户信息
     *
     * @param ids
     * @return 用户信息
     */
    List<UserInfoVo> getUsersInfo(List<Long> ids);

    /**
     * 获取微信分页
     *
     * @param dto
     * @return
     */
    PageOutput<WeChatPageVO> getPage(WeChatPageDto dto);

    /**
     * 获取用户分页
     *
     * @param dto
     * @return
     */
    PageOutput<UserPageVo> page(UserPageDto dto);

    //查询用户信息以响应组织架构以及相关岗位为目标如KA经理协防地区主管 地区主管协防业务代表
    PageOutput<UserDepartNameInfo> coachedUserList(UserListDto dto);

    List<UserDepartNameInfo> coachedUserAll(UserListDto dto);

    UserVo getInfo(Long id);

    UserInfoVo getCurrentInfo(User user);

    Boolean importData(MultipartFile file) throws IOException;


    void dealWithUser();

    boolean updateUserPostAndRole(UpdateUserDto dto);

    void sendMessage(SmsSendTaskDTO smsTaskDTO);

    void sendUserSms(String userPhone, String templateParam, String templateId);

    List<String> getUserCodeByDept(Long deptId);

}
