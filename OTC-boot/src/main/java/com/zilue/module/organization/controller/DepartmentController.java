package com.zilue.module.organization.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zilue.common.annotation.XjrLog;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.enums.YesOrNoEnum;
import com.zilue.common.handler.FormContentStyleStrategy;
import com.zilue.common.page.ConventPage;
import com.zilue.common.page.PageOutput;
import com.zilue.common.model.result.R;
import com.zilue.common.utils.ExcelUtil;
import com.zilue.common.utils.RedisUtil;
import com.zilue.common.utils.TreeUtil;
import com.zilue.common.utils.VoToColumnUtil;
import com.zilue.module.organization.dto.*;
import com.zilue.module.organization.entity.*;
import com.zilue.module.organization.service.IDepartmentService;
import com.zilue.module.organization.service.IUserDeptRelationService;
import com.zilue.module.organization.service.IUserPostRelationService;
import com.zilue.module.organization.service.IUserService;
import com.zilue.module.organization.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <p>
 * 机构 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-02
 */
@RestController
@RequestMapping(GlobalConstant.ORGANIZATION_MODULE_PREFIX + "/department")
@Api(value = GlobalConstant.ORGANIZATION_MODULE_PREFIX + "/department", tags = "机构")
@AllArgsConstructor
public class DepartmentController {

    private final IDepartmentService departmentService;

    private final IUserService userService;

    private final IUserDeptRelationService userDeptRelationService;

    private final IUserPostRelationService userPostRelationService;

    private final RedisUtil redisUtil;

    @GetMapping(value = "/list")
    @ApiOperation(value = "机构列表(不分页)")
    @XjrLog(value = "获取不分页机构列表")
    public R list() {
        List<Department> list = departmentService.list(Wrappers.lambdaQuery(Department.class)
                .orderByAsc(Department::getSortCode)
                .select(Department.class, x -> VoToColumnUtil.fieldsToColumns(DepartmentListVo.class).contains(x.getProperty())));

        List<DepartmentListVo> departmentListVos = BeanUtil.copyToList(list, DepartmentListVo.class);
        return R.ok(departmentListVos);
    }

    @GetMapping(value = "/page")
    @ApiOperation(value = "机构列表(分页)")
    @XjrLog(value = "获取分页机构列表")
    public R page(DepartmentPageDto dto) {

        LambdaQueryWrapper<Department> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(dto.getKeyword()), Department::getName, dto.getKeyword())
                .like(StrUtil.isNotBlank(dto.getKeyword()), Department::getCode, dto.getKeyword())
                .like(StrUtil.isNotBlank(dto.getName()), Department::getName, dto.getName())
                .like(StrUtil.isNotBlank(dto.getCode()), Department::getCode, dto.getCode())
                .like(ObjectUtil.isNotEmpty(dto.getEnabledMark()), Department::getEnabledMark, dto.getEnabledMark())
                .orderByAsc(Department::getSortCode)
                .select(Department.class, x -> VoToColumnUtil.fieldsToColumns(DepartmentPageVo.class).contains(x.getProperty()));

        IPage<Department> page = departmentService.page(ConventPage.getPage(dto), queryWrapper);
        PageOutput<DepartmentPageVo> pageOutput = ConventPage.getPageOutput(page, DepartmentPageVo.class);
        return R.ok(pageOutput);
    }

    @GetMapping(value = "/enabled-tree")
    @ApiOperation(value = "机构树")
    @XjrLog(value = "部门展示 所有数据")
    public R tree(DepartmentTreeDto dto) {

        List<Department> list = departmentService.list(Wrappers.<Department>lambdaQuery()
                .eq(ObjectUtil.isNotEmpty(dto.getEnabledMark()), Department::getEnabledMark, dto.getEnabledMark())
                .like(StrUtil.isNotBlank(dto.getName()), Department::getName, dto.getName())
                .like(StrUtil.isNotBlank(dto.getCode()), Department::getCode, dto.getCode())
                .orderByAsc(Department::getSortCode)
                .select(Department.class, x -> VoToColumnUtil.fieldsToColumns(DepartmentTreeVo.class).contains(x.getProperty())));

        List<DepartmentTreeVo> voList = BeanUtil.copyToList(list, DepartmentTreeVo.class);
        List<DepartmentTreeVo> treeVoList = TreeUtil.build(voList);
        return R.ok(treeVoList);
    }

    @GetMapping(value = "/tree")
    @ApiOperation(value = "部门展示 (忽略 未启用的数据)")
    @XjrLog(value = "部门展示 (忽略 未启用的数据)")
    public R treeEnabled(DepartmentTreeDto dto) {
        return R.ok(departmentService.treeEnabled(dto));
    }

    @GetMapping(value = "/childList")
    @ApiOperation(value = "通过父节点获取子节点")
    @XjrLog(value = "通过父节点获取子节点")
    public R childList(String parentId) {
        List<Department> list = departmentService.list(Wrappers.lambdaQuery(Department.class)
                .eq(ObjectUtil.isNotEmpty(parentId), Department::getParentId, parentId)
                .orderByAsc(Department::getSortCode)
                .select(Department.class, x -> VoToColumnUtil.fieldsToColumns(DepartmentListVo.class).contains(x.getProperty())));

        List<DepartmentListVo> departmentListVos = BeanUtil.copyToList(list, DepartmentListVo.class);
        return R.ok(departmentListVos);
    }

    @GetMapping(value = "/allChildList")
    @ApiOperation(value = "通过父节点获取所有下级子节点")
    @XjrLog(value = "通过父节点获取所有下级子节点")
    public R allChildList(String parentId) {
        List<Department> list = departmentService.list(Wrappers.lambdaQuery(Department.class)
                .like(StrUtil.isNotBlank(parentId), Department::getHierarchy, parentId)
                .orderByAsc(Department::getSortCode)
                .select(Department.class, x -> VoToColumnUtil.fieldsToColumns(DepartmentListVo.class).contains(x.getProperty())));
        List<DepartmentTreeVo> voList = BeanUtil.copyToList(list, DepartmentTreeVo.class);
        List<DepartmentTreeVo> treeVoList = TreeUtil.build(voList);
        return R.ok(treeVoList);
    }

    @GetMapping(value = "/info")
    @ApiOperation(value = "根据id查询机构信息")
    @XjrLog(value = "id查询获取机构信息")
    public R info(@RequestParam Long id) {
        Department dept = departmentService.getById(id);
        if (dept == null) {
            R.error("找不到此机构！");
        }
        DepartmentVo departmentVo = BeanUtil.toBean(dept, DepartmentVo.class);
        return R.ok(departmentVo);
    }

    @PostMapping
    @ApiOperation(value = "新增机构")
    @XjrLog(value = "新增机构")
    public R add(@Valid @RequestBody AddDepartmentDto dto) {
        long count = departmentService.count(Wrappers.<Department>query().lambda().eq(Department::getName, dto.getName()).or().eq(Department::getCode, dto.getCode()));
        if (count > 0) {
            return R.error("组织名称与组织编码已经存在！");
        }
        Department department = BeanUtil.toBean(dto, Department.class);
        if (ObjectUtil.isEmpty(dto.getParentId())) {
            department.setParentId(0L);
        }
        departmentService.save(department);
        //设置层级
        if (ObjectUtil.isNotEmpty(dto.getParentId())) {
            List<Department> list = redisUtil.get(GlobalConstant.DEP_CACHE_KEY, new TypeReference<List<Department>>() {
            });
            //获取父级的层级
            String hierarchy = list.stream().filter(x -> x.getId().equals(dto.getParentId())).findFirst().orElse(new Department()).getHierarchy();
            //在父级的基础上添加层级
            department.setHierarchy(hierarchy + StringPool.DASH + department.getId());
        } else {
            department.setHierarchy(department.getId().toString());
        }
        departmentService.updateById(department);
        CompletableFuture.runAsync(() -> {
            List<Department> list = departmentService.list();
            redisUtil.set(GlobalConstant.DEP_CACHE_KEY, list);
        });
        return R.ok(true);
    }

    @PutMapping
    @ApiOperation(value = "修改机构")
    @XjrLog(value = "修改机构")
    public R update(@Valid @RequestBody UpdateDepartmentDto dto) {

        long count = departmentService.count(Wrappers.<Department>query().lambda()
                .eq(Department::getName, dto.getName())
                .ne(Department::getId, dto.getId())
                .or()
                .eq(Department::getCode, dto.getCode())
                .ne(Department::getId, dto.getId()));

        if (count > 0) {
            return R.error("组织名称与组织编码已经存在！");
        }

        Department department = BeanUtil.toBean(dto, Department.class);
        if (department.getParentId() == null) {
            department.setParentId(0L);
        }
        List<Department> departmentList = redisUtil.get(GlobalConstant.DEP_CACHE_KEY, new TypeReference<List<Department>>() {
        });

        //设置层级,防止修改时改动上级，需重新设值
        if (ObjectUtil.isNotEmpty(dto.getParentId())) {
            //获取父级的层级
            String hierarchy = departmentList.stream().filter(x -> x.getId().equals(dto.getParentId())).findFirst().orElse(new Department()).getHierarchy();
            //在父级的基础上添加层级
            department.setHierarchy(hierarchy + StringPool.DASH + department.getId());
        } else {
            department.setHierarchy(department.getId().toString());
        }

        //查询当前部门的下级部门
        List<Department> childDepartmentList = departmentList.stream().filter(x -> x.getHierarchy().contains(dto.getId().toString())
                && !x.getHierarchy().equals(dto.getId().toString())).collect(Collectors.toList());
        //子级层级重新设值
        if (childDepartmentList.size() > 0) {
            //原本未改变之前的部门数据
            Department oldDepartment = departmentList.stream().filter(x -> x.getId().equals(dto.getId())).findFirst().orElse(new Department());
            String oldDepartmentHierarchy = oldDepartment.getHierarchy();
            for (Department childDepartment : childDepartmentList) {
                //原本子级的层级
                String oldChildHierarchy = childDepartment.getHierarchy();
                //将层级进行替换
                String newChildHierarchy = StrUtil.replace(oldChildHierarchy, oldDepartmentHierarchy, department.getHierarchy());
                childDepartment.setHierarchy(newChildHierarchy);
            }
        }
        //将本身的数据和自己的数据进行更新
        childDepartmentList.add(department);
        departmentService.updateBatchById(childDepartmentList);
//        departmentService.updateById(department);

        CompletableFuture.runAsync(() -> {
            List<Department> list = departmentService.list();
            redisUtil.set(GlobalConstant.DEP_CACHE_KEY, list);
        });

        return R.ok(true);
    }

    @DeleteMapping
    @ApiOperation(value = "删除")
    @XjrLog(value = "删除机构")
    public R delete(@RequestBody List<Long> ids) {
        //删除岗位时，需要判断，此机构下是不是存在人员，存在人员就不能删除
        List<UserDeptRelation> userDeptRelationList = redisUtil.get(GlobalConstant.USER_DEPT_RELATION_CACHE_KEY, new TypeReference<List<UserDeptRelation>>() {
        });
        //拿ids进行过滤，如果存在，就不能删除
        List<UserDeptRelation> users = userDeptRelationList.stream().filter(u -> ids.contains(u.getDeptId())).collect(Collectors.toList());
        if (users.size() > 0) {
            return R.error("此机构下存在用户！");
        }
        departmentService.removeBatchByIds(ids);
        CompletableFuture.runAsync(() -> {
            List<Department> list = departmentService.list();
            redisUtil.set(GlobalConstant.DEP_CACHE_KEY, list);
        });
        return R.ok(true);
    }

    @PostMapping("add-dept-user")
    @ApiOperation(value = "添加人员（组织）")
    @Transactional(rollbackFor = Exception.class)
    public R addDeptUser(@Valid @RequestBody AddDepartmentUserDto dto) {
        //先删除再新增
        userDeptRelationService.remove(Wrappers.<UserDeptRelation>query().lambda().eq(UserDeptRelation::getDeptId, dto.getId()));
        List<UserDeptRelation> userDeptRelationList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(dto.getUserIds())) {
            for (Long userId : dto.getUserIds()) {
                //将用户所选部门保存到关联表中
                UserDeptRelation userDeptRelation = new UserDeptRelation();
                userDeptRelation.setUserId(userId);
                userDeptRelation.setDeptId(dto.getId());
                userDeptRelationList.add(userDeptRelation);
            }
        }
        userDeptRelationService.saveBatch(userDeptRelationList);
        //更新缓存
        CompletableFuture.runAsync(() -> {
            List<UserDeptRelation> deptRelationList = userDeptRelationService.list(Wrappers.lambdaQuery(UserDeptRelation.class));
            redisUtil.set(GlobalConstant.USER_DEPT_RELATION_CACHE_KEY, deptRelationList);
        });
        return R.ok(true);
    }

    @GetMapping(value = "/dept-user-info")
    @ApiOperation(value = "根据组织id查询组织下的人员")
    public R DeptUserInfo(@RequestParam Long id) {
        List<Long> userIdList = userDeptRelationService.list(Wrappers.<UserDeptRelation>query().lambda().eq(UserDeptRelation::getDeptId, id)).stream().map(UserDeptRelation::getUserId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userIdList)) {
            return R.ok(new ArrayList<>());
        }
        List<User> users = userService.list(Wrappers.<User>query().lambda().in(User::getId, userIdList).select(User.class, x -> VoToColumnUtil.fieldsToColumns(UserRoleVo.class).contains(x.getProperty())));
        List<UserRoleVo> userRoleVos = BeanUtil.copyToList(users, UserRoleVo.class);
        return R.ok(userRoleVos);
    }

    @GetMapping(value = "/deptAllUserInfo")
    @ApiOperation(value = "根据组织id查询组织以及子机构下的人员")
    public R deptAllUserInfo(@RequestParam String id) {
        List<Department> list = departmentService.list(Wrappers.lambdaQuery(Department.class)
                .like(ObjectUtil.isNotEmpty(id), Department::getHierarchy, id)
                .orderByAsc(Department::getSortCode)
                .select(Department::getId));

        List<Long> departmentIds = list.stream().map(Department::getId).collect(Collectors.toList());


        List<Long> userIdList = userDeptRelationService.list(Wrappers.<UserDeptRelation>query().lambda().in(UserDeptRelation::getDeptId, departmentIds)).stream().map(UserDeptRelation::getUserId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userIdList)) {
            return R.ok(new ArrayList<>());
        }

        Map<Long, Long> userPostMap = userPostRelationService.list(Wrappers.<UserPostRelation>query().lambda().in(UserPostRelation::getUserId, userIdList)).stream().collect(Collectors.toMap(UserPostRelation::getUserId, UserPostRelation::getPostId));
        List<User> users = userService.list(Wrappers.<User>query().lambda().in(User::getId, userIdList).select(User.class, x -> VoToColumnUtil.fieldsToColumns(UserRoleVo.class).contains(x.getProperty())));
        List<UserRoleVo> userRoleVos = BeanUtil.copyToList(users, UserRoleVo.class);
        userRoleVos.forEach(userRoleVo -> {
            userRoleVo.setPostId(userPostMap.get(Long.valueOf(userRoleVo.getId())));
        });
        return R.ok(userRoleVos);
    }

    @PostMapping(value = "/info/multi")
    @ApiOperation(value = "根据组织id查询组织下的人员")
    public R deptInfoMulti(@RequestBody List<Long> ids) {
        List<Department> departmentList = departmentService.listByIds(ids);
        return R.ok(BeanUtil.copyToList(departmentList, DepartmentVo.class));
    }

    @PostMapping(value = "/export")
    @ApiOperation(value = "导出")
    @XjrLog(value = "导出表单数据")
    public ResponseEntity<byte[]> exportData() {
        List<Department> list = departmentService.list();
        List<DepartmentExportVo> postExportVos = BeanUtil.copyToList(list, DepartmentExportVo.class);
        ByteArrayOutputStream bot = new ByteArrayOutputStream();
        EasyExcel.write(bot).head(DepartmentExportVo.class).automaticMergeHead(false)
                .registerWriteHandler(new FormContentStyleStrategy())
                .excelType(ExcelTypeEnum.XLSX).sheet().doWrite(postExportVos);
        ByteArrayOutputStream resultBot = ExcelUtil.renderExportRequiredHead(bot);
        return R.fileStream(resultBot.toByteArray(), "部门导出" + ExcelTypeEnum.XLSX.getValue());
    }

    @GetMapping("/download-template")
    @ApiOperation(value = "下载模板")
    public ResponseEntity<byte[]> downloadTemplate() {
        List<PostExportVo> postExportVos = new ArrayList<>();
        ByteArrayOutputStream bot = new ByteArrayOutputStream();
        EasyExcel.write(bot).head(PostExportVo.class).automaticMergeHead(false)
                .registerWriteHandler(new FormContentStyleStrategy())
                .excelType(ExcelTypeEnum.XLSX).sheet().doWrite(postExportVos);
        ByteArrayOutputStream resultBot = ExcelUtil.renderExportRequiredHead(bot);
        return R.fileStream(resultBot.toByteArray(), "部门导入模板" + ExcelTypeEnum.XLSX.getValue());
    }

    @PostMapping("/import")
    @ApiOperation(value = "导入")
    public R importData(@RequestParam MultipartFile file) throws IOException {
        List<PostExportVo> postExportVos = EasyExcel.read(file.getInputStream()).head(PostExportVo.class).sheet().doReadSync();
        List<Department> departments = BeanUtil.copyToList(postExportVos, Department.class);
        //获取所有的岗位
        List<Department> list = departmentService.list();
        for (Department department : departments) {
            List<Department> count = list.stream().filter(x -> x.getCode().equals(department.getCode()) || x.getName().equals(department.getName())).collect(Collectors.toList());
            if (count.size() > 0) {
                return R.error("组织名称与组织编码已经存在！组合名称为：" + department.getName());
            }
        }
        return R.ok(departmentService.saveBatch(departments));
    }

    @GetMapping(value = "/dealtree")
    @ApiOperation(value = "机构树")
    @XjrLog(value = "部门展示 所有数据")
    public R dealtree() {
        departmentService.dealWithDepartment();
        return R.ok();
    }

}
