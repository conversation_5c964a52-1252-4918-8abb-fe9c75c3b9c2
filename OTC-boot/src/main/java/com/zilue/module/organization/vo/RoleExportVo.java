package com.zilue.module.organization.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/11 9:39
 */
@Data
public class RoleExportVo {

    @NotNull(message = "角色名不能为空")
    @Length(min = 1,max = 20,message = "角色名最多20个字符！")
    @ApiModelProperty("名字")
    @ExcelProperty("角色名称(*)")
    private String name;

    @NotNull(message = "角色编码不能为空")
    @Length(min = 1,max = 10,message = "编码最多20个字符！")
    @ApiModelProperty("角色编码")
    @ExcelProperty("角色编码(*)")
    private String code;

    @ApiModelProperty("启用状态")
    @ExcelProperty("启用状态(*)")
    private Integer enabledMark;

    @Length(max = 255,message = "备注最多20个字符！")
    @ApiModelProperty("备注")
    @ExcelProperty("备注")
    private String remark;
}
