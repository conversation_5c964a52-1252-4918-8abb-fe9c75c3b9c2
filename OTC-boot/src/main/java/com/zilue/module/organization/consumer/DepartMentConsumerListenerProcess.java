package com.zilue.module.organization.consumer;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.enums.DeleteMark;
import com.zilue.common.enums.EnabledMark;
import com.zilue.common.rabbitmq.MqConstant;
import com.zilue.common.utils.JsonUtil;
import com.zilue.common.utils.RedisUtil;
import com.zilue.module.business.account.dto.AddOtcAccountDto;
import com.zilue.module.organization.entity.CrmBusinessunit;
import com.zilue.module.organization.entity.Department;
import com.zilue.module.organization.entity.User;
import com.zilue.module.organization.entity.UserChargeDept;
import com.zilue.module.organization.service.IDepartmentService;
import com.zilue.module.organization.service.IUserChargeDeptService;
import com.zilue.module.organization.service.IUserService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 定义接收者（可以定义N个接受者，消息会均匀的发送到N个接收者中）
 *
 * RabbitMq接受者1
 * （@RabbitListener声明类上，一个类只能监听一个队列）
 * @author: huoyh
 * @date: 2025/01/14
 */
@Slf4j
@Component
@AllArgsConstructor
public class DepartMentConsumerListenerProcess {

    private final IDepartmentService departmentService;

    private final IUserService userService;

    private final IUserChargeDeptService userChargeDeptService;

    private final RedisUtil redisUtil;

    @RabbitListener(bindings = {
            @QueueBinding(
                    value = @Queue,
                    exchange = @Exchange(type = "topic", name = MqConstant.TOPIC_OTC),
                    key = {MqConstant.ROUTING_KEY_DEPARTMENT}
            )
    })
    public void onMessage(String message) {
        if(StringUtils.isNotBlank(message)) {
            List<Department> deptNew = JsonUtil.convertJsonToList(message, Department.class);
            List<String> businessUnitIdList = deptNew.stream().map(Department::getBusinessUnitId).distinct().collect(Collectors.toList());
            // 获取部门
            List<Department> departmentList = redisUtil.get(GlobalConstant.DEP_CACHE_KEY, new TypeReference<List<Department>>() {
            });
            if(CollectionUtil.isEmpty(departmentList)) {
                departmentList = departmentService.list(Wrappers.lambdaQuery(Department.class)
                        .ne(Department::getNewKyLevel, 6).eq(Department::getDeleteMark, DeleteMark.NODELETE.getCode())
                        .eq(Department::getEnabledMark, EnabledMark.ENABLED.getCode()));
            } else {
                departmentList = departmentList.stream().filter(dd -> !Objects.equals(dd.getNewKyLevel(), 6)).collect(Collectors.toList());
            }
            // 获取用户
            List<User> userList = redisUtil.get(GlobalConstant.USER_CACHE_KEY, new TypeReference<List<User>>() {
            });
            if(CollectionUtil.isEmpty(userList)) {
                userList = userService.list(Wrappers.lambdaQuery(User.class)
                        .eq(User::getDeleteMark, DeleteMark.NODELETE.getCode())
                        .eq(User::getEnabledMark, EnabledMark.ENABLED.getCode()));
            }
            Map<String, Long> userBusinessMap = new HashMap<>(userList.size());
            userBusinessMap = userList.stream().filter(oo -> StrUtil.isNotBlank(oo.getSystemUserId()))
                    .collect(Collectors.toMap(
                            User::getSystemUserId, // 键
                            User::getId, // 值
                            (existingValue, newValue) -> existingValue // 合并函数，取第一个值
                    ));
            // 使用流和收集器去重
            Map<String, Long> idMap = departmentList.stream().filter(oo -> StrUtil.isNotBlank(oo.getBusinessUnitId()))
                    .collect(Collectors.toMap(
                            Department::getBusinessUnitId, // 键
                            Department::getId, // 值
                            (existingValue, newValue) -> existingValue // 合并函数，取第一个值
                    ));
            List<Department> existDepartments = departmentList.stream().filter(oo -> businessUnitIdList.contains(oo.getBusinessUnitId())).distinct().collect(Collectors.toList());
            List<String> existBusinessUnitIdList = existDepartments.stream().map(Department::getBusinessUnitId).distinct().collect(Collectors.toList());
            List<Department> newDepartments = deptNew.stream().filter(oo -> !existBusinessUnitIdList.contains(oo.getBusinessUnitId())).distinct().collect(Collectors.toList());
            Boolean isAddFlag = Boolean.TRUE;
            // 处理新增
            if(CollectionUtil.isNotEmpty(newDepartments)) {
                dealDepartments(newDepartments, idMap, userBusinessMap, isAddFlag);
            }
            // 处理更新
            if(CollectionUtil.isNotEmpty(existDepartments)) {
                isAddFlag = Boolean.FALSE;
                // 将deptNew转换为Map，以业务单元ID为键，部门对象为值
                Map<String, Department> deptNewMap = deptNew.stream()
                        .collect(Collectors.toMap(Department::getBusinessUnitId, dept -> dept));
                for (Department oo : existDepartments) {
                    Department dept = deptNewMap.get(oo.getBusinessUnitId());
                    if (dept != null) {
                        BeanUtil.copyProperties(dept, oo);
                    }
                }
                dealDepartments(existDepartments, idMap,userBusinessMap, isAddFlag);
            }
        }
    }

    private void dealDepartments(List<Department> newDepartments, Map<String, Long> idMap,Map<String, Long> userBusinessMap, Boolean isAddFlag) {
       /* List<Department> syncBusinessunit3 = newDepartments.stream().filter(x -> x.getNewKyLevel().equals("3")).collect(Collectors.toList());
        insertDepartment(syncBusinessunit3, "3", idMap,userBusinessMap, isAddFlag);*/
        List<Department> syncBusinessunit4 = newDepartments.stream().filter(x -> x.getNewKyLevel().equals("4")).collect(Collectors.toList());
        insertDepartment(syncBusinessunit4, "4", idMap,userBusinessMap, isAddFlag);
        List<Department> syncBusinessunit5 = newDepartments.stream().filter(x -> x.getNewKyLevel().equals("5")).collect(Collectors.toList());
        insertDepartment(syncBusinessunit5, "5", idMap,userBusinessMap, isAddFlag);
        List<Department> syncBusinessunit6 = newDepartments.stream().filter(x -> x.getNewKyLevel().equals("6")).collect(Collectors.toList());
        insertDepartment(syncBusinessunit6, "6", idMap,userBusinessMap, isAddFlag);
    }

    private void insertDepartment(List<Department> syncBusinessunits, String level, Map<String, Long> idMap,Map<String, Long> userBusinessMap, Boolean isAddFlag) {
        if(CollectionUtil.isEmpty(syncBusinessunits)) {
            return;
        }
        List<UserChargeDept> chargeDepts = new ArrayList<>(syncBusinessunits.size());
        for (Department department : syncBusinessunits) {
            if (level.equals("4")) {
                department.setId(1l);
                department.setParentId(0l);
            } else {
                department.setParentId(idMap.get(department.getParentBusinessUnitId()));
            }
            if(isAddFlag) {
                departmentService.save(department);
                // 新增用户负责部门的关联
                if (ObjectUtil.isNotEmpty(department.getNewMgrId())) {
                    UserChargeDept userChargeDept = new UserChargeDept();
                    userChargeDept.setDeptId(department.getId());
                    userChargeDept.setUserId(Objects.nonNull(userBusinessMap.get(department.getNewMgrId())) ? userBusinessMap.get(department.getNewMgrId()) : 1000000000000000000l);
                    chargeDepts.add(userChargeDept);
                }
                idMap.put(department.getBusinessUnitId(), department.getId());
            } else {
                departmentService.updateById(department);
                if (ObjectUtil.isNotEmpty(department.getNewMgrId())) {
                    // 更新用户负责部门的关联
                    Long userId = Objects.nonNull(userBusinessMap.get(department.getNewMgrId())) ? userBusinessMap.get(department.getNewMgrId()) : 1000000000000000000l;
                    userChargeDeptService.lambdaUpdate().set(UserChargeDept::getUserId, userId).eq(UserChargeDept::getDeptId, department.getId());
                }
            }
            if (level.equals("4")) {
                department.setHierarchy(department.getId().toString());
            } else {
                List<Department> list = redisUtil.get(GlobalConstant.DEP_CACHE_KEY, new TypeReference<List<Department>>() {
                });
                //获取父级的层级
                String hierarchy = list.stream().filter(x -> x.getId().equals(department.getParentId())).findFirst().orElse(new Department()).getHierarchy();
                //在父级的基础上添加层级
                department.setHierarchy(hierarchy + StringPool.DASH + department.getId());
            }
            departmentService.updateById(department);

            // 存入缓存
            CompletableFuture.runAsync(() -> {
                List<Department> redisList = departmentService.list(Wrappers.lambdaQuery(Department.class).eq(Department::getDeleteMark, DeleteMark.NODELETE.getCode())
                        .eq(Department::getEnabledMark, EnabledMark.ENABLED.getCode()));
                redisUtil.set(GlobalConstant.DEP_CACHE_KEY, redisList);
            });
        }
        //将用户所负责的部门保存到关联表中
        if(CollectionUtil.isNotEmpty(chargeDepts)) {
            userChargeDeptService.saveBatch(chargeDepts);
        }
    }
}
