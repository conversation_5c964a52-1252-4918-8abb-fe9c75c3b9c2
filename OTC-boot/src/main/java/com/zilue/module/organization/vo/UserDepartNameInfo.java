package com.zilue.module.organization.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class UserDepartNameInfo {
    @ApiModelProperty(value = "用户主键")
    private Long userId;
    @ApiModelProperty(value = "部门名称组合")
    private String departNames;
    @ApiModelProperty(value = "部门层级")
    private String hierarchy;
    @ApiModelProperty(value = "用户名称")
    private String name;
}
