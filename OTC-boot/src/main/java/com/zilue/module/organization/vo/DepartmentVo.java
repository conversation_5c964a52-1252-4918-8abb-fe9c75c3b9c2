package com.zilue.module.organization.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @title: DepartmentVo
 * <AUTHOR>
 * @Date: 2022/4/4 16:41
 * @Version 1.0
 */
@Data
public class DepartmentVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    @ApiModelProperty("组织部门名称-EDP部门简称")
    private String name;

    @ApiModelProperty("父组织部门ID")
    private Long parentId;

    @ApiModelProperty("组织编码")
    private String code;

    @ApiModelProperty("部门层级")
    private String hierarchy;

    @ApiModelProperty("排序号")
    private Integer sortCode;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("简称")
    private String shortName;

    @ApiModelProperty("管理人-用户id")
    private Long custodian;

    @ApiModelProperty("组织类别，1：公司，0：部门")
    private Integer departmentType;

    @ApiModelProperty("对应企业微信部门id")
    private Long wechatDeptId;

    @ApiModelProperty("对应钉钉部门id")
    private Long dingtalkDeptId;

    private String dingAgentId;

    private String dingAppKey;

    private String dingAppSecret;

    @ApiModelProperty("EDP部门id")
    private String businessUnitId;

    @ApiModelProperty("EDP部门全称")
    private String businessUnitName;

    @ApiModelProperty("EDP上级部门id")
    private String parentBusinessUnitId;

    @ApiModelProperty("EDP上级部门全称")
    private String parentBusinessUnitName;

    @ApiModelProperty("EDP部门层级")
    private String newKyLevel;

    @ApiModelProperty("EDP部门经理id")
    private String newMgrId;

    @ApiModelProperty("EDP部门经理姓名")
    private String newMgrIdName;

    @ApiModelProperty("EDP部门类别")
    private String businessUnitType;

    @ApiModelProperty("EDP部门状态0在用 1停用")
    private String isDisabled;

    @ApiModelProperty("数据事业部标识 1otc 2招商代理")
    private String businessGroupType;

    @ApiModelProperty("有效标记")
    private Integer enabledMark;
}
