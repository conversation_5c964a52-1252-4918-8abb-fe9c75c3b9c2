package com.zilue.module.organization.service;

import com.zilue.module.organization.entity.Post;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zilue.module.organization.vo.SwitchPostVo;
import com.zilue.module.system.dto.SwitchPostDto;

/**
 * <p>
 * 岗位 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-02
 */
public interface IPostService extends IService<Post> {
    SwitchPostVo switchPost(SwitchPostDto dto);
}
