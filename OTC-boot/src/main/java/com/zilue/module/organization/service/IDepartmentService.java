package com.zilue.module.organization.service;

import com.github.yulichang.base.MPJBaseService;
import com.zilue.common.page.PageInput;
import com.zilue.module.organization.dto.DepartmentTreeDto;
import com.zilue.module.organization.entity.Department;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zilue.module.organization.vo.DepartmentTreeVo;

import java.util.List;

/**
 * <p>
 * 机构 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-02
 */
public interface IDepartmentService extends MPJBaseService<Department> {

    Object page(PageInput pageInput);

    List<Long> getAllChildIds(List<Long> deptIds, List<DepartmentTreeVo> voList);

    void dealWithDepartment();


    List<DepartmentTreeVo> treeEnabled(DepartmentTreeDto dto);
}
