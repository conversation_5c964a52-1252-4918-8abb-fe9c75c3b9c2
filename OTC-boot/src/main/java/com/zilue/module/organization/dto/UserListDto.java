package com.zilue.module.organization.dto;

import com.zilue.common.page.PageInput;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
@EqualsAndHashCode(callSuper = true)
@Data
public class UserListDto extends PageInput {
    @ApiModelProperty(value="部门id")
    @NotNull(message="部门id不能为空")
    private Long departmentId;

    @ApiModelProperty(value="用户姓名")
    private String userName;
    @ApiModelProperty(value="岗位id")
    @NotNull(message="岗位id不能为空")
    private Long postId;

}
