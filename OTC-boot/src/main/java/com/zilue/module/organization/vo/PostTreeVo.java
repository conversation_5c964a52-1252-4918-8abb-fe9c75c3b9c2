package com.zilue.module.organization.vo;

import com.zilue.common.model.tree.ITreeNode;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PostTreeVo implements ITreeNode<PostTreeVo,Long>, Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("名字")
    private String name;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("父级id")
    private Long parentId;

    @ApiModelProperty("排序号")
    private Integer sortCode;

    @ApiModelProperty("备注")
    private String remark;

    private Integer enabledMark;

    private List<PostTreeVo> children;
}
