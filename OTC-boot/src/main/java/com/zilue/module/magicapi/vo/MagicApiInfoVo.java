package com.zilue.module.magicapi.vo;

import lombok.Data;
import org.ssssssss.magicapi.core.model.BaseDefinition;
import org.ssssssss.magicapi.core.model.Header;
import org.ssssssss.magicapi.core.model.Parameter;
import org.ssssssss.magicapi.core.model.Path;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * magic api 信息
 * <AUTHOR>
 */
@Data
public class MagicApiInfoVo {

    private String id;

    private String method;

    private String script;

    private String groupId;

    private String name;

    private String path;

    private Long createTime;

    private Long updateTime;

    private String lock;

    private String createBy;

    private String updateBy;

    private BaseDefinition requestBodyDefinition;

    private List<Parameter> parameters;

    private List<Header> headers;

    private List<Path> paths;

    private LinkedHashMap<String,Object> properties;
}
