package com.zilue.module.business.account.dto;

import com.zilue.common.page.PageInput;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;


/**
 * @title: 分页查询入参
 * <AUTHOR>
 * @Date: 2024-12-27
 * @Version 1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OtcAccountPageDto extends PageInput {

    /**
     * 客户类别0：总部；1：分部；3：门店
     */
    @ApiModelProperty("客户类别0：总部；1：分部；2：门店 4:核心门店")
    private Integer terminalType;

    @ApiModelProperty("门店标签类别0：未关联；1：已关联")
    private Integer labelType;

    /**
     * 总部Id
     */
    @ApiModelProperty("主键Id")
    private Long id;
    /**
     * 总部名称
     */
    @ApiModelProperty("总部名称")
    private String headName;

    /**
     * 总部AccountId
     */
    @ApiModelProperty("总部AccountId")
    private String headAccountId;

    /**
     * 分部名称
     */
    @ApiModelProperty("分部名称")
    private String partName;

    /**
     * 门店名称
     */
    @ApiModelProperty("门店名称")
    private String shopName;

    /**
     * 门店名称
     */
    @ApiModelProperty("责任人名称或工号")
    private String ownerNameOrNumber;

    @ApiModelProperty("责任人id")
    private Long ownerId;

    /**
     * 0：直营；1：加盟
     */
    @ApiModelProperty("0：直营；1：加盟")
    private Integer isDirectJoin;

    /**
     * 是否DTP门店：0：否；1：是
     */
    @ApiModelProperty("是否DTP门店：0：否；1：是")
    private Integer isDtp;

    /**
     * 是否统筹门店：0：否；1：是
     */
    @ApiModelProperty("是否统筹门店：0：否；1：是")
    private Integer isWhole;

    /**
     * 是否电商门店：0：否；1：是
     */
    @ApiModelProperty("是否电商门店：0：否；1：是")
    private Integer isOnline;

    @ApiModelProperty("'门店标签id'")
    private Long customerTagId;
    @ApiModelProperty("本人经度")
    private BigDecimal longitude;
    @ApiModelProperty("本人纬度")
    private BigDecimal latitude;
    @ApiModelProperty("路线规划日期 日期格式为  yyyy-MM-dd")
    private String planDay;

    @ApiModelProperty("客户档案接口 1 是客户档案接口")
    private String customerArchive;
    @ApiModelProperty("当前人员部门id")
    private Long departmentId;
    @ApiModelProperty("门店名称")
    private String accountName;
    @ApiModelProperty("客户名称")
    private String customerName;

}
