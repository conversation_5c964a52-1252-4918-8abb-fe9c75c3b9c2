package com.zilue.module.business.taskManagement.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class VisitVo {
    @ApiModelProperty("用户主键")
    @ExcelIgnore
    private Long userId;
    @ApiModelProperty("部门层级")
    @ExcelIgnore
    private String hierarchy;;
    @ApiModelProperty("所属辖区")
    @ExcelProperty("所属辖区")
    private String departNames;

    @ApiModelProperty("员工姓名")
    @ExcelProperty("员工姓名")
    private String saleManName;

    @ApiModelProperty("EDP工号")
    @ExcelProperty("EDP工号(*)")
    private String edpCode;

    @ApiModelProperty("目标值")
    @ExcelProperty("目标值")
    private Integer target;
    @ApiModelProperty("实际值")
    @ExcelProperty("实际值")
    private Integer real;
    @ApiModelProperty("完成率")
    @ExcelProperty("完成率")
    private String completionRate;
}
