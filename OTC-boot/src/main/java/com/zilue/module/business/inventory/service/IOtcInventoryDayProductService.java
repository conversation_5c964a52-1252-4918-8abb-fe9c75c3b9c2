package com.zilue.module.business.inventory.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zilue.module.business.inventory.dto.InventoryDayDto;
import com.zilue.module.business.inventory.entity.OtcInventoryDayProduct;
import com.zilue.module.business.inventory.vo.OtcInventoryDayPartVo;
import com.zilue.module.business.salesreport.entity.OtcSalesReportMonth;

public interface IOtcInventoryDayProductService extends IService<OtcInventoryDayProduct> {
    OtcInventoryDayPartVo getStaticCount(InventoryDayDto dto);
    void syncInventoryDayProduct(String syncTime);
}
