package com.zilue.module.business.account.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class OtcAccountHeadExportVo {

    /**
     * 主键ID
     */
    @ExcelProperty(value = "客户id")
    @ApiModelProperty("客户id")
    private String clientId;
    /**
     * 连锁总部名称
     */
    @ExcelProperty(value = "连锁总部名称")
    @ApiModelProperty("连锁总部名称")
    private String name;

    /**
     * 负责人
     */
    @ExcelProperty(value = "负责人")
    @ApiModelProperty("负责人")
    private String ownerName;


    /**
     * 负责人工号
     */
    @ExcelProperty(value = "负责人工号")
    @ApiModelProperty("负责人工号")
    private String ownerNumber;

    /**
     * 详细地址
     */
    @ExcelProperty(value = "详细地址")
    @ApiModelProperty("详细地址")
    private String address1Name;

    /**
     * 客户联系电话
     */
    @ExcelProperty(value = "客户联系电话")
    @ApiModelProperty("客户联系电话")
    private String address1Telephone1;

    /**
     * 法定代表人
     */
    @ExcelProperty(value = "法定代表人")
    @ApiModelProperty("法定代表人")
    private String legalPerson;

    /**
     * 营业执照编码
     */
    @ExcelProperty(value = "营业执照编码")
    @ApiModelProperty("营业执照编码")
    private String businessLicenseCode;

}
