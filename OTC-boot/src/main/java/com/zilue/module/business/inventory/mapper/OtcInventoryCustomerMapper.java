package com.zilue.module.business.inventory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zilue.module.business.inventory.entity.OtcInventoryCustomer;
import com.zilue.module.business.inventory.vo.OtcInventoryCustomerVo;
import com.zilue.module.business.salesindicators.entity.OtcSalesmanSalesIndicators;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface OtcInventoryCustomerMapper extends BaseMapper<OtcInventoryCustomer> {

   List<OtcInventoryCustomerVo> queryCustomerList();

   /**
    * 获得总部负责人姓名
    * @return
    */
   List<OtcInventoryCustomerVo>  queryCustomerOwnerList();

}
