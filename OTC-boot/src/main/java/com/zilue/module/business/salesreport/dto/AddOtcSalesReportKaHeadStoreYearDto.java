package com.zilue.module.business.salesreport.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

import java.math.BigDecimal;


/**
* @title: ka经理管理的总部汇总按年
* <AUTHOR>
* @Date: 2025-01-20
* @Version 1.0
*/
@Data
public class AddOtcSalesReportKaHeadStoreYearDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 代表编码
    */
    @ApiModelProperty("代表编码")
    private String userCode;
    /**
    * 年计划值
    */
    @ApiModelProperty("年计划值")
    private BigDecimal planAmount;
    /**
    * 年完成值
    */
    @ApiModelProperty("年完成值")
    private BigDecimal finishAmount;
    /**
    * 业务年份
    */
    @ApiModelProperty("业务年份")
    private String businessYear;
    /**
    * 同级架构下排名
    */
    @ApiModelProperty("同级架构下排名")
    private Long rankNum;

}
