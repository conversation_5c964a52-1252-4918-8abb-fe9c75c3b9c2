package com.zilue.module.business.reportForms.service;

import com.zilue.module.business.reportForms.dto.ReportFormsDto;
import com.zilue.module.business.reportForms.vo.PersonSaleMoneyReportFormVo;
import com.zilue.module.business.reportForms.vo.PersonVisitReportFormVo;
import com.zilue.module.business.reportForms.vo.SaleMoneyReportFormVo;
import com.zilue.module.business.reportForms.vo.VisitReportFormVo;

import java.util.List;

public interface ReportFormsService {

    List<VisitReportFormVo> visitReportForm(ReportFormsDto reportFormsDto);

    PersonVisitReportFormVo selectUserSignStatistics(ReportFormsDto reportFormsDto);

    SaleMoneyReportFormVo saleMoneyReportForm(ReportFormsDto reportFormsDto);

    PersonSaleMoneyReportFormVo selectUserSaleMoneyStatistics(ReportFormsDto reportFormsDto);

    List<VisitReportFormVo> visitReportFormNew(ReportFormsDto reportFormsDto);

    List<VisitReportFormVo> visitReportFormDetails(ReportFormsDto reportFormsDto);

}
