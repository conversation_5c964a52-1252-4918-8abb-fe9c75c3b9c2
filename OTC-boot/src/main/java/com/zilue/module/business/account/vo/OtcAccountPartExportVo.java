package com.zilue.module.business.account.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class OtcAccountPartExportVo {

    /**
     * 主键ID
     */
    @ExcelProperty(value = "连锁分部ID")
    @ContentStyle(dataFormat = 49)
    @ApiModelProperty("主键ID")
    private String clientId;

    /**
     * 分部客户名称
     */
    @ExcelProperty(value = "分部客户名称")
    @ApiModelProperty("分部客户名称")
    private String name;

    @ExcelProperty(value = "连锁总部名称")
    @ApiModelProperty("连锁总部名称")
    private String newOtcAccountName;

    /**
     * 负责人
     */
    @ExcelProperty(value = "负责人")
    @ApiModelProperty("负责人")
    private String ownerName;


    /**
     * 负责人工号
     */
    @ExcelProperty(value = "负责人工号")
    @ApiModelProperty("负责人工号")
    private String ownerNumber;

    /**
     * 详细地址
     */
    @ExcelProperty(value = "详细地址")
    @ApiModelProperty("详细地址")
    private String address1Name;

    /**
     * 客户联系电话
     */
    @ExcelProperty(value = "客户联系电话")
    @ApiModelProperty("客户联系电话")
    private String address1Telephone1;

    /**
     * 法定代表人
     */
    @ExcelProperty(value = "法定代表人")
    @ApiModelProperty("法定代表人")
    private String legalPerson;

    /**
     * 营业执照编码
     */
    @ExcelProperty(value = "营业执照编码")
    @ApiModelProperty("营业执照编码")
    private String businessLicenseCode;
}
