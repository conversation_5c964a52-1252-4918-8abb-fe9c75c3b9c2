package com.zilue.module.business.salesperformance.dto;

import com.zilue.common.page.PageInput;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalTime;
import java.time.LocalDateTime;
import java.math.BigDecimal;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-01-15
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class OtcSalesmanSalesPerformancePageDto extends PageInput {

    /**
    * 
    */
    @ApiModelProperty("")
    private Long id;
    /**
    * edp数据主键
    */
    @ApiModelProperty("edp数据主键")
    private Long edpId;
    /**
    * 代表名称
    */
    @ApiModelProperty("代表名称")
    private String userName;
    /**
    * 代表编码
    */
    @ApiModelProperty("代表编码")
    private String userCode;
    /**
    * 组织名称
    */
    @ApiModelProperty("组织名称")
    private String deptName;
    /**
    * 组织编码
    */
    @ApiModelProperty("组织编码")
    private String deptCode;
    /**
    * 总部名称
    */
    @ApiModelProperty("总部名称")
    private String headquartersName;
    /**
    * 总部编码
    */
    @ApiModelProperty("总部编码")
    private String headquartersCode;
    /**
    * 分部名称
    */
    @ApiModelProperty("分部名称")
    private String subName;
    /**
    * 分部编码
    */
    @ApiModelProperty("分部编码")
    private String subCode;
    /**
    * 门店名称
    */
    @ApiModelProperty("门店名称")
    private String storeName;
    /**
    * 门店编码
    */
    @ApiModelProperty("门店编码")
    private String storeCode;
    /**
    * 产品名称
    */
    @ApiModelProperty("产品名称")
    private String productName;
    /**
    * 产品编码
    */
    @ApiModelProperty("产品编码")
    private String productCode;
    /**
    * 业绩值
    */
    @ApiModelProperty("业绩值")
    private BigDecimal amount;
    /**
    * 日期
    */
    @ApiModelProperty("日期")
    private String businessDay;
    /**
    * 数据时间
    */
    @ApiModelProperty("数据时间")
    private String syncTime;
    /**
    * 创建人ID
    */
    @ApiModelProperty("创建人ID")
    private Long createUserId;
    /**
    * 创建日期字段开始时间
    */
    @ApiModelProperty("创建日期字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createDateStart;
    /**
    * 创建日期字段结束时间
    */
    @ApiModelProperty("创建日期字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createDateEnd;
    /**
    * 修改人ID
    */
    @ApiModelProperty("修改人ID")
    private Long modifyUserId;
    /**
    * 修改日期字段开始时间
    */
    @ApiModelProperty("修改日期字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime modifyDateStart;
    /**
    * 修改日期字段结束时间
    */
    @ApiModelProperty("修改日期字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime modifyDateEnd;
    /**
    * 删除标记
    */
    @ApiModelProperty("删除标记")
    private Integer deleteMark;
    /**
    * 有效标记
    */
    @ApiModelProperty("有效标记")
    private Integer enabledMark;

}
