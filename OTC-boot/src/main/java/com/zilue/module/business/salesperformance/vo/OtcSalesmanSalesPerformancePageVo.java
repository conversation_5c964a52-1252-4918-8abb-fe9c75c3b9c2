package com.zilue.module.business.salesperformance.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import com.zilue.common.annotation.Trans;
import com.zilue.common.enums.TransType;
import java.time.LocalTime;
import java.time.LocalDateTime;
import java.math.BigDecimal;

/**
* @title: 分页列表出参
* <AUTHOR>
* @Date: 2025-01-15
* @Version 1.0
*/
@Data
public class OtcSalesmanSalesPerformancePageVo {

    /**
    * 
    */
    @ApiModelProperty("")
    private String id;
    /**
    * edp数据主键
    */
    @ApiModelProperty("edp数据主键")
    private Long edpId;
    /**
    * 代表名称
    */
    @ApiModelProperty("代表名称")
    private String userName;
    /**
    * 代表编码
    */
    @ApiModelProperty("代表编码")
    private String userCode;
    /**
    * 组织名称
    */
    @ApiModelProperty("组织名称")
    private String deptName;
    /**
    * 组织编码
    */
    @ApiModelProperty("组织编码")
    private String deptCode;
    /**
    * 总部名称
    */
    @ApiModelProperty("总部名称")
    private String headquartersName;
    /**
    * 总部编码
    */
    @ApiModelProperty("总部编码")
    private String headquartersCode;
    /**
    * 分部名称
    */
    @ApiModelProperty("分部名称")
    private String subName;
    /**
    * 分部编码
    */
    @ApiModelProperty("分部编码")
    private String subCode;
    /**
    * 门店名称
    */
    @ApiModelProperty("门店名称")
    private String storeName;
    /**
    * 门店编码
    */
    @ApiModelProperty("门店编码")
    private String storeCode;
    /**
    * 产品名称
    */
    @ApiModelProperty("产品名称")
    private String productName;
    /**
    * 产品编码
    */
    @ApiModelProperty("产品编码")
    private String productCode;
    /**
    * 业绩值
    */
    @ApiModelProperty("业绩值")
    private BigDecimal amount;
    /**
    * 日期
    */
    @ApiModelProperty("日期")
    private String businessDay;
    /**
    * 数据时间
    */
    @ApiModelProperty("数据时间")
    private String syncTime;
    /**
    * 创建人ID
    */
    @ApiModelProperty("创建人ID")
    private Long createUserId;
    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createDate;
    /**
    * 修改人ID
    */
    @ApiModelProperty("修改人ID")
    private Long modifyUserId;
    /**
    * 修改日期
    */
    @ApiModelProperty("修改日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime modifyDate;
    /**
    * 删除标记
    */
    @ApiModelProperty("删除标记")
    private Integer deleteMark;
    /**
    * 有效标记
    */
    @ApiModelProperty("有效标记")
    private Integer enabledMark;

}
