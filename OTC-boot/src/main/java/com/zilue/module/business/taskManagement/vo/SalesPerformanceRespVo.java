package com.zilue.module.business.taskManagement.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @program: OTC-boot
 * @description: 业绩目标达成明细的返回对象
 * @packagename: com.zilue.module.business.taskManagement.vo
 * @author: 曹凌
 * @date: 2025-01-16 14:15
 **/
@Data
@NoArgsConstructor
public class SalesPerformanceRespVo implements Serializable {

    @ApiModelProperty("员工姓名")
    @ExcelProperty("员工姓名")
    private String emplName;

    @ApiModelProperty("EDP工号")
    @ExcelProperty("EDP工号")
    private String erpCode;

    @ApiModelProperty("所属辖区")
    @ExcelProperty("所属辖区")
    private String businessUnitIdName;

    @ApiModelProperty("全年计划")
    @ExcelProperty("全年计划")
    private String yearPlanAmount;

    @ApiModelProperty("全年实绩")
    @ExcelProperty("全年实绩")
    private String yearFinishAmount;

    @ApiModelProperty("月计划")
    @ExcelProperty("月计划")
    private String monthPlanAmount;

    @ApiModelProperty("月实绩")
    @ExcelProperty("月实绩")
    private String monthFinishAmount;

    @ApiModelProperty("指标达成率")
    @ExcelProperty("指标达成率")
    private String finishRate;

    @ApiModelProperty("同比增长率")
    @ExcelProperty("同比增长率")
    private String yoyGrowthRate;

    @JsonIgnore
    @ExcelIgnore
    private String hierarchy;
}
