package com.zilue.module.business.salesreport.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zilue.common.annotation.RepeatSubmit;
import com.zilue.common.page.ConventPage;
import com.zilue.common.page.PageOutput;
import com.zilue.common.model.result.R;
import com.zilue.common.utils.VoToColumnUtil;
import com.zilue.module.business.salesreport.dto.AddOtcSalesReportKaHeadStoreMonthDto;
import com.zilue.module.business.salesreport.dto.UpdateOtcSalesReportKaHeadStoreMonthDto;
import cn.dev33.satoken.annotation.SaCheckPermission;

import com.zilue.module.business.salesreport.dto.OtcSalesReportKaHeadStoreMonthPageDto;
import com.zilue.module.business.salesreport.entity.OtcSalesReportKaHeadStoreMonth;
import com.zilue.module.business.salesreport.service.ISalesReportKaHeadStoreMonthService;
import com.zilue.module.business.salesreport.vo.OtcSalesReportKaHeadStoreMonthPageVo;

import com.zilue.module.business.salesreport.vo.OtcSalesReportKaHeadStoreMonthVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* @title: ka经理管理的总部汇总按月
* <AUTHOR>
* @Date: 2025-01-20
* @Version 1.0
*/
@RestController
@RequestMapping("/business" + "/salesreportkaheadstoremonth")
@Api(value = "/business"  + "/salesreportkaheadstoremonth",tags = "ka经理管理的总部汇总按月代码")
@AllArgsConstructor
public class SalesReportKaHeadStoreMonthController {


    private final ISalesReportKaHeadStoreMonthService salesReportKaHeadStoreMonthService;

    @GetMapping("/page")
    @ApiOperation(value="OtcSalesReportKaHeadStoreMonth列表(分页)")
    @SaCheckPermission("salesreportkaheadstoremonth:detail")
    public R page(@Valid OtcSalesReportKaHeadStoreMonthPageDto dto){

        LambdaQueryWrapper<OtcSalesReportKaHeadStoreMonth> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(ObjectUtil.isNotNull(dto.getId()),OtcSalesReportKaHeadStoreMonth::getId,dto.getId())
                .like(StrUtil.isNotBlank(dto.getUserCode()),OtcSalesReportKaHeadStoreMonth::getUserCode,dto.getUserCode())
                .eq(ObjectUtil.isNotNull(dto.getPlanAmount()),OtcSalesReportKaHeadStoreMonth::getPlanAmount,dto.getPlanAmount())
                .eq(ObjectUtil.isNotNull(dto.getFinishAmount()),OtcSalesReportKaHeadStoreMonth::getFinishAmount,dto.getFinishAmount())
                .like(StrUtil.isNotBlank(dto.getBusinessMonth()),OtcSalesReportKaHeadStoreMonth::getBusinessMonth,dto.getBusinessMonth())
                .eq(ObjectUtil.isNotNull(dto.getRankNum()),OtcSalesReportKaHeadStoreMonth::getRankNum,dto.getRankNum())
                .eq(ObjectUtil.isNotNull(dto.getCreateUserId()),OtcSalesReportKaHeadStoreMonth::getCreateUserId,dto.getCreateUserId())
                .between(ObjectUtil.isNotNull(dto.getCreateDateStart()) && ObjectUtil.isNotNull(dto.getCreateDateEnd()),OtcSalesReportKaHeadStoreMonth::getCreateDate,dto.getCreateDateStart(),dto.getCreateDateEnd())
                .eq(ObjectUtil.isNotNull(dto.getModifyUserId()),OtcSalesReportKaHeadStoreMonth::getModifyUserId,dto.getModifyUserId())
                .between(ObjectUtil.isNotNull(dto.getModifyDateStart()) && ObjectUtil.isNotNull(dto.getModifyDateEnd()),OtcSalesReportKaHeadStoreMonth::getModifyDate,dto.getModifyDateStart(),dto.getModifyDateEnd())
                .eq(ObjectUtil.isNotNull(dto.getDeleteMark()),OtcSalesReportKaHeadStoreMonth::getDeleteMark,dto.getDeleteMark())
                .eq(ObjectUtil.isNotNull(dto.getEnabledMark()),OtcSalesReportKaHeadStoreMonth::getEnabledMark,dto.getEnabledMark())
                    .orderByDesc(OtcSalesReportKaHeadStoreMonth::getId)
                .select(OtcSalesReportKaHeadStoreMonth.class,x -> VoToColumnUtil.fieldsToColumns(OtcSalesReportKaHeadStoreMonthPageVo.class).contains(x.getProperty()));
        IPage<OtcSalesReportKaHeadStoreMonth> page = salesReportKaHeadStoreMonthService.page(ConventPage.getPage(dto), queryWrapper);
        PageOutput<OtcSalesReportKaHeadStoreMonthPageVo> pageOutput = ConventPage.getPageOutput(page, OtcSalesReportKaHeadStoreMonthPageVo.class);
        return R.ok(pageOutput);
    }

    @GetMapping("/info")
    @ApiOperation(value="根据id查询OtcSalesReportKaHeadStoreMonth信息")
    @SaCheckPermission("salesreportkaheadstoremonth:detail")
    public R info(@RequestParam Long id){
        OtcSalesReportKaHeadStoreMonth otcSalesReportKaHeadStoreMonth = salesReportKaHeadStoreMonthService.getById(id);
        if (otcSalesReportKaHeadStoreMonth == null) {
           return R.error("找不到此数据！");
        }
        return R.ok(BeanUtil.toBean(otcSalesReportKaHeadStoreMonth, OtcSalesReportKaHeadStoreMonthVo.class));
    }


    @PostMapping("/add")
    @ApiOperation(value = "新增OtcSalesReportKaHeadStoreMonth")
    @SaCheckPermission("salesreportkaheadstoremonth:add")
    @RepeatSubmit
    public R add(@Valid @RequestBody AddOtcSalesReportKaHeadStoreMonthDto dto){
        OtcSalesReportKaHeadStoreMonth otcSalesReportKaHeadStoreMonth = BeanUtil.toBean(dto, OtcSalesReportKaHeadStoreMonth.class);
        boolean isSuccess = salesReportKaHeadStoreMonthService.save(otcSalesReportKaHeadStoreMonth);
        return R.ok(otcSalesReportKaHeadStoreMonth.getId());
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改OtcSalesReportKaHeadStoreMonth")
    @SaCheckPermission("salesreportkaheadstoremonth:edit")
    @RepeatSubmit
    public R update(@Valid @RequestBody UpdateOtcSalesReportKaHeadStoreMonthDto dto){

        OtcSalesReportKaHeadStoreMonth otcSalesReportKaHeadStoreMonth = BeanUtil.toBean(dto, OtcSalesReportKaHeadStoreMonth.class);
        return R.ok(salesReportKaHeadStoreMonthService.updateById(otcSalesReportKaHeadStoreMonth));

    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除")
    @SaCheckPermission("salesreportkaheadstoremonth:delete")
    @RepeatSubmit
    public R delete(@Valid @RequestBody List<Long> ids){
        return R.ok(salesReportKaHeadStoreMonthService.removeBatchByIds(ids));

    }

}