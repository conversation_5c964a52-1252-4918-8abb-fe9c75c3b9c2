package com.zilue.module.business.salesreport.dto;

import com.zilue.common.page.PageInput;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.math.BigDecimal;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-01-15
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class OtcSalesReportMonthPageDto extends PageInput {

    /**
    * 主键
    */
    @ApiModelProperty("主键")
    private Long id;
    /**
    * 代表编码
    */
    @ApiModelProperty("代表编码")
    private String userCode;
    /**
    * 月计划值
    */
    @ApiModelProperty("月计划值")
    private BigDecimal planAmount;
    /**
    * 月完成值
    */
    @ApiModelProperty("月完成值")
    private BigDecimal finishAmount;
    /**
    * 业务月份
    */
    @ApiModelProperty("业务月份")
    private String businessMonth;
    /**
    * 去年同期月完成值
    */
    @ApiModelProperty("去年同期月完成值")
    private BigDecimal lastYearPeriodFinishAmount;
    /**
    * 个人排名（所有员工内）
    */
    @ApiModelProperty("个人排名（所有员工内）")
    private Long rankNum;
    /**
    * 创建人ID
    */
    @ApiModelProperty("创建人ID")
    private Long createUserId;
    /**
    * 创建日期字段开始时间
    */
    @ApiModelProperty("创建日期字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createDateStart;
    /**
    * 创建日期字段结束时间
    */
    @ApiModelProperty("创建日期字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createDateEnd;
    /**
    * 修改人ID
    */
    @ApiModelProperty("修改人ID")
    private Long modifyUserId;
    /**
    * 修改日期字段开始时间
    */
    @ApiModelProperty("修改日期字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime modifyDateStart;
    /**
    * 修改日期字段结束时间
    */
    @ApiModelProperty("修改日期字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime modifyDateEnd;
    /**
    * 删除标记
    */
    @ApiModelProperty("删除标记")
    private Integer deleteMark;
    /**
    * 有效标记
    */
    @ApiModelProperty("有效标记")
    private Integer enabledMark;

}
