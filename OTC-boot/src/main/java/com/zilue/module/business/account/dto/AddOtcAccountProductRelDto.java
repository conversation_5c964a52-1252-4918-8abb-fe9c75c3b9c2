package com.zilue.module.business.account.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * @title: 目标终端产品关系
 * <AUTHOR>
 * @Date: 2025-01-07
 * @Version 1.0
 */
@Data
public class AddOtcAccountProductRelDto implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * edp系统的主键
     */
    @ApiModelProperty("edp系统的主键")
    private String rowId;
    /**
     * 同步表写入时间
     */
    @ApiModelProperty("同步表写入时间")
    private LocalDateTime createdDate;
    /**
     * EDP数据主键
     */
    @ApiModelProperty("EDP数据主键")
    private String targetedAccountId;
    /**
     * 数据单据编号
     */
    @ApiModelProperty("数据单据编号")
    private String newId;
    /**
     * 目标客户ID
     */
    @ApiModelProperty("目标客户ID")
    private String accountId;
    /**
     * 目标客户名称
     */
    @ApiModelProperty("目标客户名称")
    private String accountName;
    /**
     * 产品品种ID
     */
    @ApiModelProperty("产品品种ID")
    private String productId;
    /**
     * 产品品种名称
     */
    @ApiModelProperty("产品品种名称")
    private String productName;
    /**
     * 产品包
     */
    @ApiModelProperty("产品包")
    private String productPacketName;
    /**
     * 营销模式
     */
    @ApiModelProperty("营销模式")
    private String marketingMode;
    /**
     * 营销模式名称
     */
    @ApiModelProperty("营销模式名称")
    private String marketingModeName;

    /**
     * 销售代表
     */
    @ApiModelProperty("销售代表")
    private String dsrId;
    /**
     * 销售代表名称
     */
    @ApiModelProperty("销售代表名称")
    private String dsrName;
    /**
     * 终端负责人（销售主管）ID
     */
    @ApiModelProperty("终端负责人（销售主管）ID")
    private String mgrId;
    /**
     * 终端负责人（销售主管）姓名
     */
    @ApiModelProperty("终端负责人（销售主管）姓名")
    private String mgrName;
    /**
     * 部门ID
     */
    @ApiModelProperty("部门ID")
    private String businessunitId;
    /**
     * 部门名称
     */
    @ApiModelProperty("部门名称")
    private String businessunitName;
    /**
     * 数据状态 0在用,1停用
     */
    @ApiModelProperty("数据状态 0在用,1停用")
    private String stateCode;
    /**
     * 终端状态 1	开发中
     * 2	停用的
     * 100000000	进院的
     * 100000001	正常的
     * 100000002	零进货
     * 100000003	风险的
     * 100000004	流失的
     * 100000005	流失恢复
     * 100000006	历史的
     */
    @ApiModelProperty("终端状态")
    private String statusCode;
    /**
     * 合作状态编号（字典项）
     */
    @ApiModelProperty("合作状态编号（字典项）")
    private String coopStatus;
    /**
     * 合作状态名称（1潜在/2开发中/3合作中/4合作暂停/5终止）
     */
    @ApiModelProperty("合作状态名称（1潜在/2开发中/3合作中/4合作暂停/5终止）")
    private String coopStatusName;
    /**
     * 持续月份
     */
    @ApiModelProperty("持续月份")
    private String sustainedMonths;
    /**
     * 开发成功日期（起始期间）
     */
    @ApiModelProperty("开发成功日期（起始期间）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime successDevDate;
    /**
     * 数据事业部标识：1otc，2招商
     */
    @ApiModelProperty("数据事业部标识：1otc，2招商")
    private String businessGroupType;

}
