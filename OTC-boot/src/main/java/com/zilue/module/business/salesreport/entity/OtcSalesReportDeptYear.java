package com.zilue.module.business.salesreport.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.math.BigDecimal;


/**
* @title: 团队销售业绩按年
* <AUTHOR>
* @Date: 2025-01-15
* @Version 1.0
*/
@Data
@TableName("otc_sales_report_dept_year")
@ApiModel(value = "团队销售业绩按年对象", description = "团队销售业绩按年")
@EqualsAndHashCode(callSuper = true)
public class OtcSalesReportDeptYear extends OtcSalesPerformanceRank {

    private static final long serialVersionUID = 1L;

    /**
    *
    */
    @ApiModelProperty("")
    @TableId
    private Long id;
    /**
     * 父团队编码
     */
    @ApiModelProperty("父团队编码")
    private String parentDeptCode;
    /**
    * 团队编码
    */
    @ApiModelProperty("团队编码")
    private String deptCode;
    /**
    * 年计划值
    */
    @ApiModelProperty("年计划值")
    private BigDecimal planAmount;

    /**
    * 业务年份
    */
    @ApiModelProperty("业务年份")
    private String businessYear;

    /**
    * 创建人ID
    */
    @ApiModelProperty("创建人ID")
    @TableField(fill = FieldFill.INSERT)
    private Long createUserId;
    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createDate;
    /**
    * 修改人ID
    */
    @ApiModelProperty("修改人ID")
    @TableField(fill = FieldFill.UPDATE)
    private Long modifyUserId;
    /**
    * 修改日期
    */
    @ApiModelProperty("修改日期")
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime modifyDate;
    /**
    * 删除标记
    */
    @ApiModelProperty("删除标记")
    @TableField(fill = FieldFill.INSERT)
    @TableLogic
    private Integer deleteMark;
    /**
    * 有效标记
    */
    @ApiModelProperty("有效标记")
    @TableField(fill = FieldFill.INSERT)
    private Integer enabledMark;


}