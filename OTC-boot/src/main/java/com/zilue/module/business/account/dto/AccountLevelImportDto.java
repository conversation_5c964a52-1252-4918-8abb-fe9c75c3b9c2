package com.zilue.module.business.account.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AccountLevelImportDto {
    @ApiModelProperty("目标终端编号")
    @ExcelProperty("目标终端编号")
    private String newId;

    @ApiModelProperty("门店名称")
    @ExcelProperty("门店名称")
    private String name;

    @ApiModelProperty("门店分类")
    @ExcelProperty("门店分类")
    private String accountLevelName;
}
