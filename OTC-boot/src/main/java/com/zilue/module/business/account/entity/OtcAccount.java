package com.zilue.module.business.account.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * @title: 客户目录
 * <AUTHOR>
 * @Date: 2024-12-27
 * @Version 1.0
 */
@Data
@TableName("otc_account")
@ApiModel(value = "客户目录对象", description = "客户目录")
public class OtcAccount implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    @TableId
    private Long id;
    /**
     * 客户id
     */
    @ApiModelProperty("客户id")
    private String accountId;
    /**
     * 状态：0在用，1停用
     */
    @ApiModelProperty("状态：0在用，1停用")
    private String stateCode;
    /**
     * 客户编号
     */
    @ApiModelProperty("客户编号")
    private String accountNumber;
    /**
     * 客户名称
     */
    @ApiModelProperty("客户名称")
    private String name;
    /**
     * 上级客户id
     */
    @ApiModelProperty("上级客户id")
    private String parentAccountId;
    /**
     * 上级客户名称
     */
    @ApiModelProperty("上级客户名称")
    private String parentAccountIdName;
    /**
     * 客户类型
     * 1	医药商业
     * 	2	医院
     * 	3	药店
     * 	4	基层医疗卫生机构
     * 	5	个体
     * 	11	物流公司
     * 	12	快递公司
     * 	13	方舱医院
     * 	99	其他
     */
    @ApiModelProperty("客户类型")
    private String customerTypeCode;
    /**
     * 客户类型名称
     */
    @ApiModelProperty("客户类型名称")
    private String customerTypeName;
    /**
     * 商业类型编号
     * 	1普通商业
     * 	2连锁商业
     */
    @ApiModelProperty("商业类型编号")
    private String businessTypeCode;
    /**
     * 商业类型名称
     */
    @ApiModelProperty("商业类型名称")
    private String businessTypeName;
    /**
     * 终端业态
     */
    @ApiModelProperty("终端业态")
    private String newTypeOfOperationIdName;

    /**
     * 是否标注：0否，1是
     */
    @ApiModelProperty("是否标注：0否，1是")
    private String doNotFax;
    /**
     * 百度地图 纬度
     */
    @ApiModelProperty("百度地图-纬度")
    private String address1Latitude;
    /**
     * 百度地图 经度
     */
    @ApiModelProperty("百度地图-经度")
    private String address1Longitude;
    /**
     * edp创建时间
     */
    @ApiModelProperty("edp创建时间")
    private String createdOn;
    /**
     * edp创建者id
     */
    @ApiModelProperty("edp创建者id")
    private String createdBy;
    /**
     * edp创建者
     */
    @ApiModelProperty("edp创建者")
    private String createdByName;
    /**
     * edp修改时间
     */
    @ApiModelProperty("edp修改时间")
    private String modifiedOn;
    /**
     * edp修改者id
     */
    @ApiModelProperty("edp修改者id")
    private String modifiedBy;
    /**
     * edp修改者
     */
    @ApiModelProperty("edp修改者")
    private String modifiedByName;
    /**
     * 税号
     */
    @ApiModelProperty("税号")
    private String newIrd;
    /**
     * 省份编码
     */
    @ApiModelProperty("省份编码")
    private String provinceCode;
    /**
     * 省份名称
     */
    @ApiModelProperty("省份名称")
    private String province;
    /**
     * 城市编码
     */
    @ApiModelProperty("城市编码")
    private String cityCode;
    /**
     * 城市名称
     */
    @ApiModelProperty("城市名称")
    private String city;
    /**
     * 区县编码
     */
    @ApiModelProperty("区县编码")
    private String districtCode;
    /**
     * 区县名称
     */
    @ApiModelProperty("区县名称")
    private String district;

    /**
     * 客户联系电话
     */
    @ApiModelProperty("客户联系电话")
    private String address1Telephone1;
    /**
     * 客户资格
     */
    @ApiModelProperty("客户资格")
    private String newRelationList;
    /**
     * 市场级别编号 --数据事业部标识
     * *********	高端市场
     * 	*********	基层市场
     * 	*********	OTC市场
     * 	*********	第三终端市场
     * 	100000004	民营市场
     */
    @ApiModelProperty("市场级别编号")
    private String newShicjb;
    /**
     * 市场级别名称 -数据事业部标识
     * ********* OTC市场 为OTC事业部数据
     * 其他都是招商代理
     */
    @ApiModelProperty("市场级别名称")
    private String newShicjbName;
    /**
     * 客户级别编号
     * 	*********	三级
     * 	*********	二级
     * 	*********	一级
     * 	*********	无
     */
    @ApiModelProperty("客户级别编号")
    private String newHospitalLevel;
    /**
     * 客户级别名称
     */
    @ApiModelProperty("客户级别名称")
    private String newHospitalLevelName;
    /**
     * 客户等别编号
     * 	*********	特等
     * 	*********	甲等
     * 	*********	乙等
     * 	*********	丙等
     * 	*********	无
     */
    @ApiModelProperty("客户等别编号")
    private String newHospitalGrade;
    /**
     * 客户等别名称
     */
    @ApiModelProperty("客户等别名称")
    private String newHospitalGradeName;

    /**
     * 开户行
     */
    @ApiModelProperty("开户行")
    private String newBank;
    /**
     * 银行帐号
     */
    @ApiModelProperty("银行帐号")
    private String newBankAccount;
    /**
     * 收货地址
     */
    @ApiModelProperty("收货地址")
    private String address1Name;
    /**
     * 收货联系人
     */
    @ApiModelProperty("收货联系人")
    private String address1PrimaryContactName;
    /**
     * 收货电话
     */
    @ApiModelProperty("收货电话")
    private String address1Telephone2;
    /**
     * 同步表写入时间
     */
    @ApiModelProperty("同步表写入时间")
    private LocalDateTime createdDate;
    /**
     * 同步表id
     */
    @ApiModelProperty("同步表id")
    private String rowId;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private Long createUserId;
    /**
     * 创建日期
     */
    @ApiModelProperty("创建日期")
    private LocalDateTime createDate;
    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private Long modifyUserId;
    /**
     * 修改日期
     */
    @ApiModelProperty("修改日期")
    private LocalDateTime modifyDate;
    /**
     * 删除标记
     */
    @ApiModelProperty("删除标记")
    @TableLogic
    private Integer deleteMark;
    /**
     * 有效标记
     */
    @ApiModelProperty("有效标记")
    private Integer enabledMark;

    /**
     * 客户类别：0：总部；1:分部；2：门店
     */
    @ApiModelProperty("客户类别：0：总部；1:分部；2：门店")
    private Integer terminalType;
    @ApiModelProperty("连锁总部id")
    private String newOtcAccountId;
    @ApiModelProperty("连锁总部名称")
    private String newOtcAccountName;

    /**
     * 0：直营；1：加盟
     */
    @ApiModelProperty("0：直营；1：加盟")
    private Integer isDirectJoin;

    /**
     * 是否DTP门店：0：否；1：是
     */
    @ApiModelProperty("是否DTP门店：0：否；1：是")
    private Integer isDtp;

    /**
     * 是否统筹门店：0：否；1：是
     */
    @ApiModelProperty("是否统筹门店：0：否；1：是")
    private Integer isWhole;

    /**
     * 是否电商门店：0：否；1：是
     */
    @ApiModelProperty("是否电商门店：0：否；1：是")
    private Integer isOnline;

    /**
     * 销售规模
     */
    @ApiModelProperty("销售规模")
    private String saleScale;

    /**
     * 法定代表人
     */
    @ApiModelProperty("法定代表人")
    private String legalPerson;

    /**
     * 营业执照编码
     */
    @ApiModelProperty("营业执照编码")
    private String businessLicenseCode;

}