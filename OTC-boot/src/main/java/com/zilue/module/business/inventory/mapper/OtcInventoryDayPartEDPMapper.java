package com.zilue.module.business.inventory.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zilue.module.business.inventory.entity.OtcInventoryDayPart;
import com.zilue.module.business.inventory.vo.OtcInventoryDayPartVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@DS("slave")
public interface OtcInventoryDayPartEDPMapper extends BaseMapper<OtcInventoryDayPart> {
    List<OtcInventoryDayPartVo> queryList(@Param("syncTime") String syncTime, @Param("skip") int skip, @Param("offset") int offset);

    Integer queryCount(@Param("syncTime") String syncTime);
}
