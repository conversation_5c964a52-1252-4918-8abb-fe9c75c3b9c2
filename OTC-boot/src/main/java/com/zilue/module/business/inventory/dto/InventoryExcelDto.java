package com.zilue.module.business.inventory.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class InventoryExcelDto {
    @ApiModelProperty("总部名称")
    private String parentCustomerName;

    @ApiModelProperty("连锁实际进货配送单位EDP名称(分部)")
    private String customerName;

    @ApiModelProperty("省份")
    private String provinceName;

    @ApiModelProperty("是否10大连锁：0：否；1：是")
    private Integer isTen;

    @ApiModelProperty("品种id")
    private String productGroupId;
    @ApiModelProperty("品种编码")
    private String productGroupCode;
    @ApiModelProperty("品种")
    private String productGroup;
    @ApiModelProperty("品规id")
    private String productId;
    @ApiModelProperty("品规编码")
    private String productCode;
    @ApiModelProperty("品规")
    private String product;
    @ApiModelProperty("业务时间")
    private String businessTime;

    @ApiModelProperty("导出分部还是总部 0：总部 1：分部")
    private Integer excelType;
}
