package com.zilue.module.business.customer.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.model.result.R;
import com.zilue.module.business.customer.entity.OtcCustomerTagRel;
import com.zilue.module.business.customer.service.ICustomerTagRelService;
import com.zilue.module.business.customer.vo.OtcCustomerTagRelVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping(GlobalConstant.BUSINESS_MODULE_PREFIX + "/customerTagRel")
@Api(value = GlobalConstant.BUSINESS_MODULE_PREFIX + "/customerTagRel", tags = "标签关联客户")
@AllArgsConstructor
public class CustomerTagRelController {
    private final ICustomerTagRelService customerTagRelService;


    @PostMapping(value = "/add")
    @ApiOperation(value = "新增客户")
    public R add(@Valid @RequestBody OtcCustomerTagRelVo dto) {
        List<Long> otcCustomerTagVoList = dto.getCustomerTagIdList();
        List<OtcCustomerTagRel> otcCustomerTagRels = new ArrayList<>();

// 为每个标签 ID 创建 OtcCustomerTagRel 对象，并设置公共属性
        for (Long tagId : otcCustomerTagVoList) {
            OtcCustomerTagRel rel = new OtcCustomerTagRel();
            rel.setAccountId(tagId);
            rel.setCreateUserId(dto.getCreateUserId());
            rel.setModifyUserId(dto.getModifyUserId());
            rel.setCustomerTagId(dto.getAccountId());
            otcCustomerTagRels.add(rel);
        }

        customerTagRelService.saveBatch(otcCustomerTagRels);

        return R.ok();
    }

    @PostMapping(value = "/delete")
    @ApiOperation(value = "删除客户")
    public R remove(@Valid @RequestBody OtcCustomerTagRelVo dto) {
        List<Long> otcCustomerTagVoList = dto.getCustomerTagIdList();

        // 使用 QueryWrapper 构建删除条件，删除指定 accountId 下的多个 customerTagId
        QueryWrapper<OtcCustomerTagRel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("customer_tag_id", dto.getAccountId())
                .in("account_id", otcCustomerTagVoList);

        // 执行删除操作
        boolean result = customerTagRelService.remove(queryWrapper);

        // 返回操作结果
        if (result) {
            return R.ok("删除成功");
        } else {
            return R.error("删除失败");
        }
    }


}
