package com.zilue.module.business.product.controller;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.model.result.R;
import com.zilue.common.page.ConventPage;
import com.zilue.common.page.PageOutput;
import com.zilue.common.utils.VoToColumnUtil;
import com.zilue.module.business.product.dto.ProductDto;
import com.zilue.module.business.product.dto.ProductGroupDto;
import com.zilue.module.business.product.entity.Product;
import com.zilue.module.business.product.entity.ProductGroup;
import com.zilue.module.business.product.service.IProductGroupService;
import com.zilue.module.business.product.service.IProductService;
import com.zilue.module.business.product.vo.ProductGroupVo;
import com.zilue.module.business.product.vo.ProductVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping(GlobalConstant.BUSINESS_MODULE_PREFIX + "/Product")
@Api(value = GlobalConstant.BUSINESS_MODULE_PREFIX + "/Product", tags = "品规信息")
@AllArgsConstructor
public class ProductController {
    private final IProductGroupService productGroupService;
    private final IProductService productService;


    @PostMapping("/pageProductGroup")
    @ApiOperation(value = "品种")
    // @SaCheckPermission("punchset:detail")
    public R<PageOutput<ProductGroupVo>> page(@Valid @RequestBody ProductGroupDto dto) {
        LambdaQueryWrapper<ProductGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(ObjectUtil.isNotNull(dto.getNewName()), ProductGroup::getNewName, dto.getNewName())
                .orderByDesc(ProductGroup::getId)
                .select(ProductGroup.class, x -> VoToColumnUtil.fieldsToColumns(ProductGroupVo.class).contains(x.getProperty()));
        IPage<ProductGroup> page = productGroupService.page(ConventPage.getPage(dto), queryWrapper);
        PageOutput<ProductGroupVo> pageOutput = ConventPage.getPageOutput(page, ProductGroupVo.class);
        return R.ok(pageOutput);
    }


    @PostMapping("/pageProduct")
    @ApiOperation(value = "品规信息")
    // @SaCheckPermission("punchset:detail")
    public R<PageOutput<ProductVo>> page(@Valid @RequestBody ProductDto dto) {
        LambdaQueryWrapper<Product> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(ObjectUtil.isNotNull(dto.getName()), Product::getName, dto.getName())
                .orderByDesc(Product::getId)
                .select(Product.class, x -> VoToColumnUtil.fieldsToColumns(ProductVo.class).contains(x.getProperty()));
        IPage<Product> page = productService.page(ConventPage.getPage(dto), queryWrapper);
        PageOutput<ProductVo> pageOutput = ConventPage.getPageOutput(page, ProductVo.class);
        return R.ok(pageOutput);
    }

}
