package com.zilue.module.business.customer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zilue.common.enums.DeleteMark;
import com.zilue.common.enums.DictionaryIdEnum;
import com.zilue.common.enums.EnabledMark;
import com.zilue.common.exception.MyException;
import com.zilue.common.model.result.R;
import com.zilue.common.page.ConventPage;
import com.zilue.common.page.PageOutput;
import com.zilue.common.utils.StringUtil;
import com.zilue.module.business.customer.entity.OtcCustomerTag;
import com.zilue.module.business.customer.entity.OtcCustomerTagRel;
import com.zilue.module.business.customer.mapper.CustomerTagMapper;
import com.zilue.module.business.customer.service.ICustomerTagRelService;
import com.zilue.module.business.customer.service.ICustomerTagService;
import com.zilue.module.business.customer.vo.CustomerTagVo;
import com.zilue.module.system.entity.DictionaryDetail;
import com.zilue.module.system.service.IDictionarydetailService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class CustomerTagImpl extends ServiceImpl<CustomerTagMapper, OtcCustomerTag> implements ICustomerTagService {

    private final ICustomerTagRelService customerTagRelService;
    private final IDictionarydetailService dictionarydetailService;

    @Override
    public PageOutput<CustomerTagVo> getPage(CustomerTagVo dto) {

        LambdaQueryWrapper<OtcCustomerTag> otcCustomerTagLambdaQueryWrapper = Wrappers.lambdaQuery(OtcCustomerTag.class)
                .eq(OtcCustomerTag::getEnabledMark, EnabledMark.ENABLED.getCode())
                .eq(OtcCustomerTag::getDeleteMark, DeleteMark.NODELETE.getCode())
                .like(StringUtil.isNotBlank(dto.getTagName()), OtcCustomerTag::getTagName, dto.getTagName())
                .orderByDesc(OtcCustomerTag::getCreateDate);
        IPage<OtcCustomerTag> page = this.page(ConventPage.getPage(dto), otcCustomerTagLambdaQueryWrapper);

        PageOutput<CustomerTagVo> pageOutput = ConventPage.getPageOutput(page, CustomerTagVo.class);
        List<CustomerTagVo> customerTagVos = pageOutput.getList().stream().filter(tag -> tag.getId() != null).collect(Collectors.toList());

        //查询标签
        List<DictionaryDetail> dictionaryDetailList = dictionarydetailService.list(new LambdaQueryWrapper<DictionaryDetail>().eq(DictionaryDetail::getItemId, DictionaryIdEnum.ITEMID.getCode())
                .eq(DictionaryDetail::getEnabledMark, EnabledMark.ENABLED.getCode()).eq(DictionaryDetail::getDeleteMark, DeleteMark.NODELETE.getCode()));
        Map<String, String> dictionaryMap = dictionaryDetailList.stream().collect(Collectors.toMap(DictionaryDetail::getValue, DictionaryDetail::getName));

        if (!customerTagVos.isEmpty()) {
            // 收集所有标签的ID
            List<Long> tagIds = customerTagVos.stream().map(CustomerTagVo::getId).collect(Collectors.toList());
            // 一次性查询所有关联记录，并按 customerTagId 分组统计每个标签的关联数量
            List<Map<String, Object>> tagIdCountList = customerTagRelService.getBaseMapper().selectMaps(
                    new QueryWrapper<OtcCustomerTagRel>()
                            .select("customer_tag_id, COUNT(*) as count")
                            .in("customer_tag_id", tagIds)
                            .eq("delete_mark", DeleteMark.NODELETE.getCode())
                            .eq("enabled_mark", EnabledMark.ENABLED.getCode())
                            .groupBy("customer_tag_id")
            );

            // 将查询结果转换为 Map<Long, Long>，键为 customerTagId，值为关联数量
            Map<Long, Long> incidenceCountMap = tagIdCountList.stream()
                    .collect(Collectors.toMap(
                            map -> (Long) map.get("customer_tag_id"),
                            map -> Optional.ofNullable((Number) map.get("count")).map(Number::longValue).orElse(0L)
                    ));

            customerTagVos.forEach(tag -> {
                Long incidenceNumber = incidenceCountMap.getOrDefault(tag.getId(), 0L);
                tag.setIncidenceNumber(incidenceNumber.intValue());
                tag.setTagTypeName(dictionaryMap.get(tag.getTagType()));
            });
        }
        pageOutput.setList(customerTagVos);
        return pageOutput;
    }

    @Override
    public R addOrUpdate(OtcCustomerTag dto) {
        // 判断是否为新增操作
        // 如果 ID 为空或为 0，视为新增
        if (dto.getId() == null || dto.getId() == 0) {
            // 检查标签名是否重复
            boolean exists = baseMapper.selectCount(
                    Wrappers.lambdaQuery(OtcCustomerTag.class)
                            .eq(OtcCustomerTag::getTagName, dto.getTagName()).eq(OtcCustomerTag::getDeleteMark, DeleteMark.NODELETE.getCode())) > 0;
            boolean existsCode = baseMapper.selectCount(
                    Wrappers.lambdaQuery(OtcCustomerTag.class)
                            .eq(OtcCustomerTag::getTagCode, dto.getTagCode()).eq(OtcCustomerTag::getDeleteMark, DeleteMark.NODELETE.getCode())) > 0;

            if (exists||existsCode) {
                throw new RuntimeException("标签名或标签编码已存在");
            }
        }
        // 执行新增或编辑操作
        boolean result = this.saveOrUpdate(dto);
        return R.ok(result);
    }
}
