package com.zilue.module.business.product.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ProductGroupVo {
    @ApiModelProperty("")
    private Long id;
    @ApiModelProperty("品种名称")
    private String newName;
    @ApiModelProperty("产品品种id")
    private String newProductgroupId;

    @ApiModelProperty("标准品规id")
    private String newProductid;
    @ApiModelProperty("标准品规名称")
    private String newProductidName;

    @ApiModelProperty("已盘点/未盘点")
    private String inventoryStatus;
}
