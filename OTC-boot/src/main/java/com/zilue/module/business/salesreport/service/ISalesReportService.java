package com.zilue.module.business.salesreport.service;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/1/15 下午1:40
 * @description
 */
public interface ISalesReportService {
    /**
     * 按代表汇总数据（按月，包括排名）
     *
     * @param month
     */
    void salesReportMonth(String month);

    /**
     * 按代表汇总数据（按年，包括排名）
     *
     * @param year
     */
    void salesReportYear(String year);

    /**
     * 团队汇总按月
     *
     * @param month
     */
    void salesReportDeptMonth(String month);

    /**
     * 团队汇总按年
     *
     * @param year
     */
    void salesReportDeptYear(String year);


    /**
     * 连锁总部汇总按月
     *
     * @param month
     */
    void salesReportHeadStoreMonth(String month);


    /**
     * 连锁总部汇总按年
     *
     * @param year
     */
    void salesReportHeadStoreYear(String year);


    /**
     * KA连锁总部汇总按月
     *
     * @param month
     */
    void salesReportKaMonth(String month);


    /**
     * KA连锁总部汇总按年
     *
     * @param year
     */
    void salesReportKaYear(String year);


    /**
     * 拜访达成率以及排名的计算
     *
     * @param yesterday {@link Date 昨天}
     */
    void visitTargetRank(Date yesterday);

    void salesLevelReportMonth(String month);

    void salesLevelReportYear(String year);
}
