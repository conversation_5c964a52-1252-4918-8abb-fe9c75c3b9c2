package com.zilue.module.business.account.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class AddOtcAccountChainRelDto implements Serializable {
    /**
     * 目标客户ID
     */
    @ApiModelProperty("目标客户ID")
    private String accountId;
    /**
     * 目标客户名称
     */
    @ApiModelProperty("目标客户名称")
    private String accountName;

    /**
     * 用户id
     */
    @ApiModelProperty("用户id")
    private String systemUserId;

    /**
     * 负责人
     */
    @ApiModelProperty("负责人")
    private String ownerName;

    /**
     * 负责人工号
     */
    @ApiModelProperty("负责人工号")
    private String ownerNumber;

    /**
     * 连锁总部id
     */
    @ApiModelProperty("连锁总部id")
    private String zongBuId;

    /**
     * 连锁总部id
     */
    @ApiModelProperty("连锁分部accountId")
    private String newAccountId;
}
