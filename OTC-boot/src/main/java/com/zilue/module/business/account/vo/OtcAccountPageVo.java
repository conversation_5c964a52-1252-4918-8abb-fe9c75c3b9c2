package com.zilue.module.business.account.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.zilue.module.business.account.entity.OtcAccount;
import com.zilue.module.business.customer.vo.CustomerTagVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
* @title: 分页列表出参
* <AUTHOR>
* @Date: 2024-12-27
* @Version 1.0
*/
@Data
public class OtcAccountPageVo {

    /**
    * 主键ID
    */
    @ExcelIgnore
    @ApiModelProperty("主键ID")
    private Long id;
    /**
    * 客户编号
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty(value = "客户编号", index =1)
    @ApiModelProperty("客户编号")
    private String accountNumber;
    /**
    * 客户名称
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty(value = "客户名称", index =2)
    @ApiModelProperty("客户名称")
    private String name;
    /**
    * 上级客户名称
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty(value = "分部客户名称", index =3)
    @ApiModelProperty("分部客户名称")
    private String parentAccountIdName;

    @ApiModelProperty("连锁总部名称")
    private String newOtcAccountName;
    /**
    * 客户类型
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty(value = "客户类型名称", index =4)
    @ApiModelProperty("客户类型名称")
    private String customerTypeCodeName;

    /**
     * 标签名称
     */
    @ExcelProperty(value = "标签名称")
    @ApiModelProperty("标签名称")
    private String customerTagName;

    /**
    * 商业类型：1普通商业，2连锁商业
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty(value = "商业类型名称", index =5)
    @ApiModelProperty("商业类型名称")
    private String businessTypeName;
    /**
    * 终端业态
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty(value = "终端业态", index =6)
    @ApiModelProperty("终端业态")
    private String newTypeOfOperationIdName;
    /**
    * 税号
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty(value = "税号", index =7)
    @ApiModelProperty("税号")
    private String newIrd;
    /**
    * 终端级别
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty(value = "终端级别", index =8)
    @ApiModelProperty("终端级别")
    private String newHospitalLevel;
    /**
    * 终端等别
    */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty(value = "终端等别", index =9)
    @ApiModelProperty("终端等别")
    private String newHospitalGrade;

    @ApiModelProperty("所属辖区")
    private String belongRegion;

    /**
     * 负责人
     */
    @ApiModelProperty("负责人")
    private String ownerName;

    /**
     * 负责人工号
     */
    @ApiModelProperty("负责人工号")
    private String ownerNumber;

    /**
     * 省份名称
     */
    @ApiModelProperty("省份名称")
    private String province;

    /**
     * 城市名称
     */
    @ApiModelProperty("城市名称")
    private String city;

    /**
     * 区县名称
     */
    @ApiModelProperty("区县名称")
    private String district;

    /**
     * 详细地址
     */
    @ApiModelProperty("详细地址")
    private String address1Name;
    /**
     * 百度地图 纬度
     */
    @ApiModelProperty("百度地图-纬度")
    private String address1Latitude;
    /**
     * 百度地图 经度
     */
    @ApiModelProperty("百度地图-经度")
    private String address1Longitude;
    /**
     * 客户标签名称集合
     */
    @ApiModelProperty("客户标签名称集合")
    private List<CustomerTagVo> customerTagList;

    /**
     * 0：直营；1：加盟
     */
    @ApiModelProperty("0：直营；1：加盟")
    private Integer isDirectJoin;

    /**
     * 是否DTP门店：0：否；1：是
     */
    @ApiModelProperty("是否DTP门店：0：否；1：是")
    private Integer isDtp;

    /**
     * 是否统筹门店：0：否；1：是
     */
    @ApiModelProperty("是否统筹门店：0：否；1：是")
    private Integer isWhole;

    /**
     * 是否电商门店：0：否；1：是
     */
    @ApiModelProperty("是否电商门店：0：否；1：是")
    private Integer isOnline;
    @ApiModelProperty("距离(千米)")
    private Integer distance;
    @ApiModelProperty("当月拜访家次")
    private Integer real;

    /**
     * 销售规模
     */
    @ApiModelProperty("销售规模")
    private String saleScale;

    /**
     * 客户联系电话
     */
    @ApiModelProperty("客户联系电话")
    private String address1Telephone1;

    /**
     * 法定代表人
     */
    @ApiModelProperty("法定代表人")
    private String legalPerson;

    /**
     * 营业执照编码
     */
    @ApiModelProperty("营业执照编码")
    private String businessLicenseCode;

    /**
     * 高德地图 纬度
     */
    @ApiModelProperty("高德地图-纬度")
    private String address2Latitude;
    /**
     * 高德地图 经度
     */
    @ApiModelProperty("高德地图-经度")
    private String address2Longitude;
    @ApiModelProperty("客户类别：0：总部；1:分部；2：门店")
    private Integer terminalType;
}
