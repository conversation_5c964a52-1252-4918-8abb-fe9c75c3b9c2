package com.zilue.module.business.salesindicators.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zilue.common.utils.StringUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * @title: 销售指标
 * <AUTHOR>
 * @Date: 2025-01-15
 * @Version 1.0
 */
@Data
@TableName("crm_OTC_BP")
@ApiModel(value = "销售指标对象", description = "销售指标")
public class OtcSalesmanSalesIndicators implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @TableId
    private String id;
    /**
     * edp数据主键
     */
    @ApiModelProperty("edp数据主键")
    private String edpId;
    /**
     * 代表名称
     */
    @ApiModelProperty("代表名称")
    private String userName;
    /**
     * 代表编码
     */
    @ApiModelProperty("代表编码")
    private String userCode;
    /**
     * 组织名称
     */
    @ApiModelProperty("组织名称")
    private String deptName;
    /**
     * 组织编码
     */
    @ApiModelProperty("组织编码")
    private String deptCode;
    /**
     * 总部名称
     */
    @ApiModelProperty("总部名称")
    private String headquartersName;
    /**
     * 总部编码
     */
    @ApiModelProperty("总部编码")
    private String headquartersCode;
    /**
     * 分部名称
     */
    @ApiModelProperty("分部名称")
    private String subName;
    /**
     * 分部编码
     */
    @ApiModelProperty("分部编码")
    private String subCode;
    /**
     * 门店名称
     */
    @ApiModelProperty("门店名称")
    private String storeName;
    /**
     * 门店编码
     */
    @ApiModelProperty("门店编码")
    private String storeCode;
    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    private String productName;
    /**
     * 产品编码
     */
    @ApiModelProperty("产品编码")
    private String productCode;

    /**
     * 数量
     */
    @ApiModelProperty("数量")
    @TableField("数量")
    private Float edpSalesNum;

    /**
     * 数量
     */
    @ApiModelProperty("数量")
    private BigDecimal salesNum;


    /**
     * 单价
     */
    @ApiModelProperty("单价")
    private BigDecimal unitPrice;


    /**
     * 业绩值
     */
    @ApiModelProperty("业绩值")
    private Float edpAmount;

    /**
     * 指标值
     */
    @ApiModelProperty("指标值")
    private BigDecimal amount;

    /**
     * 日期
     */
    @ApiModelProperty("日期")
    private String businessDay;
    /**
     * 月份
     */
    @ApiModelProperty("月份")
    private String businessMonth;
    /**
     * 数据时间
     */
    @ApiModelProperty("数据时间")
    private String syncTime;


    /**
     * 产品编码
     */
    @ApiModelProperty("产品线")
    private String productLine;


    /**
     * 同步批次号
     */
    @TableField(exist = false)
    private Long batchId;

    /**
     * 创建时间
     */
    @TableField(exist = false)
    private String createTime;


    /**
     * 删除标记
     */
    @ApiModelProperty("删除标记")
    private Integer deleteMark;


    public Float getEdpSalesNum() {
        return edpSalesNum;
    }

    public void setEdpSalesNum(Float edpSalesNum) {
        this.edpSalesNum = edpSalesNum;
        this.salesNum = new BigDecimal(String.valueOf(edpSalesNum));
    }

    public Float getEdpAmount() {
        return edpAmount;
    }

    public void setEdpAmount(Float edpAmount) {
        this.edpAmount = edpAmount;
        this.amount = new BigDecimal(String.valueOf(edpAmount));
    }

    public String getBusinessDay() {
        return businessDay;
    }

    public void setBusinessDay(String businessDay) {
        this.businessDay = businessDay;
        if (StringUtil.isNotBlank(businessDay) && businessDay.length() >= 7) {
            this.businessMonth = businessDay.substring(0, 7);
        } else {
            this.businessMonth = businessDay;
        }
    }
}