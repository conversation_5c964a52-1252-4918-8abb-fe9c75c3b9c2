package com.zilue.module.business.signreached.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.github.yulichang.annotation.EntityMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalTime;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.util.List;


/**
* @title: 部门打卡排名报表
* <AUTHOR>
* @Date: 2025-01-17
* @Version 1.0
*/
@Data
@TableName("otc_sales_report_sign_reached")
@ApiModel(value = "部门打卡排名报表对象", description = "部门打卡排名报表")
@Accessors(chain = true)
public class OtcSalesReportSignReached implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 
    */
    @ApiModelProperty("")
    @TableId
    private Long id;
    /**
    * 部门id
    */
    @ApiModelProperty("部门id")
    private Long deptId;
    /**
    * 上级部门id
    */
    @ApiModelProperty("上级部门id")
    private Long parentDeptId;
    /**
    * 达成人数
    */
    @ApiModelProperty("达成人数")
    private Integer reachNum;
    /**
    * 部门人数
    */
    @ApiModelProperty("部门人数")
    private Integer deptPersonNum;
    /**
    * 达成率
    */
    @ApiModelProperty("达成率")
    private BigDecimal reachRate;
    /**
    * 部门排名（同级部门中）
    */
    @ApiModelProperty("部门排名（同级部门中）")
    private Long rankNum;
    /**
    * 统计日期
    */
    @ApiModelProperty("统计日期")
    private LocalDateTime reportDate;
    /**
    * 创建人ID
    */
    @ApiModelProperty("创建人ID")
    @TableField(fill = FieldFill.INSERT)
    private Long createUserId;
    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createDate;
    /**
    * 修改人ID
    */
    @ApiModelProperty("修改人ID")
    @TableField(fill = FieldFill.UPDATE)
    private Long modifyUserId;
    /**
    * 修改日期
    */
    @ApiModelProperty("修改日期")
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime modifyDate;
    /**
    * 删除标记
    */
    @ApiModelProperty("删除标记")
    @TableField(fill = FieldFill.INSERT)
    @TableLogic
    private Integer deleteMark;
    /**
    * 有效标记
    */
    @ApiModelProperty("有效标记")
    @TableField(fill = FieldFill.INSERT)
    private Integer enabledMark;


}