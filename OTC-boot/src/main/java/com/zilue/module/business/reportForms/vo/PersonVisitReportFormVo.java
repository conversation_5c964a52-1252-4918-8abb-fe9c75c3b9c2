package com.zilue.module.business.reportForms.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

@Data
public class PersonVisitReportFormVo {

    @ApiModelProperty("机构id")
    private String departmentId;

    @ApiModelProperty("机构名称")
    private String departmentName;

    @ApiModelProperty("员工编号")
    private Long personId;

    @ApiModelProperty("姓名")
    private String personName;

    @ApiModelProperty("达标天数")
    private Integer reachedDayNum;

    @ApiModelProperty("目标值")
    private Integer standardNum;

    @ApiModelProperty("达标率")
    private BigDecimal reachedRate;

    @ApiModelProperty("开始年月")
    private String startYearMonth;

    @ApiModelProperty("结束年月")
    private String endYearMonth;

    @ApiModelProperty("核算频率，1代表日，2代表月 看字典")
    private String frequency;

    @ApiModelProperty("打卡次/日")
    private Map<String, Long> signDateMap;

}
