package com.zilue.module.business.account.consumer;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zilue.common.rabbitmq.MqConstant;
import com.zilue.common.utils.JsonUtil;
import com.zilue.module.business.account.dto.AddOtcAccountDto;
import com.zilue.module.business.account.dto.AddOtcAccountProductRelDto;
import com.zilue.module.business.account.entity.OtcAccount;
import com.zilue.module.business.account.entity.OtcAccountUserRelation;
import com.zilue.module.business.account.service.IAccountService;
import com.zilue.module.business.account.service.IAccountUserRelationService;
import com.zilue.module.organization.entity.Post;
import com.zilue.module.organization.entity.User;
import com.zilue.module.organization.service.IUserService;
import com.zilue.module.wechat.sign.entity.OtcSignIn;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 定义接收者（可以定义N个接受者，消息会均匀的发送到N个接收者中）
 *  终端和负责人同步
 * RabbitMq接受者1
 * （@RabbitListener声明类上，一个类只能监听一个队列）
 * @author: huoyh
 * @date: 2025/01/14
 */
@Slf4j
@Component
@AllArgsConstructor
public class AccountRelUserConsumerListenerProcess {

    private final IAccountService accountService;

    private final IUserService userService;

    private final IAccountUserRelationService userRelationService;

    private static final int BATCH_SIZE = 1000; // 每批处理的数据量

  /*  @RabbitListener(bindings = {
            @QueueBinding(
                    value = @Queue,
                    exchange = @Exchange(type = "topic", name = MqConstant.TOPIC_OTC),
                    key = {MqConstant.ROUTING_KEY_CUSTOMER_PRODUCT_REL}
            )
    })
    public void onMessage(String message) {

        if(StringUtils.isNotBlank(message)) {
            List<AddOtcAccountProductRelDto> accountDtoList = JsonUtil.convertJsonToList(message, AddOtcAccountProductRelDto.class);
            if(CollectionUtil.isNotEmpty(accountDtoList)) {
                List<String> userIdRels = accountDtoList.stream().map(AddOtcAccountProductRelDto::getMgrId).distinct().collect(Collectors.toList());
                List<String> accountIds = accountDtoList.stream().map(AddOtcAccountProductRelDto::getAccountId).distinct().collect(Collectors.toList());
                // 终端客户id和edp用户id的Map集合
                Map<String, String> uniqueAccountsMap = accountDtoList.stream().filter(oo -> StrUtil.isNotBlank(oo.getAccountId()) && StrUtil.isNotBlank(oo.getMgrId()))
                        .collect(Collectors.toMap(
                                AddOtcAccountProductRelDto::getAccountId, // 键
                                AddOtcAccountProductRelDto::getMgrId, // 值
                                (existingValue, newValue) -> existingValue // 合并函数，取第一个值
                        ));
                List<User> userList = userService.lambdaQuery().in(User::getSystemUserId, userIdRels).eq(User::getIsDisabled, 0).list();
                // 用户edpID和当前系统用户id的Map集合
                Map<String, List<User>> userMap = userList.stream().collect(Collectors.groupingBy(User::getSystemUserId));

                List<OtcAccount> otcAccounts = accountService.lambdaQuery().in(OtcAccount::getAccountId, accountIds).list();
                Map<String, Long> accountMap = otcAccounts.stream().filter(oo -> StrUtil.isNotBlank(oo.getAccountId()))
                        .collect(Collectors.toMap(
                                OtcAccount::getAccountId, // 键
                                OtcAccount::getId, // 值
                                (existingValue, newValue) -> existingValue // 合并函数，取第一个值
                        ));

                List<OtcAccountUserRelation> exsitUserRelations = userRelationService.list(Wrappers.lambdaQuery(OtcAccountUserRelation.class).in(OtcAccountUserRelation::getAccountId, accountIds));
                List<String> exsitRelAccountIds = exsitUserRelations.stream().map(OtcAccountUserRelation::getAccountId).collect(Collectors.toList());
                if(CollectionUtil.isNotEmpty(exsitRelAccountIds)) {
                    exsitUserRelations.forEach(ee -> {
                        if(uniqueAccountsMap.containsKey(ee.getAccountId())) {
                            String systemUserId = uniqueAccountsMap.get(ee.getAccountId());
                            ee.setSystemUserId(systemUserId);
                            if(userMap.containsKey(systemUserId)) {
                                ee.setUserId(userMap.get(systemUserId).get(0).getId());
                                ee.setOwnerName(userMap.get(systemUserId).get(0).getName());
                                ee.setOwnerNumber(userMap.get(systemUserId).get(0).getCode());
                            }
                        }
                        if(accountMap.containsKey(ee.getAccountId())) {
                            ee.setTerminalId(accountMap.get(ee.getAccountId()));
                        }
                        ee.setModifyDate(LocalDateTime.now());
                    });
                    userRelationService.updateBatchById(exsitUserRelations);
                }

                List<AddOtcAccountProductRelDto> addAccountRelDtos = accountDtoList.stream().filter(oo -> !exsitRelAccountIds.contains(oo.getAccountId())).collect(Collectors.toList());
                if(CollectionUtil.isNotEmpty(addAccountRelDtos)) {
                    List<OtcAccountUserRelation> userRelationList = new ArrayList<>(addAccountRelDtos.size());
                    addAccountRelDtos.forEach(oo -> {
                        OtcAccountUserRelation userRelation = new OtcAccountUserRelation();
                        userRelation.setAccountId(oo.getAccountId());
                        userRelation.setSystemUserId(oo.getMgrId());
                        if(userMap.containsKey(oo.getMgrId())) {
                            userRelation.setUserId(userMap.get(oo.getMgrId()).get(0).getId());
                            userRelation.setOwnerName(userMap.get(oo.getMgrId()).get(0).getName());
                            userRelation.setOwnerNumber(userMap.get(oo.getMgrId()).get(0).getCode());
                        }
                        if(accountMap.containsKey(oo.getAccountId())) {
                            userRelation.setTerminalId(accountMap.get(oo.getAccountId()));
                        }
                        userRelation.setCreateDate(LocalDateTime.now());
                        userRelationList.add(userRelation);
                    });
                    userRelationService.saveBatch(userRelationList);
                }
            }
        }
    }*/
}
