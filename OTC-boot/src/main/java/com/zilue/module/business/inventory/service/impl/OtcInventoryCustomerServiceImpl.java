package com.zilue.module.business.inventory.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zilue.common.enums.TerminalType;
import com.zilue.module.business.account.entity.OtcAccount;
import com.zilue.module.business.account.service.IAccountService;
import com.zilue.module.business.account.service.IAccountUserRelationService;
import com.zilue.module.business.inventory.dto.InventoryExcelDto;
import com.zilue.module.business.inventory.entity.OtcInventoryCustomer;
import com.zilue.module.business.inventory.mapper.OtcInventoryCustomerMapper;
import com.zilue.module.business.inventory.service.IOtcInventoryCustomerService;
import com.zilue.module.business.inventory.vo.InventoryCustomerImportVo;
import com.zilue.module.business.inventory.vo.OtcInventoryCustomerVo;
import com.zilue.module.business.signreached.entity.OtcSalesReportSignReached;
import com.zilue.module.business.signreached.mapper.OtcSalesReportSignReachedMapper;
import com.zilue.module.business.signreached.service.ISignReachedService;
import com.zilue.module.organization.entity.Department;
import com.zilue.module.organization.entity.User;
import com.zilue.module.organization.mapper.UserPostRelationMapper;
import com.zilue.module.organization.service.IDepartmentService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class OtcInventoryCustomerServiceImpl extends ServiceImpl<OtcInventoryCustomerMapper, OtcInventoryCustomer> implements IOtcInventoryCustomerService {

    private final IDepartmentService departmentService;

    private final IAccountService accountService;

    private final OtcInventoryCustomerMapper otcInventoryCustomerMapper;


    @Override
    public void importCustomer(List<InventoryCustomerImportVo> dictionaryItemExportVos) {
        //先处理总部
        List<String> parentCustomerName = dictionaryItemExportVos.stream().map(InventoryCustomerImportVo::getParentCustomerName).collect(Collectors.toList());
        Map<String, String> headNameTenMap = dictionaryItemExportVos.stream().collect(Collectors.toMap(InventoryCustomerImportVo::getParentCustomerName, InventoryCustomerImportVo::getIsTen,      // 值：name
                (existing, replacement) -> existing));
        List<OtcAccount> otcAccountLists = accountService.list(Wrappers.lambdaQuery(OtcAccount.class).in(OtcAccount::getTerminalType, 0).orderByDesc(OtcAccount::getId));
        Map<String, String> accountHeadMap = otcAccountLists.stream().collect(Collectors.toMap(OtcAccount::getName, OtcAccount::getAccountId,      // 值：name
                (existing, replacement) -> existing));
        Set<String> headNames = new HashSet<>();
        Map<String, OtcInventoryCustomer> headInventoryCustomer = new HashMap<>();
        for (String key : headNameTenMap.keySet()) {
            if (!headInventoryCustomer.keySet().contains(key)) {
                OtcInventoryCustomer otcInventoryCustomer = new OtcInventoryCustomer();
                otcInventoryCustomer.setAccountId(accountHeadMap.get(key));
                otcInventoryCustomer.setCustomerName(key);
                otcInventoryCustomer.setTerminalType(TerminalType.HEAD.getCode());
                otcInventoryCustomer.setIsTen(headNameTenMap.get(key).equals("是") ? 1 : 0);
                this.save(otcInventoryCustomer);
                headInventoryCustomer.put(key, otcInventoryCustomer);
            }
        }
        List<OtcAccount> otcAccountPartLists = accountService.list(Wrappers.lambdaQuery(OtcAccount.class).in(OtcAccount::getTerminalType, 1).orderByDesc(OtcAccount::getId));
        Map<String, OtcAccount> accountPartMap = otcAccountPartLists.stream().collect(Collectors.toMap(OtcAccount::getName, account -> account));
        // Map<Long, User> userMap = userList.stream().collect(Collectors.toMap(User::getId, user -> user));
        for (InventoryCustomerImportVo inventoryCustomerImportVo : dictionaryItemExportVos) {
            OtcInventoryCustomer otcInventoryCustomerMap = headInventoryCustomer.get(inventoryCustomerImportVo.getParentCustomerName());
            Department provincialCompany = departmentService.getOne(Wrappers.lambdaQuery(Department.class).eq(Department::getName, "OTC" + inventoryCustomerImportVo.getProvincialCompanyName()).orderByDesc(Department::getId));
            Department areaCompany = departmentService.getOne(Wrappers.lambdaQuery(Department.class).eq(Department::getName, "OTC" + inventoryCustomerImportVo.getAreaCompanyName()).orderByDesc(Department::getId));
            OtcInventoryCustomer otcInventoryCustomer = new OtcInventoryCustomer();
            otcInventoryCustomer.setCustomerName(inventoryCustomerImportVo.getCustomerName());
            otcInventoryCustomer.setAccountId(accountPartMap.get(inventoryCustomerImportVo.getCustomerName()).getAccountId());
            otcInventoryCustomer.setParentCustomerId(otcInventoryCustomerMap.getId().toString());
            otcInventoryCustomer.setParentCustomerName(inventoryCustomerImportVo.getParentCustomerName());
            otcInventoryCustomer.setParentAccountId(accountHeadMap.get(inventoryCustomerImportVo.getParentCustomerName()));
            otcInventoryCustomer.setTerminalType(TerminalType.PART.getCode());
            otcInventoryCustomer.setIsTen(inventoryCustomerImportVo.getIsTen().equals("是") ? 1 : 0);
            otcInventoryCustomer.setProvincialCompanyId(provincialCompany.getId().toString());
            otcInventoryCustomer.setProvincialCompanyUnitId(provincialCompany.getBusinessUnitId());
            otcInventoryCustomer.setProvincialCompanyName(inventoryCustomerImportVo.getProvincialCompanyName());
            otcInventoryCustomer.setAreaCompanyId(areaCompany.getId().toString());
            otcInventoryCustomer.setAreaCompanyUnitId(areaCompany.getBusinessUnitId());
            otcInventoryCustomer.setAreaCompanyName(inventoryCustomerImportVo.getAreaCompanyName());
            otcInventoryCustomer.setCustomerProvincialName(inventoryCustomerImportVo.getCustomerProvincialName());
            this.save(otcInventoryCustomer);
        }
        return;
    }

    /**
     * 获得总部负责人姓名
     *
     * @return
     */
    @Override
    public List<OtcInventoryCustomerVo> queryCustomerOwnerList() {
        return otcInventoryCustomerMapper.queryCustomerOwnerList();
    }


    @Override
    public List<OtcInventoryCustomerVo> queryCustomerInfoList(InventoryExcelDto dto) {
        LambdaQueryWrapper<OtcInventoryCustomer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(dto.getCustomerName()), OtcInventoryCustomer::getCustomerName, dto.getCustomerName())
                .like(StrUtil.isNotBlank(dto.getParentCustomerName()), OtcInventoryCustomer::getParentCustomerName, dto.getParentCustomerName())
                .orderByDesc(OtcInventoryCustomer::getCreateDate);
        List<OtcInventoryCustomer> otcInventoryCustomerList =this.list(queryWrapper);
        List<OtcInventoryCustomerVo> otcInventoryCustomerVoList = BeanUtil.copyToList(otcInventoryCustomerList, OtcInventoryCustomerVo.class);
        return otcInventoryCustomerVoList;
    }

}
