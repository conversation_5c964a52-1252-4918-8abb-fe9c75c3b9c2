package com.zilue.module.business.product.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
* @title: 品规信息
* <AUTHOR>
* @Date: 2024-12-26
* @Version 1.0
*/
@Data
@TableName("otc_product")
@ApiModel(value = "品规信息", description = "品规信息")
public class Product implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 
    */
    @ApiModelProperty("")
    @TableId
    private Long id;
    @ApiModelProperty("品规id")
    private String productId;
    @ApiModelProperty("品规")
    private String name;
    @ApiModelProperty("产品品种id")
    private String newProductgroupid;
    @ApiModelProperty("产品品种名称")
    private String newProductgroupidName;
    @ApiModelProperty("计价单位id")
    private String defaultUoMscheduleId;
    @ApiModelProperty("计价单位名称")
    private String defaultUoMscheduleIdName;
    @ApiModelProperty("折标准品规率")
    private String newConvertproductrate;
    @ApiModelProperty(" 状态 0 在用 1 停用")
    private String stateCode;
    @ApiModelProperty("剂型（字典值）")
    private String jixing;
    @ApiModelProperty("更新字段")
    private String rowId;
    @ApiModelProperty("是否otc产品 1 otc 0 非otc")
    private String isotc;
    @ApiModelProperty("剂型名称")
    private String jixingname;
    /**
    * 创建人ID
    */
    @ApiModelProperty("创建人ID")
    @TableField(fill = FieldFill.INSERT)
    private Long createUserId;
    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createDate;
    /**
    * 修改人ID
    */
    @ApiModelProperty("修改人ID")
    @TableField(fill = FieldFill.UPDATE)
    private Long modifyUserId;
    /**
    * 修改日期
    */
    @ApiModelProperty("修改日期")
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime modifyDate;
    /**
    * 删除标记
    */
    @ApiModelProperty("删除标记")
    @TableField(fill = FieldFill.INSERT)
    @TableLogic
    private Integer deleteMark;
    /**
    * 有效标记
    */
    @ApiModelProperty("有效标记")
    @TableField(fill = FieldFill.INSERT)
    private Integer enabledMark;


}