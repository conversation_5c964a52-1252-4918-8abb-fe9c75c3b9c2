package com.zilue.module.business.customer.vo;

import com.zilue.common.page.PageInput;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CustomerTagVo extends PageInput {
    @ApiModelProperty("主键ID")
    private Long id;
    @ApiModelProperty("标签名称")
    private String tagName;
    @ApiModelProperty("标签类型")
    private String tagCode;
    @ApiModelProperty("标签名称")
    private String tagTypeName;
    @ApiModelProperty("客户id")
    private Long accountId;
    @ApiModelProperty("'标签颜色'")
    private String tagColor;
    @ApiModelProperty("'标签类型'")
    private String tagType;
    @ApiModelProperty("'排序'")
    private Long sort;
    @ApiModelProperty("前端是否显示{N:0,否;Y:1,是;}")
    private String showStatus;
    @ApiModelProperty("关联门店数")
    private Integer incidenceNumber;

}
