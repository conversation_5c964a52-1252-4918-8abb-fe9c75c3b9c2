package com.zilue.module.business.salesindicators.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zilue.module.business.salesindicators.entity.OtcSalesmanSalesIndicators;
import com.zilue.module.business.salesperformance.entity.OtcSalesmanSalesPerformance;
import lombok.Data;
import java.util.List;

/**
* @title: service
* <AUTHOR>
* @Date: 2025-01-15
* @Version 1.0
*/
public interface ISalesIndicatorsService {
    /**
     * 查询指标，按个人汇总
     * @param startYearMonth
     * @param endYearMonth
     * @return
     */
    List<OtcSalesmanSalesIndicators> queryIndicators(String startYearMonth, String endYearMonth);

    void save(List<OtcSalesmanSalesIndicators> list);

    /**
     * 按连锁总部汇总
     * @param month
     * @return
     */
    List<OtcSalesmanSalesIndicators> queryIndicatorsGroupByHeardStore(String month);


    /**
     * edp指标分页查询
     *
     * @param syncTime
     * @param page
     * @param pageSize
     * @return
     */
    List<OtcSalesmanSalesIndicators> queryList(String syncTime, int page, int pageSize);

    /**
     * edp指标count
     *
     * @param syncTime
     * @return
     */
    Integer queryCount(String syncTime);


    /**
     * 同步
     */
    void syncIndicators(String syncTime);
}
