package com.zilue.module.business.taskManagement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zilue.common.page.PageOutput;
import com.zilue.module.business.taskManagement.dto.AddOtcSalesmanTaskDto;
import com.zilue.module.business.taskManagement.dto.OtcSalesmanTaskPageDto;
import com.zilue.module.business.taskManagement.dto.SalesPerformancePageDto;
import com.zilue.module.business.taskManagement.dto.UpdateOtcSalesmanTaskDto;
import com.zilue.module.business.taskManagement.entity.OtcSalesmanTask;
import com.zilue.module.business.taskManagement.vo.*;

import java.util.List;

/**
 * +* @title: service+* <AUTHOR> @Date: 2024-12-31+* @Version 1.0+
 */


public interface ITaskManagementService extends IService<OtcSalesmanTask> {
    PageOutput<OtcSalesmanTaskPageVo> getPageList(OtcSalesmanTaskPageDto pageDto);
    OtcSalesmanTaskVo getOtcSalesmanTask(Long id);
    boolean saveOtcSalesmanTask(AddOtcSalesmanTaskDto dto);
    boolean UpdateOtcSalesmanTask(UpdateOtcSalesmanTaskDto dto);
    boolean UpdateTaskIsEnabled(UpdateOtcSalesmanTaskDto dto);
    void updateSalesmanTaskStatusForJob();

    /**
     * 获取业绩达成明细
     *
     * @param dto {@link SalesPerformancePageDto 分页查询业绩明细的dto}
     * @return {@link SalesPerformanceRespVo 业绩目标达成明细的返回对象}
     */
    DetailPageOutput<SalesPerformanceRespVo> salesPerformanceDetailPage(SalesPerformancePageDto dto);

    /**
     * 导出业绩明细
     *
     * @param dto {@link SalesPerformancePageDto 分页查询业绩明细的dto}
     * @return {@link SalesPerformanceRespVo 业绩目标达成明细的excel 导出对象}
     */
    List<SalesPerformanceRespVo> getSalesPerformanceDetailList(SalesPerformancePageDto dto);
}