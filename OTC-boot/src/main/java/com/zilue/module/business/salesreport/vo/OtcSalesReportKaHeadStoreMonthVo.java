package com.zilue.module.business.salesreport.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
* @title: 表单出参
* <AUTHOR>
* @Date: 2025-01-20
* @Version 1.0
*/
@Data
public class OtcSalesReportKaHeadStoreMonthVo {

    /**
    * 
    */
    @ApiModelProperty("")
    private Long id;
    /**
    * 用户编码
    */
    @ApiModelProperty("用户编码")
    private String userCode;
    /**
    * 年计划值
    */
    @ApiModelProperty("年计划值")
    private BigDecimal planAmount;
    /**
    * 年完成值
    */
    @ApiModelProperty("年完成值")
    private BigDecimal finishAmount;
    /**
    * 业务月份
    */
    @ApiModelProperty("业务月份")
    private String businessMonth;
    /**
    * 排名
    */
    @ApiModelProperty("排名")
    private Long rankNum;



}
