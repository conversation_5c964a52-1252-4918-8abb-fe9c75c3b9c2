package com.zilue.module.business.account.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class OtcAccountStoreExportVo {

    /**
     * 主键ID
     */
    @ExcelProperty(value = "门店ID")
    @ApiModelProperty("门店ID")
    private String clientId;

    /**
     * 客户名称
     */
    @ExcelProperty(value = "门店名称")
    @ApiModelProperty("门店名称")
    private String name;
    /**
     * 上级客户名称
     */
    @ExcelProperty(value = "分部客户名称")
    @ApiModelProperty("分部客户名称")
    private String parentAccountIdName;

    @ExcelProperty(value = "连锁总部名称")
    @ApiModelProperty("连锁总部名称")
    private String newOtcAccountName;

    /**
     * 省份名称
     */
    @ExcelProperty(value = "省份名称")
    @ApiModelProperty("省份名称")
    private String province;

    /**
     * 城市名称
     */
    @ExcelProperty(value = "城市名称")
    @ApiModelProperty("城市名称")
    private String city;

    /**
     * 区县名称
     */
    @ExcelProperty(value = "区县名称")
    @ApiModelProperty("区县名称")
    private String district;

    @ExcelProperty(value = "所属辖区")
    @ApiModelProperty("所属辖区")
    private String belongRegion;

    /**
     * 负责人
     */
    @ExcelProperty(value = "负责人")
    @ApiModelProperty("负责人")
    private String ownerName;


    /**
     * 负责人工号
     */
    @ExcelProperty(value = "负责人工号")
    @ApiModelProperty("负责人工号")
    private String ownerNumber;

    /**
     * 0：直营；1：加盟
     */
    @ExcelProperty(value = "直营/加盟")
    @ApiModelProperty("0：直营；1：加盟")
    private String isDirectJoinStr;

    /**
     * 标签名称
     */
    @ExcelProperty(value = "标签名称")
    @ApiModelProperty("标签名称")
    private String customerTagName;

    /**
     * 客户类型
     */
    @ExcelProperty(value = "客户类型")
    @ApiModelProperty("客户类型")
    private String customerTypeCodeName;

    /**
     * 销售规模
     */
    @ExcelProperty(value = "销售规模")
    @ApiModelProperty("销售规模")
    private String saleScale;

    /**
     * 是否DTP门店：0：否；1：是
     */
    @ExcelProperty(value = "是否DTP门店")
    @ApiModelProperty("是否DTP门店：0：否；1：是")
    private String isDtpStr;

    /**
     * 是否统筹门店：0：否；1：是
     */
    @ExcelProperty(value = "是否统筹门店")
    @ApiModelProperty("是否统筹门店：0：否；1：是")
    private String isWholeStr;

    /**
     * 是否电商门店：0：否；1：是
     */
    @ExcelProperty(value = "是否电商门店")
    @ApiModelProperty("是否电商门店：0：否；1：是")
    private String isOnlineStr;

}
