package com.zilue.module.business.salesreport.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.math.BigDecimal;

/**
* @title: 分页列表出参
* <AUTHOR>
* @Date: 2025-01-15
* @Version 1.0
*/
@Data
public class OtcSalesReportDeptMonthPageVo {

    /**
    * 
    */
    @ApiModelProperty("")
    private String id;
    /**
    * 代表编码
    */
    @ApiModelProperty("代表编码")
    private String deptCode;
    /**
    * 年计划值
    */
    @ApiModelProperty("年计划值")
    private BigDecimal planAmount;
    /**
    * 年完成值
    */
    @ApiModelProperty("年完成值")
    private BigDecimal finishAmount;
    /**
    * 业务月份
    */
    @ApiModelProperty("业务月份")
    private String businessMonth;
    /**
    * 同级架构下排名
    */
    @ApiModelProperty("同级架构下排名")
    private Long rankNum;
    /**
    * 创建人ID
    */
    @ApiModelProperty("创建人ID")
    private Long createUserId;
    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createDate;
    /**
    * 修改人ID
    */
    @ApiModelProperty("修改人ID")
    private Long modifyUserId;
    /**
    * 修改日期
    */
    @ApiModelProperty("修改日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime modifyDate;
    /**
    * 删除标记
    */
    @ApiModelProperty("删除标记")
    private Integer deleteMark;
    /**
    * 有效标记
    */
    @ApiModelProperty("有效标记")
    private Integer enabledMark;

}
