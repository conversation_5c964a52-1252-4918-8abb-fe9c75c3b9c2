package com.zilue.module.business.reportForms.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zilue.common.enums.PostCodeEnum;
import com.zilue.common.enums.TaskFrequencyEnum;
import com.zilue.common.enums.CategoryEnum;
import com.zilue.common.utils.DateUtils;
import com.zilue.common.utils.RedisUtil;
import com.zilue.common.utils.VoToColumnUtil;
import com.zilue.module.business.account.service.impl.AccountUserRelationServiceImpl;
import com.zilue.module.business.reportForms.dto.ReportFormsDto;
import com.zilue.module.business.reportForms.service.ReportFormsService;
import com.zilue.module.business.reportForms.vo.*;
import com.zilue.module.business.salesindicators.service.ILevelSalesIndicatorsService;
import com.zilue.module.business.salesindicators.service.ISalesIndicatorsService;
import com.zilue.module.business.salesperformance.service.ISalesPerformanceService;
import com.zilue.module.business.salesreport.entity.OtcLevelSalesReportMonth;
import com.zilue.module.business.salesreport.entity.OtcSalesReportDeptMonth;
import com.zilue.module.business.salesreport.service.*;
import com.zilue.module.business.salesreport.service.impl.LevelSalesReportMonthServiceImpl;
import com.zilue.module.business.salesreport.service.impl.SalesReportKaMonthServiceImpl;
import com.zilue.module.business.salesreport.service.impl.SalesReportKaYearServiceImpl;
import com.zilue.module.business.salesreport.service.impl.SalesReportMonthServiceImpl;
import com.zilue.module.business.signreached.service.ISignReachedService;
import com.zilue.module.business.taskManagement.entity.OtcSalesmanTask;
import com.zilue.module.business.taskManagement.entity.OtcSalesmanTaskVisit;
import com.zilue.module.business.taskManagement.service.IOtcSalesmanTaskVisit;
import com.zilue.module.business.taskManagement.service.ITaskManagementService;
import com.zilue.module.business.taskManagement.vo.OtcSalesmanTaskVo;
import com.zilue.module.organization.entity.*;
import com.zilue.module.organization.service.*;
import com.zilue.module.organization.service.impl.PostServiceImpl;
import com.zilue.module.organization.vo.DepartmentListVo;
import com.zilue.module.wechat.sign.entity.OtcSignIn;
import com.zilue.module.wechat.sign.mapper.OtcSignInMapper;
import com.zilue.module.wechat.sign.service.ISignService;
import com.zilue.module.wechat.sign.vo.OtcSignInVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;


@Service
@AllArgsConstructor
@Slf4j
public class ReportFormsServiceImpl implements ReportFormsService {
    private final IDepartmentService departmentService;

    private final IUserService userService;

    private final IUserDeptRelationService userDeptRelationService;

    private final IUserPostRelationService userPostRelationService;

    private final ISignService signService;

    private final ITaskManagementService taskManagementService;

    private final IOtcSalesmanTaskVisit otcSalesmanTaskVisit;

    private final ISalesIndicatorsService indicatorsService;
    private final ILevelSalesIndicatorsService levelSalesIndicatorsService;
    private final ISalesPerformanceService performanceService;
    private final SalesReportMonthServiceImpl reportMonthService;
    private final ISalesReportYearService reportYearService;

    private final LevelSalesReportMonthServiceImpl reportMonthLevelService;
    private final ILevelSalesReportYearService reportYearLevelService;


    private final ISalesReportDeptMonthService reportDeptMonthService;
    private final ISalesReportDeptYearService reportDeptYearService;

    private final RedisUtil redisUtil;
    private final OtcSignInMapper otcSignInMapper;
    private final ISignReachedService signReachedService;

//    private final IAccountService accountService;

    private final SalesReportKaMonthServiceImpl reportKaMonthService;
    private final SalesReportKaYearServiceImpl reportKaYearService;

    private final AccountUserRelationServiceImpl accountUserRelationService;

    private final ISalesReportKaHeadStoreMonthService kaHeadStoreMonthService;
    private final ISalesReportKaHeadStoreYearService kaHeadStoreYearService;
    private final IPostService postService;


    /**
     * 拜访/协访任务报表
     *
     * @param reportFormsDto
     * @return
     */
    @Override
    public List<VisitReportFormVo> visitReportForm(ReportFormsDto reportFormsDto) {
        List<VisitReportFormVo> VisitReportFormVos = new ArrayList<>();
        List<String> visitCategory = Arrays.asList(CategoryEnum.VISITSHOP.getCode());
        List<String> coachCategory = Arrays.asList(CategoryEnum.COACHSHOP.getCode(), CategoryEnum.COACHSEGMENT.getCode(), CategoryEnum.VISITSEGMENT.getCode(), CategoryEnum.VISITHEAD.getCode());
        String parentId = reportFormsDto.getDepartmentId();
        String postId = reportFormsDto.getPostId();
        String monthDay = reportFormsDto.getMonthDay();
        String category = reportFormsDto.getCategory();
        Date monthDayBeginDate = null;
        Date monthDayEndDate = null;
        if (visitCategory.contains(category)) {
            monthDayBeginDate = DateUtils.stringToDate(monthDay, DateUtils.DATE_PATTERN);
            monthDayEndDate = DateUtils.addDateDays(monthDayBeginDate, 1);
        } else {
            monthDayBeginDate = DateUtils.stringToDate(monthDay, DateUtils.MONTH_PATTERN);
            monthDayEndDate = DateUtils.addDateMonths(monthDayBeginDate, 1);
        }

        //获得目标家次
        Integer reachedStandardDay = 0;
        OtcSalesmanTaskVo otcSalesmanTaskVo = new OtcSalesmanTaskVo();
        otcSalesmanTaskVo.setTaskType(category);
        otcSalesmanTaskVo.setFrequency(TaskFrequencyEnum.MOUTH.getCode());
        if (visitCategory.contains(category)) {
            otcSalesmanTaskVo.setFrequency(TaskFrequencyEnum.DAY.getCode());
        }
        reachedStandardDay = otcSignInMapper.selectTaskGoal(otcSalesmanTaskVo);

        //获得组织机构信息和用户信息
        List<Department> departmentList = departmentService.list(Wrappers.lambdaQuery(Department.class)
                .eq(ObjectUtil.isNotEmpty(parentId), Department::getParentId, parentId)
                .orderByAsc(Department::getSortCode)
                .select(Department.class, x -> VoToColumnUtil.fieldsToColumns(DepartmentListVo.class).contains(x.getProperty())));
        if (CollectionUtil.isEmpty(departmentList)) {
            departmentList = departmentService.list(Wrappers.lambdaQuery(Department.class)
                    .eq(ObjectUtil.isNotEmpty(parentId), Department::getId, parentId)
                    .orderByAsc(Department::getSortCode)
                    .select(Department.class, x -> VoToColumnUtil.fieldsToColumns(DepartmentListVo.class).contains(x.getProperty())));
        }
        List<Department> departmentAllList = departmentService.list();
        List<UserDeptRelation> userDeptRelationList = userDeptRelationService.list();
        List<UserPostRelation> userPostRelationList = userPostRelationService.list();
        List<User> userList = userService.list();
        Map<Long, String> userMap = userList.stream().collect(Collectors.toMap(User::getId, User::getName));

        //获得类型获得绩效信息
        List<OtcSalesmanTask> salesmanTaskList = taskManagementService.list(Wrappers.lambdaQuery(OtcSalesmanTask.class)
                .eq(ObjectUtil.isNotEmpty(parentId), OtcSalesmanTask::getTaskType, category)
                .eq(OtcSalesmanTask::getTaskStatus, "2")
                .orderByAsc(OtcSalesmanTask::getCreateDate));
        List<String> taskPostIdList = Arrays.asList(salesmanTaskList.get(0).getPostId().split(","));
        List<UserPostRelation> taskUserPostRelationList = userPostRelationList.stream().filter(x -> taskPostIdList.contains(x.getPostId().toString())).collect(Collectors.toList());

        //获得签到信息
        List<OtcSignIn> OtcSignInList = signService.list(Wrappers.lambdaQuery(OtcSignIn.class)
                .eq(OtcSignIn::getSiginType, 2)
                .eq(OtcSignIn::getCategory, category)
                //.in(OtcSignIn::getCategory, Arrays.asList(TaskTypeEnum.VISITSHOP.getCode(), TaskTypeEnum.VISITSEGMENT.getCode(), TaskTypeEnum.VISITHEAD.getCode()))
                .between(OtcSignIn::getCreateDate, monthDayBeginDate, monthDayEndDate)
                .orderByAsc(OtcSignIn::getCreateDate)
                .select(OtcSignIn.class, x -> VoToColumnUtil.fieldsToColumns(OtcSignInVo.class).contains(x.getProperty())));
        // 按照 id 分组并计算 signCount 的和
        Map<Long, Long> signCountMap = OtcSignInList.stream().collect(Collectors.groupingBy(OtcSignIn::getPersonId, Collectors.summingLong(OtcSignIn::getSignCount)));

        //Map<Long, List<Long>> parentDepartmentIdMap = new HashMap<>();
        for (Department department : departmentList) {
            VisitReportFormVo visitReportFormVo = new VisitReportFormVo();
            List<PersonVisitReportFormVo> reachedPersonVisitList = new ArrayList<>();
            List<PersonVisitReportFormVo> noReachPersonVisitList = new ArrayList<>();
            List<Department> childDepartmentList = departmentAllList.stream().filter(x -> x.getHierarchy().contains(department.getId().toString())).collect(Collectors.toList());
            List<Long> childDepartmentIds = childDepartmentList.stream().map(Department::getId).collect(Collectors.toList());
            //parentDepartmentIdMap.put(department.getId(), childDepartmentIds);
            List<UserDeptRelation> currentUserDeptRelationList = userDeptRelationList.stream().filter(x -> childDepartmentIds.contains(x.getDeptId())).collect(Collectors.toList());
            List<Long> userIds = new ArrayList<>();
            List<Long> currentUserId = currentUserDeptRelationList.stream().map(UserDeptRelation::getUserId).collect(Collectors.toList());
            //协访的时候要根据岗位确认有协访任务的人
            if (coachCategory.contains(category)) {
                List<UserPostRelation> taskCurrentUserPostRelationList = taskUserPostRelationList.stream().filter(x -> currentUserId.contains(x.getUserId())).collect(Collectors.toList());
                userIds = taskCurrentUserPostRelationList.stream().map(UserPostRelation::getUserId).collect(Collectors.toList());
            } else {
                userIds = currentUserId;
            }
            Integer reachedNum = 0;
            for (Long userId : userIds) {
                Long signCount = signCountMap.get(userId);
                if (signCount != null && signCount >= reachedStandardDay) {
                    reachedNum = reachedNum + 1;
                    PersonVisitReportFormVo personVisitReportFormVo = new PersonVisitReportFormVo();
                    personVisitReportFormVo.setPersonId(userId);
                    personVisitReportFormVo.setPersonName(userMap.get(userId));
                    personVisitReportFormVo.setReachedDayNum(null != signCountMap.get(userId) ? Integer.parseInt(signCountMap.get(userId).toString()) : 0);
                    personVisitReportFormVo.setStandardNum(reachedStandardDay);
                    personVisitReportFormVo.setReachedRate(new BigDecimal(personVisitReportFormVo.getReachedDayNum())
                            .divide(new BigDecimal(reachedStandardDay), 2, RoundingMode.HALF_UP));
                    reachedPersonVisitList.add(personVisitReportFormVo);
                } else {
                    PersonVisitReportFormVo personVisitReportFormVo = new PersonVisitReportFormVo();
                    personVisitReportFormVo.setPersonId(userId);
                    personVisitReportFormVo.setPersonName(userMap.get(userId));
                    personVisitReportFormVo.setReachedDayNum(null != signCountMap.get(userId) ? Integer.parseInt(signCountMap.get(userId).toString()) : 0);
                    personVisitReportFormVo.setStandardNum(reachedStandardDay);
                    personVisitReportFormVo.setReachedRate(new BigDecimal(personVisitReportFormVo.getReachedDayNum())
                            .divide(new BigDecimal(reachedStandardDay), 2, RoundingMode.HALF_UP));
                    noReachPersonVisitList.add(personVisitReportFormVo);
                }
            }

            visitReportFormVo.setDepartmentName(department.getName());
            visitReportFormVo.setPersonNum(userIds.size());
            visitReportFormVo.setReachedPersonVisits(reachedPersonVisitList);
            visitReportFormVo.setNoReachPersonVisits(noReachPersonVisitList);
            visitReportFormVo.setReachedNum(reachedNum);
            visitReportFormVo.setNoReachedNum(userIds.size() - reachedNum);
            // 确保 currentUserId.size() 不为 0，避免除以 0 的错误
            if (userIds.size() > 0) {
                visitReportFormVo.setReachedRate(new BigDecimal(reachedNum)
                        .divide(new BigDecimal(userIds.size()), 2, RoundingMode.HALF_UP));
            } else {
                visitReportFormVo.setReachedRate(BigDecimal.ZERO);
            }
            VisitReportFormVos.add(visitReportFormVo);
        }
        return VisitReportFormVos;
    }

    /**
     * 个人拜访/协访任务报表
     *
     * @param reportFormsDto
     * @return
     */
    @Override
    public PersonVisitReportFormVo selectUserSignStatistics(ReportFormsDto reportFormsDto) {
        List<PersonVisitReportFormVo> personVisitList = new ArrayList<>();//List<String> visitCategory = Arrays.asList(CategoryEnum.VISITSHOP.getCode(), CategoryEnum.VISITSEGMENT.getCode(), CategoryEnum.VISITHEAD.getCode());
        // List<String> coachCategory = Arrays.asList(CategoryEnum.COACHSHOP.getCode(), CategoryEnum.COACHSEGMENT.getCode());
        PersonVisitReportFormVo personVisitReportFormVo = new PersonVisitReportFormVo();
        String personCode = reportFormsDto.getPersonCode();
        String monthDay = reportFormsDto.getMonthDay();
        String category = reportFormsDto.getCategory();
        String postId = reportFormsDto.getPostId();
        Date monthDayDate = DateUtils.stringToDate(monthDay, DateUtils.MONTH_PATTERN);

        // 获得当前月的开始和结束日期
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(monthDayDate);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        Date startDate = calendar.getTime();
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        Date endDate = calendar.getTime();

        // 生成当前月的日期列表
        List<String> dateList = new ArrayList<>();
        Date currentDate = startDate;
        while (!currentDate.after(endDate)) {
            dateList.add(DateUtils.format(currentDate, DateUtils.DATE_PATTERN));
            currentDate = DateUtils.addDateDays(currentDate, 1);
        }

        //获得目标家次
        OtcSalesmanTaskVo otcSalesmanTask = new OtcSalesmanTaskVo();
        Integer reachedStandardDay = 0;
        OtcSalesmanTaskVo otcSalesmanTaskVo = new OtcSalesmanTaskVo();
        otcSalesmanTaskVo.setTaskType(category);
        List<OtcSalesmanTaskVo> salesmanTaskInfos = otcSignInMapper.selectTaskInfo(otcSalesmanTaskVo);
        for (OtcSalesmanTaskVo salesmanTaskVo : salesmanTaskInfos) {
            List<String> postIds = Arrays.asList(salesmanTaskVo.getPostId().split(","));
            if (postIds.contains(postId)) {
                otcSalesmanTask = salesmanTaskVo;
                break;
            }
        }
        reachedStandardDay = ObjectUtil.isNotEmpty(otcSalesmanTask) && ObjectUtil.isNotEmpty(otcSalesmanTask.getGoal()) ? otcSalesmanTask.getGoal() : 0;
        //获得签到信息
        List<OtcSignIn> OtcSignInList = signService.list(Wrappers.lambdaQuery(OtcSignIn.class)
                .eq(OtcSignIn::getSiginType, 2)
                .eq(OtcSignIn::getCategory, category)
                .eq(OtcSignIn::getPersonCode, personCode)
                //.in(OtcSignIn::getCategory, Arrays.asList(TaskTypeEnum.VISITSHOP.getCode(), TaskTypeEnum.VISITSEGMENT.getCode(), TaskTypeEnum.VISITHEAD.getCode()))
                .between(OtcSignIn::getCreateDate, monthDayDate, DateUtils.addDateMonths(monthDayDate, 1))
                .orderByAsc(OtcSignIn::getCreateDate)
                .select(OtcSignIn.class, x -> VoToColumnUtil.fieldsToColumns(OtcSignInVo.class).contains(x.getProperty())));
        // 将 createDate 转换为 yyyy-MM-dd 格式的字符串
        Map<String, Long> signCountMap = OtcSignInList.stream().collect(Collectors.groupingBy(
                otcSignIn -> DateUtils.format(otcSignIn.getCreateDate(), DateUtils.DATE_PATTERN),
                Collectors.summingLong(OtcSignIn::getSignCount)));

        Map<String, Long> signDateMap = new TreeMap<>();
        Integer reachedNum = 0;
        // 处理 personVisitList
        for (String date : dateList) {
            Long signCount = signCountMap.get(date);
            if (signCount == null) {
                signCount = -1L;
            } else {
                reachedNum = reachedNum + Integer.parseInt(signCount.toString());
            }
            signDateMap.put(date, signCount);
        }

       /* Integer reachedNum = 0;
        for (Long signCount : signCountMap.values()) {
            if (signCount >= reachedStandardDay) {
                reachedNum++;
            }
        }*/
        personVisitReportFormVo.setSignDateMap(signDateMap);
        personVisitReportFormVo.setReachedDayNum(reachedNum);
        personVisitReportFormVo.setStandardNum(reachedStandardDay);
        personVisitReportFormVo.setFrequency(otcSalesmanTask.getFrequency());
        personVisitReportFormVo.setStartYearMonth(otcSalesmanTask.getStartYearMonth());
        personVisitReportFormVo.setEndYearMonth(otcSalesmanTask.getEndYearMonth());
        if (reachedStandardDay > 0) {
            personVisitReportFormVo.setReachedRate(new BigDecimal(personVisitReportFormVo.getReachedDayNum())
                    .divide(new BigDecimal(reachedStandardDay), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100)));
        } else {
            personVisitReportFormVo.setReachedRate(BigDecimal.ZERO);
        }
        return personVisitReportFormVo;
    }

    /**
     * 销售业绩报表
     *
     * @param reportFormsDto
     * @return
     */
    @Override
    public SaleMoneyReportFormVo saleMoneyReportForm(ReportFormsDto reportFormsDto) {

        SaleMoneyReportFormVo saleMoneyReportFormVo = new SaleMoneyReportFormVo();
        List<DepartmentSaleMoneyReportFormVo> saleReportFormVos = new ArrayList<>();
        String parentId = reportFormsDto.getDepartmentId();
        String monthDay = reportFormsDto.getMonthDay();
        Date monthDayBeginDate = DateUtils.stringToDate(monthDay, DateUtils.MONTH_PATTERN);
        Date monthDayEndDate = DateUtils.addDateMonths(monthDayBeginDate, 1);
        String monthDayEndDateStr = DateUtils.format(monthDayEndDate, DateUtils.MONTH_PATTERN);
        Boolean isLowestUnit = false;
        //获得组织机构信息和用户信息
        List<Department> departmentList = departmentService.list(Wrappers.lambdaQuery(Department.class)
                .eq(ObjectUtil.isNotEmpty(parentId), Department::getParentId, parentId)
                .orderByAsc(Department::getSortCode)
                .select(Department.class, x -> VoToColumnUtil.fieldsToColumns(DepartmentListVo.class).contains(x.getProperty())));
        if (CollectionUtil.isEmpty(departmentList)) {
            departmentList = departmentService.list(Wrappers.lambdaQuery(Department.class)
                    .eq(ObjectUtil.isNotEmpty(parentId), Department::getId, parentId)
                    .orderByAsc(Department::getSortCode)
                    .select(Department.class, x -> VoToColumnUtil.fieldsToColumns(DepartmentListVo.class).contains(x.getProperty())));
            isLowestUnit = true;
        }
        List<Department> departmentAllList = departmentService.list();
        List<UserDeptRelation> userDeptRelationList = userDeptRelationService.list();
        //List<User> userList = userService.list();
        //Map<Long, String> userMap = userList.stream().collect(Collectors.toMap(User::getId, User::getName));
        //获得当前机构前6个月的业绩和目标
        List<OtcSalesReportDeptMonth> otcSalesReportDeptMonthSelfBeforeList = reportDeptMonthService.list(Wrappers.lambdaQuery(OtcSalesReportDeptMonth.class)
                .eq(ObjectUtil.isNotEmpty(parentId), OtcSalesReportDeptMonth::getDeptCode, parentId)
                .between(OtcSalesReportDeptMonth::getBusinessMonth, DateUtils.format(DateUtils.addDateMonths(monthDayBeginDate, -5), DateUtils.MONTH_PATTERN), monthDay)
                .orderByAsc(OtcSalesReportDeptMonth::getRankNum));

        Map<String, BigDecimal> targetSaleMoneyMap = new TreeMap<>();
        Map<String, BigDecimal> realSaleMoneyMap = new TreeMap<>();
        for (OtcSalesReportDeptMonth otcSalesReportDeptMonth : otcSalesReportDeptMonthSelfBeforeList) {
            targetSaleMoneyMap.put(otcSalesReportDeptMonth.getBusinessMonth().substring(5, 7), otcSalesReportDeptMonth.getPlanAmount());
            realSaleMoneyMap.put(otcSalesReportDeptMonth.getBusinessMonth().substring(5, 7), otcSalesReportDeptMonth.getFinishAmount());
        }
        //获得自身的当前月业绩和目标
        List<OtcSalesReportDeptMonth> otcSalesReportDeptMonthSelfList = reportDeptMonthService.list(Wrappers.lambdaQuery(OtcSalesReportDeptMonth.class)
                .eq(ObjectUtil.isNotEmpty(parentId), OtcSalesReportDeptMonth::getDeptCode, parentId)
                .between(OtcSalesReportDeptMonth::getBusinessMonth, monthDay, monthDay)
                .orderByAsc(OtcSalesReportDeptMonth::getRankNum));
        //获得参数所传机构的子机构的业绩和目标
        List<OtcSalesReportDeptMonth> otcSalesReportDeptMonthList = reportDeptMonthService.list(Wrappers.lambdaQuery(OtcSalesReportDeptMonth.class)
                .eq(ObjectUtil.isNotEmpty(parentId), OtcSalesReportDeptMonth::getParentDeptCode, parentId)
                .between(OtcSalesReportDeptMonth::getBusinessMonth, monthDay, monthDay)
                .orderByAsc(OtcSalesReportDeptMonth::getRankNum));
        Map<String, OtcSalesReportDeptMonth> otcSalesReportDeptMonthMap = otcSalesReportDeptMonthList.stream().collect(Collectors.toMap(OtcSalesReportDeptMonth::getDeptCode, p -> p));
        saleMoneyReportFormVo.setTargetSaleMoney(otcSalesReportDeptMonthSelfList.stream().map(OtcSalesReportDeptMonth::getPlanAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        if (CollectionUtil.isNotEmpty(otcSalesReportDeptMonthList)) {
            saleMoneyReportFormVo.setRealSaleMoney(otcSalesReportDeptMonthList.stream().map(OtcSalesReportDeptMonth::getFinishAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        } else {
            saleMoneyReportFormVo.setRealSaleMoney(otcSalesReportDeptMonthSelfList.stream().map(OtcSalesReportDeptMonth::getFinishAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        }

        saleMoneyReportFormVo.setTargetSaleMoneyMap(targetSaleMoneyMap);
        saleMoneyReportFormVo.setRealSaleMoneyMap(realSaleMoneyMap);
        BigDecimal reachedRateDep = BigDecimal.ZERO;
        if (saleMoneyReportFormVo.getTargetSaleMoney().compareTo(BigDecimal.ZERO) > 0) {
            reachedRateDep = saleMoneyReportFormVo.getRealSaleMoney().divide(saleMoneyReportFormVo.getTargetSaleMoney(), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
        }
        saleMoneyReportFormVo.setReachedRate(reachedRateDep);
        //Map<Long, List<Long>> parentDepartmentIdMap = new HashMap<>();
        //最底级机构departmentList只有1个值 就是他自己
        for (Department department : departmentList) {
            DepartmentSaleMoneyReportFormVo saleReportFormVo = new DepartmentSaleMoneyReportFormVo();
            List<PersonVisitReportFormVo> PersonVisitList = new ArrayList<>();
            List<Department> childDepartmentList = departmentAllList.stream().filter(x -> x.getHierarchy().contains(department.getId().toString())).collect(Collectors.toList());
            List<Long> childDepartmentIds = childDepartmentList.stream().map(Department::getId).collect(Collectors.toList());
            //parentDepartmentIdMap.put(department.getId(), childDepartmentIds);
            List<UserDeptRelation> currentUserDeptRelationList = userDeptRelationList.stream().filter(x -> childDepartmentIds.contains(x.getDeptId())).collect(Collectors.toList());
            List<Long> currentUserId = currentUserDeptRelationList.stream().map(UserDeptRelation::getUserId).collect(Collectors.toList());
            if (!isLowestUnit) {//非最底级机构获取机构人数的业绩、指标之和
                Integer userNum = currentUserId.size();
                saleReportFormVo.setPersonNum(userNum);
                if (ObjectUtil.isNotEmpty(otcSalesReportDeptMonthMap.get(department.getId().toString()))) {
                    saleReportFormVo.setTargetSaleMoney(otcSalesReportDeptMonthMap.get(department.getId().toString()).getPlanAmount());
                    saleReportFormVo.setRealSaleMoney(otcSalesReportDeptMonthMap.get(department.getId().toString()).getFinishAmount());
                } else {
                    saleReportFormVo.setTargetSaleMoney(BigDecimal.ZERO);
                    saleReportFormVo.setRealSaleMoney(BigDecimal.ZERO);
                }

                BigDecimal reachedRate = BigDecimal.ZERO;
                if (saleReportFormVo.getTargetSaleMoney().compareTo(BigDecimal.ZERO) > 0) {
                    reachedRate = saleReportFormVo.getRealSaleMoney().divide(saleReportFormVo.getTargetSaleMoney(), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
                }
                saleReportFormVo.setReachedRate(reachedRate);
                saleReportFormVo.setDepartmentName(department.getName());
                saleReportFormVos.add(saleReportFormVo);
            } else {//最底级机构时候直接获取最底级下面的人员的业绩和指标
                List<User> userList = userService.list();
                List<User> userLowestUnitList = userList.stream().filter(x -> currentUserId.contains(x.getId())).collect(Collectors.toList());
                List<String> currentUserCodes = userLowestUnitList.stream().map(User::getCode).collect(Collectors.toList());
                Map<Long, String> userMap = userList.stream().collect(Collectors.toMap(User::getId, User::getName));
                Map<Long, String> userCodeMap = userList.stream().collect(Collectors.toMap(User::getId, User::getCode));
                //获得当前机构前6个月的业绩和目标
                List<OtcLevelSalesReportMonth> otcSalesReportMonthSelfList = reportMonthLevelService.list(Wrappers.lambdaQuery(OtcLevelSalesReportMonth.class)
                        .in(OtcLevelSalesReportMonth::getUserCode, currentUserCodes)
                        .eq(OtcLevelSalesReportMonth::getBusinessMonth, monthDay)
                        .orderByAsc(OtcLevelSalesReportMonth::getRankNum));
                Map<String, List<OtcLevelSalesReportMonth>> otcSalesReportMonthSelfMap = otcSalesReportMonthSelfList.stream().collect(Collectors.groupingBy(OtcLevelSalesReportMonth::getUserCode));
                for (Long userId : currentUserId) {
                    DepartmentSaleMoneyReportFormVo saleReportFormLowestUnitVo = new DepartmentSaleMoneyReportFormVo();
                    String userCode = userCodeMap.getOrDefault(userId, "");
                    if (ObjectUtil.isNotEmpty(otcSalesReportMonthSelfMap.get(userCode))) {
                        saleReportFormLowestUnitVo.setTargetSaleMoney(otcSalesReportMonthSelfMap.get(userCode).stream().map(OtcLevelSalesReportMonth::getPlanAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                        saleReportFormLowestUnitVo.setRealSaleMoney(otcSalesReportMonthSelfMap.get(userCode).stream().map(OtcLevelSalesReportMonth::getFinishAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                    } else {
                        saleReportFormLowestUnitVo.setTargetSaleMoney(BigDecimal.ZERO);
                        saleReportFormLowestUnitVo.setRealSaleMoney(BigDecimal.ZERO);
                    }

                    BigDecimal reachedRate = BigDecimal.ZERO;
                    if (saleReportFormLowestUnitVo.getTargetSaleMoney().compareTo(BigDecimal.ZERO) > 0) {
                        reachedRate = saleReportFormLowestUnitVo.getRealSaleMoney().divide(saleReportFormLowestUnitVo.getTargetSaleMoney(), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
                    }
                    saleReportFormLowestUnitVo.setReachedRate(reachedRate);
                    saleReportFormLowestUnitVo.setDepartmentName(userMap.getOrDefault(userId, ""));
                    saleReportFormVos.add(saleReportFormLowestUnitVo);
                }
            }
        }
        saleMoneyReportFormVo.setIsLowestUnit(isLowestUnit);
        saleMoneyReportFormVo.setDepartmentSaleMoneys(saleReportFormVos.stream()
                .sorted(Comparator.comparing(DepartmentSaleMoneyReportFormVo::getReachedRate))
                .collect(Collectors.toList()));
        return saleMoneyReportFormVo;
    }

    /**
     * 销售员业绩报表
     *
     * @param reportFormsDto
     * @return
     */
    @Override
    public PersonSaleMoneyReportFormVo selectUserSaleMoneyStatistics(ReportFormsDto reportFormsDto) {
        String personCode = reportFormsDto.getPersonCode();
        String monthDay = reportFormsDto.getMonthDay();
        String category = reportFormsDto.getCategory();
        Date monthDayDate = DateUtils.stringToDate(monthDay, DateUtils.MONTH_PATTERN);
        //获得当前机构前6个月的业绩和目标
        List<OtcLevelSalesReportMonth> otcSalesReportDeptMonthSelfBeforeList = reportMonthLevelService.list(Wrappers.lambdaQuery(OtcLevelSalesReportMonth.class)
                .eq(ObjectUtil.isNotEmpty(personCode), OtcLevelSalesReportMonth::getUserCode, personCode)
                .between(OtcLevelSalesReportMonth::getBusinessMonth, DateUtils.addDateMonths(monthDayDate, -6), monthDayDate)
                .orderByAsc(OtcLevelSalesReportMonth::getRankNum));
        Map<String, BigDecimal> targetSaleMoneyMap = new TreeMap<>();
        Map<String, BigDecimal> realSaleMoneyMap = new TreeMap<>();
        for (OtcLevelSalesReportMonth otcSalesReportDeptMonth : otcSalesReportDeptMonthSelfBeforeList) {
            targetSaleMoneyMap.put(otcSalesReportDeptMonth.getBusinessMonth().substring(5, 7), otcSalesReportDeptMonth.getPlanAmount());
            realSaleMoneyMap.put(otcSalesReportDeptMonth.getBusinessMonth().substring(5, 7), otcSalesReportDeptMonth.getFinishAmount());
        }
        // 按照 businessMonth 倒序排序
        otcSalesReportDeptMonthSelfBeforeList = otcSalesReportDeptMonthSelfBeforeList.stream()
                .sorted(Comparator.comparing(OtcLevelSalesReportMonth::getBusinessMonth).reversed())
                .collect(Collectors.toList());
        OtcLevelSalesReportMonth otcSalesReportDeptMonth = CollectionUtil.isNotEmpty(otcSalesReportDeptMonthSelfBeforeList) ? otcSalesReportDeptMonthSelfBeforeList.get(0) : null;
        PersonSaleMoneyReportFormVo personSaleMoneyReportFormVo = new PersonSaleMoneyReportFormVo();
        personSaleMoneyReportFormVo.setTargetSaleMoney(null != otcSalesReportDeptMonth ? otcSalesReportDeptMonth.getPlanAmount() : BigDecimal.ZERO);
        personSaleMoneyReportFormVo.setRealSaleMoney(null != otcSalesReportDeptMonth ? otcSalesReportDeptMonth.getFinishAmount() : BigDecimal.ZERO);
        //personSaleMoneyReportFormVo.setPersonName();
        BigDecimal reachedRate = BigDecimal.ZERO;
        if (personSaleMoneyReportFormVo.getTargetSaleMoney().compareTo(BigDecimal.ZERO) > 0) {
            reachedRate = personSaleMoneyReportFormVo.getRealSaleMoney().divide(personSaleMoneyReportFormVo.getTargetSaleMoney(), 2, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
        }
        personSaleMoneyReportFormVo.setReachedRate(reachedRate);
        personSaleMoneyReportFormVo.setTargetSaleMoneyMap(targetSaleMoneyMap);
        personSaleMoneyReportFormVo.setRealSaleMoneyMap(realSaleMoneyMap);
        return personSaleMoneyReportFormVo;
    }

    public List<VisitReportFormVo> visitReportFormNew(ReportFormsDto reportFormsDto) {
        List<VisitReportFormVo> VisitReportFormVos = new ArrayList<>();
        List<String> visitCategory = Arrays.asList(CategoryEnum.VISITSHOP.getCode());
        List<String> coachCategory = Arrays.asList(CategoryEnum.COACHSHOP.getCode(), CategoryEnum.COACHSEGMENT.getCode(), CategoryEnum.VISITSEGMENT.getCode(), CategoryEnum.VISITHEAD.getCode());
        String departId = reportFormsDto.getDepartmentId();
        String postId = reportFormsDto.getPostId();
        String category = reportFormsDto.getCategory();
        String loginPostId = reportFormsDto.getLoginPostId();
        //获得目标家次
        Integer reachedStandardDay = 0;
        OtcSalesmanTaskVo otcSalesmanTaskVo = new OtcSalesmanTaskVo();
        otcSalesmanTaskVo.setTaskType(category);
        List<OtcSalesmanTaskVo> salesmanTaskInfos = otcSignInMapper.selectTaskInfo(otcSalesmanTaskVo);
        //获取当前任务的目标家次
        Department currendDepartment = departmentService.getOne(Wrappers.lambdaQuery(Department.class)
                .eq(ObjectUtil.isNotEmpty(departId), Department::getId, departId)
                .orderByAsc(Department::getSortCode)
                .select(Department.class, x -> VoToColumnUtil.fieldsToColumns(DepartmentListVo.class).contains(x.getProperty())));

        //获得组织机构信息和用户信息
        /*List<Department> departmentList = departmentService.list(Wrappers.lambdaQuery(Department.class)
                .eq(ObjectUtil.isNotEmpty(departId), Department::getParentId, departId)
                .orderByAsc(Department::getSortCode)
                .select(Department.class, x -> VoToColumnUtil.fieldsToColumns(DepartmentListVo.class).contains(x.getProperty())));
        if (CollectionUtil.isEmpty(departmentList)) {
            departmentList = departmentService.list(Wrappers.lambdaQuery(Department.class)
                    .eq(ObjectUtil.isNotEmpty(departId), Department::getId, departId)
                    .orderByAsc(Department::getSortCode)
                    .select(Department.class, x -> VoToColumnUtil.fieldsToColumns(DepartmentListVo.class).contains(x.getProperty())));
        }*/

        List<Department> departmentAllList = departmentService.list();
        List<UserDeptRelation> userDeptRelationList = userDeptRelationService.list();
        List<UserPostRelation> userPostRelationList = userPostRelationService.list();
        List<User> userList = userService.list();
        List<Post> postList = postService.list();
        Map<Long, String> userMap = userList.stream().collect(Collectors.toMap(User::getId, User::getName));
        Map<Long, String> postMap = postList.stream().collect(Collectors.toMap(Post::getId, Post::getName));
        Map<String, Long> postCodeMap = postList.stream().collect(Collectors.toMap(Post::getCode, Post::getId));

        //todo 可以根据hierarchyList的个数或者层级level来判断是顶级还是最底级
        List<String> hierarchyList = Arrays.asList(currendDepartment.getHierarchy().split("-"));
        List<Department> childDepartmentList = departmentAllList.stream().filter(x -> x.getHierarchy().contains(currendDepartment.getId().toString())).collect(Collectors.toList());
        List<Long> childDepartmentIds = childDepartmentList.stream().map(Department::getId).collect(Collectors.toList());
        List<UserDeptRelation> currentUserDeptRelationList = userDeptRelationList.stream().filter(x -> childDepartmentIds.contains(x.getDeptId())).collect(Collectors.toList());
        List<Long> currentUserId = currentUserDeptRelationList.stream().map(UserDeptRelation::getUserId).collect(Collectors.toList());
       /* if (loginPostId.equals(postCodeMap.getOrDefault(PostCodeEnum.KAMANAGER.getCode(), 0L).toString())) {
            for (OtcSalesmanTaskVo otcSalesmanTask : salesmanTaskInfos) {
                List<String> postIds = Arrays.asList(otcSalesmanTask.getPostId().split(","));
                if (postIds.contains(loginPostId)) {
                    postIds = postIds.stream().filter(x -> x.equals(loginPostId)).collect(Collectors.toList());
                }
                for (String postIdStr : postIds) {
                    VisitReportFormVo visitReportFormVo = new VisitReportFormVo();
                    List<UserPostRelation> currentUserPostRelationList = userPostRelationList.stream().filter(x -> currentUserId.contains(x.getUserId()) && postIdStr.equals(x.getPostId().toString())).collect(Collectors.toList());
                    List<Long> currentUserPostIds = currentUserPostRelationList.stream().map(UserPostRelation::getUserId).collect(Collectors.toList());
                    // List<User> currentUserList = userList.stream().filter(x -> currentUserPostIds.contains(x.getId())).collect(Collectors.toList());
                    visitReportFormVo.setPostId(postIdStr);
                    visitReportFormVo.setPostName(postMap.getOrDefault(Long.valueOf(postIdStr), "未知岗位"));
                    visitReportFormVo.setPersonNum(currentUserPostIds.size());
                    visitReportFormVo.setFrequency(otcSalesmanTask.getFrequency());
                    visitReportFormVo.setTargetNum(otcSalesmanTask.getGoal());
                    visitReportFormVo.setTaskId(otcSalesmanTask.getId().toString());
                    visitReportFormVo.setStartYearMonth(otcSalesmanTask.getStartYearMonth());
                    visitReportFormVo.setEndYearMonth(otcSalesmanTask.getEndYearMonth());
                    VisitReportFormVos.add(visitReportFormVo);
                }
            }
        } else {*/
        for (OtcSalesmanTaskVo otcSalesmanTask : salesmanTaskInfos) {
            List<String> postIds = Arrays.asList(otcSalesmanTask.getPostId().split(","));
            //登录人是KA岗位只看自己
            if (ObjectUtil.isNotEmpty(loginPostId) && loginPostId.equals(postCodeMap.getOrDefault(PostCodeEnum.KAMANAGER.getCode(), 0L).toString()) && postIds.contains(loginPostId)) {
                postIds = postIds.stream().filter(x -> x.equals(loginPostId)).collect(Collectors.toList());

            }
            for (String postIdStr : postIds) {
                if (postIdStr.equals(postCodeMap.getOrDefault(PostCodeEnum.DIRECTOR.getCode(), 0L).toString())) {
                    continue;
                }
                if (hierarchyList.size() == 2) {
                    if (postIdStr.equals(postCodeMap.getOrDefault(PostCodeEnum.KAMANAGER.getCode(), 0L).toString()) || postIdStr.equals(postCodeMap.getOrDefault(PostCodeEnum.MANAGER.getCode(), 0L).toString())) {
                        continue;
                    }
                }
                if (hierarchyList.size() == 3) {
                    if (postIdStr.equals(postCodeMap.getOrDefault(PostCodeEnum.LEADER.getCode(), 0L).toString()) || postIdStr.equals(postCodeMap.getOrDefault(PostCodeEnum.DIRECTOR.getCode(), 0L).toString()) || postIdStr.equals(postCodeMap.getOrDefault(PostCodeEnum.MANAGER.getCode(), 0L).toString()) || postIdStr.equals(postCodeMap.getOrDefault(PostCodeEnum.KAMANAGER.getCode(), 0L).toString())) {
                        continue;
                    }
                }
                VisitReportFormVo visitReportFormVo = new VisitReportFormVo();
                List<UserPostRelation> currentUserPostRelationList = userPostRelationList.stream().filter(x -> currentUserId.contains(x.getUserId()) && postIdStr.equals(x.getPostId().toString())).collect(Collectors.toList());
                List<Long> currentUserPostIds = currentUserPostRelationList.stream().map(UserPostRelation::getUserId).collect(Collectors.toList());
                // List<User> currentUserList = userList.stream().filter(x -> currentUserPostIds.contains(x.getId())).collect(Collectors.toList());
                visitReportFormVo.setPostId(postIdStr);
                visitReportFormVo.setPostName(postMap.getOrDefault(Long.valueOf(postIdStr), "未知岗位"));
                visitReportFormVo.setPersonNum(currentUserPostIds.size());
                visitReportFormVo.setFrequency(otcSalesmanTask.getFrequency());
                visitReportFormVo.setTargetNum(otcSalesmanTask.getGoal());
                visitReportFormVo.setTaskId(otcSalesmanTask.getId().toString());
                visitReportFormVo.setStartYearMonth(otcSalesmanTask.getStartYearMonth());
                visitReportFormVo.setEndYearMonth(otcSalesmanTask.getEndYearMonth());
                VisitReportFormVos.add(visitReportFormVo);
            }
            //  }
        }
        if (ObjectUtil.isNotEmpty(loginPostId) && loginPostId.equals(postCodeMap.getOrDefault(PostCodeEnum.KAMANAGER.getCode(), 0L).toString())) {
            VisitReportFormVos.clear();
        }
        return VisitReportFormVos.stream()
                .sorted(Comparator.comparing(VisitReportFormVo::getPostId))
                .collect(Collectors.toList());
    }

    @Override
    public List<VisitReportFormVo> visitReportFormDetails(ReportFormsDto reportFormsDto) {
        List<VisitReportFormVo> VisitReportFormVos = new ArrayList<>();
        String departId = reportFormsDto.getDepartmentId();
        String postId = reportFormsDto.getPostId();
        String category = reportFormsDto.getCategory();
        String frequency = reportFormsDto.getFrequency();
        String monthDay = reportFormsDto.getMonthDay();
        String taskId = reportFormsDto.getTaskId();
        Date monthDayBeginDate = null;
        Date monthDayEndDate = null;
        if (frequency.equals(TaskFrequencyEnum.DAY.getCode())) {
            monthDayBeginDate = DateUtils.stringToDate(monthDay, DateUtils.DATE_PATTERN);
            monthDayEndDate = DateUtils.addDateDays(monthDayBeginDate, 1);
        } else {
            monthDayBeginDate = DateUtils.stringToDate(monthDay, DateUtils.MONTH_PATTERN);
            monthDayEndDate = DateUtils.addDateMonths(monthDayBeginDate, 1);
        }

        //获得目标家次
        Integer reachedStandardDay = 0;
        reachedStandardDay = otcSalesmanTaskVisit.getOne(Wrappers.lambdaQuery(OtcSalesmanTaskVisit.class).eq(OtcSalesmanTaskVisit::getTaskId, taskId)).getGoal();


        //获得组织机构信息和用户信息
        List<Department> departmentList = departmentService.list(Wrappers.lambdaQuery(Department.class)
                .eq(ObjectUtil.isNotEmpty(departId), Department::getParentId, departId)
                .orderByAsc(Department::getSortCode)
                .select(Department.class, x -> VoToColumnUtil.fieldsToColumns(DepartmentListVo.class).contains(x.getProperty())));
        if (CollectionUtil.isEmpty(departmentList)) {
            departmentList = departmentService.list(Wrappers.lambdaQuery(Department.class)
                    .eq(ObjectUtil.isNotEmpty(departId), Department::getId, departId)
                    .orderByAsc(Department::getSortCode)
                    .select(Department.class, x -> VoToColumnUtil.fieldsToColumns(DepartmentListVo.class).contains(x.getProperty())));
        }
        List<Department> departmentAllList = departmentService.list();
        List<UserDeptRelation> userDeptRelationList = userDeptRelationService.list();
        List<UserPostRelation> userPostRelationList = userPostRelationService.list();
        List<User> userList = userService.list();
        Map<Long, String> userMap = userList.stream().collect(Collectors.toMap(User::getId, User::getName));

        //获得类型获得绩效信息
        /*List<OtcSalesmanTask> salesmanTaskList = taskManagementService.list(Wrappers.lambdaQuery(OtcSalesmanTask.class)
                .eq(ObjectUtil.isNotEmpty(category), OtcSalesmanTask::getTaskType, category)
                .eq(OtcSalesmanTask::getTaskStatus, "2")
                .orderByAsc(OtcSalesmanTask::getCreateDate));
        List<String> taskPostIdList = Arrays.asList(salesmanTaskList.get(0).getPostId().split(","));
        List<UserPostRelation> taskUserPostRelationList = userPostRelationList.stream().filter(x -> taskPostIdList.contains(x.getPostId().toString())).collect(Collectors.toList());*/

        //获得签到信息
        List<OtcSignIn> OtcSignInList = signService.list(Wrappers.lambdaQuery(OtcSignIn.class)
                .eq(OtcSignIn::getSiginType, 2)
                .eq(OtcSignIn::getCategory, category)
                //.in(OtcSignIn::getCategory, Arrays.asList(TaskTypeEnum.VISITSHOP.getCode(), TaskTypeEnum.VISITSEGMENT.getCode(), TaskTypeEnum.VISITHEAD.getCode()))
                .between(OtcSignIn::getCreateDate, monthDayBeginDate, monthDayEndDate)
                .orderByAsc(OtcSignIn::getCreateDate)
                .select(OtcSignIn.class, x -> VoToColumnUtil.fieldsToColumns(OtcSignInVo.class).contains(x.getProperty())));
        // 按照 id 分组并计算 signCount 的和
        Map<Long, Long> signCountMap = OtcSignInList.stream().collect(Collectors.groupingBy(OtcSignIn::getPersonId, Collectors.summingLong(OtcSignIn::getSignCount)));

        //Map<Long, List<Long>> parentDepartmentIdMap = new HashMap<>();
        for (Department department : departmentList) {
            VisitReportFormVo visitReportFormVo = new VisitReportFormVo();
            List<PersonVisitReportFormVo> reachedPersonVisitList = new ArrayList<>();
            List<PersonVisitReportFormVo> noReachPersonVisitList = new ArrayList<>();
            List<Department> childDepartmentList = departmentAllList.stream().filter(x -> x.getHierarchy().contains(department.getId().toString())).collect(Collectors.toList());
            List<Long> childDepartmentIds = childDepartmentList.stream().map(Department::getId).collect(Collectors.toList());
            //parentDepartmentIdMap.put(department.getId(), childDepartmentIds);
            List<UserDeptRelation> currentUserDeptRelationList = userDeptRelationList.stream().filter(x -> childDepartmentIds.contains(x.getDeptId())).collect(Collectors.toList());
            List<Long> userIds = new ArrayList<>();
            List<Long> currentUserId = currentUserDeptRelationList.stream().map(UserDeptRelation::getUserId).collect(Collectors.toList());
            //协访的时候要根据岗位确认有协访任务的人
            List<UserPostRelation> taskUserPostRelationList = userPostRelationList.stream().filter(x -> x.getPostId().toString().equals(postId)).collect(Collectors.toList());
            List<UserPostRelation> taskCurrentUserPostRelationList = taskUserPostRelationList.stream().filter(x -> currentUserId.contains(x.getUserId())).collect(Collectors.toList());
            userIds = taskCurrentUserPostRelationList.stream().map(UserPostRelation::getUserId).collect(Collectors.toList());

            Integer reachedNum = 0;
            for (Long userId : userIds) {
                Long signCount = signCountMap.get(userId);
                if (signCount != null && signCount >= reachedStandardDay) {
                    reachedNum = reachedNum + 1;
                    PersonVisitReportFormVo personVisitReportFormVo = new PersonVisitReportFormVo();
                    personVisitReportFormVo.setPersonId(userId);
                    personVisitReportFormVo.setPersonName(userMap.get(userId));
                    personVisitReportFormVo.setReachedDayNum(null != signCountMap.get(userId) ? Integer.parseInt(signCountMap.get(userId).toString()) : 0);
                    personVisitReportFormVo.setStandardNum(reachedStandardDay);
                    personVisitReportFormVo.setReachedRate(new BigDecimal(personVisitReportFormVo.getReachedDayNum())
                            .divide(new BigDecimal(reachedStandardDay), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100)));
                    reachedPersonVisitList.add(personVisitReportFormVo);
                } else {
                    PersonVisitReportFormVo personVisitReportFormVo = new PersonVisitReportFormVo();
                    personVisitReportFormVo.setPersonId(userId);
                    personVisitReportFormVo.setPersonName(userMap.get(userId));
                    personVisitReportFormVo.setReachedDayNum(null != signCountMap.get(userId) ? Integer.parseInt(signCountMap.get(userId).toString()) : 0);
                    personVisitReportFormVo.setStandardNum(reachedStandardDay);
                    personVisitReportFormVo.setReachedRate(new BigDecimal(personVisitReportFormVo.getReachedDayNum())
                            .divide(new BigDecimal(reachedStandardDay), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100)));
                    noReachPersonVisitList.add(personVisitReportFormVo);
                }
            }

            visitReportFormVo.setDepartmentName(department.getName());
            visitReportFormVo.setPersonNum(userIds.size());
            visitReportFormVo.setReachedPersonVisits(reachedPersonVisitList);
            visitReportFormVo.setNoReachPersonVisits(noReachPersonVisitList);
            visitReportFormVo.setReachedNum(reachedNum);
            visitReportFormVo.setNoReachedNum(userIds.size() - reachedNum);
            // 确保 currentUserId.size() 不为 0，避免除以 0 的错误
            if (userIds.size() > 0) {
                visitReportFormVo.setReachedRate(new BigDecimal(reachedNum)
                        .divide(new BigDecimal(userIds.size()), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100)));
            } else {
                visitReportFormVo.setReachedRate(BigDecimal.ZERO);
            }
            VisitReportFormVos.add(visitReportFormVo);
        }
        return VisitReportFormVos;
    }
}
