package com.zilue.module.business.taskManagement.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.zilue.common.annotation.XjrLog;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.handler.FormContentStyleStrategy;
import com.zilue.common.model.result.R;
import com.zilue.common.page.PageOutput;
import com.zilue.common.utils.ExcelUtil;
import com.zilue.module.business.taskManagement.vo.VisitTotalVo;
import com.zilue.module.business.taskManagement.vo.VisitVo;
import com.zilue.module.business.taskManagement.dto.VisitQueryDto;
import com.zilue.module.business.taskManagement.service.TaskDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import java.io.ByteArrayOutputStream;
import java.util.List;


@RestController
@RequestMapping(GlobalConstant.BUSINESS_MODULE_PREFIX + "/taskDetail")
@Api(value = GlobalConstant.BUSINESS_MODULE_PREFIX   + "/taskDetail",tags = "拜访明细")
@AllArgsConstructor
public class TaskDetailController{
    private final TaskDetailService taskDetailService;

    @PostMapping("/visitList")
    @ApiOperation(value="拜访任务明细")
    @XjrLog(value = "拜访任务明细")
    public R<PageOutput<VisitVo>> visitList(@Valid @RequestBody VisitQueryDto dto){
        return R.ok(taskDetailService.pageList(dto));
    }

    @PostMapping("/visitTotal")
    @ApiOperation(value="拜访任务统计")
    @XjrLog(value = "拜访任务统计")
    public R<VisitTotalVo> visitTotal(@Valid @RequestBody VisitQueryDto dto){
        return R.ok(taskDetailService.visitTotal(dto));
    }
    @PostMapping(value = "/visitExport")
    @ApiOperation(value = "拜访任务明细导出")
    @XjrLog(value = "拜访任务明细导出")
    public ResponseEntity<byte[]> visitExport(@Valid @RequestBody VisitQueryDto dto) {
        List<VisitVo> list = taskDetailService.exportList(dto);
        ByteArrayOutputStream bot = new ByteArrayOutputStream();
        EasyExcel.write(bot).head(VisitVo.class).automaticMergeHead(false)
                .registerWriteHandler(new FormContentStyleStrategy())
                .excelType(ExcelTypeEnum.XLSX).sheet().doWrite(list);
        ByteArrayOutputStream resultBot = ExcelUtil.renderExportRequiredHead(bot);
        return R.fileStream(resultBot.toByteArray(), "拜访任务明细导出" + ExcelTypeEnum.XLSX.getValue());
    }
}
