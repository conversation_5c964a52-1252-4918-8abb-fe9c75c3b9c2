package com.zilue.module.business.signreached.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

import java.time.LocalTime;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.List;



/**
* @title: 部门打卡排名报表
* <AUTHOR>
* @Date: 2025-01-17
* @Version 1.0
*/
@Data
public class UpdateOtcSalesReportSignReachedDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 
    */
    @ApiModelProperty("")
    private Long id;
    /**
    * 部门id
    */
    @ApiModelProperty("部门id")
    private Long deptId;
    /**
    * 上级部门id
    */
    @ApiModelProperty("上级部门id")
    private Long parentDeptId;
    /**
    * 达成人数
    */
    @ApiModelProperty("达成人数")
    private Integer reachNum;
    /**
    * 部门人数
    */
    @ApiModelProperty("部门人数")
    private Integer deptPersonNum;
    /**
    * 达成率
    */
    @ApiModelProperty("达成率")
    private BigDecimal reachRate;
    /**
    * 部门排名（同级部门中）
    */
    @ApiModelProperty("部门排名（同级部门中）")
    private Long rankNum;
    /**
    * 统计日期
    */
    @ApiModelProperty("统计日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reportDate;

}
