package com.zilue.module.business.taskManagement.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**+ * @title: 目标管理-拜访协防类的辅助表+ * <AUTHOR> * @Date: 2024-12-31+ * @Version 1.0+ */
@Data
@TableName("otc_salesman_task_visit")
@ApiModel(value = "目标管理对象", description = "目标管理")
public class OtcSalesmanTaskVisit implements Serializable {

    private static final long serialVersionUID = 1L;
    /**+     *+     */
    @ApiModelProperty("")
    @TableId
    private Long id;
    @ApiModelProperty("代表目标，家次")
    private Integer goal;
    @ApiModelProperty("任务id")
    private Long taskId;
    /**+     * 创建人ID+     */
    @ApiModelProperty("创建人ID")
    @TableField(fill = FieldFill.INSERT)
    private Long createUserId;
    /**+     * 创建日期+     */
    @ApiModelProperty("创建日期")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createDate;
    /**+     * 修改人ID+     */
    @ApiModelProperty("修改人ID")
    @TableField(fill = FieldFill.UPDATE)
    private Long modifyUserId;
    /**+     * 修改日期+     */
    @ApiModelProperty("修改日期")
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime modifyDate;
    /**+     * 删除标记+     */
    @ApiModelProperty("删除标记")
    @TableField(fill = FieldFill.INSERT)
    @TableLogic
    private Integer deleteMark;
    /**+     * 有效标记+     */
   @ApiModelProperty("有效标记")
    @TableField(fill = FieldFill.INSERT)
    private Integer enabledMark;
}
