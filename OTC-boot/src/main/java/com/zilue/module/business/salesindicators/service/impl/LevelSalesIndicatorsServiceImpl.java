package com.zilue.module.business.salesindicators.service.impl;

import com.zilue.common.constant.MongoCollectionConstant;
import com.zilue.common.utils.DateUtils;
import com.zilue.common.utils.StringUtil;
import com.zilue.module.business.salesindicators.entity.OtcSalesmanLevelSalesIndicators;
import com.zilue.module.business.salesindicators.entity.OtcSalesmanSalesIndicators;
import com.zilue.module.business.salesindicators.mapper.OtcSalesmanLevelSalesIndicatorsMapper;
import com.zilue.module.business.salesindicators.mapper.OtcSalesmanSalesIndicatorsMapper;
import com.zilue.module.business.salesindicators.service.ILevelSalesIndicatorsService;
import com.zilue.module.business.salesindicators.service.ISalesIndicatorsService;
import com.zilue.module.business.salesperformance.service.ISalesPerformanceService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOptions;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Random;

/**
 * @title: service
 * <AUTHOR>
 * @Date: 2025-01-15
 * @Version 1.0
 */
@Service
@AllArgsConstructor
@Slf4j
public class LevelSalesIndicatorsServiceImpl implements ILevelSalesIndicatorsService {

    public static final String ZHIBIAO2 = "zhibiao2";
    public static final int pageSize = 4000;

    private final MongoTemplate mongoTemplate;
    private final OtcSalesmanLevelSalesIndicatorsMapper indicatorsLevelMapper;

    private ISalesPerformanceService salesPerformanceService;

    /**
     * 查询指标，按个人汇总
     *
     * @param startYearMonth
     * @param endYearMonth
     * @return
     */
    @Override
    public List<OtcSalesmanLevelSalesIndicators> queryIndicators(String startYearMonth, String endYearMonth) {
        Criteria criteria = new Criteria();
        criteria.and("businessMonth").gte(startYearMonth).lte(endYearMonth);
        AggregationOptions aggregationOptions = AggregationOptions.builder().allowDiskUse(true).build();
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group("userCode")
                        .first("userCode").as("userCode")
                        .first("businessMonth").as("businessMonth")
                        .sum("amount").as("amount")
        ).withOptions(aggregationOptions);
        AggregationResults<OtcSalesmanLevelSalesIndicators> result = mongoTemplate.aggregate(aggregation, MongoCollectionConstant.LEVEL_SALES_INDICATORS, OtcSalesmanLevelSalesIndicators.class);
        return result.getMappedResults();
    }

    /**
     * 按连锁总部汇总
     *
     * @param month
     * @return
     */
    @Override
    public List<OtcSalesmanLevelSalesIndicators> queryIndicatorsGroupByHeardStore(String month) {
        Criteria criteria = new Criteria();
        criteria.and("businessMonth").is(month);
        AggregationOptions aggregationOptions = AggregationOptions.builder().allowDiskUse(true).build();
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group("headquartersCode")
                        .first("headquartersCode").as("headquartersCode")
                        .first("businessMonth").as("businessMonth")
                        .sum("amount").as("amount")
        ).withOptions(aggregationOptions);
        AggregationResults<OtcSalesmanLevelSalesIndicators> result = mongoTemplate.aggregate(aggregation, MongoCollectionConstant.LEVEL_SALES_INDICATORS, OtcSalesmanLevelSalesIndicators.class);
        return result.getMappedResults();
    }

    @Override
    public void save(List<OtcSalesmanLevelSalesIndicators> list) {
        for (OtcSalesmanLevelSalesIndicators bean : list) {
            bean.setEdpId(String.valueOf(System.currentTimeMillis()));
            bean.setSyncTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            bean.setAmount(new BigDecimal(getRandomNumber() * 100));


        }
        BulkOperations operations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, MongoCollectionConstant.LEVEL_SALES_INDICATORS);
        operations.insert(list);
        operations.execute();
    }


    /**
     * edp指标分页查询
     *
     * @param syncTime
     * @param page
     * @param pageSize
     * @return
     */
    @Override
    public List<OtcSalesmanLevelSalesIndicators> queryList(String syncTime, int page, int pageSize) {
        return indicatorsLevelMapper.queryList(syncTime, (page - 1) * pageSize, pageSize);
    }

    /**
     * edp指标count
     *
     * @param syncTime
     * @return
     */
    @Override
    public Integer queryCount(String syncTime) {
        return indicatorsLevelMapper.queryCount(syncTime);
    }


    /**
     * 同步
     *
     * @param syncTime
     */
    @Override
    public void syncIndicators(String syncTime) {
        if (StringUtil.isBlank(syncTime)) {
            syncTime = salesPerformanceService.getMaxSyncTime(ZHIBIAO2);
        }
        int count = queryCount(syncTime);
        if (count == 0) {
            log.warn("[指标数据同步] 同步数据为空，syncTime = {}", syncTime);
            return;
        }
        String maxSyncTime = null;
        int pageCount = count / pageSize;
        long batchId = System.currentTimeMillis();
        String createTime = DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN);
        // 清空集合  全量清空
        mongoTemplate.remove(new Query(), MongoCollectionConstant.LEVEL_SALES_INDICATORS);

        for (int i = 0; i < pageCount; i++) {
            maxSyncTime = syncIndicatorsByBatch(syncTime, i + 1, batchId, createTime);
        }
        long mod = count % pageSize;
        if (mod > 0) {
            maxSyncTime = syncIndicatorsByBatch(syncTime, pageCount + 1, batchId, createTime);
        }
        if (maxSyncTime == null) {
            maxSyncTime = createTime;
        }
        salesPerformanceService.saveSyncRecordTime(ZHIBIAO2, maxSyncTime, batchId, createTime);
    }

    private String syncIndicatorsByBatch(String syncTime, int page, long batchId, String createTime) {
        List<OtcSalesmanLevelSalesIndicators> list = queryList(syncTime, page, pageSize);
        if (list.isEmpty()) {
            return null;
        }
        for (OtcSalesmanLevelSalesIndicators bean : list) {
            bean.setBatchId(batchId);
            bean.setCreateTime(createTime);
        }
        BulkOperations operations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, MongoCollectionConstant.LEVEL_SALES_INDICATORS);
//        BulkOperations operations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, "otc_salesman_sales_indicators_test");
        operations.insert(list);
        operations.execute();
        return list.get(list.size() - 1).getSyncTime();
    }


    public static int getRandomNumber() {
        Random random = new Random();
        return random.nextInt(6) + 1;
    }


}
