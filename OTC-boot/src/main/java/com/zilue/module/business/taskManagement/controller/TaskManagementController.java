package com.zilue.module.business.taskManagement.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.zilue.common.annotation.RepeatSubmit;
import com.zilue.common.annotation.XjrLog;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.handler.FormContentStyleStrategy;
import com.zilue.common.model.result.R;
import com.zilue.common.page.PageOutput;
import com.zilue.common.utils.ExcelUtil;
import com.zilue.module.business.taskManagement.dto.AddOtcSalesmanTaskDto;
import com.zilue.module.business.taskManagement.dto.OtcSalesmanTaskPageDto;
import com.zilue.module.business.taskManagement.dto.SalesPerformancePageDto;
import com.zilue.module.business.taskManagement.dto.UpdateOtcSalesmanTaskDto;
import com.zilue.module.business.taskManagement.service.ITaskManagementService;
import com.zilue.module.business.taskManagement.vo.DetailPageOutput;
import com.zilue.module.business.taskManagement.vo.OtcSalesmanTaskPageVo;
import com.zilue.module.business.taskManagement.vo.OtcSalesmanTaskVo;
import com.zilue.module.business.taskManagement.vo.SalesPerformanceRespVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * +* @title: 目标管理+* <AUTHOR> @Date: 2024-12-31+* @Version 1.0+
 */
@RestController
@RequestMapping(GlobalConstant.BUSINESS_MODULE_PREFIX + "/taskmanagement")
@Api(value = GlobalConstant.BUSINESS_MODULE_PREFIX + "/taskmanagement", tags = "目标管理代码")
@AllArgsConstructor
public class TaskManagementController {
    private final ITaskManagementService taskManagementService;

    @PostMapping("/page")
    @ApiOperation(value = "OtcSalesmanTask列表(分页)")
    // @SaCheckPermission("taskmanagement:detail")
    public R<PageOutput<OtcSalesmanTaskPageVo>> page(@Valid @RequestBody OtcSalesmanTaskPageDto dto) {
        PageOutput<OtcSalesmanTaskPageVo> pageOutput = taskManagementService.getPageList(dto);
        return R.ok("查询成功", pageOutput);
    }

    @GetMapping("/getOtcSalesmanTask")
    @ApiOperation(value = "根据id查询OtcSalesmanTask信息")
    //@SaCheckPermission("taskmanagement:detail")
    public R<OtcSalesmanTaskVo> getOtcSalesmanTask(@RequestParam Long id) {
        OtcSalesmanTaskVo otcSalesmanTaskVo = taskManagementService.getOtcSalesmanTask(id);

        if (otcSalesmanTaskVo == null) {
            return R.error("查询数据为空", otcSalesmanTaskVo);
        }
        return R.ok(otcSalesmanTaskVo);
    }

    @PostMapping("/saveOtcSalesmanTask")
    @ApiOperation(value = "保存OtcSalesmanTask")
    // @SaCheckPermission("taskmanagement:add")
    @RepeatSubmit
    public R<Boolean> saveOtcSalesmanTask(@Valid @RequestBody AddOtcSalesmanTaskDto dto) {
        Boolean isSuccess = taskManagementService.saveOtcSalesmanTask(dto);
        return R.ok(isSuccess);
    }

    @PostMapping("/UpdateOtcSalesmanTask")
    @ApiOperation(value = "修改OtcSalesmanTask")
    //@SaCheckPermission("taskmanagement:edit")
    @RepeatSubmit
    public R<Boolean> UpdateOtcSalesmanTask(@Valid @RequestBody UpdateOtcSalesmanTaskDto dto) {
        return R.ok(taskManagementService.UpdateOtcSalesmanTask(dto));
    }

    @PostMapping("/UpdateTaskIsEnabled")
    @ApiOperation(value = "修改任务状态")
    //@SaCheckPermission("taskmanagement:edit")
    @RepeatSubmit
    public R<Boolean> UpdateTaskIsEnabled(@Valid @RequestBody UpdateOtcSalesmanTaskDto dto) {
        return R.ok(taskManagementService.UpdateTaskIsEnabled(dto));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除")
    //@SaCheckPermission("taskmanagement:delete")
    @RepeatSubmit
    public R<Boolean> delete(@Valid @RequestBody List<Long> ids) {
        return R.ok(taskManagementService.removeBatchByIds(ids));
    }

    @GetMapping("salesPerformanceDetailPage")
    @ApiOperation(value = "分页获取销售业绩明细")
    @XjrLog(value = "分页查询销售业绩明细")
    public R<DetailPageOutput<SalesPerformanceRespVo>> salesPerformanceDetailPage(@Valid SalesPerformancePageDto dto) {
        return R.ok(taskManagementService.salesPerformanceDetailPage(dto));
    }

    @GetMapping("exportSalesPerformanceDetail")
    @ApiOperation(value = "导出销售业绩明细")
    @XjrLog(value = "导出销售业绩明细")
    public ResponseEntity<byte[]> exportSalesPerformanceDetail(@Valid SalesPerformancePageDto dto) {
        List<SalesPerformanceRespVo> list = taskManagementService.getSalesPerformanceDetailList(dto);
        ByteArrayOutputStream bot = new ByteArrayOutputStream();
        EasyExcel.write(bot).head(SalesPerformanceRespVo.class).automaticMergeHead(true)
                .registerWriteHandler(new FormContentStyleStrategy())
                .excelType(ExcelTypeEnum.XLSX).sheet().doWrite(list);
        ByteArrayOutputStream resultBot = ExcelUtil.renderExportRequiredHead(bot);
        return R.fileStream(resultBot.toByteArray(), "销售业绩明细" + ExcelTypeEnum.XLSX.getValue());
    }

    @GetMapping("updateSalesmanTaskStatusForJob")
    @ApiOperation(value = "更新任务状态")
    @XjrLog(value = "更新任务状态")
    public R updateSalesmanTaskStatusForJob() {
        taskManagementService.updateSalesmanTaskStatusForJob();
        return R.ok();
    }
}
