package com.zilue.module.business.punchset.dto;

import com.zilue.common.page.PageInput;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalTime;
import java.time.LocalDateTime;
import java.math.BigDecimal;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2024-12-26
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class OtcPunchSetPageDto extends PageInput {

    /**
    * 允许打卡范围
    */
    @ApiModelProperty("允许打卡范围")
    private Integer outerScope;

}
