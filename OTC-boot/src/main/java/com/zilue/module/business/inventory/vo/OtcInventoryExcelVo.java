package com.zilue.module.business.inventory.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class OtcInventoryExcelVo {

    @ExcelProperty("连锁名称")
    private String customerName;
    @ExcelProperty("连锁总部")
    private String parentCustomerName;
    @ExcelProperty("是否10大连锁 0 否 1是")
    private Integer isTen;
    @ExcelProperty("是否10大连锁")
    private String isTenStr;
    @ExcelProperty("省份")
    private String customerProvincialName;
    @ExcelProperty("EDP品规")
    private String product;
    @ExcelProperty("品种类型")
    private String productGroup;

    @ExcelProperty("业绩考核单价")
    private BigDecimal examPrice;

    @ExcelProperty("期初库存数量")
    private Integer beginWarehouseNumCount;
    @ExcelProperty("期初库存金额(万元)")
    private BigDecimal beginWarehouseMoney;


    @ExcelProperty("期间出库数量")
    private Integer periodOutCount;
    @ExcelProperty("期间出库金额(万元)")
    private BigDecimal periodOutMoney;


    @ExcelProperty("流到商业")
    private Integer outCommerceCount;
    @ExcelProperty("流到终端")
    private Integer outTerminalCount;

    @ExcelProperty("期间入库数量")
    private Integer periodComeCount;
    @ExcelProperty("期间入库金额(万元)")
    private BigDecimal periodComeMoney;

    @ExcelProperty("期末库存数量")
    private Integer warehouseNum;
    @ExcelProperty("期末库存金额(万元)")
    private BigDecimal warehouseMoney;

}
