package com.zilue.module.business.salesperformance.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;


/**
* @title: 销售业绩
* <AUTHOR>
* @Date: 2025-01-15
* @Version 1.0
*/
@Data
public class EdpSyncRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 
    */
    @ApiModelProperty("")
    @TableId
    private String id;
    /**
     * edp数据主键 zhibiao2基层业务员指标
     */
    @ApiModelProperty("zhibiao,zhibiao2,yeji")
    private String type;

    /**
    * 数据时间
    */
    @ApiModelProperty("数据时间")
    private String syncTime;

    /**
     * 同步批次号
     */
    private Long batchId;

    @ApiModelProperty("创建时间")
    private String createTime;


}