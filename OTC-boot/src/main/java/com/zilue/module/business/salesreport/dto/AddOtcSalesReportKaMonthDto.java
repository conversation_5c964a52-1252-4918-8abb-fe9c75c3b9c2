package com.zilue.module.business.salesreport.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

import java.math.BigDecimal;


/**
* @title: 连锁总部业绩按月
* <AUTHOR>
* @Date: 2025-01-15
* @Version 1.0
*/
@Data
public class AddOtcSalesReportKaMonthDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 代表编码
    */
    @ApiModelProperty("代表编码")
    private String deptCode;
    /**
    * 年计划值
    */
    @ApiModelProperty("年计划值")
    private BigDecimal planAmount;
    /**
    * 年完成值
    */
    @ApiModelProperty("年完成值")
    private BigDecimal finishAmount;
    /**
    * 业务月份
    */
    @ApiModelProperty("业务月份")
    private String businessMonth;
    /**
    * 同级架构下排名
    */
    @ApiModelProperty("同级架构下排名")
    private Long rankNum;

}
