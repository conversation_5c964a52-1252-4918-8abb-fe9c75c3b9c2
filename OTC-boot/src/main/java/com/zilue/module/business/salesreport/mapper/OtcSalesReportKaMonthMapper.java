package com.zilue.module.business.salesreport.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zilue.module.business.salesreport.entity.OtcSalesReportKaMonth;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* @title: mapper
* <AUTHOR>
* @Date: 2025-01-15
* @Version 1.0
*/
@Mapper
public interface OtcSalesReportKaMonthMapper extends BaseMapper<OtcSalesReportKaMonth> {

    List<OtcSalesReportKaMonth> querySumGroupByStoreCode(@Param("startMonth") String startMonth, @Param("endMonth") String endMonth);

    List<OtcSalesReportKaMonth> querySumGroupByUserCode(@Param("month") String month);

    List<OtcSalesReportKaMonth> querySumByYearGroupByUserCode(@Param("startMonth") String startMonth, @Param("endMonth") String endMonth);
}
