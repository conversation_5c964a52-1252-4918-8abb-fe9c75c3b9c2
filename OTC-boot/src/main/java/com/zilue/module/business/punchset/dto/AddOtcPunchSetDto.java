package com.zilue.module.business.punchset.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalTime;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.util.List;



/**
* @title: 打卡设置
* <AUTHOR>
* @Date: 2024-12-26
* @Version 1.0
*/
@Data
public class AddOtcPunchSetDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 允许打卡范围
    */
    @ApiModelProperty("允许打卡范围")
    private Integer outerScope;
    /**
    * 可超范围打卡(1=是 2=否)
    */
    @ApiModelProperty("可超范围打卡(1=是 2=否)")
    private Integer superableScope;
    /**
    * 可超范围文字说明(1=是 2=否)
    */
    @ApiModelProperty("可超范围文字说明(1=是 2=否)")
    private Integer superableExplain;

}
