package com.zilue.module.business.punchset.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zilue.common.model.result.R;
import com.zilue.module.business.punchset.dto.AddOtcPunchSetDto;
import com.zilue.module.business.punchset.dto.OtcVisitMattersDto;
import com.zilue.module.business.punchset.entity.OtcPunchSet;
import com.zilue.module.business.punchset.entity.OtcVisitMatters;
import com.zilue.module.business.punchset.mapper.OtcVisitMattersMapper;
import com.zilue.module.business.punchset.service.IVisitMattersService;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class VisitMattersServiceImpl extends ServiceImpl<OtcVisitMattersMapper, OtcVisitMatters> implements IVisitMattersService {

    @Override
    public List<OtcVisitMatters> visitMattersList() {
        List<OtcVisitMatters> OtcVisitMattersList = this.list().stream().sorted(Comparator.comparingInt(OtcVisitMatters::getMattersType))
                .collect(Collectors.toList());

        return OtcVisitMattersList;
    }

    @Override
    public void addVisitMatters(OtcVisitMattersDto dto) {
        OtcVisitMatters otcVisitMatters = BeanUtil.toBean(dto, OtcVisitMatters.class);
        boolean isSuccess = this.save(otcVisitMatters);
        return;
    }

    @Override
    public void updateVisitMatters(List<OtcVisitMattersDto> dtos) {
        for (OtcVisitMattersDto dto : dtos) {
            OtcVisitMatters otcVisitMatters = BeanUtil.toBean(dto, OtcVisitMatters.class);
            boolean isSuccess = this.updateById(otcVisitMatters);
        }

        return;
    }


}
