package com.zilue.module.business.punchset.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zilue.common.annotation.RepeatSubmit;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.model.result.R;
import com.zilue.common.page.ConventPage;
import com.zilue.common.page.PageOutput;
import com.zilue.common.utils.VoToColumnUtil;
import com.zilue.module.business.punchset.dto.AddOtcPunchSetDto;
import com.zilue.module.business.punchset.dto.OtcPunchSetPageDto;
import com.zilue.module.business.punchset.dto.OtcVisitMattersDto;
import com.zilue.module.business.punchset.dto.UpdateOtcPunchSetDto;
import com.zilue.module.business.punchset.entity.OtcPunchSet;
import com.zilue.module.business.punchset.entity.OtcVisitMatters;
import com.zilue.module.business.punchset.service.IPunchSetService;
import com.zilue.module.business.punchset.service.IVisitMattersService;
import com.zilue.module.business.punchset.vo.OtcPunchSetPageVo;
import com.zilue.module.business.punchset.vo.OtcPunchSetVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import java.util.List;

/**
 * @title: 打卡设置
 * <AUTHOR>
 * @Date: 2024-12-26
 * @Version 1.0
 */
@RestController
@RequestMapping(GlobalConstant.BUSINESS_MODULE_PREFIX + "/punchset")
@Api(value = GlobalConstant.BUSINESS_MODULE_PREFIX + "/punchset", tags = "打卡设置代码")
@AllArgsConstructor
public class PunchSetController {


    private final IPunchSetService punchSetService;

    private final IVisitMattersService visitMattersService;

    @GetMapping("/page")
    @ApiOperation(value = "OtcPunchSet列表(分页)")
    // @SaCheckPermission("punchset:detail")
    public R<PageOutput<OtcPunchSetPageVo>> page(@Valid OtcPunchSetPageDto dto) {

        LambdaQueryWrapper<OtcPunchSet> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(ObjectUtil.isNotNull(dto.getOuterScope()), OtcPunchSet::getOuterScope, dto.getOuterScope())
                .orderByDesc(OtcPunchSet::getId)
                .select(OtcPunchSet.class, x -> VoToColumnUtil.fieldsToColumns(OtcPunchSetPageVo.class).contains(x.getProperty()));
        IPage<OtcPunchSet> page = punchSetService.page(ConventPage.getPage(dto), queryWrapper);
        PageOutput<OtcPunchSetPageVo> pageOutput = ConventPage.getPageOutput(page, OtcPunchSetPageVo.class);
        return R.ok(pageOutput);
    }

    @GetMapping("/info")
    @ApiOperation(value = "根据id查询OtcPunchSet信息")
    //@SaCheckPermission("punchset:detail")
    public R<OtcPunchSetVo> info(@RequestParam Long id) {
        OtcPunchSet otcPunchSet = punchSetService.getById(id);
        OtcPunchSetVo otcPunchSetVo = BeanUtil.toBean(otcPunchSet, OtcPunchSetVo.class);
        if (otcPunchSetVo == null) {
            return R.error("找不到此数据！", otcPunchSetVo);
        }
        return R.ok(otcPunchSetVo);
    }


    @PostMapping("/add")
    @ApiOperation(value = "新增OtcPunchSet")
    // @SaCheckPermission("punchset:add")
    @RepeatSubmit
    public R<Long> add(@Valid @RequestBody AddOtcPunchSetDto dto) {
        OtcPunchSet otcPunchSet = BeanUtil.toBean(dto, OtcPunchSet.class);
        boolean isSuccess = punchSetService.save(otcPunchSet);
        return R.ok(otcPunchSet.getId());
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改OtcPunchSet")
    // @SaCheckPermission("punchset:edit")
    @RepeatSubmit
    public R<Boolean> update(@Valid @RequestBody UpdateOtcPunchSetDto dto) {

        OtcPunchSet otcPunchSet = BeanUtil.toBean(dto, OtcPunchSet.class);
        return R.ok(punchSetService.updateById(otcPunchSet));

    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除")
    // @SaCheckPermission("punchset:delete")
    @RepeatSubmit
    public R<Boolean> delete(@Valid @RequestBody List<Long> ids) {
        return R.ok(punchSetService.removeBatchByIds(ids));

    }


    /**
     * 查询列表visitMattersList
     *
     * @param
     * @return
     */
    @PostMapping("/visitMattersList")
    @ApiOperation(value = "visitMattersList")
    public R<List<OtcVisitMatters>> visitMattersList() {
        return R.ok(visitMattersService.visitMattersList());
    }

    /**
     * 添加签到次数
     *
     * @param dto
     * @return
     */
    @PostMapping("/addVisitMatters")
    @ApiOperation(value = "新增aisitMatters")
    public R addVisitMatters(@Valid @RequestBody OtcVisitMattersDto dto) {
        visitMattersService.addVisitMatters(dto);
        return R.ok();
    }

    /**
     * 修改签到次数
     *
     * @param dtos
     * @return
     */
    @PostMapping("/updateVisitMatters")
    @ApiOperation(value = "更新aisitMatters")
    public R updateVisitMatters(@Valid @RequestBody List<OtcVisitMattersDto> dtos) {
        visitMattersService.updateVisitMatters(dtos);
        return R.ok();
    }
}