package com.zilue.module.business.account.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class OtcAccountExportVo {

    /**
     * 主键ID
     */
    @ExcelProperty(value = "客户id", index = 0)
    @ApiModelProperty("主键ID")
    private Long id;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "客户id", index = 0)
    @ApiModelProperty("客户id")
    private String clientId;

    /**
     * 客户编号
     */
    @ExcelProperty(value = "客户编号", index = 1)
    @ApiModelProperty("客户编号")
    private String accountNumber;
    /**
     * 客户名称
     */
    @ExcelProperty(value = "客户名称", index = 2)
    @ApiModelProperty("客户名称")
    private String name;
    /**
     * 上级客户名称
     */
    @ExcelProperty(value = "分部客户名称", index = 3)
    @ApiModelProperty("分部客户名称")
    private String parentAccountIdName;

    @ExcelProperty(value = "连锁总部名称", index = 4)
    @ApiModelProperty("连锁总部名称")
    private String newOtcAccountName;
    /**
     * 负责人
     */
    @ExcelProperty(value = "负责人", index = 5)
    @ApiModelProperty("负责人")
    private String ownerName;


    /**
     * 负责人工号
     */
    @ExcelProperty(value = "负责人工号", index = 6)
    @ApiModelProperty("负责人工号")
    private String ownerNumber;

    /**
     * 省份名称
     */
    @ExcelProperty(value = "省份名称", index = 7)
    @ApiModelProperty("省份名称")
    private String province;

    /**
     * 城市名称
     */
    @ExcelProperty(value = "城市名称", index = 8)
    @ApiModelProperty("城市名称")
    private String city;

    /**
     * 区县名称
     */
    @ExcelProperty(value = "区县名称", index = 9)
    @ApiModelProperty("区县名称")
    private String district;

    /**
     * 详细地址
     */
    @ExcelProperty(value = "详细地址", index = 10)
    @ApiModelProperty("详细地址")
    private String address1Name;

    /**
     * 客户类型
     */
    @ExcelProperty(value = "客户类型", index = 11)
    @ApiModelProperty("客户类型")
    private String customerTypeCodeName;


    /**
     * 标签名称
     */
    @ExcelProperty(value = "标签名称", index = 12)
    @ApiModelProperty("标签名称")
    private String customerTagName;

    /**
     * 0：直营；1：加盟
     */
    @ExcelProperty(value = "直营/加盟", index = 13)
    @ApiModelProperty("0：直营；1：加盟")
    private Integer isDirectJoin;

    /**
     * 0：直营；1：加盟
     */
    @ExcelProperty(value = "直营/加盟", index = 14)
    @ApiModelProperty("0：直营；1：加盟")
    private String isDirectJoinStr;

    /**
     * 是否DTP门店：0：否；1：是
     */
    @ExcelProperty(value = "是否DTP门店", index = 15)
    @ApiModelProperty("是否DTP门店：0：否；1：是")
    private Integer isDtp;

    /**
     * 是否DTP门店：0：否；1：是
     */
    @ExcelProperty(value = "是否DTP门店", index = 16)
    @ApiModelProperty("是否DTP门店：0：否；1：是")
    private String isDtpStr;

    /**
     * 是否统筹门店：0：否；1：是
     */
    @ExcelProperty(value = "是否统筹门店", index = 17)
    @ApiModelProperty("是否统筹门店：0：否；1：是")
    private Integer isWhole;

    /**
     * 是否统筹门店：0：否；1：是
     */
    @ExcelProperty(value = "是否统筹门店", index = 18)
    @ApiModelProperty("是否统筹门店：0：否；1：是")
    private String isWholeStr;

    /**
     * 是否电商门店：0：否；1：是
     */
    @ExcelProperty(value = "是否电商门店", index = 19)
    @ApiModelProperty("是否电商门店：0：否；1：是")
    private Integer isOnline;

    /**
     * 是否电商门店：0：否；1：是
     */
    @ExcelProperty(value = "是否电商门店", index = 20)
    @ApiModelProperty("是否电商门店：0：否；1：是")
    private String isOnlineStr;

    /**
     * 销售规模
     */
    @ExcelProperty(value = "销售规模", index = 21)
    @ApiModelProperty("销售规模")
    private String saleScale;

    /**
     * 客户联系电话
     */
    @ExcelProperty(value = "客户联系电话", index = 22)
    @ApiModelProperty("客户联系电话")
    private String address1Telephone1;

    /**
     * 法定代表人
     */
    @ExcelProperty(value = "法定代表人", index = 23)
    @ApiModelProperty("法定代表人")
    private String legalPerson;

    /**
     * 营业执照编码
     */
    @ExcelProperty(value = "营业执照编码", index = 24)
    @ApiModelProperty("营业执照编码")
    private String businessLicenseCode;

    @ExcelProperty(value = "所属辖区", index = 25)
    @ApiModelProperty("所属辖区")
    private String belongRegion;
}
