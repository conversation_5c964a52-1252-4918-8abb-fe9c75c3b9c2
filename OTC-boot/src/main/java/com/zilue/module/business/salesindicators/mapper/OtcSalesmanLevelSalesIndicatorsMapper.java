package com.zilue.module.business.salesindicators.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zilue.module.business.salesindicators.entity.OtcSalesmanLevelSalesIndicators;
import com.zilue.module.business.salesindicators.entity.OtcSalesmanSalesIndicators;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* @title: mapper
* <AUTHOR>
* @Date: 2025-01-15
* @Version 1.0
*/
@Mapper
@DS("slave")
public interface OtcSalesmanLevelSalesIndicatorsMapper extends BaseMapper<OtcSalesmanLevelSalesIndicators> {
    List<OtcSalesmanLevelSalesIndicators> queryList(@Param("syncTime") String syncTime, @Param("skip") int skip, @Param("offset") int offset);
    Integer queryCount(@Param("syncTime") String syncTime);
}
