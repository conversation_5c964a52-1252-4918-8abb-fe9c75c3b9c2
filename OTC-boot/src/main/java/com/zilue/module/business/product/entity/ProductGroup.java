package com.zilue.module.business.product.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@TableName("otc_productgroup")
@ApiModel(value = "品种信息", description = "品种信息")
public class ProductGroup implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("")
    @TableId
    private Long id;
    @ApiModelProperty("产品品种id")
    private String newProductgroupId;
    @ApiModelProperty("品种名称")
    private String newName;
    @ApiModelProperty("标准品规id")
    private String newProductid;
    @ApiModelProperty("标准品规")
    private String newProductidName;
    @ApiModelProperty("产品线id")
    private String newKyBusinessgroupid;
    @ApiModelProperty("产品线")
    private String newKyBusinessgroupidName;
    @ApiModelProperty("状态 0 在用 1 停用")
    private String stateCode;
    @ApiModelProperty("更新字段")
    private String rowId;
    /**
     * 创建人ID
     */
    @ApiModelProperty("创建人ID")
    @TableField(fill = FieldFill.INSERT)
    private Long createUserId;
    /**
     * 创建日期
     */
    @ApiModelProperty("创建日期")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createDate;
    /**
     * 修改人ID
     */
    @ApiModelProperty("修改人ID")
    @TableField(fill = FieldFill.UPDATE)
    private Long modifyUserId;
    /**
     * 修改日期
     */
    @ApiModelProperty("修改日期")
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime modifyDate;
    /**
     * 删除标记
     */
    @ApiModelProperty("删除标记")
    @TableField(fill = FieldFill.INSERT)
    @TableLogic
    private Integer deleteMark;
    /**
     * 有效标记
     */
    @ApiModelProperty("有效标记")
    @TableField(fill = FieldFill.INSERT)
    private Integer enabledMark;
}
