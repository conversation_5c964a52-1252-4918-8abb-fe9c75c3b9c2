package com.zilue.module.business.inventory.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zilue.module.business.inventory.dto.InventoryDayDto;
import com.zilue.module.business.inventory.entity.OtcInventoryDayPart;
import com.zilue.module.business.inventory.entity.OtcInventoryDayProduct;
import com.zilue.module.business.inventory.vo.OtcInventoryDayPartVo;
import org.apache.ibatis.annotations.Mapper;


public interface IOtcInventoryDayPartService extends IService<OtcInventoryDayPart> {

    OtcInventoryDayPartVo getStaticCount(InventoryDayDto dto);

    void syncPartInventory(String syncTime);
}
