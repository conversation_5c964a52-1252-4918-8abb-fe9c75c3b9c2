package com.zilue.module.business.account.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @title: 客户管理
 * <AUTHOR>
 * @Date: 2024-12-30
 * @Version 1.0
 */
@Data
@TableName("otc_account_user_relation")
@ApiModel(value = "终端客户与用户关对象", description = "终端客户与用户关联表")
public class OtcAccountUserRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    @TableId
    private Long id;

    /**
     * 终端客户id
     */
    @ApiModelProperty("终端客户id")
    private Long terminalId;

    /**
     * EDP的终端客户id
     */
    @ApiModelProperty("EDP的终端客户id")
    private String accountId;

    /**
     * 用户id
     */
    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("EDP用户ID")
    private String systemUserId;

    /**
     * 负责人
     */
    @ApiModelProperty("负责人")
    private String ownerName;

    /**
     * 负责人工号
     */
    @ApiModelProperty("负责人工号")
    private String ownerNumber;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private Long createUserId;
    /**
     * 创建日期
     */
    @ApiModelProperty("创建日期")
    private LocalDateTime createDate;
    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private Long modifyUserId;
    /**
     * 修改日期
     */
    @ApiModelProperty("修改日期")
    private LocalDateTime modifyDate;
    /**
     * 删除标记
     */
    @ApiModelProperty("删除标记")
    //@TableLogic
    private Integer deleteMark;
    /**
     * 有效标记
     */
    @ApiModelProperty("有效标记")
    private Integer enabledMark;
}
