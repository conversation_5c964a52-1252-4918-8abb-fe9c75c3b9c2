package com.zilue.module.business.customer.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.enums.DeleteMark;
import com.zilue.common.enums.EnabledMark;
import com.zilue.common.model.result.R;
import com.zilue.common.page.PageOutput;
import com.zilue.module.business.account.service.IAccountService;
import com.zilue.module.business.customer.entity.OtcCustomerTag;
import com.zilue.module.business.customer.service.ICustomerTagRelService;
import com.zilue.module.business.customer.service.ICustomerTagService;
import com.zilue.module.business.customer.vo.CustomerTagVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @title: 客户标签
 * <AUTHOR>
 * @Date: 2024-01-09
 * @Version 1.0
 */
@RestController
@RequestMapping(GlobalConstant.BUSINESS_MODULE_PREFIX+ "/customer")
@Api(value = GlobalConstant.BUSINESS_MODULE_PREFIX + "/customer", tags = "客户标签")
@AllArgsConstructor
public class CustomerTagController {

    private final ICustomerTagService customerTagService;
    private final ICustomerTagRelService customerTagRelService;
    private final IAccountService accountService;


    @GetMapping("/tagList")
    @ApiOperation(value = "标签列表")
    public R<List<OtcCustomerTag>> tagList() {
        List<OtcCustomerTag> tagList = customerTagService.list(Wrappers.lambdaQuery(OtcCustomerTag.class)
                .eq(OtcCustomerTag::getEnabledMark, EnabledMark.ENABLED.getCode())
                .eq(OtcCustomerTag::getDeleteMark, DeleteMark.NODELETE.getCode()));
        return R.ok("查询成功",tagList);
    }

    @GetMapping("/page")
    @ApiOperation(value = "标签分页列表")
    public R<PageOutput<CustomerTagVo>> page(@Valid CustomerTagVo dto) {
        PageOutput<CustomerTagVo> pageOutput =customerTagService.getPage(dto);
        return R.ok(pageOutput);
    }

    @PostMapping("/submit")
    @ApiOperation(value = "新增或编辑")
    public R submit(@Valid @RequestBody OtcCustomerTag dto){
        return R.ok(customerTagService.addOrUpdate(dto));
    }

    @DeleteMapping
    @ApiOperation(value = "删除数据")
    public R delete(@RequestBody List<Long> ids){
        return R.ok(customerTagService.removeBatchByIds(ids));
    }




}
