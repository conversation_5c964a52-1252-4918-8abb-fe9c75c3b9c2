package com.zilue.module.business.taskManagement.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * +* @title: 目标管理
 * +* <AUTHOR>
 * +* @Date: 2024-12-31
 * +* @Version 1.0
 * +
 */
@Data
public class AddOtcSalesmanTaskDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**+    * 任务类型任务类型0拜访任务1协访任务2拓客任务3业绩任务4客户数发展目标+    */
    @ApiModelProperty("任务类型任务类型 门店拜访1连锁分部拜访2连锁总部拜访3门店协防4连锁分部协防5销售业绩6 看字典")
    @NotBlank(message = "目标类型不能为空")
    private String taskType;
    /**+    * 开始年月+    */
    @ApiModelProperty("开始年月")
    @NotBlank(message = "开始年月不能为空")
    private String startYearMonth;
    /**+    * 结束年月+    */
    @ApiModelProperty("结束年月")
    @NotBlank(message = "结束年月不能为空")
    private String endYearMonth;
    /**+    * 任务状态-1创建中0待开始1进行中2已过期+    */
    @ApiModelProperty("任务状态-创建中0待开始1进行中2已过期3 看字典")
    private String taskStatus;
    /**+    * 核算频率，0代表日，1代表月+    */
    @ApiModelProperty("核算频率，0代表日，1代表月 看字典")
    @NotBlank(message = "核算频率不能为空")
    private String frequency;
    @ApiModelProperty("执行人岗位")
    @NotNull(message = "执行人岗位不能为空")
    private String postId;
    /**+     * 核算频率，0代表日，1代表月+     */
    @ApiModelProperty("是否启用停用  看字典")
    @NotNull(message = "是否启用停用不能为空")
    private String isEnabled;
    @ApiModelProperty("目标值")
    private Integer goal;

}
