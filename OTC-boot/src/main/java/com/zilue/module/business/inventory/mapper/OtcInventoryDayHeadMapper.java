package com.zilue.module.business.inventory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zilue.module.business.inventory.dto.InventoryDayDto;
import com.zilue.module.business.inventory.entity.OtcInventoryDayHead;
import com.zilue.module.business.inventory.entity.OtcInventoryDayPart;
import com.zilue.module.business.inventory.vo.OtcInventoryDayPartVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface OtcInventoryDayHeadMapper extends BaseMapper<OtcInventoryDayHead> {
    OtcInventoryDayPartVo queryHeadCount(@Param("dto") InventoryDayDto dto);
}
