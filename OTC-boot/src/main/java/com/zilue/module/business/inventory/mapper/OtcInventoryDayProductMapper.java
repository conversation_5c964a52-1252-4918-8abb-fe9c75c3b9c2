package com.zilue.module.business.inventory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zilue.module.business.inventory.dto.InventoryDayDto;
import com.zilue.module.business.inventory.entity.OtcInventoryDayProduct;
import com.zilue.module.business.inventory.vo.OtcInventoryDayPartVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface OtcInventoryDayProductMapper extends BaseMapper<OtcInventoryDayProduct> {
    OtcInventoryDayPartVo queryProductCount(@Param("dto") InventoryDayDto dto);

}

