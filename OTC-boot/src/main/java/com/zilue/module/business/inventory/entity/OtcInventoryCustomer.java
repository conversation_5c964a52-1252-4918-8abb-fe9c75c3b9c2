package com.zilue.module.business.inventory.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@TableName("otc_inventory_customer")
@ApiModel(value = "进销存客户目录表", description = "进销存客户目录表")
public class OtcInventoryCustomer implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty("")
    @TableId
    private Long id;

    @ApiModelProperty("'客户主键'")
    private String accountId;

    @ApiModelProperty("'客户名称'")
    private String customerName;

    @ApiModelProperty("'上级客户主键'")
    private String parentAccountId;

    @ApiModelProperty("'上级客户id'")
    private String parentCustomerId;

    @ApiModelProperty("'上级客户名称'")
    private String parentCustomerName;

    @ApiModelProperty("客户类别：0：总部；1:分部")
    private Integer terminalType;

    @ApiModelProperty("是否10大连锁：0：否；1：是")
    private Integer isTen;

    @ApiModelProperty("'省公司id'")
    private String provincialCompanyId;
    
    @ApiModelProperty("'省公司机构id'")
    private String provincialCompanyUnitId;

    @ApiModelProperty("'省公司名称'")
    private String provincialCompanyName;

    @ApiModelProperty("'主管区id'")
    private String areaCompanyId;


    @ApiModelProperty("'主管区机构id'")
    private String areaCompanyUnitId;

    @ApiModelProperty("'主管区名称'")
    private String areaCompanyName;

    @ApiModelProperty("'分部客户所在省份'")
    private String customerProvincialName;
    /**
     * 创建人ID
     */
    @ApiModelProperty("创建人ID")
    @TableField(fill = FieldFill.INSERT)
    private Long createUserId;
    /**
     * 创建日期
     */
    @ApiModelProperty("创建日期")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createDate;
    /**
     * 修改人ID
     */
    @ApiModelProperty("修改人ID")
    @TableField(fill = FieldFill.UPDATE)
    private Long modifyUserId;
    /**
     * 修改日期
     */
    @ApiModelProperty("修改日期")
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime modifyDate;
    /**
     * 删除标记
     */
    @ApiModelProperty("删除标记")
    @TableField(fill = FieldFill.INSERT)
    private Integer deleteMark;
    /**
     * 有效标记
     */
    @ApiModelProperty("有效标记")
    @TableField(fill = FieldFill.INSERT)
    private Integer enabledMark;


}
