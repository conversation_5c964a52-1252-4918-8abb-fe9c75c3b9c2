package com.zilue.module.business.punchset.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zilue.module.business.punchset.dto.OtcVisitMattersDto;
import com.zilue.module.business.punchset.entity.OtcPunchSet;
import com.zilue.module.business.punchset.entity.OtcVisitMatters;

import java.util.List;

public interface IVisitMattersService extends IService<OtcVisitMatters> {
    List<OtcVisitMatters> visitMattersList();

    void addVisitMatters(OtcVisitMattersDto dto);

    void updateVisitMatters(List<OtcVisitMattersDto> dtos) ;
}
