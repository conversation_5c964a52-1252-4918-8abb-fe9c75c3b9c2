package com.zilue.module.business.reportForms.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
public class SaleMoneyReportFormVo {
    @ApiModelProperty("机构id")
    private String departmentId;

    @ApiModelProperty("机构名称")
    private String departmentName;

    @ApiModelProperty("目标")
    private BigDecimal targetSaleMoney;

    @ApiModelProperty("实际达成")
    private BigDecimal realSaleMoney;

    @ApiModelProperty("达标率")
    private BigDecimal reachedRate;

    @ApiModelProperty("是否最底层机构")
    private Boolean isLowestUnit;

    @ApiModelProperty("前6个月目标")
    private Map<String, BigDecimal> targetSaleMoneyMap;

    @ApiModelProperty("前6个月实际达成")
    private Map<String, BigDecimal> realSaleMoneyMap;

    @ApiModelProperty("机构业绩达成列表")
    private List<DepartmentSaleMoneyReportFormVo> departmentSaleMoneys;
}
