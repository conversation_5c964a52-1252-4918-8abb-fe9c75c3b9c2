package com.zilue.module.business.salesreport.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.math.BigDecimal;


/**
* @title: ka经理管理的总部汇总按月
* <AUTHOR>
* @Date: 2025-01-20
* @Version 1.0
*/
@Data
@TableName("otc_sales_report_ka_head_store_month")
@ApiModel(value = "ka经理管理的总部汇总按月对象", description = "ka经理管理的总部汇总按月")
@EqualsAndHashCode(callSuper = true)
public class OtcSalesReportKaHeadStoreMonth extends OtcSalesPerformanceRank {

    private static final long serialVersionUID = 1L;

    /**
    * 
    */
    @ApiModelProperty("")
    @TableId
    private Long id;
    /**
    * 用户编码
    */
    @ApiModelProperty("用户编码")
    private String userCode;
    /**
    * 年计划值
    */
    @ApiModelProperty("年计划值")
    private BigDecimal planAmount;

    /**
    * 业务月份
    */
    @ApiModelProperty("业务月份")
    private String businessMonth;

    /**
    * 创建人ID
    */
    @ApiModelProperty("创建人ID")
    @TableField(fill = FieldFill.INSERT)
    private Long createUserId;
    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createDate;
    /**
    * 修改人ID
    */
    @ApiModelProperty("修改人ID")
    @TableField(fill = FieldFill.UPDATE)
    private Long modifyUserId;
    /**
    * 修改日期
    */
    @ApiModelProperty("修改日期")
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime modifyDate;
    /**
    * 删除标记
    */
    @ApiModelProperty("删除标记")
    @TableField(fill = FieldFill.INSERT)
    @TableLogic
    private Integer deleteMark;
    /**
    * 有效标记
    */
    @ApiModelProperty("有效标记")
    @TableField(fill = FieldFill.INSERT)
    private Integer enabledMark;


}