package com.zilue.module.business.inventory.service;

import com.zilue.module.business.inventory.dto.InventoryExcelDto;
import com.zilue.module.business.inventory.vo.InventoryCustomerImportVo;
import com.zilue.module.business.inventory.vo.OtcInventoryCustomerVo;

import java.util.List;

public interface IOtcInventoryCustomerService {
    void importCustomer(List<InventoryCustomerImportVo> dictionaryItemExportVos);

    List<OtcInventoryCustomerVo> queryCustomerOwnerList();

    List<OtcInventoryCustomerVo> queryCustomerInfoList(InventoryExcelDto dto);
}
