package com.zilue.module.business.sign.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @title: 分页列表出参
 * <AUTHOR>
 * @Date: 2025-01-13
 * @Version 1.0
 */
@Data
public class OtcSignInAdminPageVo {

    /**
     * erp编码
     */
    @ApiModelProperty("edp编码")
    private String personCode;

    private String serialNumber;

    /**
     * 打卡超范围备注
     */
    private String superableScopeRemarks;

    /**
     * 签退超范围的备注
     */
    private String descrOutOfScope;
    /**
     * 打卡人姓名
     */
    @ApiModelProperty("打卡人姓名")
    private String personName;

    /**
     * 协防人姓名
     */
    @ApiModelProperty("协防人姓名")
    private String coachedPersonName;

    /**
     * 协防人工号
     */
    @ApiModelProperty("协防人工号")
    private String coachedPersonCode;

    /**
     * 打卡类别，1门店拜访，2连锁分部拜访,3连锁总部拜访
     */
    @ApiModelProperty("打卡类别，1门店拜访，2连锁分部拜访,3连锁总部拜访")
    private String category;

    /**
     * 客户姓名
     */
    @ApiModelProperty("客户姓名")
    private String purMerchantName;
    /**
     * 目的地(客户地址)
     */
    @ApiModelProperty("目的地(客户地址)")
    private String destination;


    /**
     * SignExceptionStatusEnum：
     #是否为异常记录（异常：2,正常：1,默认：1）
     */
    @ApiModelProperty("SignExceptionStatusEnum：#是否为签到异常记录（异常：2,正常：1,默认：1）")
    private Integer signExceptionStatus;
    /**
     * 异常原因(0=正常 1=超范围打卡 2=未打卡 3=迟到打卡 4=早退)
     */
    @ApiModelProperty("签到异常原因(0=正常 1=超范围打卡 2=未打卡 3=迟到打卡 4=早退)")
    private Integer signExceptionReason;

    /**
     * SignExceptionStatusEnum：
     #是否为异常记录（异常：2,正常：1,默认：1）
     */
    @ApiModelProperty("SignExceptionStatusEnum：#是否为签退异常记录（异常：2,正常：1,默认：1）")
    private Integer signOutExceptionStatus;
    /**
     * 异常原因(0=正常 1=超范围打卡 2=未打卡 3=迟到打卡 4=早退)
     */
    @ApiModelProperty("签退异常原因(0=正常 1=超范围打卡 2=未打卡 3=迟到打卡 4=早退)")
    private Integer signOutExceptionReason;

    /**
     * 签到日期
     */
    @ApiModelProperty("签到日期")
    private LocalDateTime signDate;

    /**
     * 签退日期
     */
    @ApiModelProperty("签退日期")
    private LocalDateTime signOutDate;

    @ApiModelProperty("在店时长单位分")
    private long betWeenMinus;

    /**
     * 打卡偏差
     */
    @ApiModelProperty("签到打卡偏差")
    private Integer signOffset;

    /**
     * 打卡偏差
     */
    @ApiModelProperty("签退打卡偏差")
    private Integer signOutOffset;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remarks;

    /**
     *  照片路径(英文逗号分隔,)
     */
    @ApiModelProperty(" 照片路径(英文逗号分隔,)")
    private String imageIds;

    /**
     * 访前计划
     */
    @ApiModelProperty("访前计划")
    private double preVisitPlan;
    /**
     * 工作态度
     */
    @ApiModelProperty("工作态度")
    private double workAttitude;
    /**
     * 客情管理
     */
    @ApiModelProperty("客情管理")
    private double cusRelationshipManagement;
    /**
     * 陈述利益
     */
    @ApiModelProperty("陈述利益")
    private double statementInterests;

    /**
     * 综合评价
     */
    @ApiModelProperty("综合评价")
    private double evaluate;

    /**
     * 客户满意度
     */
    @ApiModelProperty("建议")
    private String suggestion;

    /**
     *  照片路径
     */
    @ApiModelProperty(" 照片路径")
    private List<String> imageUrls;

}
