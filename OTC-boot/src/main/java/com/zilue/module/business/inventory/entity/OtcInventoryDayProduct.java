package com.zilue.module.business.inventory.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zilue.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;



@Data
@TableName("otc_inventory_day_product")
@ApiModel(value = "进销存产品日库存表", description = "进销存产品日库存表")
public class OtcInventoryDayProduct extends AuditEntity implements Serializable {
    @ApiModelProperty("")
    @TableId
    private Long id;
    @ApiModelProperty("品种id")
    private String productGroupId;
    @ApiModelProperty("品种编码")
    private String productGroupCode;
    @ApiModelProperty("品种")
    private String productGroup;
    @ApiModelProperty("品规id")
    private String productId;
    @ApiModelProperty("品规编码")
    private String productCode;
    @ApiModelProperty("品规")
    private String product;
    @ApiModelProperty("考核价")
    private BigDecimal examPrice;
    @ApiModelProperty("库存数量")
    private Integer warehouseNum;
    @ApiModelProperty("库存总金额")
    private BigDecimal warehouseMoney;
    @ApiModelProperty("业务时间")
    private String BusinessTime;

    @ApiModelProperty("期初库存数量总计")
    private Integer beginWarehouseNumCount;
    @ApiModelProperty("本期购进")
    private Integer periodComeCount;
    @ApiModelProperty("流到商业")
    private Integer outCommerceCount;
    @ApiModelProperty("流到终端")
    private Integer outTerminalCount;
    @ApiModelProperty("计价单位")
    private String unit;
}
