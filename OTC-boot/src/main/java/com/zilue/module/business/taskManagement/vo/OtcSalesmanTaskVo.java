package com.zilue.module.business.taskManagement.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalTime;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.util.List;

/**
 * +* @title: 表单出参
 * +* <AUTHOR>
 * +* @Date: 2024-12-31
 * +* @Version 1.0
 * +
 */
@Data
public class OtcSalesmanTaskVo {
    @ApiModelProperty("")
    private Long id;
    @ApiModelProperty("任务类型 门店拜访1 连锁分部拜访2 连锁总部拜访3 门店协防4 连锁分部协防5 销售业绩6 贴柜培训7 铁杆积累8 进销存采集9 竞品采集10 陈列检查11 看字典")
    private String taskType;
    @ApiModelProperty("开始年月")
    private String startYearMonth;

    @ApiModelProperty("结束年月")
    private String endYearMonth;

    @ApiModelProperty("核算频率，1代表日，2代表月 看字典")
    private String frequency;
    @ApiModelProperty("代表目标，家次")
    private Integer goal;
    @ApiModelProperty("0 停用，1启用 看字典")
    private String isEnabled;
    @ApiModelProperty("执行人岗位")
    private String postId;

}
