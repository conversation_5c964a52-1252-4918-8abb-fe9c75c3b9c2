package com.zilue.module.business.account.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.query.MPJQueryWrapper;
import com.github.yulichang.toolkit.MPJWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.tencentcloudapi.cwp.v20180228.models.ScreenTrendsChart;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.enums.*;
import com.zilue.common.model.result.R;
import com.zilue.common.page.ConventPage;
import com.zilue.common.page.PageOutput;
import com.zilue.common.utils.*;
import com.zilue.module.business.account.dto.*;
import com.zilue.module.business.account.entity.OtcAccount;
import com.zilue.module.business.account.entity.OtcAccountUserRelation;
import com.zilue.module.business.account.mapper.AccountSqlServerMapper;
import com.zilue.module.business.account.mapper.OtcAccountMapper;
import com.zilue.module.business.account.service.IAccountService;
import com.zilue.module.business.account.service.IAccountUserRelationService;
import com.zilue.module.business.account.vo.OtcAccountExportVo;
import com.zilue.module.business.account.vo.OtcAccountPageVo;
import com.zilue.module.business.account.vo.OtcHeadAccountVo;
import com.zilue.module.business.customer.entity.OtcCustomerTag;
import com.zilue.module.business.customer.entity.OtcCustomerTagRel;
import com.zilue.module.business.customer.service.ICustomerTagRelService;
import com.zilue.module.business.customer.service.ICustomerTagService;
import com.zilue.module.business.customer.vo.CustomerTagVo;
import com.zilue.module.business.punchset.vo.DistanceVo;
import com.zilue.module.organization.dto.UserPageDto;
import com.zilue.module.organization.dto.UserRoleInfoDto;
import com.zilue.module.organization.entity.User;
import com.zilue.module.organization.mapper.UserPostRelationMapper;
import com.zilue.module.organization.service.IUserService;
import com.zilue.module.organization.vo.UserPageVo;
import com.zilue.module.wechat.sign.entity.OtcSignIn;
import com.zilue.module.wechat.sign.service.ISignService;
import com.zilue.module.workflow.entity.WorkflowSchema;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * @title: service
 * <AUTHOR>
 * @Date: 2024-12-27
 * @Version 1.0
 */
@Service
@AllArgsConstructor
public class AccountServiceImpl extends MPJBaseServiceImpl<OtcAccountMapper, OtcAccount> implements IAccountService {

    private static final String NEW_SHIC_JB = "*********";

    private static final int BATCH_SIZE = 1000; // 每批处理的数据量

    private final AccountSqlServerMapper accountSqlServerMapper;

    private final UserPostRelationMapper userPostRelationMapper;
    private final IAccountUserRelationService accountUserRelationService;
    private final ICustomerTagRelService customerTagRelService;
    private final ICustomerTagService customerTagService;
    private final IUserService userService;
    private static Long CUSTOMERTAGID = 1879073797771878401L;
    private final ISignService signService;

    @Override
    public PageOutput<OtcAccountPageVo> getPageList(OtcAccountPageDto pageDto) {
        MPJLambdaWrapper<OtcAccount> finalWrapper = null;

        MPJLambdaWrapper<OtcAccount> queryWrapper = initializeQueryWrapper(pageDto);
        finalWrapper = queryWrapper;

        //基层业务代表带上全部分部
        List<UserRoleInfoDto> userRoleInfoDtoList = userPostRelationMapper.getUserPostInfo(Arrays.asList(pageDto.getOwnerId()));
        if (CollectionUtils.isNotEmpty(userRoleInfoDtoList) && ObjectUtil.isEmpty(pageDto.getTerminalType())) {
            UserRoleInfoDto userRoleInfoDto = userRoleInfoDtoList.get(0);
            if (userRoleInfoDto.getCode().equals("10005")) {
 /*               MPJLambdaWrapper<OtcAccount> queryWrapper2 = new MPJLambdaWrapper<>(OtcAccount.class);
                queryWrapper2.selectAll(OtcAccount.class)
                        .like(StrUtil.isNotBlank(pageDto.getAccountName()), OtcAccount::getName, pageDto.getAccountName())
                        .eq(OtcAccount::getTerminalType, 1);


                MPJLambdaWrapper<OtcAccount> unionWrapper = queryWrapper.union(queryWrapper2);

                // 使用 MPJQueryWrapper 替代 lambdaJoin()
                MPJLambdaWrapper<OtcAccount> wrapper = MPJWrappers.lambdaJoin();
                wrapper.custom(" (" + unionWrapper.getTargetSql() + ") ");
                wrapper.orderByDesc(OtcAccount::getTerminalType); // 在外层排序*/
                finalWrapper.or().eq(OtcAccount::getTerminalType, TerminalType.PART.getCode()).like(ObjectUtil.isNotEmpty(pageDto.getAccountName()), OtcAccount::getName, pageDto.getAccountName()).distinct().orderByDesc(OtcAccount::getTerminalType);
            }
        }


        IPage<OtcAccount> page = this.selectJoinListPage(ConventPage.getPage(pageDto), OtcAccount.class, finalWrapper);
        PageOutput<OtcAccountPageVo> pageOutput = ConventPage.getPageOutput(page, OtcAccountPageVo.class);

        // 根据标签查询已关联关联门店
        if (Objects.nonNull(pageDto.getCustomerTagId()) && Objects.nonNull(pageDto.getLabelType()) && pageDto.getLabelType() == 1) {
            List<Object> customerTagId = findCustomerTagId(pageDto.getCustomerTagId());
            // 确保 customerTagId 列表不为空
            if (customerTagId.isEmpty()) {
                pageOutput.setList(null);
            }
        }

        List<OtcAccountPageVo> pageOutputList = pageOutput.getList();
        if (CollectionUtils.isNotEmpty(pageOutputList)) {
            List<Long> terminalIds = pageOutputList.stream().map(OtcAccountPageVo::getId).collect(Collectors.toList());
            List<OtcAccountUserRelation> userRelationList = accountUserRelationService.list(Wrappers.lambdaQuery(OtcAccountUserRelation.class).in(OtcAccountUserRelation::getTerminalId, terminalIds));
            if (CollectionUtils.isNotEmpty(userRelationList)) {
                List<Long> userIds = userRelationList.stream().map(OtcAccountUserRelation::getUserId).distinct().collect(Collectors.toList());
                Map<Long, Long> userRelationMap = new ConcurrentHashMap<>(userRelationList.size());
                userRelationMap = userRelationList.stream().collect(Collectors.toMap(OtcAccountUserRelation::getTerminalId, // 键
                        OtcAccountUserRelation::getUserId, // 值
                        (existingValue, newValue) -> existingValue // 合并函数，取第一个值
                ));
                Map<Long, Long> finalUserRelationMapMap = userRelationMap;
                //CUSTOMERTAGID
                Set<Long> terminalIdList = pageOutputList.stream().map(OtcAccountPageVo::getId).collect(Collectors.toSet());
                List<CustomerTagVo> OtcCustomerTagList = customerTagService.selectJoinList(CustomerTagVo.class, MPJWrappers.<OtcCustomerTag>lambdaJoin().distinct().in(ObjectUtil.isNotNull(terminalIdList), OtcCustomerTagRel::getAccountId, terminalIdList).select(OtcCustomerTag::getId, OtcCustomerTag::getTagName, OtcCustomerTag::getTagColor, OtcCustomerTag::getShowStatus, OtcCustomerTag::getSort).select(OtcCustomerTagRel::getAccountId).select(OtcCustomerTag.class, x -> VoToColumnUtil.fieldsToColumns(CustomerTagVo.class).contains(x.getProperty())).leftJoin(OtcCustomerTagRel.class, OtcCustomerTagRel::getCustomerTagId, OtcCustomerTag::getId).selectAs(OtcCustomerTagRel::getAccountId, "accountId"));
                Map<Long, List<CustomerTagVo>> mapOtcCustomerTag = OtcCustomerTagList.stream().collect(Collectors.groupingBy(CustomerTagVo::getAccountId));
                LambdaQueryWrapper<OtcSignIn> queryWrapperOtcSignIn = new LambdaQueryWrapper<>();
                queryWrapperOtcSignIn.eq(OtcSignIn::getEnabledMark, YesOrNoEnum.YES.getCode()).eq(OtcSignIn::getPersonId, pageDto.getOwnerId()).eq(OtcSignIn::getSiginType, GlobalConstant.SIGINTYPE).in(OtcSignIn::getPurMerchantId, terminalIdList).apply("DATE_FORMAT(create_date, '%Y-%m') = {0}", DateUtil.format(DateUtil.parse(pageDto.getPlanDay()), DateUtils.MONTH_PATTERN)).orderByDesc(OtcSignIn::getCreateDate);
                List<OtcSignIn> otcSignInList = signService.list(queryWrapperOtcSignIn);
                Map<Long, List<OtcSignIn>> mapOtcSignIn = otcSignInList.stream().collect(Collectors.groupingBy(OtcSignIn::getPurMerchantId));

                //部门处理
                List<User> userList = userService.selectJoinList(User.class, MPJWrappers.<User>lambdaJoin().in(ObjectUtil.isNotNull(userIds), User::getId, userIds).select(User::getId, User::getName, User::getCode, User::getBusinessUnitIdName));
                Map<Long, List<User>> userMap = new ConcurrentHashMap<>(userList.size());
                if (CollectionUtils.isNotEmpty(userList)) {
                    userMap = userList.stream().collect(Collectors.groupingBy(User::getId));
                }
                Map<Long, List<User>> finalUserMap = userMap;
                Map<Long, List<OtcSignIn>> finalMapOtcSignIn = mapOtcSignIn;
                pageOutputList.forEach(oo -> {
                    if (finalUserRelationMapMap.containsKey(oo.getId())) {
                        Long userId = finalUserRelationMapMap.get(oo.getId());
                        // 使用Stream API和Collectors.joining()方法连接字符串
                        if (!finalUserMap.isEmpty() && finalUserMap.containsKey(userId)) {
                            User user = finalUserMap.get(userId).get(0);
                            oo.setOwnerName(user.getName());
                            oo.setOwnerNumber(user.getCode());
                            String businessUnitIdName = user.getBusinessUnitIdName();
                            if (StringUtils.isNotBlank(businessUnitIdName)) {
                                String[] parts = businessUnitIdName.split(" ");
                                String targetElement = "OTC事业部"; // 指定的元素
                                List<String> partList = Arrays.asList(parts);
                                int index = partList.indexOf(targetElement);
                                String belongRegion = "";
                                if (index != -1) { // 如果找到目标元素
                                    belongRegion = String.join("/", partList.subList(index, partList.size()));
                                } else {
                                    belongRegion = String.join("/", partList);
                                }
                                oo.setBelongRegion(belongRegion);
                            }
                        }
                        if (ObjectUtil.isNull(oo.getAddress1Latitude()) || ObjectUtil.isNull(oo.getAddress1Longitude()) || ObjectUtil.isNull(pageDto.getLongitude()) || ObjectUtil.isNull(pageDto.getLatitude())) {
                            oo.setDistance(0);
                        } else {
                            DistanceVo gpsSource = new DistanceVo(pageDto.getLongitude(), pageDto.getLatitude());
                            DistanceVo gpsTarget = new DistanceVo(new BigDecimal(oo.getAddress1Longitude()), new BigDecimal(oo.getAddress1Latitude()));
                            oo.setDistance(DistanceUtils.getDistance(gpsSource, gpsTarget).intValue() / 1000);

                            Map<String, BigDecimal> originMap = SearchBaiDuMapApiUtil.baiduToGaoDe(new BigDecimal(oo.getAddress1Longitude()), new BigDecimal(oo.getAddress1Latitude()));
                            //System.out.println(originMap.get("lat").toString()+";"+originMap.get("lon").toString());
                            oo.setAddress2Longitude(originMap.get("lon").toString());
                            oo.setAddress2Latitude(originMap.get("lat").toString());
                        }
                        if (mapOtcCustomerTag.containsKey(oo.getId())) {
                            oo.setCustomerTagList(mapOtcCustomerTag.get(oo.getId()));
                            String customerTagName = mapOtcCustomerTag.get(oo.getId()).stream().map(CustomerTagVo::getTagName) // 获取每个元素的ownerName属性
                                    .collect(Collectors.joining(",")); // 以逗号分隔连接字符串
                            oo.setCustomerTagName(customerTagName);
                        }
                    }
                    if (finalMapOtcSignIn.containsKey(oo.getId())) {
                        oo.setReal(finalMapOtcSignIn.get(oo.getId()).size());
                    }
                    if (mapOtcCustomerTag.containsKey(oo.getId())) {
                        oo.setCustomerTagList(mapOtcCustomerTag.get(oo.getId()));
                    }
                });
            }
        }
        pageOutput.setList(pageOutputList);
        return pageOutput;
    }

    @Override
    public List<OtcAccountExportVo> getRecordList(OtcAccountExportDto exportDto) {
        List<OtcAccount> accounts = this.list(Wrappers.lambdaQuery(OtcAccount.class).in(OtcAccount::getId, exportDto.getIds()).orderByDesc(OtcAccount::getId));
        List<OtcAccountExportVo> outputList = new ArrayList<>(accounts.size());
        if (CollectionUtils.isNotEmpty(accounts)) {
            outputList = BeanUtil.copyToList(accounts, OtcAccountExportVo.class);
            List<Long> terminalIds = outputList.stream().map(OtcAccountExportVo::getId).collect(Collectors.toList());
            List<OtcAccountUserRelation> userRelationList = accountUserRelationService.list(Wrappers.lambdaQuery(OtcAccountUserRelation.class).in(OtcAccountUserRelation::getTerminalId, terminalIds));
            Map<Long, Long> userRelationMap = new ConcurrentHashMap<>(userRelationList.size());
            Map<Long, List<User>> userMap = new ConcurrentHashMap<>(userRelationList.size());
            Map<Long, List<CustomerTagVo>> mapOtcCustomerTag = new HashMap<>();
            if (CollectionUtils.isNotEmpty(userRelationList)) {
                List<Long> userIds = userRelationList.stream().map(OtcAccountUserRelation::getUserId).distinct().collect(Collectors.toList());
                userRelationMap = userRelationList.stream().collect(Collectors.toMap(OtcAccountUserRelation::getTerminalId, // 键
                        OtcAccountUserRelation::getUserId, // 值
                        (existingValue, newValue) -> existingValue // 合并函数，取第一个值
                ));

                //CUSTOMERTAGID
                Set<Long> terminalIdList = outputList.stream().map(OtcAccountExportVo::getId).collect(Collectors.toSet());
                List<CustomerTagVo> OtcCustomerTagList = customerTagService.selectJoinList(CustomerTagVo.class, MPJWrappers.<OtcCustomerTag>lambdaJoin().distinct().in(ObjectUtil.isNotNull(terminalIdList), OtcCustomerTagRel::getAccountId, terminalIdList).select(OtcCustomerTag::getId, OtcCustomerTag::getTagName, OtcCustomerTag::getTagColor, OtcCustomerTag::getShowStatus, OtcCustomerTag::getSort).select(OtcCustomerTagRel::getAccountId).select(OtcCustomerTag.class, x -> VoToColumnUtil.fieldsToColumns(CustomerTagVo.class).contains(x.getProperty())).leftJoin(OtcCustomerTagRel.class, OtcCustomerTagRel::getCustomerTagId, OtcCustomerTag::getId).selectAs(OtcCustomerTagRel::getAccountId, "accountId"));
                mapOtcCustomerTag = OtcCustomerTagList.stream().collect(Collectors.groupingBy(CustomerTagVo::getAccountId));

                //部门处理
                List<User> userList = userService.selectJoinList(User.class, MPJWrappers.<User>lambdaJoin().in(ObjectUtil.isNotNull(userIds), User::getId, userIds).select(User::getId, User::getName, User::getCode, User::getBusinessUnitIdName));

                if (CollectionUtils.isNotEmpty(userList)) {
                    userMap = userList.stream().collect(Collectors.groupingBy(User::getId));
                }
            }
            Map<Long, Long> finalUserRelationMapMap = userRelationMap;
            Map<Long, List<User>> finalUserMap = userMap;
            Map<Long, List<CustomerTagVo>> finalMapOtcCustomerTag = mapOtcCustomerTag;
            outputList.forEach(oo -> {
                oo.setClientId(oo.getId().toString());
                oo.setIsDirectJoinStr((Objects.nonNull(oo.getIsDirectJoin()) && Objects.equals(oo.getIsDirectJoin(), 1)) ? "加盟" : "直营");
                oo.setIsDtpStr((Objects.nonNull(oo.getIsDtp()) && Objects.equals(oo.getIsDtp(), 1)) ? "是" : "否");
                oo.setIsWholeStr((Objects.nonNull(oo.getIsWhole()) && Objects.equals(oo.getIsWhole(), 1)) ? "是" : "否");
                oo.setIsOnlineStr((Objects.nonNull(oo.getIsOnline()) && Objects.equals(oo.getIsOnline(), 1)) ? "是" : "否");
                if (finalUserRelationMapMap.containsKey(oo.getId())) {
                    Long userId = finalUserRelationMapMap.get(oo.getId());
                    // 使用Stream API和Collectors.joining()方法连接字符串
                    if (!finalUserMap.isEmpty() && finalUserMap.containsKey(userId)) {
                        User user = finalUserMap.get(userId).get(0);
                        oo.setOwnerName(user.getName());
                        oo.setOwnerNumber(user.getCode());
                        String businessUnitIdName = user.getBusinessUnitIdName();
                        if (StringUtils.isNotBlank(businessUnitIdName)) {
                            String[] parts = businessUnitIdName.split(" ");
                            String targetElement = "OTC事业部"; // 指定的元素
                            List<String> partList = Arrays.asList(parts);
                            int index = partList.indexOf(targetElement);
                            String belongRegion = "";
                            if (index != -1) { // 如果找到目标元素
                                belongRegion = String.join("/", partList.subList(index, partList.size()));
                            } else {
                                belongRegion = String.join("/", partList);
                            }
                            oo.setBelongRegion(belongRegion);
                        }

                    }
                    if (finalMapOtcCustomerTag.containsKey(oo.getId())) {
                        String customerTagName = finalMapOtcCustomerTag.get(oo.getId()).stream().map(CustomerTagVo::getTagName) // 获取每个元素的ownerName属性
                                .collect(Collectors.joining(",")); // 以逗号分隔连接字符串
                        oo.setCustomerTagName(customerTagName);
                    }
                }
            });
        }
        return outputList;
    }

    @Override
    public String getExportColumns(Integer terminalType) {
        String columns = StringUtils.EMPTY;
        switch (terminalType) {
            case 0: //总部
                columns = "id,name,ownerName,ownerNumber,address1Name,address1Telephone1,legalPerson,businessLicenseCode";
                break;
            case 1: //分部
                columns = "id,name,newOtcAccountName,ownerName,ownerNumber,address1Name,address1Telephone1,legalPerson,businessLicenseCode";
                break;
            case 2: //门店
                columns = "id,name,parentAccountIdName,newOtcAccountName,province,city,district,belongRegion,address1Name,ownerName,ownerNumber,address1Telephone1,isDirectJoinStr,saleScale,customerTagName,customerTypeCodeName,lisDtpStr," + "isWholeStr,isOnlineStr,egalPerson,businessLicenseCode";
                break;
            default:
        }
        return columns;
    }

    @Override
    public List<OtcHeadAccountVo> getHeadList() {
        List<OtcAccount> accountList = this.list(Wrappers.lambdaQuery(OtcAccount.class).select(OtcAccount::getId, OtcAccount::getAccountId, OtcAccount::getName).eq(OtcAccount::getTerminalType, TerminalType.HEAD.getCode()).eq(OtcAccount::getStateCode, StateCodeEnum.ENABLED.getCode()).eq(OtcAccount::getDeleteMark, DeleteMark.NODELETE.getCode()).eq(OtcAccount::getEnabledMark, EnabledMark.ENABLED.getCode()).orderByDesc(OtcAccount::getId));
        List<OtcHeadAccountVo> accountVoList = new ArrayList<>(accountList.size());
        if (CollectionUtils.isNotEmpty(accountList)) {
            accountList.forEach(oo -> {
                OtcHeadAccountVo accountVo = BeanUtil.toBean(oo, OtcHeadAccountVo.class);
                accountVoList.add(accountVo);
            });
        }
        return accountVoList;
    }

    @Override
    public Boolean updateAccountById(UpdateOtcAccountDto dto) {
        LambdaUpdateWrapper<OtcAccount> set = new LambdaUpdateWrapper<OtcAccount>().eq(OtcAccount::getId, dto.getId()).set(Objects.nonNull(dto.getIsDtp()), OtcAccount::getIsDtp, dto.getIsDtp()).set(Objects.nonNull(dto.getIsDirectJoin()), OtcAccount::getIsDirectJoin, dto.getIsDirectJoin()).set(Objects.nonNull(dto.getIsOnline()), OtcAccount::getIsOnline, dto.getIsOnline()).set(Objects.nonNull(dto.getIsWhole()), OtcAccount::getIsWhole, dto.getIsWhole()).set(StringUtils.isNotBlank(dto.getSaleScale()), OtcAccount::getSaleScale, dto.getSaleScale()).set(StringUtils.isNotBlank(dto.getAddress1Name()), OtcAccount::getAddress1Name, dto.getAddress1Name()).set(Objects.nonNull(dto.getAddress1Latitude()), OtcAccount::getAddress1Latitude, dto.getAddress1Latitude()).set(Objects.nonNull(dto.getAddress1Longitude()), OtcAccount::getAddress1Longitude, dto.getAddress1Longitude());
        return this.update(set);
    }

    @Override
    public Boolean relation(AddOtcAccountRelationDto relationDto) {
        List<Long> terminalIds = relationDto.getTerminalIds();
        List<OtcAccountUserRelation> userRelations = new ArrayList<>(terminalIds.size());
        if (CollectionUtils.isNotEmpty(terminalIds)) {
            User user = userService.getById(relationDto.getUserId());
            List<OtcAccount> otcAccounts = this.lambdaQuery().in(OtcAccount::getId, terminalIds).list();
            // 使用流和收集器去重
            Map<Long, String> uniqueAccountsMap = otcAccounts.stream().filter(oo -> StrUtil.isNotBlank(oo.getAccountId())).collect(Collectors.toMap(OtcAccount::getId, // 键
                    OtcAccount::getAccountId, // 值
                    (existingValue, newValue) -> existingValue // 合并函数，取第一个值
            ));
            List<OtcAccountUserRelation> accountUserRelations = accountUserRelationService.lambdaQuery().in(OtcAccountUserRelation::getTerminalId, terminalIds).list();
            // 使用流和收集器去重
            Map<Long, Long> accountRelationMap = accountUserRelations.stream().filter(oo -> StrUtil.isNotBlank(oo.getAccountId())).collect(Collectors.toMap(OtcAccountUserRelation::getTerminalId, // 键
                    OtcAccountUserRelation::getUserId, // 值
                    (existingValue, newValue) -> existingValue // 合并函数，取第一个值
            ));

            List<SyncAddAccountUserRelDto> userRelDtos = new ArrayList<>();

            terminalIds.forEach(oo -> {
                OtcAccountUserRelation userRelation = BeanUtil.toBean(relationDto, OtcAccountUserRelation.class);
                userRelation.setTerminalId(oo);
                userRelation.setSystemUserId(user.getSystemUserId());
                if (uniqueAccountsMap.containsKey(oo)) {
                    userRelation.setAccountId(uniqueAccountsMap.get(oo));
                }
                userRelations.add(userRelation);

                if (accountRelationMap.containsKey(oo)) {
                    Long userId = accountRelationMap.get(oo);
                    if (!Objects.equals(userId, relationDto.getUserId()) && uniqueAccountsMap.containsKey(oo)) {
                        userRelDtos.add(SyncAddAccountUserRelDto.builder().accountId(uniqueAccountsMap.get(oo)).systemUserId(user.getSystemUserId()).build());
                    }
                } else {
                    if (uniqueAccountsMap.containsKey(oo)) {
                        userRelDtos.add(SyncAddAccountUserRelDto.builder().accountId(uniqueAccountsMap.get(oo)).systemUserId(user.getSystemUserId()).build());
                    }
                }
            });

            accountUserRelationService.remove(Wrappers.lambdaQuery(OtcAccountUserRelation.class).in(OtcAccountUserRelation::getTerminalId, terminalIds));
            Boolean flag = accountUserRelationService.saveBatch(userRelations);
            if (CollectionUtils.isNotEmpty(userRelDtos)) {
                accountSqlServerMapper.syncSaveAccountRelation(userRelDtos);
            }
//            List<String> accountIdList = otcAccounts.stream().map(OtcAccount::getAccountId).distinct().collect(Collectors.toList());
//            Map<Long, List<String>> userRelMap = new HashMap<>();
//            userRelMap.put(relationDto.getUserId(), accountIdList);
//            String relationStr = JSON.toJSONString(userRelMap);
//            amqpTemplate.convertAndSend(MqConstant.TOPIC_OTC, MqConstant.ROUTING_KEY_USER_RELATION, relationStr);
            return flag;
        }
        return Boolean.FALSE;
    }

    @Override
    public PageOutput<OtcAccountPageVo> labelCustomerPage(OtcAccountPageDto pageDto) {

        MPJLambdaWrapper<OtcAccount> queryWrapper = labelCustomerQueryWrapper(pageDto);
//        MPJLambdaWrapper<OtcAccount> queryWrapper = initializeQueryWrapper(pageDto);
        IPage<OtcAccount> page = this.selectJoinListPage(ConventPage.getPage(pageDto), OtcAccount.class, queryWrapper);
        PageOutput<OtcAccountPageVo> pageOutput = ConventPage.getPageOutput(page, OtcAccountPageVo.class);

        // 根据标签查询已关联关联门店
        if (Objects.nonNull(pageDto.getCustomerTagId()) && Objects.nonNull(pageDto.getLabelType()) && pageDto.getLabelType() == 1) {
            List<Object> customerTagId = findCustomerTagId(pageDto.getCustomerTagId());
            // 确保 customerTagId 列表不为空
            if (customerTagId.isEmpty()) {
                pageOutput.setList(null);
                pageOutput.setTotal(0);
            }
        }

        List<OtcAccountPageVo> pageOutputList = pageOutput.getList();
        if (CollectionUtils.isNotEmpty(pageOutputList)) {
            List<Long> terminalIds = pageOutputList.stream().map(OtcAccountPageVo::getId).collect(Collectors.toList());
            List<OtcAccountUserRelation> userRelationList = accountUserRelationService.list(Wrappers.lambdaQuery(OtcAccountUserRelation.class).in(OtcAccountUserRelation::getTerminalId, terminalIds));
            if (CollectionUtils.isNotEmpty(userRelationList)) {
                List<Long> userIds = userRelationList.stream().map(OtcAccountUserRelation::getUserId).distinct().collect(Collectors.toList());
                Map<Long, Long> userRelationMap = new ConcurrentHashMap<>(userRelationList.size());
                userRelationMap = userRelationList.stream().collect(Collectors.toMap(OtcAccountUserRelation::getTerminalId, // 键
                        OtcAccountUserRelation::getUserId, // 值
                        (existingValue, newValue) -> existingValue // 合并函数，取第一个值
                ));
                Map<Long, Long> finalUserRelationMapMap = userRelationMap;
                //CUSTOMERTAGID
                Set<Long> terminalIdList = pageOutputList.stream().map(OtcAccountPageVo::getId).collect(Collectors.toSet());
                List<CustomerTagVo> OtcCustomerTagList = customerTagService.selectJoinList(CustomerTagVo.class, MPJWrappers.<OtcCustomerTag>lambdaJoin().distinct().in(ObjectUtil.isNotNull(terminalIdList), OtcCustomerTagRel::getAccountId, terminalIdList).select(OtcCustomerTag::getId, OtcCustomerTag::getTagName, OtcCustomerTag::getTagColor, OtcCustomerTag::getShowStatus, OtcCustomerTag::getSort).select(OtcCustomerTagRel::getAccountId).select(OtcCustomerTag.class, x -> VoToColumnUtil.fieldsToColumns(CustomerTagVo.class).contains(x.getProperty())).leftJoin(OtcCustomerTagRel.class, OtcCustomerTagRel::getCustomerTagId, OtcCustomerTag::getId).selectAs(OtcCustomerTagRel::getAccountId, "accountId"));
                Map<Long, List<CustomerTagVo>> mapOtcCustomerTag = OtcCustomerTagList.stream().collect(Collectors.groupingBy(CustomerTagVo::getAccountId));
                LambdaQueryWrapper<OtcSignIn> queryWrapperOtcSignIn = new LambdaQueryWrapper<>();
                queryWrapperOtcSignIn.eq(OtcSignIn::getEnabledMark, YesOrNoEnum.YES.getCode()).eq(OtcSignIn::getPersonId, pageDto.getOwnerId()).eq(OtcSignIn::getSiginType, GlobalConstant.SIGINTYPE).in(OtcSignIn::getPurMerchantId, terminalIdList).apply("DATE_FORMAT(create_date, '%Y-%m') = {0}", DateUtil.format(DateUtil.parse(pageDto.getPlanDay()), DateUtils.MONTH_PATTERN)).orderByDesc(OtcSignIn::getCreateDate);
                List<OtcSignIn> otcSignInList = signService.list(queryWrapperOtcSignIn);
                Map<Long, List<OtcSignIn>> mapOtcSignIn = otcSignInList.stream().collect(Collectors.groupingBy(OtcSignIn::getPurMerchantId));

                //部门处理
                List<User> userList = userService.selectJoinList(User.class, MPJWrappers.<User>lambdaJoin().in(ObjectUtil.isNotNull(userIds), User::getId, userIds).select(User::getId, User::getName, User::getCode, User::getBusinessUnitIdName));
                Map<Long, List<User>> userMap = new ConcurrentHashMap<>(userList.size());
                if (CollectionUtils.isNotEmpty(userList)) {
                    userMap = userList.stream().collect(Collectors.groupingBy(User::getId));
                }
                Map<Long, List<User>> finalUserMap = userMap;
                Map<Long, List<OtcSignIn>> finalMapOtcSignIn = mapOtcSignIn;
                pageOutputList.forEach(oo -> {
                    if (finalUserRelationMapMap.containsKey(oo.getId())) {
                        Long userId = finalUserRelationMapMap.get(oo.getId());
                        // 使用Stream API和Collectors.joining()方法连接字符串
                        if (!finalUserMap.isEmpty() && finalUserMap.containsKey(userId)) {
                            User user = finalUserMap.get(userId).get(0);
                            oo.setOwnerName(user.getName());
                            oo.setOwnerNumber(user.getCode());
                            String businessUnitIdName = user.getBusinessUnitIdName();
                            if (StringUtils.isNotBlank(businessUnitIdName)) {
                                String[] parts = businessUnitIdName.split(" ");
                                String targetElement = "OTC事业部"; // 指定的元素
                                List<String> partList = Arrays.asList(parts);
                                int index = partList.indexOf(targetElement);
                                String belongRegion = "";
                                if (index != -1) { // 如果找到目标元素
                                    belongRegion = String.join("/", partList.subList(index, partList.size()));
                                } else {
                                    belongRegion = String.join("/", partList);
                                }
                                oo.setBelongRegion(belongRegion);
                            }
                        }
                        if (ObjectUtil.isNull(oo.getAddress1Latitude()) || ObjectUtil.isNull(oo.getAddress1Longitude()) || ObjectUtil.isNull(pageDto.getLongitude()) || ObjectUtil.isNull(pageDto.getLatitude())) {
                            oo.setDistance(0);
                        } else {
                            DistanceVo gpsSource = new DistanceVo(pageDto.getLongitude(), pageDto.getLatitude());
                            DistanceVo gpsTarget = new DistanceVo(new BigDecimal(oo.getAddress1Longitude()), new BigDecimal(oo.getAddress1Latitude()));
                            oo.setDistance(DistanceUtils.getDistance(gpsSource, gpsTarget).intValue() / 1000);

                            Map<String, BigDecimal> originMap = SearchBaiDuMapApiUtil.baiduToGaoDe(new BigDecimal(oo.getAddress1Longitude()), new BigDecimal(oo.getAddress1Latitude()));
                            //System.out.println(originMap.get("lat").toString()+";"+originMap.get("lon").toString());
                            oo.setAddress2Longitude(originMap.get("lon").toString());
                            oo.setAddress2Latitude(originMap.get("lat").toString());
                        }
                        if (mapOtcCustomerTag.containsKey(oo.getId())) {
                            oo.setCustomerTagList(mapOtcCustomerTag.get(oo.getId()));
                            String customerTagName = mapOtcCustomerTag.get(oo.getId()).stream().map(CustomerTagVo::getTagName) // 获取每个元素的ownerName属性
                                    .collect(Collectors.joining(",")); // 以逗号分隔连接字符串
                            oo.setCustomerTagName(customerTagName);
                        }
                    }
                    if (finalMapOtcSignIn.containsKey(oo.getId())) {
                        oo.setReal(finalMapOtcSignIn.get(oo.getId()).size());
                    }
                    if (mapOtcCustomerTag.containsKey(oo.getId())) {
                        oo.setCustomerTagList(mapOtcCustomerTag.get(oo.getId()));
                    }
                });
            }
        }
        pageOutput.setList(pageOutputList);
        return pageOutput;
    }

    private MPJLambdaWrapper<OtcAccount> labelCustomerQueryWrapper(OtcAccountPageDto pageDto) {
        Integer terminalType = pageDto.getTerminalType();

        List<Long> userIds = new ArrayList<>();
        //如果是请求员工档案接口 获得当前人员的岗位进一步获得当前部门的所有人员id
        if (StringUtil.isNotBlank(pageDto.getCustomerArchive()) && pageDto.getCustomerArchive().equals(String.valueOf(EnabledMark.ENABLED.getCode()))) {
            List<UserRoleInfoDto> userRoleInfoDtoList = userPostRelationMapper.getUserPostInfo(Arrays.asList(pageDto.getOwnerId()));
            if (CollectionUtils.isNotEmpty(userRoleInfoDtoList)) {
                UserRoleInfoDto userRoleInfoDto = userRoleInfoDtoList.get(0);
                if (!(userRoleInfoDto.getCode().equals("10004") || userRoleInfoDto.getCode().equals("10005"))) {
                    List<String> userCodes = userService.getUserCodeByDept(pageDto.getDepartmentId());
                    userIds = userService.listObjs(Wrappers.<User>query().lambda().select(User::getId).in(ObjectUtil.isNotNull(userCodes), User::getCode, userCodes));
                } else {
                    userIds.add(pageDto.getOwnerId());
                }
            } else {
                userIds.add(pageDto.getOwnerId());
            }
        } else {
            if (Objects.nonNull(pageDto.getOwnerId())) {
                userIds.add(pageDto.getOwnerId());
            }
        }

        MPJLambdaWrapper<OtcAccount> queryWrapper = MPJWrappers.<OtcAccount>lambdaJoin();

        queryWrapper.eq(Objects.nonNull(pageDto.getId()), OtcAccount::getId, pageDto.getId()).eq(OtcAccount::getStateCode, StateCodeEnum.ENABLED.getCode()).eq(OtcAccount::getEnabledMark, EnabledMark.ENABLED.getCode()).like(StringUtil.isNotBlank(pageDto.getAccountName()), OtcAccount::getName, pageDto.getAccountName()).orderByDesc(OtcAccount::getId);


        if (ObjectUtil.isNotNull(terminalType)) {
            switch (terminalType) {
                case 0: //总部
                    queryWrapper.eq(OtcAccount::getTerminalType, terminalType);
                    if (StringUtils.isNotBlank(pageDto.getHeadName())) {
                        queryWrapper.like(OtcAccount::getName, pageDto.getHeadName());
                    }
                    break;
                case 1: //分部
                    queryWrapper.eq(OtcAccount::getTerminalType, terminalType);
                    if (StringUtils.isNotBlank(pageDto.getHeadAccountId())) {
                        queryWrapper.eq(OtcAccount::getNewOtcAccountId, pageDto.getHeadAccountId());
                    }
                    if (StringUtils.isNotBlank(pageDto.getHeadName())) {
                        List<Object> branches = findBranchesByHeadquarterName(pageDto.getHeadName());
                        if (CollectionUtils.isNotEmpty(branches)) {
                            queryWrapper.in(OtcAccount::getId, branches);
                        }
                    }
                    if (StringUtils.isNotBlank(pageDto.getPartName())) {
                        queryWrapper.like(OtcAccount::getName, pageDto.getPartName());
                    }
                    break;
                case 2: //门店
                    queryWrapper.eq(OtcAccount::getTerminalType, terminalType).eq(Objects.nonNull(pageDto.getIsDirectJoin()), OtcAccount::getIsDirectJoin, pageDto.getIsDirectJoin()).eq(Objects.nonNull(pageDto.getIsDtp()), OtcAccount::getIsDtp, pageDto.getIsDtp()).eq(Objects.nonNull(pageDto.getIsWhole()), OtcAccount::getIsWhole, pageDto.getIsWhole()).eq(Objects.nonNull(pageDto.getIsOnline()), OtcAccount::getIsOnline, pageDto.getIsOnline());
                    List<Object> headBranches;
                    List<Object> partBranches;
                    List<Object> branches = null;
                    if (StringUtils.isNotBlank(pageDto.getHeadAccountId())) {
                        queryWrapper.eq(OtcAccount::getNewOtcAccountId, pageDto.getHeadAccountId());
                    }
                    if (StringUtils.isNotEmpty(pageDto.getHeadName()) && StringUtils.isEmpty(pageDto.getPartName())) {
                        branches = findBranchesByHeadquarterName(pageDto.getHeadName());
                    } else if (StringUtils.isNotEmpty(pageDto.getPartName()) && StringUtils.isEmpty(pageDto.getHeadName())) {
                        branches = findBranchesByBranchName(pageDto.getPartName());
                    } else if (StringUtils.isNotEmpty(pageDto.getPartName()) && StringUtils.isNotBlank(pageDto.getHeadName())) {
                        headBranches = findBranchesByHeadquarterName(pageDto.getHeadName());
                        partBranches = findBranchesByBranchName(pageDto.getPartName());
                        branches = headBranches.stream()  // 将第一个集合转换为流
                                .filter(partBranches::contains)  // 过滤出在第二个集合中也存在的元素
                                .collect(Collectors.toList());  // 将结果收集到一个新的列表中
                    }
                    if (CollectionUtils.isNotEmpty(branches)) {
                        queryWrapper.in(OtcAccount::getParentAccountId, branches);
                    }
                    if (StringUtils.isNotBlank(pageDto.getShopName())) {
                        queryWrapper.like(OtcAccount::getName, pageDto.getShopName());
                    }
                    if (Objects.nonNull(pageDto.getCustomerTagId())) {
                        LambdaQueryWrapper<OtcCustomerTagRel> OtcCustomerQueryWrapper = new LambdaQueryWrapper<>();
                        OtcCustomerQueryWrapper.eq(OtcCustomerTagRel::getCustomerTagId, pageDto.getCustomerTagId()).eq(OtcCustomerTagRel::getDeleteMark, DeleteMark.NODELETE.getCode());
                        List<OtcCustomerTagRel> otcCustomerTagRellist = customerTagRelService.list(OtcCustomerQueryWrapper);
                        if (CollectionUtils.isNotEmpty(otcCustomerTagRellist) && pageDto.getLabelType() == null) {
                            List<Long> accountIdList = otcCustomerTagRellist.stream().map(OtcCustomerTagRel::getAccountId).collect(Collectors.toList());
                            queryWrapper.in(OtcAccount::getId, accountIdList);
                        }
                    }
                    // 根据标签查询未关联门店
                    if (Objects.nonNull(pageDto.getCustomerTagId()) && Objects.nonNull(pageDto.getLabelType()) && pageDto.getLabelType() == 0) {
                        List<Object> customerTagId = findCustomerTagId(pageDto.getCustomerTagId());
                        // 确保 customerTagId 列表不为空
                        if (!customerTagId.isEmpty()) {
                            queryWrapper.notIn(OtcAccount::getId, customerTagId);
                        }
                    }

                    // 根据标签查询已关联关联门店
                    if (Objects.nonNull(pageDto.getCustomerTagId()) && Objects.nonNull(pageDto.getLabelType()) && pageDto.getLabelType() == 1) {
                        List<Object> customerTagId = findCustomerTagId(pageDto.getCustomerTagId());
                        // 确保 customerTagId 列表不为空
                        if (!customerTagId.isEmpty()) {
                            queryWrapper.in(OtcAccount::getId, customerTagId);
                        }
                    }

                    break;
                case 4://核心门店
                    LambdaQueryWrapper<OtcCustomerTagRel> OtcCustomerQueryWrapper = new LambdaQueryWrapper<>();
                    //CUSTOMERTAGID
                    OtcCustomerQueryWrapper.eq(OtcCustomerTagRel::getCustomerTagId, CUSTOMERTAGID).eq(OtcCustomerTagRel::getDeleteMark, DeleteMark.NODELETE.getCode());
                    List<OtcCustomerTagRel> otcCustomerTagRellist = customerTagRelService.list(OtcCustomerQueryWrapper);
                    if (CollectionUtils.isNotEmpty(otcCustomerTagRellist)) {
                        queryWrapper.leftJoin(OtcCustomerTagRel.class, onWrapper -> onWrapper.eq(OtcAccount::getId, OtcCustomerTagRel::getAccountId));
                        queryWrapper.eq(OtcCustomerTagRel::getCustomerTagId, CUSTOMERTAGID).eq(OtcCustomerTagRel::getDeleteMark, DeleteMark.NODELETE.getCode());
                        // terminalIdList = otcCustomerTagRellist.stream().map(OtcCustomerTagRel::getAccountId).collect(Collectors.toList());
                        //queryWrapper.in(OtcAccount::getId, terminalIdList);
                    } else {
                        queryWrapper.eq(OtcAccount::getId, -1);
                    }
                default:
            }
        }

        if (CollectionUtils.isNotEmpty(userIds) || StrUtil.isNotBlank(pageDto.getOwnerNameOrNumber())) {
            queryWrapper.leftJoin(OtcAccountUserRelation.class, onWrapper -> onWrapper.eq(OtcAccountUserRelation::getTerminalId, OtcAccount::getId));
            queryWrapper.in(CollectionUtils.isNotEmpty(userIds), OtcAccountUserRelation::getUserId, userIds);
            queryWrapper.eq(StrUtil.isNotBlank(pageDto.getOwnerNameOrNumber()), OtcAccountUserRelation::getOwnerName, pageDto.getOwnerNameOrNumber()).or().eq(StrUtil.isNotBlank(pageDto.getOwnerNameOrNumber()), OtcAccountUserRelation::getOwnerNumber, pageDto.getOwnerNameOrNumber());
        }
        if (StringUtil.isNotBlank(pageDto.getCustomerArchive()) && pageDto.getCustomerArchive().equals(String.valueOf(EnabledMark.ENABLED.getCode()))) {
            queryWrapper.like(StrUtil.isNotBlank(pageDto.getCustomerName()), OtcAccount::getName, pageDto.getCustomerName());
        }
        return queryWrapper;
    }

    /**
     * 同步edp客户数据
     * 中间表同步规则约定：
     * 开始全量一次，后期增量更新
     * 插入：中间表的数据是按数据操作变更记录持续新增
     * 取值：按数据ID分组，日期倒序，同步频次查询上次调用日期到现在日期的变更时间数据为最新同步数据
     */
    @Override
    public R syncAccount() {
        // 获取最大日期
        String date = this.list(Wrappers.lambdaQuery(OtcAccount.class).select(OtcAccount::getCreatedDate).ne(OtcAccount::getCreatedDate, null)).stream() // 确保 createdDate 不为 null).stream()
                .map(OtcAccount::getCreatedDate).filter(Objects::nonNull) // 过滤掉 null 值
                .max(LocalDateTime::compareTo).map(LocalDateTime::toString).orElse(null);
        // 获取终端的客户数据
        List<AddOtcAccountDto> accountList = accountSqlServerMapper.syncAccount(NEW_SHIC_JB, date);

        if (accountList == null || accountList.isEmpty()) {
            return R.ok("无新增数据", null);
        }
        //  int count = save(accountList);

        int count = newSave(accountList);

        //TODO 同步数据到业务系统
        return R.ok("本次同步：" + count + "条数据", null);
    }

    @Override
    public R addAccountRelation(String userCode) {
        List<User> userList = userService.lambdaQuery().in(User::getCode, userCode).eq(User::getIsDisabled, 0).list();
        User user = userList.get(0);
        String userId = user.getSystemUserId();
        List<AddOtcAccountProductRelDto> accountProductRelDtos = accountSqlServerMapper.syncAccountUserRel(userId);
        List<String> accountIds = accountProductRelDtos.stream().map(AddOtcAccountProductRelDto::getAccountId).collect(Collectors.toList());
        List<OtcAccount> accountList = this.lambdaQuery().in(OtcAccount::getAccountId, accountIds).eq(OtcAccount::getStateCode, StateCodeEnum.ENABLED.getCode()).eq(OtcAccount::getDeleteMark, DeleteMark.NODELETE.getCode()).eq(OtcAccount::getEnabledMark, EnabledMark.ENABLED.getCode()).list();
        // 使用流和收集器去重,获取终端客户的关联map集合
        Map<String, Long> accountRelMap = accountList.stream().filter(oo -> StrUtil.isNotBlank(oo.getAccountId())).collect(Collectors.toMap(OtcAccount::getAccountId, // 键
                OtcAccount::getId, // 值
                (existingValue, newValue) -> existingValue // 合并函数，取第一个值
        ));
        List<OtcAccountUserRelation> userAddRelations = new ArrayList<>(accountProductRelDtos.size());
        for (AddOtcAccountProductRelDto acc : accountProductRelDtos) {
            OtcAccountUserRelation userRelation = new OtcAccountUserRelation();
            userRelation.setAccountId(acc.getAccountId());
            userRelation.setTerminalId(accountRelMap.get(acc.getAccountId()));
            userRelation.setSystemUserId(acc.getDsrId());
            userRelation.setUserId(user.getId());
            userRelation.setOwnerName(user.getName());
            userRelation.setOwnerNumber(user.getCode());
            userRelation.setCreateUserId(1000000000000000000L);
            userRelation.setModifyUserId(1000000000000000000L);
            userRelation.setCreateDate(LocalDateTime.now());
            userAddRelations.add(userRelation);
        }
        userAddRelations = userAddRelations.stream().filter(o -> null != o.getTerminalId()).collect(Collectors.toList());
        accountUserRelationService.saveBatch(userAddRelations);
        return R.ok("本次同步：" + userAddRelations.size() + "条数据", null);
    }

    public R syncAccountRelation() {
        // 获取最大日期
        String date = accountUserRelationService.list(Wrappers.lambdaQuery(OtcAccountUserRelation.class)
                        .select(OtcAccountUserRelation::getCreateDate)).stream().map(OtcAccountUserRelation::getCreateDate).filter(Objects::nonNull) // 过滤掉 null 值
                .max(LocalDateTime::compareTo).map(LocalDateTime::toString).orElse(null);
        // 获取终端与用户的关联
        List<AddOtcAccountProductRelDto> accountProductRelDtos = accountSqlServerMapper.syncAccountProductRel(date);
        if (CollectionUtils.isEmpty(accountProductRelDtos)) {
            return R.ok("无新增数据", null);
        }
        int count = saveAccountUserRel(accountProductRelDtos);
        //TODO 同步数据到业务系统
        return R.ok("本次同步：" + count + "条数据", null);
    }

    private int saveAccountUserRel(List<AddOtcAccountProductRelDto> accountProductRelDtos) {
        // 终端客户负责
        List<AddOtcAccountProductRelDto> accountListNoExists = new ArrayList<>();
        List<OtcAccountUserRelation> userAddRelations = new ArrayList<>(accountProductRelDtos.size());
        List<OtcAccountUserRelation> userUpdateRelations = new ArrayList<>(accountProductRelDtos.size());
        List<String> systemUserIds = accountProductRelDtos.stream().map(AddOtcAccountProductRelDto::getDsrId).distinct().collect(Collectors.toList());
        List<User> userList = userService.lambdaQuery().in(User::getSystemUserId, systemUserIds).eq(User::getIsDisabled, 0).list();
        User newUser = new User();
        newUser.setSystemUserId("365F50D7-2903-E711-80C0-00505696ECED");
        newUser.setName("CRM股份管理员");
        newUser.setId(9999999999l);
        newUser.setCode("CRM股份管理员");
        userList.add(newUser);
        List<String> accountIds = accountProductRelDtos.stream().map(AddOtcAccountProductRelDto::getAccountId).distinct().collect(Collectors.toList());
        List<OtcAccount> accountList = this.lambdaQuery().eq(OtcAccount::getStateCode, StateCodeEnum.ENABLED.getCode()).eq(OtcAccount::getDeleteMark, DeleteMark.NODELETE.getCode()).eq(OtcAccount::getEnabledMark, EnabledMark.ENABLED.getCode()).list();
        accountList = accountList.stream().filter(account -> accountIds.contains(account.getAccountId())).collect(Collectors.toList());
        List<OtcAccountUserRelation> userRelations = accountUserRelationService.lambdaQuery().eq(OtcAccountUserRelation::getDeleteMark, DeleteMark.NODELETE.getCode()).eq(OtcAccountUserRelation::getEnabledMark, EnabledMark.ENABLED.getCode()).list();
        userRelations = userRelations.stream().filter(account -> accountIds.contains(account.getAccountId())).collect(Collectors.toList());
        Map<String, List<OtcAccountUserRelation>> userRelationsMap = userRelations.stream().filter(oo -> StrUtil.isNotBlank(oo.getAccountId()) && Objects.nonNull(oo.getUserId())).distinct().collect(Collectors.groupingBy(OtcAccountUserRelation::getAccountId));
        // 使用流和收集器去重,获取用户的关联map集合
        // 用户edpID和当前系统用户id的Map集合
        Map<String, List<User>> userRelMap = userList.stream().filter(oo -> StrUtil.isNotBlank(oo.getSystemUserId())).collect(Collectors.groupingBy(User::getSystemUserId));
        // 使用流和收集器去重,获取终端客户的关联map集合
        Map<String, Long> accountRelMap = accountList.stream().filter(oo -> StrUtil.isNotBlank(oo.getAccountId())).collect(Collectors.toMap(OtcAccount::getAccountId, // 键
                OtcAccount::getId, // 值
                (existingValue, newValue) -> existingValue // 合并函数，取第一个值
        ));
        Set<AddOtcAccountProductRelDto> uniqueValues = new HashSet<>();
        for (AddOtcAccountProductRelDto acc : accountProductRelDtos) {
            if (uniqueValues.add(acc)) {
                OtcAccountUserRelation userRelation = new OtcAccountUserRelation();
                if (accountRelMap.containsKey(acc.getAccountId())) {
                    userRelation.setAccountId(acc.getAccountId());
                    userRelation.setTerminalId(accountRelMap.get(acc.getAccountId()));
                } else {
                    accountListNoExists.add(acc);
                    continue;
                }
                if (!userRelMap.containsKey(acc.getDsrId())) {
                    continue;
                } else {
                    userRelation.setSystemUserId(acc.getDsrId());
                    userRelation.setUserId(userRelMap.get(acc.getDsrId()).get(0).getId());
                    userRelation.setOwnerName(userRelMap.get(acc.getDsrId()).get(0).getName());
                    userRelation.setOwnerNumber(userRelMap.get(acc.getDsrId()).get(0).getCode());
                }
                if (userRelationsMap.containsKey(acc.getAccountId())) {
                    userRelation.setId(userRelationsMap.get(acc.getAccountId()).get(0).getId());
                    userRelation.setCreateUserId(userRelationsMap.get(acc.getAccountId()).get(0).getCreateUserId());
                    userRelation.setCreateUserId(userRelationsMap.get(acc.getAccountId()).get(0).getModifyUserId());
                    userRelation.setModifyDate(LocalDateTime.now());
                    userUpdateRelations.add(userRelation);
                } else {
                    userRelation.setCreateUserId(1000000000000000000L);
                    userRelation.setModifyUserId(1000000000000000000L);
                    userRelation.setCreateDate(LocalDateTime.now());
                    userAddRelations.add(userRelation);
                }
            }
        }
        insertOrUpdateAccountRelAsync(userAddRelations, userUpdateRelations);
        return accountProductRelDtos.size();
    }

    private int newSave(List<AddOtcAccountDto> accountList) {
        // 准备插入和更新
        List<OtcAccount> accountsToInsert = new ArrayList<>();
        List<OtcAccount> accountsToUpdate = new ArrayList<>();
        List<AddOtcAccountDto> allAccountList = accountSqlServerMapper.syncAllAccount(NEW_SHIC_JB, null);
        Map<String, AddOtcAccountDto> allAccountListMap = allAccountList.stream().collect(Collectors.toMap(
                AddOtcAccountDto::getAccountId,
                addOtcAccountDto -> addOtcAccountDto,
                (existing, replacement) -> existing
        ));
        //获得所有总部信息
        List<AddOtcAccountChainRelDto> otcAccountZongBuDtos = accountSqlServerMapper.getAccountZongBuRel();
        List<String> headList = otcAccountZongBuDtos.stream().map(AddOtcAccountChainRelDto::getAccountId).collect(Collectors.toList());
        for (AddOtcAccountChainRelDto entry : otcAccountZongBuDtos) {
            OtcAccount otcAccountEntity = new OtcAccount();
            otcAccountEntity.setAccountId(entry.getZongBuId());
            otcAccountEntity.setName(entry.getAccountName());
            otcAccountEntity.setTerminalType(TerminalType.HEAD.getCode());
            otcAccountEntity.setCreateDate(LocalDateTime.now());
            accountsToInsert.add(otcAccountEntity);
        }
        accountList = accountList.stream().filter(oo -> !headList.contains(oo.getAccountId())).collect(Collectors.toList());

        //获得所有分部信息
        Map<String, Map<String, String>> otcAllAccountMap = allAccountList.stream().filter(acc -> (StringUtils.isNotBlank(acc.getParentAccountId()) && StringUtils.isNotBlank(acc.getNewOtcAccountId()))).collect(Collectors.toMap(i -> i.getParentAccountId(), i -> {
            Map<String, String> accMap = new HashMap<>();
            accMap.put("newOtcAccountId", i.getNewOtcAccountId());
            accMap.put("newOtcAccountName", i.getNewOtcAccountName());
            return accMap;
        }, (existing, replacement) -> {
            // 这里可以根据实际需求来决定如何合并两个冲突的值
            // 例如，可以选择保留旧的值
            return existing;
        }));

        List<AddOtcAccountChainRelDto> addOtcAccountChainDtos = accountSqlServerMapper.getAccountChainRel();
        List<String> partList = addOtcAccountChainDtos.stream().map(AddOtcAccountChainRelDto::getAccountId).collect(Collectors.toList());
        for (AddOtcAccountChainRelDto entry : addOtcAccountChainDtos) {
            Map<String, String> mapFenToZong = otcAllAccountMap.get(entry.getAccountId());
            OtcAccount otcAccountEntity = new OtcAccount();
            otcAccountEntity.setAccountId(entry.getAccountId());
            otcAccountEntity.setName(entry.getAccountName());
            otcAccountEntity.setTerminalType(TerminalType.PART.getCode());
            if (null != mapFenToZong && !mapFenToZong.isEmpty()) {
                otcAccountEntity.setNewOtcAccountId(mapFenToZong.get("newOtcAccountId"));
                otcAccountEntity.setNewOtcAccountName(mapFenToZong.get("newOtcAccountName"));
            }
            // otcAccountEntity.
            otcAccountEntity.setCreateDate(LocalDateTime.now());
            accountsToInsert.add(otcAccountEntity);
        }

        Map<String, OtcAccount> otcAccountInsertListMap = accountsToInsert.stream().collect(Collectors.toMap(
                OtcAccount::getAccountId,
                otcAccountDto -> otcAccountDto,
                (existing, replacement) -> existing
        ));

        //处理分部做为单独的一列放在account里
        for (AddOtcAccountChainRelDto entry : addOtcAccountChainDtos) {
            AddOtcAccountDto otcAccountDto = allAccountListMap.get(entry.getAccountId());
            if (ObjectUtil.isNotEmpty(otcAccountDto)) {
                OtcAccount otcAccountEntity = BeanUtil.copyProperties(otcAccountDto, OtcAccount.class);
                if (Objects.isNull(otcAccountEntity.getParentAccountId()) || Objects.isNull(otcAccountEntity.getParentAccountIdName())) {
                    OtcAccount otcAccount = otcAccountInsertListMap.get(entry.getAccountId());
                    otcAccountEntity.setNewOtcAccountId(otcAccount.getParentAccountId());
                    otcAccountEntity.setNewOtcAccountName(otcAccount.getParentAccountIdName());
                }
                otcAccountEntity.setCreateDate(LocalDateTime.now());
                otcAccountEntity.setTerminalType(TerminalType.PART.getCode());
                accountsToInsert.removeIf(n -> n.getAccountId().equals(entry.getAccountId()));
                accountsToInsert.add(otcAccountEntity);
            }
        }
        accountList = accountList.stream().filter(oo -> !partList.contains(oo.getAccountId())).collect(Collectors.toList());

        //获得所有分部信息
        for (AddOtcAccountDto account : accountList) {
            OtcAccount otcAccountEntity = BeanUtil.copyProperties(account, OtcAccount.class);
            otcAccountEntity.setTerminalType(TerminalType.STORE.getCode());
            otcAccountEntity.setCreateDate(LocalDateTime.now());
            accountsToInsert.add(otcAccountEntity);
        }
        // 批量插入和更新
        insertOrUpdateAccountsAsync(accountsToInsert, accountsToUpdate);
        return accountsToInsert.size();
    }


    private int save(List<AddOtcAccountDto> accountList) {
        // 获取现有 accountid 集合
        Map<String, Long> existingAccountIdsMap = this.list().stream().collect(Collectors.toMap(OtcAccount::getAccountId, // 键
                OtcAccount::getId, // 值
                (existingValue, newValue) -> existingValue // 合并函数，取第一个值
        ));
        // 准备插入和更新
        List<OtcAccount> accountsToInsert = new ArrayList<>();
        List<OtcAccount> accountsToUpdate = new ArrayList<>();
        Map<String, Map<String, String>> otcAccountMap = accountList.stream().filter(acc -> (StringUtils.isNotBlank(acc.getParentAccountId()) && StringUtils.isNotBlank(acc.getNewOtcAccountId()))).collect(Collectors.toMap(i -> i.getParentAccountId(), i -> {
            Map<String, String> accMap = new HashMap<>();
            accMap.put("newOtcAccountId", i.getNewOtcAccountId());
            accMap.put("newOtcAccountName", i.getNewOtcAccountName());
            return accMap;
        }, (existing, replacement) -> {
            // 这里可以根据实际需求来决定如何合并两个冲突的值
            // 例如，可以选择保留旧的值
            return existing;
        }));
        // 总部
        List<String> headList = accountList.stream().map(AddOtcAccountDto::getNewOtcAccountId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(headList)) {
            // 获取总部数据
            List<AddOtcAccountDto> accountHeadList = accountSqlServerMapper.syncAccountByIds(headList);
            if (CollectionUtils.isNotEmpty(accountHeadList)) {
                accountHeadList.forEach(oo -> {
                    OtcAccount otcAccountEntity = BeanUtil.copyProperties(oo, OtcAccount.class);
                    otcAccountEntity.setTerminalType(TerminalType.HEAD.getCode());
                    if (existingAccountIdsMap.containsKey(otcAccountEntity.getAccountId())) {
                        otcAccountEntity.setId(existingAccountIdsMap.get(otcAccountEntity.getAccountId()));
                        accountsToUpdate.add(otcAccountEntity);
                    } else {
                        if (!accountsToInsert.stream().map(OtcAccount::getAccountId).collect(Collectors.toList()).contains(oo.getAccountId())) {
                            otcAccountEntity.setCreateDate(LocalDateTime.now());
                            accountsToInsert.add(otcAccountEntity);
                        }
                    }
                });
                accountList = accountList.stream().filter(oo -> !headList.contains(oo.getAccountId())).collect(Collectors.toList());
            } else {
                // 使用流和收集器去重
                Map<String, String> uniqueAccounts = accountList.stream().filter(oo -> StrUtil.isNotBlank(oo.getNewOtcAccountId()) && headList.contains(oo.getNewOtcAccountId())).collect(Collectors.toMap(AddOtcAccountDto::getNewOtcAccountId, // 键
                        AddOtcAccountDto::getNewOtcAccountName, // 值
                        (existingValue, newValue) -> existingValue // 合并函数，取第一个值
                ));
                if (!uniqueAccounts.isEmpty()) {
                    uniqueAccounts.entrySet().stream().forEach(entry -> {
                        if (!accountsToInsert.stream().map(OtcAccount::getAccountId).collect(Collectors.toList()).contains(entry.getKey())) {
                            OtcAccount otcAccountEntity = new OtcAccount();
                            otcAccountEntity.setAccountId(entry.getKey());
                            otcAccountEntity.setName(entry.getValue());
                            otcAccountEntity.setTerminalType(TerminalType.HEAD.getCode());
                            otcAccountEntity.setCreateDate(LocalDateTime.now());
                            accountsToInsert.add(otcAccountEntity);
                        }
                    });
                }
            }

        }
        // 分部
        List<String> partList = accountList.stream().map(AddOtcAccountDto::getParentAccountId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(partList)) {
            List<AddOtcAccountDto> accountPartList = new ArrayList<>(partList.size());
            // 获取分部数据
            List<AddOtcAccountDto> accountInnerPartList = accountList.stream().filter(acc -> partList.contains(acc.getAccountId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(accountInnerPartList)) {
                //处理的场景是ABCD在ParentAccountId列，但在AccountId列有ABC。所以这里要处理下D

                List<String> innerList = accountInnerPartList.stream().map(AddOtcAccountDto::getAccountId).collect(Collectors.toList());
                List<String> outList = partList.stream().filter(acc -> !innerList.contains(acc)).collect(Collectors.toList());
                List<AddOtcAccountDto> accountOutPartList = accountSqlServerMapper.syncAccountByIds(outList);
                accountPartList.addAll(accountInnerPartList);
                accountPartList.addAll(accountOutPartList);
            } else {
                accountPartList = accountSqlServerMapper.syncAccountByIds(partList);
            }
            if (CollectionUtils.isNotEmpty(accountPartList)) {
                accountPartList.forEach(oo -> {
                    OtcAccount otcAccountEntity = BeanUtil.copyProperties(oo, OtcAccount.class);
                    otcAccountEntity.setTerminalType(TerminalType.PART.getCode());
                    if (otcAccountMap.containsKey(otcAccountEntity.getAccountId())) {
                        otcAccountEntity.setNewOtcAccountId(otcAccountMap.get(otcAccountEntity.getAccountId()).get("newOtcAccountId"));
                        otcAccountEntity.setNewOtcAccountName(otcAccountMap.get(otcAccountEntity.getAccountId()).get("newOtcAccountName"));
                    }
                    if (existingAccountIdsMap.containsKey(otcAccountEntity.getAccountId())) {
                        otcAccountEntity.setId(existingAccountIdsMap.get(otcAccountEntity.getAccountId()));
                        otcAccountEntity.setModifyDate(LocalDateTime.now());
                        accountsToUpdate.add(otcAccountEntity);
                    } else {
                        otcAccountEntity.setCreateDate(LocalDateTime.now());
                        accountsToInsert.add(otcAccountEntity);
                    }

                });
                accountList = accountList.stream().filter(oo -> !partList.contains(oo.getAccountId())).collect(Collectors.toList());
            }
        }
        // 获取异常数据
        List<AddOtcAccountDto> exceptionalAccountList = accountList.stream().filter(acc -> (Objects.isNull(acc.getParentAccountId()) && partList.contains(acc.getAccountId()))).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(exceptionalAccountList)) {
            List<String> exceptionList = exceptionalAccountList.stream().map(AddOtcAccountDto::getAccountId).distinct().collect(Collectors.toList());
            // 获取下级数据
            List<AddOtcAccountDto> accountLowerList = accountSqlServerMapper.syncAccountByParentAccountIds(exceptionList);
            exceptionalAccountList.forEach(exce -> {
                OtcAccount otcAccountEntity = BeanUtil.copyProperties(exce, OtcAccount.class);
                List<AddOtcAccountDto> filterAccountList = accountLowerList.stream().filter(oo -> StrUtil.equals(oo.getParentAccountId(), exce.getAccountId())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(filterAccountList)) {
                    otcAccountEntity.setTerminalType(TerminalType.PART.getCode());
                    if (otcAccountMap.containsKey(otcAccountEntity.getAccountId())) {
                        otcAccountEntity.setNewOtcAccountId(otcAccountMap.get(otcAccountEntity.getAccountId()).get("newOtcAccountId"));
                        otcAccountEntity.setNewOtcAccountName(otcAccountMap.get(otcAccountEntity.getAccountId()).get("newOtcAccountName"));
                    }
                } else {
                    otcAccountEntity.setTerminalType(TerminalType.STORE.getCode());
                }
                if (existingAccountIdsMap.containsKey(otcAccountEntity.getAccountId())) {
                    otcAccountEntity.setId(existingAccountIdsMap.get(otcAccountEntity.getAccountId()));
                    otcAccountEntity.setModifyDate(LocalDateTime.now());
                    accountsToUpdate.add(otcAccountEntity);
                } else {
                    otcAccountEntity.setCreateDate(LocalDateTime.now());
                    accountsToInsert.add(otcAccountEntity);
                }
            });
            accountList = accountList.stream().filter(oo -> !exceptionList.contains(oo.getAccountId())).collect(Collectors.toList());
        }

        // 处理其他的终端客户
        for (AddOtcAccountDto account : accountList) {
            OtcAccount otcAccountEntity = BeanUtil.copyProperties(account, OtcAccount.class);
            otcAccountEntity.setTerminalType(TerminalType.STORE.getCode());
            if (existingAccountIdsMap.containsKey(otcAccountEntity.getAccountId())) {
                otcAccountEntity.setId(existingAccountIdsMap.get(otcAccountEntity.getAccountId()));
                otcAccountEntity.setModifyDate(LocalDateTime.now());
                accountsToUpdate.add(otcAccountEntity);
            } else {
                otcAccountEntity.setCreateDate(LocalDateTime.now());
                accountsToInsert.add(otcAccountEntity);
            }
        }
        // 批量插入和更新
        insertOrUpdateAccountsAsync(accountsToInsert, accountsToUpdate);
        return accountList.size();
    }

    private void insertOrUpdateAccountsAsync(List<OtcAccount> accountsToInsert, List<OtcAccount> accountsToUpdate) {
        // 创建一个线程池，根据实际情况调整线程数量
        int threadPool = Runtime.getRuntime().availableProcessors();
        ExecutorService executorService = Executors.newFixedThreadPool(threadPool);
        List<Future<?>> futures = new ArrayList<>();
        // 如果有需要插入的账户，将插入任务提交给线程池
        if (!accountsToInsert.isEmpty()) {
            futures.add(executorService.submit(() -> processBatches(accountsToInsert, this::saveBatch)));
        }

        // 如果有需要更新的账户，将更新任务提交给线程池
        if (!accountsToUpdate.isEmpty()) {
            futures.add(executorService.submit(() -> processBatches(accountsToUpdate, this::updateBatchById)));
        }

        // 等待所有任务完成
        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (InterruptedException | ExecutionException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("Error processing accounts", e);
            }
        }


        // 关闭线程池
        executorService.shutdown();
//        if (!accountsToInsert.isEmpty()) {
//            processBatches(accountsToInsert, this::saveBatch);
//        }
//
//        if (!accountsToUpdate.isEmpty()) {
//            processBatches(accountsToUpdate, this::updateBatchById);
//        }
    }

    private void processBatches(List<OtcAccount> accounts, Consumer<List<OtcAccount>> batchProcessor) {
        int batchSize = BATCH_SIZE;
        int totalSize = accounts.size();

        for (int start = 0; start < totalSize; start += batchSize) {
            int end = Math.min(start + batchSize, totalSize);
            List<OtcAccount> batch = accounts.subList(start, end);
            batchProcessor.accept(batch);
        }
    }

    private void insertOrUpdateAccountRelAsync(List<OtcAccountUserRelation> accountsToInsert, List<OtcAccountUserRelation> accountsToUpdate) {

        // 创建一个线程池，根据实际情况调整线程数量
        int threadPool = Runtime.getRuntime().availableProcessors();
        ExecutorService executorService = Executors.newFixedThreadPool(threadPool);
        List<Future<?>> futures = new ArrayList<>();

        // 如果有需要插入的账户，将插入任务提交给线程池
        if (!accountsToInsert.isEmpty()) {
            futures.add(executorService.submit(() -> processRelBatches(accountsToInsert, accountUserRelationService::saveBatch)));
        }

        // 如果有需要更新的账户，将更新任务提交给线程池
        if (!accountsToUpdate.isEmpty()) {
            futures.add(executorService.submit(() -> processRelBatches(accountsToUpdate, accountUserRelationService::updateBatchById)));
        }

        // 等待所有任务完成
        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (InterruptedException | ExecutionException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("Error processing accounts", e);
            }
        }

        // 关闭线程池
        executorService.shutdown();

//        if (!accountsToInsert.isEmpty()) {
//            processRelBatches(accountsToInsert, accountUserRelationService::saveBatch);
//        }
//
//        if (!accountsToUpdate.isEmpty()) {
//            processRelBatches(accountsToUpdate, accountUserRelationService::updateBatchById);
//        }
    }

    private void processRelBatches(List<OtcAccountUserRelation> accounts, Consumer<List<OtcAccountUserRelation>> batchProcessor) {
        int batchSize = BATCH_SIZE;
        int totalSize = accounts.size();

        for (int start = 0; start < totalSize; start += batchSize) {
            int end = Math.min(start + batchSize, totalSize);
            List<OtcAccountUserRelation> batch = accounts.subList(start, end);
            batchProcessor.accept(batch);
        }
    }


    private MPJLambdaWrapper<OtcAccount> initializeQueryWrapper(OtcAccountPageDto pageDto) {
        Integer terminalType = pageDto.getTerminalType();

        List<Long> userIds = new ArrayList<>();
        //如果是请求员工档案接口 获得当前人员的岗位进一步获得当前部门的所有人员id
        if (StringUtil.isNotBlank(pageDto.getCustomerArchive()) && pageDto.getCustomerArchive().equals(String.valueOf(EnabledMark.ENABLED.getCode()))) {
            List<UserRoleInfoDto> userRoleInfoDtoList = userPostRelationMapper.getUserPostInfo(Arrays.asList(pageDto.getOwnerId()));
            if (CollectionUtils.isNotEmpty(userRoleInfoDtoList)) {
                UserRoleInfoDto userRoleInfoDto = userRoleInfoDtoList.get(0);
                if (!(userRoleInfoDto.getCode().equals("10004") || userRoleInfoDto.getCode().equals("10005"))) {
                    List<String> userCodes = userService.getUserCodeByDept(pageDto.getDepartmentId());
                    userIds = userService.listObjs(Wrappers.<User>query().lambda().select(User::getId).in(ObjectUtil.isNotNull(userCodes), User::getCode, userCodes));
                } else {
                    userIds.add(pageDto.getOwnerId());
                }
            } else {
                userIds.add(pageDto.getOwnerId());
            }
        } else {
            if (Objects.nonNull(pageDto.getOwnerId())) {
                userIds.add(pageDto.getOwnerId());
            }
        }

        List<Object> terminalIdList = new ArrayList<>();
//        if (CollectionUtils.isNotEmpty(userIds) || StrUtil.isNotBlank(pageDto.getOwnerNameOrNumber())) {
//            terminalIdList = accountUserRelationService.listObjs(Wrappers.<OtcAccountUserRelation>query().lambda()
//                    .select(OtcAccountUserRelation::getTerminalId)
//                    .in(ObjectUtil.isNotNull(pageDto.getOwnerId()), OtcAccountUserRelation::getUserId, userIds)
//                    .eq(StrUtil.isNotBlank(pageDto.getOwnerNameOrNumber()), OtcAccountUserRelation::getOwnerName, pageDto.getOwnerNameOrNumber()).or()
//                    .eq(StrUtil.isNotBlank(pageDto.getOwnerNameOrNumber()), OtcAccountUserRelation::getOwnerNumber, pageDto.getOwnerNameOrNumber())
//            );
//        }
        MPJLambdaWrapper<OtcAccount> queryWrapper = MPJWrappers.<OtcAccount>lambdaJoin();
//        LambdaQueryWrapper<OtcAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(pageDto.getId()), OtcAccount::getId, pageDto.getId()).eq(OtcAccount::getStateCode, StateCodeEnum.ENABLED.getCode()).eq(OtcAccount::getEnabledMark, EnabledMark.ENABLED.getCode()).like(StringUtil.isNotBlank(pageDto.getAccountName()), OtcAccount::getName, pageDto.getAccountName());
        //如果terminalIdList为空的情况-打卡和路线规划种选择客户种使用
       /* if (ObjectUtil.isNotNull(pageDto.getOwnerId())) {
            queryWrapper.eq(OtcAccount::getId, -1);
        }*/

        //其中核心门店要除外，另外计算固加上这个条件
        // if (ObjectUtil.isNull(terminalType) || (ObjectUtil.isNotNull(terminalType) && terminalType != 4)) {

        // }

        if (ObjectUtil.isNotNull(terminalType)) {
            switch (terminalType) {
                case 0: //总部
                    queryWrapper.eq(OtcAccount::getTerminalType, terminalType);
                    if (StringUtils.isNotBlank(pageDto.getHeadName())) {
                        queryWrapper.like(OtcAccount::getName, pageDto.getHeadName());
                    }
                    break;
                case 1: //分部
                    queryWrapper.eq(OtcAccount::getTerminalType, terminalType);
                    if (StringUtils.isNotBlank(pageDto.getHeadAccountId())) {
                        queryWrapper.eq(OtcAccount::getNewOtcAccountId, pageDto.getHeadAccountId());
                    }
                    if (StringUtils.isNotBlank(pageDto.getHeadName())) {
                        List<Object> branches = findBranchesByHeadquarterName(pageDto.getHeadName());
                        if (CollectionUtils.isNotEmpty(branches)) {
                            queryWrapper.in(OtcAccount::getId, branches);
                        }
                    }
                    if (StringUtils.isNotBlank(pageDto.getPartName())) {
                        queryWrapper.like(OtcAccount::getName, pageDto.getPartName());
                    }
                    break;
                case 2: //门店
                    queryWrapper.eq(OtcAccount::getTerminalType, terminalType).eq(Objects.nonNull(pageDto.getIsDirectJoin()), OtcAccount::getIsDirectJoin, pageDto.getIsDirectJoin()).eq(Objects.nonNull(pageDto.getIsDtp()), OtcAccount::getIsDtp, pageDto.getIsDtp()).eq(Objects.nonNull(pageDto.getIsWhole()), OtcAccount::getIsWhole, pageDto.getIsWhole()).eq(Objects.nonNull(pageDto.getIsOnline()), OtcAccount::getIsOnline, pageDto.getIsOnline());
                    List<Object> headBranches;
                    List<Object> partBranches;
                    List<Object> branches = null;
                    if (StringUtils.isNotBlank(pageDto.getHeadAccountId())) {
                        queryWrapper.eq(OtcAccount::getNewOtcAccountId, pageDto.getHeadAccountId());
                    }
                    if (StringUtils.isNotEmpty(pageDto.getHeadName()) && StringUtils.isEmpty(pageDto.getPartName())) {
                        branches = findBranchesByHeadquarterName(pageDto.getHeadName());
                    } else if (StringUtils.isNotEmpty(pageDto.getPartName()) && StringUtils.isEmpty(pageDto.getHeadName())) {
                        branches = findBranchesByBranchName(pageDto.getPartName());
                    } else if (StringUtils.isNotEmpty(pageDto.getPartName()) && StringUtils.isNotBlank(pageDto.getHeadName())) {
                        headBranches = findBranchesByHeadquarterName(pageDto.getHeadName());
                        partBranches = findBranchesByBranchName(pageDto.getPartName());
                        branches = headBranches.stream()  // 将第一个集合转换为流
                                .filter(partBranches::contains)  // 过滤出在第二个集合中也存在的元素
                                .collect(Collectors.toList());  // 将结果收集到一个新的列表中
                    }
                    if (CollectionUtils.isNotEmpty(branches)) {
                        queryWrapper.in(OtcAccount::getParentAccountId, branches);
                    } else {
                        if (StringUtils.isNotEmpty(pageDto.getPartName()) || StringUtils.isNotBlank(pageDto.getHeadName())) {
                            queryWrapper.eq(OtcAccount::getId, -1);
                        }
                    }
                    if (StringUtils.isNotBlank(pageDto.getShopName())) {
                        queryWrapper.like(OtcAccount::getName, pageDto.getShopName());
                    }
                    if (Objects.nonNull(pageDto.getCustomerTagId())) {
                        LambdaQueryWrapper<OtcCustomerTagRel> OtcCustomerQueryWrapper = new LambdaQueryWrapper<>();
                        OtcCustomerQueryWrapper.eq(OtcCustomerTagRel::getCustomerTagId, pageDto.getCustomerTagId()).eq(OtcCustomerTagRel::getDeleteMark, DeleteMark.NODELETE.getCode());
                        List<OtcCustomerTagRel> otcCustomerTagRellist = customerTagRelService.list(OtcCustomerQueryWrapper);
                        if (CollectionUtils.isNotEmpty(otcCustomerTagRellist)) {
                            List<Long> accountIdList = otcCustomerTagRellist.stream().map(OtcCustomerTagRel::getAccountId).collect(Collectors.toList());
                            queryWrapper.in(OtcAccount::getId, accountIdList);
                        }
                    }
                    break;
                case 4://核心门店
                    LambdaQueryWrapper<OtcCustomerTagRel> OtcCustomerQueryWrapper = new LambdaQueryWrapper<>();
                    //CUSTOMERTAGID
                    OtcCustomerQueryWrapper.eq(OtcCustomerTagRel::getCustomerTagId, pageDto.getCustomerTagId()).eq(OtcCustomerTagRel::getDeleteMark, DeleteMark.NODELETE.getCode());
                    List<OtcCustomerTagRel> otcCustomerTagRellist = customerTagRelService.list(OtcCustomerQueryWrapper);
                    if (CollectionUtils.isNotEmpty(otcCustomerTagRellist)) {
                        queryWrapper.leftJoin(OtcCustomerTagRel.class, onWrapper -> onWrapper.eq(OtcAccount::getId, OtcCustomerTagRel::getAccountId));
                        queryWrapper.eq(OtcCustomerTagRel::getCustomerTagId, pageDto.getCustomerTagId()).eq(OtcCustomerTagRel::getDeleteMark, DeleteMark.NODELETE.getCode());
                        // terminalIdList = otcCustomerTagRellist.stream().map(OtcCustomerTagRel::getAccountId).collect(Collectors.toList());
                        //queryWrapper.in(OtcAccount::getId, terminalIdList);
                    } else {
                        queryWrapper.eq(OtcAccount::getId, -1);
                    }
                default:
            }
        }

        if (CollectionUtils.isNotEmpty(userIds) || StrUtil.isNotBlank(pageDto.getOwnerNameOrNumber())) {
            queryWrapper.leftJoin(OtcAccountUserRelation.class, onWrapper -> onWrapper.eq(OtcAccountUserRelation::getTerminalId, OtcAccount::getId));
            queryWrapper.in(CollectionUtils.isNotEmpty(userIds), OtcAccountUserRelation::getUserId, userIds);
            queryWrapper.eq(OtcAccountUserRelation::getDeleteMark, DeleteMark.NODELETE.getCode());
            if (StrUtil.isNotBlank(pageDto.getOwnerNameOrNumber())) {
                queryWrapper.and(x -> x.eq(OtcAccountUserRelation::getOwnerName, pageDto.getOwnerNameOrNumber())
                        .or().eq(OtcAccountUserRelation::getOwnerNumber, pageDto.getOwnerNameOrNumber()));

            }
        }
        if (StringUtil.isNotBlank(pageDto.getCustomerArchive()) && pageDto.getCustomerArchive().equals(String.valueOf(EnabledMark.ENABLED.getCode()))) {
            queryWrapper.like(StrUtil.isNotBlank(pageDto.getCustomerName()), OtcAccount::getName, pageDto.getCustomerName());
        }
        return queryWrapper;
    }

    /**
     * 根据总部名称查询分部的数据
     *
     * @param headquarterName
     * @return
     */
    public List<Object> findBranchesByHeadquarterName(String headquarterName) {
        LambdaQueryWrapper<OtcAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(OtcAccount::getName, headquarterName).eq(OtcAccount::getTerminalType, 0); // 假设总部的parent_account_id为null
        List<OtcAccount> headquarters = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(headquarters)) {
            return Collections.emptyList(); // 如果没有找到总部，返回空列表
        }
        List<String> accountIdlist = headquarters.stream().map(OtcAccount::getAccountId).distinct().collect(Collectors.toList());
        queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(OtcAccount::getAccountId);
        queryWrapper.in(OtcAccount::getNewOtcAccountId, accountIdlist);
        queryWrapper.eq(OtcAccount::getTerminalType, 1);
        return this.listObjs(queryWrapper); // 使用service层的list方法获取分部列表
    }

    /**
     * 根据标签id查询门店id
     *
     * @param customerTagId
     * @return
     */
    public List<Object> findCustomerTagId(Long customerTagId) {
        LambdaQueryWrapper<OtcCustomerTagRel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OtcCustomerTagRel::getCustomerTagId, customerTagId);
        queryWrapper.select(OtcCustomerTagRel::getAccountId);
        return customerTagRelService.listObjs(queryWrapper);
    }

    /**
     * 根据分部名称查询分部的数据
     *
     * @param branchName
     * @return
     */
    public List<Object> findBranchesByBranchName(String branchName) {
        LambdaQueryWrapper<OtcAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(OtcAccount::getName, branchName).eq(OtcAccount::getTerminalType, 1);
        queryWrapper.select(OtcAccount::getAccountId);
        return this.listObjs(queryWrapper); // 使用service层的list方法获取分部列表
    }

    @Override
    public List<OtcAccountPageVo> getAccountUserList(OtcAccountPageDto pageDto) {
        List<OtcAccountPageVo> otcAccountPageVoLists = new ArrayList<>();
      /*      List<OtcAccountUserRelation> userRelationList = accountUserRelationService.list(Wrappers.lambdaQuery(OtcAccountUserRelation.class).in(OtcAccountUserRelation::getUserId, pageDto.getOwnerId()));
        List<Long> terminalIds = userRelationList.stream().map(OtcAccountUserRelation::getTerminalId).collect(Collectors.toList());
        List<OtcAccount> otcAccountLists = list(Wrappers.lambdaQuery(OtcAccount.class).in(OtcAccount::getId, terminalIds));*/

        MPJLambdaWrapper<OtcAccount> queryWrapper = initializeQueryWrapper(pageDto);
        queryWrapper.like(StringUtil.isNotBlank(pageDto.getCustomerName()), OtcAccount::getName, pageDto.getCustomerName());
        List<OtcAccount> otcAccountLists1 = list(queryWrapper);
        otcAccountPageVoLists = BeanUtil.copyToList(otcAccountLists1, OtcAccountPageVo.class);
        otcAccountPageVoLists.forEach(oo -> {
            if (ObjectUtil.isNull(oo.getAddress1Latitude()) || ObjectUtil.isNull(oo.getAddress1Longitude()) || ObjectUtil.isNull(pageDto.getLongitude()) || ObjectUtil.isNull(pageDto.getLatitude())) {
                oo.setDistance(0);
            } else {
                DistanceVo gpsSource = new DistanceVo(pageDto.getLongitude(), pageDto.getLatitude());
                DistanceVo gpsTarget = new DistanceVo(new BigDecimal(oo.getAddress1Longitude()), new BigDecimal(oo.getAddress1Latitude()));
                oo.setDistance(DistanceUtils.getDistance(gpsSource, gpsTarget).intValue() / 1000);

                Map<String, BigDecimal> originMap = SearchBaiDuMapApiUtil.baiduToGaoDe(new BigDecimal(oo.getAddress1Longitude()), new BigDecimal(oo.getAddress1Latitude()));
                //System.out.println(originMap.get("lat").toString()+";"+originMap.get("lon").toString());
                oo.setAddress2Longitude(originMap.get("lon").toString());
                oo.setAddress2Latitude(originMap.get("lat").toString());
            }
        });
        return otcAccountPageVoLists;
    }


    @Override
    public R syncAccountChainRelation() {
        List<AddOtcAccountChainRelDto> addOtcAccountChainRelDtos = accountSqlServerMapper.getAccountChainRel();
        for (AddOtcAccountChainRelDto addOtcAccountChainRel : addOtcAccountChainRelDtos) {
            List<OtcAccountUserRelation> otcAccountUserRelations = accountUserRelationService.lambdaQuery().eq(OtcAccountUserRelation::getAccountId, addOtcAccountChainRel.getAccountId()).eq(OtcAccountUserRelation::getDeleteMark, DeleteMark.NODELETE.getCode()).eq(OtcAccountUserRelation::getEnabledMark, EnabledMark.ENABLED.getCode()).list();
            if (CollectionUtils.isNotEmpty(otcAccountUserRelations)) {
                OtcAccountUserRelation userRelation = otcAccountUserRelations.get(0);
                List<User> userList = userService.lambdaQuery().eq(User::getSystemUserId, addOtcAccountChainRel.getSystemUserId()).eq(User::getIsDisabled, 0).list();
                userRelation.setSystemUserId(addOtcAccountChainRel.getSystemUserId());
                userRelation.setUserId(userList.get(0).getId());
                userRelation.setOwnerName(addOtcAccountChainRel.getOwnerName());
                userRelation.setOwnerNumber(addOtcAccountChainRel.getOwnerNumber());
                userRelation.setModifyDate(LocalDateTime.now());
                accountUserRelationService.updateById(userRelation);
            } else {
                List<User> userList = userService.lambdaQuery().eq(User::getSystemUserId, addOtcAccountChainRel.getSystemUserId()).eq(User::getIsDisabled, 0).list();
                List<OtcAccount> accountLists = this.list(Wrappers.lambdaQuery(OtcAccount.class).eq(OtcAccount::getAccountId, addOtcAccountChainRel.getAccountId()).orderByDesc(OtcAccount::getId));
                if (CollectionUtils.isNotEmpty(userList) && CollectionUtils.isNotEmpty(accountLists)) {
                    OtcAccountUserRelation userRelation = new OtcAccountUserRelation();
                    userRelation.setAccountId(addOtcAccountChainRel.getAccountId());
                    userRelation.setTerminalId(accountLists.get(0).getId());
                    userRelation.setSystemUserId(addOtcAccountChainRel.getSystemUserId());
                    userRelation.setUserId(userList.get(0).getId());
                    userRelation.setOwnerName(addOtcAccountChainRel.getOwnerName());
                    userRelation.setOwnerNumber(addOtcAccountChainRel.getOwnerNumber());
                    userRelation.setModifyDate(LocalDateTime.now());
                    userRelation.setCreateUserId(1000000000000000000L);
                    userRelation.setModifyUserId(1000000000000000000L);
                    userRelation.setCreateDate(LocalDateTime.now());
                    accountUserRelationService.save(userRelation);
                }
            }
        }
        return R.ok("本次连锁分部负责人同步结束");
    }

    @Override
    public R syncAccountZongBuRelation() {
        List<AddOtcAccountChainRelDto> addOtcAccountChainRelDtos = accountSqlServerMapper.getAccountZongBuRel();
        for (AddOtcAccountChainRelDto addOtcAccountChainRel : addOtcAccountChainRelDtos) {
            List<User> userList = userService.lambdaQuery().eq(User::getSystemUserId, addOtcAccountChainRel.getSystemUserId()).eq(User::getIsDisabled, 0).list();
            List<OtcAccount> accountLists = this.list(Wrappers.lambdaQuery(OtcAccount.class).eq(OtcAccount::getTerminalType, 0).eq(OtcAccount::getName, addOtcAccountChainRel.getAccountName()).orderByDesc(OtcAccount::getId));
            if (CollectionUtils.isNotEmpty(accountLists)) {
                List<Long> otcAccountIds = accountLists.stream().map(OtcAccount::getId).collect(Collectors.toList());
                List<OtcAccountUserRelation> otcAccountUserRelationList = accountUserRelationService.lambdaQuery().in(OtcAccountUserRelation::getTerminalId, otcAccountIds).eq(OtcAccountUserRelation::getDeleteMark, DeleteMark.NODELETE.getCode()).eq(OtcAccountUserRelation::getEnabledMark, EnabledMark.ENABLED.getCode()).list();
                if (CollectionUtils.isNotEmpty(otcAccountUserRelationList)) {
                    accountUserRelationService.remove(Wrappers.lambdaQuery(OtcAccountUserRelation.class).in(OtcAccountUserRelation::getTerminalId, otcAccountIds));
                }
            }
            if (CollectionUtils.isNotEmpty(userList) && CollectionUtils.isNotEmpty(accountLists)) {
                OtcAccountUserRelation userRelation = new OtcAccountUserRelation();
                userRelation.setAccountId(addOtcAccountChainRel.getZongBuId());
                userRelation.setTerminalId(accountLists.get(0).getId());
                userRelation.setSystemUserId(addOtcAccountChainRel.getSystemUserId());
                userRelation.setUserId(userList.get(0).getId());
                userRelation.setOwnerName(addOtcAccountChainRel.getOwnerName());
                userRelation.setOwnerNumber(addOtcAccountChainRel.getOwnerNumber());
                userRelation.setModifyDate(LocalDateTime.now());
                userRelation.setCreateUserId(1000000000000000000L);
                userRelation.setModifyUserId(1000000000000000000L);
                userRelation.setCreateDate(LocalDateTime.now());
                accountUserRelationService.save(userRelation);
            }

        }
        return R.ok("本次连锁总部负责人同步结束");
    }

    /**
     * 更新或新增总部、分部、门店负责人
     *
     * @return
     */
    public R saveOrUpdateAccountRelation() {
        //更新或新增连锁总部负责人
        saveOrUpdateZongBuRelation();

        //更新或新增连锁分部负责人
        saveOrUpdateChainRelation();

        List<OtcAccount> accountList = this.lambdaQuery().in(OtcAccount::getTerminalType, Arrays.asList(TerminalType.HEAD.getCode(), TerminalType.PART.getCode())).eq(OtcAccount::getStateCode, StateCodeEnum.ENABLED.getCode()).eq(OtcAccount::getDeleteMark, DeleteMark.NODELETE.getCode()).eq(OtcAccount::getEnabledMark, EnabledMark.ENABLED.getCode()).list();
        List<String> accountIds = accountList.stream().map(OtcAccount::getAccountId).collect(Collectors.toList());
        Set<String> accountIdSet = new HashSet<>(accountIds); // 提升查找效率
        List<OtcAccountUserRelation> otcAccountUserRelationList = accountUserRelationService.list(Wrappers.lambdaQuery(OtcAccountUserRelation.class).eq(OtcAccountUserRelation::getDeleteMark, DeleteMark.NODELETE.getCode())
                .select(OtcAccountUserRelation::getCreateDate, OtcAccountUserRelation::getAccountId, OtcAccountUserRelation::getTerminalId));
        otcAccountUserRelationList = otcAccountUserRelationList.stream().filter(Objects::nonNull).filter(a -> Objects.nonNull(a.getAccountId())).filter(a -> Objects.nonNull(a.getCreateDate())).filter(a -> !accountIdSet.contains(a.getAccountId())).collect(Collectors.toList());

        // 获取最大日期
        String date = otcAccountUserRelationList.stream().map(OtcAccountUserRelation::getCreateDate).filter(Objects::nonNull) // 过滤掉 null 值
                .max(LocalDateTime::compareTo).map(LocalDateTime::toString).orElse(null);

        // 获取终端与用户的关联
        List<AddOtcAccountProductRelDto> accountProductRelDtos = accountSqlServerMapper.syncAccountProductRel(date);
        if (CollectionUtils.isEmpty(accountProductRelDtos)) {
            return R.ok("无新增数据", null);
        }
        int count = saveAccountUserRel(accountProductRelDtos);
        //TODO 同步数据到业务系统
        return R.ok("本次同步：" + count + "条数据", null);
    }


    /**
     * 更新或新增连锁总部负责人
     *
     * @return
     */
    private R saveOrUpdateZongBuRelation() {
        List<OtcAccountUserRelation> accountRelListHeadsInsert = new ArrayList<>();
        List<OtcAccountUserRelation> accountRelListHeadsUpdate = new ArrayList<>();
        List<AddOtcAccountChainRelDto> addOtcAccountChainRelDtos = accountSqlServerMapper.getAccountZongBuRel();
        List<OtcAccount> accountListHeads = this.list(Wrappers.lambdaQuery(OtcAccount.class).eq(OtcAccount::getTerminalType, TerminalType.HEAD.getCode()).orderByDesc(OtcAccount::getId));
        List<User> userListAll = userService.lambdaQuery().eq(User::getDeleteMark, DeleteMark.NODELETE.getCode()).eq(User::getEnabledMark, EnabledMark.ENABLED.getCode()).eq(User::getIsDisabled, 0).list();
        List<OtcAccountUserRelation> otcAccountUserRelationAll = accountUserRelationService.lambdaQuery().eq(OtcAccountUserRelation::getDeleteMark, DeleteMark.NODELETE.getCode()).eq(OtcAccountUserRelation::getEnabledMark, EnabledMark.ENABLED.getCode()).list();

        for (AddOtcAccountChainRelDto addOtcAccountChainRel : addOtcAccountChainRelDtos) {
            List<OtcAccountUserRelation> otcAccountUserRelations = otcAccountUserRelationAll.stream().filter(otcAccountUserRelation -> otcAccountUserRelation.getAccountId().equals(addOtcAccountChainRel.getZongBuId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(otcAccountUserRelations)) {
                OtcAccountUserRelation userRelation = otcAccountUserRelations.get(0);
                List<User> userList = userListAll.stream().filter(user -> StringUtils.isNotBlank(user.getSystemUserId()) && user.getSystemUserId().equals(addOtcAccountChainRel.getSystemUserId())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(userList)) {
                    userRelation.setSystemUserId(addOtcAccountChainRel.getSystemUserId());
                    userRelation.setUserId(userList.get(0).getId());
                    userRelation.setOwnerName(addOtcAccountChainRel.getOwnerName());
                    userRelation.setOwnerNumber(addOtcAccountChainRel.getOwnerNumber());
                    userRelation.setModifyDate(LocalDateTime.now());
                    accountUserRelationService.updateById(userRelation);
                    accountRelListHeadsUpdate.add(userRelation);
                    int s = 0;
                }
            } else {
                List<User> userList = userListAll.stream().filter(user -> StringUtils.isNotBlank(user.getSystemUserId()) && user.getSystemUserId().equals(addOtcAccountChainRel.getSystemUserId())).collect(Collectors.toList());
                List<OtcAccount> accountLists = accountListHeads.stream().filter(account -> account.getAccountId().equals(addOtcAccountChainRel.getZongBuId())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(userList) && CollectionUtils.isNotEmpty(accountLists)) {
                    OtcAccountUserRelation userRelation = new OtcAccountUserRelation();
                    userRelation.setAccountId(addOtcAccountChainRel.getAccountId());
                    userRelation.setTerminalId(accountLists.get(0).getId());
                    userRelation.setSystemUserId(addOtcAccountChainRel.getSystemUserId());
                    userRelation.setUserId(userList.get(0).getId());
                    userRelation.setOwnerName(addOtcAccountChainRel.getOwnerName());
                    userRelation.setOwnerNumber(addOtcAccountChainRel.getOwnerNumber());
                    userRelation.setModifyDate(LocalDateTime.now());
                    userRelation.setCreateUserId(1000000000000000000L);
                    userRelation.setModifyUserId(1000000000000000000L);
                    userRelation.setCreateDate(LocalDateTime.now());
                    accountUserRelationService.save(userRelation);
                    accountRelListHeadsInsert.add(userRelation);
                    int s = 0;
                }
            }
        }
        return R.ok("本次连锁总部负责人同步结束");
    }

    /**
     * 更新或新增连锁分部负责人
     *
     * @return
     */
    public R saveOrUpdateChainRelation() {
        List<OtcAccountUserRelation> accountRelListHeadsInsert = new ArrayList<>();
        List<OtcAccountUserRelation> accountRelListHeadsUpdate = new ArrayList<>();
        List<AddOtcAccountChainRelDto> addOtcAccountChainRelDtos = accountSqlServerMapper.getAccountChainRel();
        List<OtcAccount> accountListParts = this.list(Wrappers.lambdaQuery(OtcAccount.class).eq(OtcAccount::getTerminalType, TerminalType.PART.getCode()).orderByDesc(OtcAccount::getId));
        List<User> userListAll = userService.lambdaQuery().eq(User::getDeleteMark, DeleteMark.NODELETE.getCode()).eq(User::getEnabledMark, EnabledMark.ENABLED.getCode()).eq(User::getIsDisabled, 0).list();
        List<OtcAccountUserRelation> otcAccountUserRelationAll = accountUserRelationService.lambdaQuery().eq(OtcAccountUserRelation::getDeleteMark, DeleteMark.NODELETE.getCode()).eq(OtcAccountUserRelation::getEnabledMark, EnabledMark.ENABLED.getCode()).list();

        for (AddOtcAccountChainRelDto addOtcAccountChainRel : addOtcAccountChainRelDtos) {
            List<OtcAccountUserRelation> otcAccountUserRelations = otcAccountUserRelationAll.stream().filter(otcAccountUserRelation -> otcAccountUserRelation.getAccountId().equals(addOtcAccountChainRel.getAccountId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(otcAccountUserRelations)) {
                OtcAccountUserRelation userRelation = otcAccountUserRelations.get(0);
                List<User> userList = userListAll.stream().filter(user -> StringUtils.isNotBlank(user.getSystemUserId()) && user.getSystemUserId().equals(addOtcAccountChainRel.getSystemUserId())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(userList)) {
                    userRelation.setSystemUserId(addOtcAccountChainRel.getSystemUserId());
                    userRelation.setUserId(userList.get(0).getId());
                    userRelation.setOwnerName(addOtcAccountChainRel.getOwnerName());
                    userRelation.setOwnerNumber(addOtcAccountChainRel.getOwnerNumber());
                    userRelation.setModifyDate(LocalDateTime.now());
                    accountUserRelationService.updateById(userRelation);
                    accountRelListHeadsUpdate.add(userRelation);
                }
            } else {
                List<User> userList = userListAll.stream().filter(user -> StringUtils.isNotBlank(user.getSystemUserId()) && user.getSystemUserId().equals(addOtcAccountChainRel.getSystemUserId())).collect(Collectors.toList());
                List<OtcAccount> accountLists = accountListParts.stream().filter(account -> account.getAccountId().equals(addOtcAccountChainRel.getAccountId())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(userList) && CollectionUtils.isNotEmpty(accountLists)) {
                    OtcAccountUserRelation userRelation = new OtcAccountUserRelation();
                    userRelation.setAccountId(addOtcAccountChainRel.getAccountId());
                    userRelation.setTerminalId(accountLists.get(0).getId());
                    userRelation.setSystemUserId(addOtcAccountChainRel.getSystemUserId());
                    userRelation.setUserId(userList.get(0).getId());
                    userRelation.setOwnerName(addOtcAccountChainRel.getOwnerName());
                    userRelation.setOwnerNumber(addOtcAccountChainRel.getOwnerNumber());
                    userRelation.setModifyDate(LocalDateTime.now());
                    userRelation.setCreateUserId(1000000000000000000L);
                    userRelation.setModifyUserId(1000000000000000000L);
                    userRelation.setCreateDate(LocalDateTime.now());
                    accountUserRelationService.save(userRelation);
                    accountRelListHeadsInsert.add(userRelation);
                }
            }
        }
        return R.ok("本次连锁分部负责人同步结束");
    }

    /**
     * 新增客户同步到OTC系统
     *
     * @param accountNum
     * @return
     */
    @Override
    public R addSyncAccount(String accountNum, String userCode) {
        List<OtcAccount> accountsToInsert = new ArrayList<>();
        List<OtcAccountUserRelation> otcAccountUserRelations = new ArrayList<>();

        List<String> accountNums = Arrays.asList(accountNum.split(","));
        List<AddOtcAccountDto> addOtcAccountDtos = accountSqlServerMapper.syncAccountByAccountNums(accountNums);
        List<User> userList = userService.list();
        Map<String, User> userMap = userList.stream().collect(Collectors.toMap(User::getCode, User -> User,
                (existing, replacement) -> existing));
        for (AddOtcAccountDto addOtcAccountDto : addOtcAccountDtos) {
            OtcAccount otcAccountEntity = BeanUtil.copyProperties(addOtcAccountDto, OtcAccount.class);
            otcAccountEntity.setTerminalType(TerminalType.STORE.getCode());
            otcAccountEntity.setCreateDate(LocalDateTime.now());
            this.save(otcAccountEntity);
            accountsToInsert.add(otcAccountEntity);

            OtcAccountUserRelation userRelation = new OtcAccountUserRelation();
            userRelation.setAccountId(otcAccountEntity.getAccountId());
            userRelation.setTerminalId(otcAccountEntity.getId());
            userRelation.setSystemUserId(userMap.getOrDefault(userCode, userList.get(0)).getSystemUserId());
            userRelation.setUserId(userMap.getOrDefault(userCode, userList.get(0)).getId());
            userRelation.setOwnerName(userMap.getOrDefault(userCode, userList.get(0)).getName());
            userRelation.setOwnerNumber(userCode);
            userRelation.setModifyDate(LocalDateTime.now());
            userRelation.setCreateUserId(1000000000000000000L);
            userRelation.setModifyUserId(1000000000000000000L);
            userRelation.setCreateDate(LocalDateTime.now());
            otcAccountUserRelations.add(userRelation);
            accountUserRelationService.save(userRelation);

        }
        // this.saveBatch(accountsToInsert);
        return R.ok("本次新增客户同步到OTC系统");
    }

    /**
     * 更新客户以及客户代表关系 同步到OTC系统
     *
     * @param newIdList
     * @return
     */
    @Override
    public R updateSyncAccountAndRelation(List<String> newIdList) {

        List<AddOtcAccountDto> accountsNotExists = new ArrayList<>();
        List<AddOtcAccountProductRelDto> accountsRelNotExists = new ArrayList<>();
        List<String> newIds = newIdList;
        Integer batchSize = 1000;
        int batch = (int) Math.ceil((double) newIds.size() / batchSize);
        for (int start = 0; start < batch; start++) {
            List<OtcAccount> accountsToUpdate = new ArrayList<>();
            List<OtcAccountUserRelation> otcAccountUserRelationToUpdate = new ArrayList<>();
            int end = Math.min(start + batchSize, newIds.size());
            List<String> batchIds = newIds.subList(start, end);
            List<AddOtcAccountProductRelDto> addOtcAccountProductRels = accountSqlServerMapper.syncAccountUserRelByNewId(batchIds);
            List<String> otcAccountProductRelAccountIds = addOtcAccountProductRels.stream().map(AddOtcAccountProductRelDto::getAccountId).collect(Collectors.toList());
            List<AddOtcAccountDto> addOtcAccountDtos = accountSqlServerMapper.syncAccountByIds(otcAccountProductRelAccountIds);

            // 获取现有 account 集合
            Map<String, Long> existingAccountIdsMap = this.list().stream().collect(Collectors.toMap(OtcAccount::getAccountId, // 键
                    OtcAccount::getId, // 值
                    (existingValue, newValue) -> existingValue // 合并函数，取第一个值
            ));
            // 获取现有 AccountUserRelation 集合
            List<OtcAccountUserRelation> otcAccountUserRelations = accountUserRelationService.list();
            Map<String, OtcAccountUserRelation> existingAccountUserRelIdsMap = otcAccountUserRelations.stream().collect(Collectors.toMap(OtcAccountUserRelation::getAccountId, // 键
                    OtcAccountUserRelation -> OtcAccountUserRelation, // 值
                    (existingValue, newValue) -> existingValue // 合并函数，取第一个值
            ));

            for (AddOtcAccountDto addOtcAccountDto : addOtcAccountDtos) {
                OtcAccount otcAccountEntity = BeanUtil.copyProperties(addOtcAccountDto, OtcAccount.class);
                if ((existingAccountIdsMap.containsKey(otcAccountEntity.getAccountId()))) {
                    otcAccountEntity.setId(existingAccountIdsMap.get(otcAccountEntity.getAccountId()));
                    accountsToUpdate.add(otcAccountEntity);
                } else {
                    accountsNotExists.add(addOtcAccountDto);
                }
            }
            this.updateBatchById(accountsToUpdate);

            List<User> userList = userService.list();
            Map<String, User> userMap = userList.stream().collect(Collectors.toMap(User::getSystemUserId, // 键
                    User -> User, // 值
                    (existingValue, newValue) -> existingValue // 合并函数，取第一个值
            ));
            for (AddOtcAccountProductRelDto addOtcAccountProductRel : addOtcAccountProductRels) {
                //todo 此处应该改为关系表有就变更没有的话就新增（代表为管理员除外不用新增）
                if (existingAccountUserRelIdsMap.containsKey(addOtcAccountProductRel.getAccountId())) {
                    OtcAccountUserRelation userRelation = new OtcAccountUserRelation();
                    userRelation.setId(existingAccountUserRelIdsMap.get(addOtcAccountProductRel.getAccountId()).getId());
                    userRelation.setAccountId(addOtcAccountProductRel.getAccountId());
                    userRelation.setTerminalId(existingAccountUserRelIdsMap.get(addOtcAccountProductRel.getAccountId()).getTerminalId());
                    userRelation.setSystemUserId(addOtcAccountProductRel.getDsrId());
                    userRelation.setUserId(userMap.getOrDefault(addOtcAccountProductRel.getDsrId(), userList.get(0)).getId());
                    userRelation.setOwnerName(userMap.getOrDefault(addOtcAccountProductRel.getDsrId(), userList.get(0)).getName());
                    userRelation.setOwnerNumber(userMap.getOrDefault(addOtcAccountProductRel.getDsrId(), userList.get(0)).getCode());
                    userRelation.setModifyDate(LocalDateTime.now());
                    userRelation.setCreateUserId(1000000000000000000L);
                    userRelation.setModifyUserId(1000000000000000000L);
                    userRelation.setCreateDate(LocalDateTime.now());
                    otcAccountUserRelationToUpdate.add(userRelation);
                } else {
                    accountsRelNotExists.add(addOtcAccountProductRel);
                }
            }
            accountUserRelationService.updateBatchById(otcAccountUserRelationToUpdate);
        }

        return R.ok("本次更新客户以及客户代表关系 同步到OTC系统");
    }


    @Override
    public R syncAccountLevelImport(List<AccountLevelImportDto> accountLevelImportDtos) {
        List<OtcCustomerTag> otcCustomerTags = customerTagService.list();
        Map<String, Long> idToTagNameMap = otcCustomerTags.stream().collect(Collectors.toMap(OtcCustomerTag::getTagName, // value: tagName,
                OtcCustomerTag::getId // key: id
        ));
        List<OtcCustomerTagRel> otcCustomerTagRels = new ArrayList<>();
        for (AccountLevelImportDto accountLevelImportDto : accountLevelImportDtos) {
            if (accountLevelImportDto.getAccountLevelName() != null) {
                OtcAccount otcAccount = null;
                // OtcAccount otcAccounts1 = this.getOne(Wrappers.lambdaQuery(OtcAccount.class).eq(OtcAccount::getName, accountLevelImportDto.getName()));
                List<OtcAccount> otcAccounts = this.list(Wrappers.lambdaQuery(OtcAccount.class).eq(OtcAccount::getName, accountLevelImportDto.getName()));


                if (otcAccounts.size() > 0 && otcAccounts.get(0) != null) {
                    otcAccount = otcAccounts.stream().filter(x -> x.getName().equals(accountLevelImportDto.getName())).collect(Collectors.toList()).get(0);
                }
                if (otcAccount != null) {
                    OtcCustomerTagRel otcCustomerTagRel = new OtcCustomerTagRel();
                    // 设置字段值（根据你的业务逻辑填写）
                    otcCustomerTagRel.setCustomerTagId(idToTagNameMap.getOrDefault(accountLevelImportDto.getAccountLevelName(), 0l)); // 客户标签ID
                    otcCustomerTagRel.setAccountId(otcAccount.getId()); // 门店ID
                    otcCustomerTagRels.add(otcCustomerTagRel);
                    customerTagRelService.save(otcCustomerTagRel);
                }
            }
        }


        return R.ok();
    }


    @Override
    public R saveOrUpdateAccount() {
        // 获取最大日期

        //List<OtcAccount> accountList1= this.list(Wrappers.lambdaQuery(OtcAccount.class).select(OtcAccount::getCreatedDate).eq(OtcAccount::getTerminalType, TerminalType.STORE.getCode()).ne(OtcAccount::getCreatedDate, null));
        String date = this.list(Wrappers.lambdaQuery(OtcAccount.class).select(OtcAccount::getCreatedDate).eq(OtcAccount::getTerminalType, TerminalType.STORE.getCode()).isNotNull(OtcAccount::getCreatedDate)).stream() // 确保 createdDate 不为 null).stream()
                .map(OtcAccount::getCreatedDate).filter(Objects::nonNull) // 过滤掉 null 值
                .max(LocalDateTime::compareTo).map(LocalDateTime::toString).orElse(null);
        // 获取终端的客户数据
        List<AddOtcAccountDto> accountList = accountSqlServerMapper.syncAccount(NEW_SHIC_JB, date);

        if (accountList == null || accountList.isEmpty()) {
            return R.ok("无新增数据", null);
        }
        int count = saveOrUpdateAccount(accountList);

        //TODO 同步数据到业务系统
        return R.ok("本次同步：" + 1 + "条数据", null);
    }

    private int saveOrUpdateAccount(List<AddOtcAccountDto> accountList) {
        //获得已存在的客户信息
        List<OtcAccount> accountLists = this.list(Wrappers.lambdaQuery(OtcAccount.class).eq(OtcAccount::getDeleteMark, DeleteMark.NODELETE.getCode()).eq(OtcAccount::getEnabledMark, EnabledMark.ENABLED.getCode()));
        // 准备插入和更新
        List<OtcAccount> accountsToInsert = new ArrayList<>();
        List<OtcAccount> accountsToUpdate = new ArrayList<>();
        List<AddOtcAccountDto> allAccountList = accountSqlServerMapper.syncAllAccount(NEW_SHIC_JB, null);
        Map<String, AddOtcAccountDto> allAccountListMap = allAccountList.stream().collect(Collectors.toMap(
                AddOtcAccountDto::getAccountId,
                addOtcAccountDto -> addOtcAccountDto,
                (existing, replacement) -> existing
        ));
        //获得所有总部信息
        List<AddOtcAccountChainRelDto> otcAccountZongBuDtos = accountSqlServerMapper.getAccountZongBuRel();
        List<String> headList = otcAccountZongBuDtos.stream().map(AddOtcAccountChainRelDto::getAccountId).collect(Collectors.toList());
        List<OtcAccount> accountListHeads = accountLists.stream().filter(x -> x.getTerminalType().toString().equals(String.valueOf(TerminalType.HEAD.getCode()))).collect(Collectors.toList());
        for (AddOtcAccountChainRelDto entry : otcAccountZongBuDtos) {
            //已存在的不用管
            if (accountListHeads.stream().anyMatch(x -> x.getAccountId().equals(entry.getZongBuId()))) {
                continue;
            }
            OtcAccount otcAccountEntity = new OtcAccount();
            otcAccountEntity.setAccountId(entry.getZongBuId());
            otcAccountEntity.setName(entry.getAccountName());
            otcAccountEntity.setTerminalType(TerminalType.HEAD.getCode());
            otcAccountEntity.setCreateDate(LocalDateTime.now());
            accountsToInsert.add(otcAccountEntity);

        }
        accountList = accountList.stream().filter(oo -> !headList.contains(oo.getAccountId())).collect(Collectors.toList());

        //获得所有分部信息
        Map<String, Map<String, String>> otcAllAccountMap = allAccountList.stream().filter(acc -> (StringUtils.isNotBlank(acc.getParentAccountId()) && StringUtils.isNotBlank(acc.getNewOtcAccountId()))).collect(Collectors.toMap(i -> i.getParentAccountId(), i -> {
            Map<String, String> accMap = new HashMap<>();
            accMap.put("newOtcAccountId", i.getNewOtcAccountId());
            accMap.put("newOtcAccountName", i.getNewOtcAccountName());
            return accMap;
        }, (existing, replacement) -> {
            // 这里可以根据实际需求来决定如何合并两个冲突的值
            // 例如，可以选择保留旧的值
            return existing;
        }));

        List<AddOtcAccountChainRelDto> addOtcAccountChainDtos = accountSqlServerMapper.getAccountChainRel();
        List<OtcAccount> accountListParts = accountLists.stream().filter(x -> x.getTerminalType().toString().equals(String.valueOf(TerminalType.PART.getCode()))).collect(Collectors.toList());
        List<String> partList = addOtcAccountChainDtos.stream().map(AddOtcAccountChainRelDto::getAccountId).collect(Collectors.toList());
        for (AddOtcAccountChainRelDto entry : addOtcAccountChainDtos) {
            if (accountListParts.stream().anyMatch(x -> x.getAccountId().equals(entry.getAccountId()))) {
                continue;
            }
            Map<String, String> mapFenToZong = otcAllAccountMap.get(entry.getAccountId());
            OtcAccount otcAccountEntity = new OtcAccount();
            otcAccountEntity.setAccountId(entry.getAccountId());
            otcAccountEntity.setName(entry.getAccountName());
            otcAccountEntity.setTerminalType(TerminalType.PART.getCode());
            if (null != mapFenToZong && !mapFenToZong.isEmpty()) {
                otcAccountEntity.setNewOtcAccountId(mapFenToZong.get("newOtcAccountId"));
                otcAccountEntity.setNewOtcAccountName(mapFenToZong.get("newOtcAccountName"));
            }
            otcAccountEntity.setCreateDate(LocalDateTime.now());
            otcAccountEntity.setModifyDate(LocalDateTime.now());
            accountsToInsert.add(otcAccountEntity);
        }

        Map<String, OtcAccount> otcAccountInsertListMap = accountsToInsert.stream().collect(Collectors.toMap(
                OtcAccount::getAccountId,
                otcAccountDto -> otcAccountDto,
                (existing, replacement) -> existing
        ));

        //处理分部做为单独的一列放在account里
        for (AddOtcAccountChainRelDto entry : addOtcAccountChainDtos) {
            if (accountListParts.stream().anyMatch(x -> x.getAccountId().equals(entry.getAccountId()))) {
                continue;
            }
            AddOtcAccountDto otcAccountDto = allAccountListMap.get(entry.getAccountId());
            if (ObjectUtil.isNotEmpty(otcAccountDto)) {
                OtcAccount otcAccountEntity = BeanUtil.copyProperties(otcAccountDto, OtcAccount.class);
                if (Objects.isNull(otcAccountEntity.getParentAccountId()) || Objects.isNull(otcAccountEntity.getParentAccountIdName())) {
                    OtcAccount otcAccount = otcAccountInsertListMap.get(entry.getAccountId());
                    otcAccountEntity.setNewOtcAccountId(otcAccount.getParentAccountId());
                    otcAccountEntity.setNewOtcAccountName(otcAccount.getParentAccountIdName());
                }
                otcAccountEntity.setCreateDate(LocalDateTime.now());
                otcAccountEntity.setModifyDate(LocalDateTime.now());
                otcAccountEntity.setTerminalType(TerminalType.PART.getCode());
                accountsToInsert.removeIf(n -> n.getAccountId().equals(entry.getAccountId()));
                accountsToInsert.add(otcAccountEntity);
            }
        }
        accountList = accountList.stream().filter(oo -> !partList.contains(oo.getAccountId())).collect(Collectors.toList());

        //获得所有门店信息
        List<OtcAccount> accountListStores = accountLists.stream().filter(x -> x.getTerminalType().toString().equals(String.valueOf(TerminalType.STORE.getCode()))).collect(Collectors.toList());
        for (AddOtcAccountDto account : accountList) {
            if (accountListStores.stream().anyMatch(x -> x.getAccountId().equals(account.getAccountId()))) {
                Optional<OtcAccount> matchedAccount = accountListStores.stream().filter(x -> x.getAccountId().equals(account.getAccountId())).findFirst();
                if (matchedAccount.isPresent() && (Objects.nonNull(account.getCity()) || Objects.nonNull(account.getNewTypeOfOperationIdName()))) {
                    OtcAccount otcAccountEntity = new OtcAccount();
                    otcAccountEntity.setId(matchedAccount.get().getId());
                    otcAccountEntity.setModifyDate(LocalDateTime.now());
                    if (Objects.nonNull(account.getCity())) {
                        otcAccountEntity.setCity(account.getCity());
                    }
                    if (Objects.nonNull(account.getNewTypeOfOperationIdName())) {
                        otcAccountEntity.setNewTypeOfOperationIdName(account.getNewTypeOfOperationIdName());
                    }
                    accountsToUpdate.add(otcAccountEntity);
                }
            } else {
                OtcAccount otcAccountEntity = BeanUtil.copyProperties(account, OtcAccount.class);
                otcAccountEntity.setTerminalType(TerminalType.STORE.getCode());
                otcAccountEntity.setCreateDate(LocalDateTime.now());
                otcAccountEntity.setModifyDate(LocalDateTime.now());
                accountsToInsert.add(otcAccountEntity);
            }
        }

        // 批量插入和更新
        insertOrUpdateAccountsAsync(accountsToInsert, accountsToUpdate);
        return accountsToInsert.size();
    }


}
