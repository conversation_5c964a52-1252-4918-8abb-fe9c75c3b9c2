package com.zilue.module.business.salesperformance.service;

import com.zilue.module.business.salesperformance.entity.OtcSalesmanSalesPerformance;

import java.util.List;

/**
 * @title: service
 * <AUTHOR>
 * @Date: 2025-01-15
 * @Version 1.0
 */
public interface ISalesPerformanceService {
    List<OtcSalesmanSalesPerformance> queryPerformance(String startDay, String endDay);

    void save(List<OtcSalesmanSalesPerformance> list);

    /**
     * 按连锁总部汇总
     *
     * @param startDay
     * @param endDay
     * @return
     */
    List<OtcSalesmanSalesPerformance> queryPerformanceGroupByHeardStore(String startDay, String endDay);

    /**
     * edp业绩分页查询
     *
     * @param syncTime
     * @param page
     * @param pageSize
     * @return
     */
    List<OtcSalesmanSalesPerformance> queryList(String syncTime, int page, int pageSize);

    /**
     * edp业绩count
     *
     * @param syncTime
     * @return
     */
    Integer queryCount(String syncTime);

    /**
     * 查询最大的同步时间
     *
     * @param type
     * @return
     */
    String getMaxSyncTime(String type);

    /**
     * @param type
     * @param syncTime
     * @param batchId
     * @param createTime
     */
    void saveSyncRecordTime(String type, String syncTime, long batchId, String createTime);


    /**
     * 同步
     */
    void syncPerformance(String syncTime);
}
