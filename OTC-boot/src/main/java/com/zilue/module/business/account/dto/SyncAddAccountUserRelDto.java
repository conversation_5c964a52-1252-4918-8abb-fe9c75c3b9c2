package com.zilue.module.business.account.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * 同步到中间库的实体类
 */
@Data
@Builder
public class SyncAddAccountUserRelDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 目标客户ID
     */
    @ApiModelProperty("目标客户ID")
    private String accountId;

    /**
     * 终端负责人ID
     */
    @ApiModelProperty("终端负责人ID")
    private String systemUserId;
}
