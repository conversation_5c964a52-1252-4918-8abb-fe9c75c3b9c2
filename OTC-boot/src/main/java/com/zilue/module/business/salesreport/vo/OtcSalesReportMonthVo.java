package com.zilue.module.business.salesreport.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
* @title: 表单出参
* <AUTHOR>
* @Date: 2025-01-15
* @Version 1.0
*/
@Data
public class OtcSalesReportMonthVo {

    /**
    * 主键
    */
    @ApiModelProperty("主键")
    private Long id;
    /**
    * 代表编码
    */
    @ApiModelProperty("代表编码")
    private String userCode;
    /**
    * 月计划值
    */
    @ApiModelProperty("月计划值")
    private BigDecimal planAmount;
    /**
    * 月完成值
    */
    @ApiModelProperty("月完成值")
    private BigDecimal finishAmount;
    /**
    * 业务月份
    */
    @ApiModelProperty("业务月份")
    private String businessMonth;
    /**
    * 去年同期月完成值
    */
    @ApiModelProperty("去年同期月完成值")
    private BigDecimal lastYearPeriodFinishAmount;
    /**
    * 个人排名（所有员工内）
    */
    @ApiModelProperty("个人排名（所有员工内）")
    private Long rankNum;



}
