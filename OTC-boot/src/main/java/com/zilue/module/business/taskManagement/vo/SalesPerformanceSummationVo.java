package com.zilue.module.business.taskManagement.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @program: OTC-boot
 * @description: 合计查询结果返回对象
 * @packagename: com.zilue.module.business.taskManagement.vo
 * @author: 曹凌
 * @date: 2025-01-16 16:26
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SalesPerformanceSummationVo {
    @ApiModelProperty(value = "月计划合计")
    private String totalMonthPlan;
    @ApiModelProperty(value = "月实际合计")
    private String totalMonthActual;
    @ApiModelProperty(value = "月统计合计")
    private String totalMonthStatistics;
}
