package com.zilue.module.business.salesperformance.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.zilue.common.constant.MongoCollectionConstant;
import com.zilue.common.utils.DateUtils;
import com.zilue.common.utils.StringUtil;
import com.zilue.module.business.salesperformance.entity.EdpSyncRecord;
import com.zilue.module.business.salesperformance.entity.OtcSalesmanSalesPerformance;
import com.zilue.module.business.salesperformance.mapper.OtcSalesmanSalesPerformanceMapper;
import com.zilue.module.business.salesperformance.service.ISalesPerformanceService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOptions;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * @title: service
 * <AUTHOR>
 * @Date: 2025-01-15
 * @Version 1.0
 */
@Service
@AllArgsConstructor
@Slf4j
public class SalesPerformanceServiceImpl implements ISalesPerformanceService {
    public static final int pageSize = 4000;
    public static final String YEJI = "yeji";
    private final MongoTemplate mongoTemplate;
    private final OtcSalesmanSalesPerformanceMapper salesmanSalesPerformanceMapper;

    @Override
    public List<OtcSalesmanSalesPerformance> queryPerformance(String startDay, String endDay) {
        Criteria criteria = new Criteria();
        criteria.and("businessDay").gte(startDay).lte(endDay);
        AggregationOptions aggregationOptions = AggregationOptions.builder().allowDiskUse(true).build();
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group("userCode")
                        .first("userCode").as("userCode")
                        .first("businessDay").as("businessDay")
                        .sum("amount").as("amount")
        ).withOptions(aggregationOptions);
        AggregationResults<OtcSalesmanSalesPerformance> result = mongoTemplate.aggregate(aggregation, MongoCollectionConstant.SALES_PERFORMANCE, OtcSalesmanSalesPerformance.class);
        return result.getMappedResults();
    }

    /**
     * 按连锁总部汇总
     *
     * @param startDay
     * @param endDay
     * @return
     */
    @Override
    public List<OtcSalesmanSalesPerformance> queryPerformanceGroupByHeardStore(String startDay, String endDay) {
        Criteria criteria = new Criteria();
        criteria.and("businessDay").gte(startDay).lte(endDay);
        AggregationOptions aggregationOptions = AggregationOptions.builder().allowDiskUse(true).build();
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group("headquartersCode")
                        .first("headquartersCode").as("headquartersCode")
                        .first("businessDay").as("businessDay")
                        .sum("amount").as("amount")
        ).withOptions(aggregationOptions);
        AggregationResults<OtcSalesmanSalesPerformance> result = mongoTemplate.aggregate(aggregation, MongoCollectionConstant.SALES_PERFORMANCE, OtcSalesmanSalesPerformance.class);
        return result.getMappedResults();
    }

    @Override
    public void save(List<OtcSalesmanSalesPerformance> list) {
        List<String> stringList = generateDateRange("2024-01-01", "2025-01-15");
        for (String day : stringList) {

            for (OtcSalesmanSalesPerformance bean : list) {
                bean.setId(null);
                bean.setEdpId(String.valueOf(System.currentTimeMillis()));
                bean.setSyncTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                bean.setAmount(new BigDecimal(getRandomNumber() * 100));
                bean.setBusinessDay(day);
            }
            BulkOperations operations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, MongoCollectionConstant.SALES_PERFORMANCE);
            operations.insert(list);
            operations.execute();

        }


    }


    /**
     * edp业绩分页查询
     *
     * @param syncTime
     * @param page
     * @param pageSize
     * @return
     */
    @Override
    public List<OtcSalesmanSalesPerformance> queryList(String syncTime, int page, int pageSize) {
        return salesmanSalesPerformanceMapper.queryList(syncTime, (page - 1) * pageSize, pageSize);
    }

    /**
     * edp业绩count
     *
     * @param syncTime
     * @return
     */
    @Override
    public Integer queryCount(String syncTime) {
        return salesmanSalesPerformanceMapper.queryCount(syncTime);
    }


    /**
     * 查询最大的同步时间
     *
     * @param type
     * @return
     */
    @Override
    public String getMaxSyncTime(String type) {
        Query query = new Query(Criteria.where("type").is(type));
        query.with(Sort.by(Sort.Direction.DESC, "syncTime"));
        query.limit(1);
        List<EdpSyncRecord> result = mongoTemplate.find(query, EdpSyncRecord.class, MongoCollectionConstant.SYNC_RECORD);
        if (CollectionUtil.isEmpty(result)) {
            return DateUtils.format(new Date());
        }
        return result.get(0).getSyncTime();
    }


    /**
     * 保存本次最大的同步时间
     * @param type
     * @param syncTime
     * @param batchId
     * @param createTime
     */
    @Override
    public void saveSyncRecordTime(String type, String syncTime, long batchId, String createTime) {
        EdpSyncRecord record = new EdpSyncRecord();
        record.setCreateTime(createTime);
        record.setType(type);
        record.setSyncTime(syncTime);
        record.setBatchId(batchId);
        mongoTemplate.save(record,MongoCollectionConstant.SYNC_RECORD);
    }


    /**
     * 同步
     *
     * @param syncTime
     */
    @Override
    public void syncPerformance(String syncTime) {
        if (StringUtil.isBlank(syncTime)) {
            syncTime = getMaxSyncTime(YEJI);
        }
        int count = queryCount(syncTime);
        if (count == 0) {
            log.warn("[业绩数据同步] 同步数据为空，syncTime = {}", syncTime);
            return;
        }
        String maxSyncTime = null;
        int pageCount = count / pageSize;
        long batchId = System.currentTimeMillis();
        String createTime = DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN);
        for (int i = 0; i < pageCount; i++) {
            maxSyncTime = syncPerformanceByBatch(syncTime, i + 1, batchId, createTime);
        }
        long mod = count % pageSize;
        if (mod > 0) {
            maxSyncTime = syncPerformanceByBatch(syncTime, pageCount + 1, batchId, createTime);
        }
        if (maxSyncTime == null) {
            maxSyncTime = createTime;
        }
        saveSyncRecordTime(YEJI, maxSyncTime, batchId, createTime);
    }

    private String syncPerformanceByBatch(String syncTime, int page, long batchId, String createTime) {
        List<OtcSalesmanSalesPerformance> list = queryList(syncTime, page, pageSize);
        if (list.isEmpty()) {
            return null;
        }
        for (OtcSalesmanSalesPerformance bean : list) {
            bean.setBatchId(batchId);
            bean.setCreateTime(createTime);
        }
        BulkOperations operations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, MongoCollectionConstant.SALES_PERFORMANCE);
//        BulkOperations operations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, "otc_salesman_sales_performance_test");
        operations.insert(list);
        operations.execute();
        return list.get(list.size()-1).getSyncTime();
    }

    public static List<String> generateDateRange(String startDateStr, String endDateStr) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate startDate = LocalDate.parse(startDateStr, formatter);
        LocalDate endDate = LocalDate.parse(endDateStr, formatter);

        List<String> dateList = new ArrayList<>();
        while (!startDate.isAfter(endDate)) {
            dateList.add(startDate.format(formatter));
            startDate = startDate.plusDays(1);
        }

        return dateList;
    }

    public static int getRandomNumber() {
        Random random = new Random();
        return random.nextInt(6) + 1;
    }
}
