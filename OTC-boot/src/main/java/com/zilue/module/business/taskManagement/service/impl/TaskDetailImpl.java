package com.zilue.module.business.taskManagement.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.toolkit.MPJWrappers;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.enums.DeleteMark;
import com.zilue.common.enums.TaskTypeEnum;
import com.zilue.common.page.ConventPage;
import com.zilue.common.page.PageOutput;
import com.zilue.common.utils.DateUtils;
import com.zilue.common.utils.RedisUtil;
import com.zilue.common.utils.StringUtil;
import com.zilue.module.business.taskManagement.vo.VisitTotalVo;
import com.zilue.module.business.taskManagement.vo.VisitVo;
import com.zilue.module.business.taskManagement.dto.VisitQueryDto;
import com.zilue.module.business.taskManagement.service.ITaskManagementService;
import com.zilue.module.business.taskManagement.service.TaskDetailService;
import com.zilue.module.business.taskManagement.vo.OtcSalesmanTaskVo;
import com.zilue.module.organization.entity.Department;
import com.zilue.module.organization.entity.User;
import com.zilue.module.organization.entity.UserDeptRelation;
import com.zilue.module.organization.entity.UserPostRelation;
import com.zilue.module.organization.service.IUserService;
import com.zilue.module.report.entity.ProfessionalReport;
import com.zilue.module.report.entity.ReportRelation;
import com.zilue.module.report.vo.ReportReleasePageVo;
import com.zilue.module.wechat.sign.dto.CountUserOctSignDto;
import com.zilue.module.wechat.sign.entity.OtcPersionMatters;
import com.zilue.module.wechat.sign.entity.OtcSignIn;
import com.zilue.module.wechat.sign.mapper.OtcSignInMapper;
import com.zilue.module.wechat.sign.service.IOtcPersionMattersService;
import com.zilue.module.wechat.sign.service.ISignService;
import lombok.AllArgsConstructor;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import java.time.YearMonth;

@Service
@AllArgsConstructor
public class TaskDetailImpl implements TaskDetailService {
    private final RedisUtil redisUtil;
    private final IUserService userService;
    private final ITaskManagementService taskManagementService;
    private final ISignService signService;
    private final IOtcPersionMattersService otcPersionMattersService;
    private final OtcSignInMapper otcSignInMapper;
    private static String SIGINTYPE = "2";

    @Override
    public PageOutput<VisitVo> pageList(VisitQueryDto dto) {
        //1.针对拜访和协防进行区分
        OtcSalesmanTaskVo otcSalesmanTaskVo = taskManagementService.getOtcSalesmanTask(dto.getTaskId());
        List<Department> list = redisUtil.get(GlobalConstant.DEP_CACHE_KEY, new TypeReference<List<Department>>() {
        });
        List<Long> deptIds = this.getDeptIds(dto.getDepartId(), list);
        //2.获取所有相关的人员分页数据
        IPage<VisitVo> page = userService.selectJoinListPage(ConventPage.getPage(dto), VisitVo.class,
                MPJWrappers.<User>lambdaJoin()
                        .distinct()
                        .and(wrapper -> {
                            if (StrUtil.isNotBlank(dto.getEdpCodeOrName())) {
                                wrapper.like(User::getCode, dto.getEdpCodeOrName())
                                        .or()
                                        .like(User::getName, dto.getEdpCodeOrName());
                            } else {
                                wrapper.apply("1=1");
                            }
                        })
                        .in(ObjectUtil.isNotNull(dto.getDepartId()) && CollectionUtil.isNotEmpty(deptIds), UserDeptRelation::getDeptId, deptIds)
                        .in(ObjectUtil.isNotNull(otcSalesmanTaskVo.getPostId()), UserPostRelation::getPostId, Arrays.asList(otcSalesmanTaskVo.getPostId().split(",")))
                        .orderByDesc(User::getCreateDate)
                        .select(User::getId, User::getName, User::getCode, User::getCreateDate)
                        .select(Department::getHierarchy)
                        .leftJoin(UserDeptRelation.class, UserDeptRelation::getUserId, User::getId)
                        .leftJoin(Department.class, Department::getId, UserDeptRelation::getDeptId)
                        .leftJoin(UserPostRelation.class, UserPostRelation::getUserId, User::getId)
                        .selectAs(User::getId, "userId")
                        .selectAs(User::getName, "saleManName")
                        .selectAs(User::getCode, "edpCode")
        );
        List<VisitVo> visitDtoList = page.getRecords();
        if (CollectionUtil.isEmpty(visitDtoList)) {
            return ConventPage.getPageOutput(page);
        }
        //设置目标值，实际值，以及相关百分比
        this.setVisitDto(visitDtoList, otcSalesmanTaskVo, dto);
        //设置机构名称
        this.setDepartNames(visitDtoList, list);
        page.setRecords(visitDtoList);
        return ConventPage.getPageOutput(page);
    }

    private void setDepartNames(List<VisitVo> visitDtoList, List<Department> list) {
        Map<Long, List<Department>> mapDepartment = list.stream().collect(Collectors.groupingBy(Department::getId));
        visitDtoList.stream().forEach(VisitDto -> {
            String[] deparIds = VisitDto.getHierarchy().split("-");
            StringBuffer departNames = new StringBuffer();
            for (int i = 0; i < deparIds.length; i++) {
                if (mapDepartment.containsKey(Long.valueOf(deparIds[i]))) {
                    if (ObjectUtil.isNull(departNames)) {
                        departNames.append(mapDepartment.get(Long.valueOf(deparIds[i])).get(0).getName());
                    } else {
                        departNames.append("/").append(mapDepartment.get(Long.valueOf(deparIds[i])).get(0).getName());
                    }
                }
            }
            VisitDto.setDepartNames(departNames.toString());
        });
    }

    private void setVisitDto(List<VisitVo> visitDtoList, OtcSalesmanTaskVo otcSalesmanTaskVo, VisitQueryDto dto) {

        String taskTypeParentCode = TaskTypeEnum.getParentCodeByCode(otcSalesmanTaskVo.getTaskType());
        //获取所有主键ids
        List<Long> userIds = visitDtoList.stream().map(VisitVo::getUserId).collect(Collectors.toList());
        //门店拜访里面的代表行为统计
        if (taskTypeParentCode.equals(TaskTypeEnum.VISITSHOP.getCode())) {
            String taskTypeMattersTypeCode = TaskTypeEnum.getMattersTypeCode(otcSalesmanTaskVo.getTaskType());

            List<OtcPersionMatters> otcPersionMatters = otcPersionMattersService.selectJoinList(OtcPersionMatters.class, MPJWrappers.<OtcPersionMatters>lambdaJoin()
                    .in(ObjectUtil.isNotEmpty(userIds), OtcPersionMatters::getPersonId, userIds)
                    .eq(ObjectUtil.isNotEmpty(taskTypeMattersTypeCode), OtcPersionMatters::getMattersType, taskTypeMattersTypeCode)
                    .between(ObjectUtil.isNotEmpty(dto.getStartTime()) && ObjectUtil.isNotEmpty(dto.getEndTime()), OtcPersionMatters::getCreateDate, dto.getStartTime(), dto.getEndTime())
                    .orderByAsc(OtcPersionMatters::getCreateDate)
                    .leftJoin(OtcSignIn.class, OtcSignIn::getSerialNumber, OtcPersionMatters::getSerialNumber)
                    .eq(OtcSignIn::getSiginType, SIGINTYPE));
            Map<Long, Integer> finalMap = otcPersionMatters.stream().collect(Collectors.groupingBy(OtcPersionMatters::getPersonId, Collectors.collectingAndThen(Collectors.counting(), Long::intValue)));
            setvisitDtoList(visitDtoList, otcSalesmanTaskVo, dto, finalMap);
        } else {
            List<CountUserOctSignDto> countUserOctSignDtos = otcSignInMapper.selectUserSignStatistics(otcSalesmanTaskVo.getTaskType(), userIds, dto.getStartTime(), dto.getEndTime());
            Map<Long, Integer> finalMap = countUserOctSignDtos.stream().collect(Collectors.toMap(CountUserOctSignDto::getUserId, CountUserOctSignDto::getSignCount));
            setvisitDtoList(visitDtoList, otcSalesmanTaskVo, dto, finalMap);
        }
    }

    private void setvisitDtoList(List<VisitVo> visitDtoList, OtcSalesmanTaskVo otcSalesmanTaskVo, VisitQueryDto dto, Map<Long, Integer> finalMap) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        visitDtoList.forEach(visitDto -> {
            visitDto.setReal(finalMap.getOrDefault(visitDto.getUserId(), 0));
            if (otcSalesmanTaskVo.getFrequency().equals(GlobalConstant.daily)
                    && StringUtil.isNotBlank(dto.getStartTime())
                    && StringUtil.isNotBlank(dto.getEndTime())
            ) {
                // 解析字符串为 LocalDate
                LocalDate startDate = LocalDate.parse(dto.getStartTime().substring(0, 10), formatter);
                LocalDate endDate = LocalDate.parse(dto.getEndTime().substring(0, 10), formatter);
                // 计算天数差
                Long daysDifference = ChronoUnit.DAYS.between(startDate, endDate) + 1;
                visitDto.setTarget(otcSalesmanTaskVo.getGoal() * daysDifference.intValue());
                double percentage = (double) visitDto.getReal() / visitDto.getTarget() * 100;
                String formattedPercentage = String.format("%.2f%%", percentage);
                visitDto.setCompletionRate(formattedPercentage);
            } else if (otcSalesmanTaskVo.getFrequency().equals(GlobalConstant.monthly)
                    && StringUtil.isNotBlank(dto.getStartTime())
                    && StringUtil.isNotBlank(dto.getEndTime())
            ) {
                // 解析字符串为 LocalDate
                LocalDate startDate = LocalDate.parse(dto.getStartTime().substring(0, 10), formatter);
                LocalDate endDate = LocalDate.parse(dto.getEndTime().substring(0, 10), formatter);

                // 转换为 YearMonth
                YearMonth startYM = YearMonth.from(startDate);
                YearMonth endYM = YearMonth.from(endDate);

                // 计算自然月差
                Long monthsDifference = ChronoUnit.MONTHS.between(startYM.atDay(1), endYM.atDay(1)) + 1;
                visitDto.setTarget(otcSalesmanTaskVo.getGoal() * monthsDifference.intValue());
                double percentage = (double) visitDto.getReal() / visitDto.getTarget() * 100;
                String formattedPercentage = String.format("%.2f%%", percentage);
                visitDto.setCompletionRate(formattedPercentage);
            }
        });

    }

    private List<Long> getDeptIds(Long departId, List<Department> list) {
        //查询组织架构树
        List<Long> deptIds = new ArrayList<>();
        if (ObjectUtil.isNotNull(departId)) {

            //当前部门的层级
            String hierarchy = list.stream().filter(x -> x.getId().equals(departId)).findFirst().orElse(new Department()).getHierarchy();
            if (StrUtil.isNotBlank(hierarchy)) {
                //层级里面包含当前部门层级的就是它的子集，如1-1，下面包含了1-1、1-1-2这种
                deptIds = list.stream().filter(x -> StrUtil.isNotBlank(x.getHierarchy()) && x.getHierarchy().contains(hierarchy)).map(Department::getId).collect(Collectors.toList());
            } else {
                //如果不存在层级就查询自己的数据
                deptIds.add(departId);
            }
        }
        return deptIds;
    }

    @Override
    public List<VisitVo> exportList(VisitQueryDto dto) {
        //1.针对拜访和协防进行区分
        OtcSalesmanTaskVo otcSalesmanTaskVo = taskManagementService.getOtcSalesmanTask(dto.getTaskId());
        List<Department> list = redisUtil.get(GlobalConstant.DEP_CACHE_KEY, new TypeReference<List<Department>>() {
        });
        List<Long> deptIds = this.getDeptIds(dto.getDepartId(), list);
        //2.获取所有相关的人员数据
        List<VisitVo> visitDtoList = userService.selectJoinList(VisitVo.class,
                MPJWrappers.<User>lambdaJoin()
                        .distinct()
                        .and(wrapper -> {
                            if (StrUtil.isNotBlank(dto.getEdpCodeOrName())) {
                                wrapper.like(User::getCode, dto.getEdpCodeOrName())
                                        .or()
                                        .like(User::getName, dto.getEdpCodeOrName());
                            } else {
                                wrapper.apply("1=1");
                            }
                        })
                        .in(ObjectUtil.isNotNull(dto.getDepartId()), UserDeptRelation::getDeptId, deptIds)
                        .in(ObjectUtil.isNotNull(otcSalesmanTaskVo.getPostId()), UserPostRelation::getPostId, Arrays.asList(otcSalesmanTaskVo.getPostId().split(",")))
                        .orderByDesc(User::getCreateDate)
                        .select(User::getId, User::getName, User::getCode, User::getCreateDate)
                        .select(Department::getHierarchy)
                        //.select(User.class, x -> VoToColumnUtil.fieldsToColumns(VisitDto.class).contains(x.getProperty()))
                        .leftJoin(UserDeptRelation.class, UserDeptRelation::getUserId, User::getId)
                        .leftJoin(Department.class, Department::getId, UserDeptRelation::getDeptId)
                        .leftJoin(UserPostRelation.class, UserPostRelation::getUserId, User::getId)
                        .selectAs(User::getId, "userId")
                        .selectAs(User::getName, "saleManName")
                        .selectAs(User::getCode, "edpCode")
        );
        //设置目标值，实际值，以及相关百分比
        this.setVisitDto(visitDtoList, otcSalesmanTaskVo, dto);
        //设置机构名称
        this.setDepartNames(visitDtoList, list);
        return visitDtoList;
    }

    @Override
    public VisitTotalVo visitTotal(VisitQueryDto dto) {
        VisitTotalVo visitTotalVo = new VisitTotalVo();
        //1.针对拜访和协防进行区分
        OtcSalesmanTaskVo otcSalesmanTaskVo = taskManagementService.getOtcSalesmanTask(dto.getTaskId());
        List<Department> list = redisUtil.get(GlobalConstant.DEP_CACHE_KEY, new TypeReference<List<Department>>() {
        });
        List<Long> deptIds = this.getDeptIds(dto.getDepartId(), list);
        //2.获取所有相关的人员分页数据
        List<VisitVo> visitDtoList = userService.selectJoinList(VisitVo.class,
                MPJWrappers.<User>lambdaJoin()
                        .distinct()
                        .and(wrapper -> {
                            if (StrUtil.isNotBlank(dto.getEdpCodeOrName())) {
                                wrapper.like(User::getCode, dto.getEdpCodeOrName())
                                        .or()
                                        .like(User::getName, dto.getEdpCodeOrName());
                            } else {
                                wrapper.apply("1=1");
                            }
                        })
                        .in(ObjectUtil.isNotNull(dto.getDepartId()), UserDeptRelation::getDeptId, deptIds)
                        .in(ObjectUtil.isNotNull(otcSalesmanTaskVo.getPostId()), UserPostRelation::getPostId, Arrays.asList(otcSalesmanTaskVo.getPostId().split(",")))
                        .orderByDesc(User::getCreateDate)
                        .select(User::getId, User::getName, User::getCode, User::getCreateDate)
                        .select(Department::getHierarchy)
                        //.select(User.class, x -> VoToColumnUtil.fieldsToColumns(VisitDto.class).contains(x.getProperty()))
                        .leftJoin(UserDeptRelation.class, UserDeptRelation::getUserId, User::getId)
                        .leftJoin(Department.class, Department::getId, UserDeptRelation::getDeptId)
                        .leftJoin(UserPostRelation.class, UserPostRelation::getUserId, User::getId)
                        .selectAs(User::getId, "userId")
                        .selectAs(User::getName, "saleManName")
                        .selectAs(User::getCode, "edpCode")
        );
        //设置目标值，实际值，以及相关百分比
        Set<Long> userIds = visitDtoList.stream().map(VisitVo::getUserId).collect(Collectors.toSet());
        Long count = 0L;
        Map<Long, List<OtcSignIn>> map = new HashMap<>();
        //设置目标值，实际值以及相关百分比
        LambdaQueryWrapper<OtcSignIn> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OtcSignIn::getCategory, otcSalesmanTaskVo.getTaskType())
                .eq(OtcSignIn::getSiginType, SIGINTYPE)
                .eq(OtcSignIn::getDeleteMark, DeleteMark.NODELETE.getCode())
                .in(CollectionUtil.isNotEmpty(userIds), OtcSignIn::getPersonId, userIds);
        if (ObjectUtil.isNotNull(dto.getStartTime())) {
            queryWrapper.apply("create_date >= {0}", dto.getStartTime());
        }
        if (ObjectUtil.isNotNull(dto.getEndTime())) {
            queryWrapper.apply("create_date <= {0}", dto.getEndTime());
        }
        if (CollectionUtil.isNotEmpty(userIds)) {
            count = signService.count(queryWrapper);
        }
        visitTotalVo.setReal(Long.valueOf(count).intValue());
        if (otcSalesmanTaskVo.getFrequency().equals(GlobalConstant.daily)
                && StringUtil.isNotBlank(dto.getStartTime())
                && StringUtil.isNotBlank(dto.getEndTime())
                && dto.getStartTime().substring(0, 10).equals(dto.getEndTime().substring(0, 10))) {
            visitTotalVo.setTarget(otcSalesmanTaskVo.getGoal() * userIds.size());
            double percentage = (double) visitTotalVo.getReal() / visitTotalVo.getTarget() * 100;
            String formattedPercentage = String.format("%.2f%%", percentage);
            visitTotalVo.setCompletionRate(formattedPercentage);
        } else if (otcSalesmanTaskVo.getFrequency().equals(GlobalConstant.monthly)
                && StringUtil.isNotBlank(dto.getStartTime())
                && StringUtil.isNotBlank(dto.getEndTime())
                && dto.getStartTime().substring(0, 7).equals(dto.getEndTime().substring(0, 7))) {
            visitTotalVo.setTarget(otcSalesmanTaskVo.getGoal() * userIds.size());
            double percentage = (double) visitTotalVo.getReal() / visitTotalVo.getTarget() * 100;
            String formattedPercentage = String.format("%.2f%%", percentage);
            visitTotalVo.setCompletionRate(formattedPercentage);
        }
        return visitTotalVo;
    }

}
