package com.zilue.module.business.signreached.dto;

import com.zilue.common.page.PageInput;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalTime;
import java.time.LocalDateTime;
import java.math.BigDecimal;


/**
* @title: 分页查询入参
* <AUTHOR>
* @Date: 2025-01-17
* @Version 1.0
*/
@Data
@EqualsAndHashCode(callSuper = false)
public class OtcSalesReportSignReachedPageDto extends PageInput {

    /**
    * 
    */
    @ApiModelProperty("")
    private Long id;
    /**
    * 部门id
    */
    @ApiModelProperty("部门id")
    private Long deptId;
    /**
    * 上级部门id
    */
    @ApiModelProperty("上级部门id")
    private Long parentDeptId;
    /**
    * 达成人数
    */
    @ApiModelProperty("达成人数")
    private Integer reachNum;
    /**
    * 部门人数
    */
    @ApiModelProperty("部门人数")
    private Integer deptPersonNum;
    /**
    * 达成率
    */
    @ApiModelProperty("达成率")
    private BigDecimal reachRate;
    /**
    * 部门排名（同级部门中）
    */
    @ApiModelProperty("部门排名（同级部门中）")
    private Long rankNum;
    /**
    * 统计日期字段开始时间
    */
    @ApiModelProperty("统计日期字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reportDateStart;
    /**
    * 统计日期字段结束时间
    */
    @ApiModelProperty("统计日期字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reportDateEnd;
    /**
    * 创建人ID
    */
    @ApiModelProperty("创建人ID")
    private Long createUserId;
    /**
    * 创建日期字段开始时间
    */
    @ApiModelProperty("创建日期字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createDateStart;
    /**
    * 创建日期字段结束时间
    */
    @ApiModelProperty("创建日期字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createDateEnd;
    /**
    * 修改人ID
    */
    @ApiModelProperty("修改人ID")
    private Long modifyUserId;
    /**
    * 修改日期字段开始时间
    */
    @ApiModelProperty("修改日期字段开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime modifyDateStart;
    /**
    * 修改日期字段结束时间
    */
    @ApiModelProperty("修改日期字段结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime modifyDateEnd;
    /**
    * 删除标记
    */
    @ApiModelProperty("删除标记")
    private Integer deleteMark;
    /**
    * 有效标记
    */
    @ApiModelProperty("有效标记")
    private Integer enabledMark;

}
