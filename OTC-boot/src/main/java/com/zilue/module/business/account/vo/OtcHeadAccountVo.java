package com.zilue.module.business.account.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @title: 表单出参
 * <AUTHOR>
 * @Date: 2025-01-08
 * @Version 1.0
 */
@Data
public class OtcHeadAccountVo {

    /**
     * 主键ID
     */
    @ExcelIgnore
    @ApiModelProperty("主键ID")
    private Long id;

    /**
     * 客户名称
     */
    @ContentStyle(dataFormat = 49)
    @ExcelProperty(value = "客户名称", index =2)
    @ApiModelProperty("客户名称")
    private String name;

    /**
     * 客户id
     */
    @ApiModelProperty("客户id")
    private String accountId;
}
