package com.zilue.module.business.salesreport.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
* @title: 表单出参
* <AUTHOR>
* @Date: 2025-01-15
* @Version 1.0
*/
@Data
public class OtcSalesReportYearVo {

    /**
    * 
    */
    @ApiModelProperty("")
    private Long id;
    /**
    * 代表编码
    */
    @ApiModelProperty("代表编码")
    private String userCode;
    /**
    * 年计划值
    */
    @ApiModelProperty("年计划值")
    private BigDecimal planAmount;
    /**
    * 年完成值
    */
    @ApiModelProperty("年完成值")
    private BigDecimal finishAmount;
    /**
    * 业务年份
    */
    @ApiModelProperty("业务年份")
    private String businessYear;
    /**
    * 个人排名（所有员工内）
    */
    @ApiModelProperty("个人排名（所有员工内）")
    private Long rankNum;



}
