package com.zilue.module.business.salesreport.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.yulichang.toolkit.MPJWrappers;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.utils.RedisUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tencentcloudapi.tdcpg.v20211118.models.Account;
import com.zilue.common.utils.BigDecimalUtil;
import com.zilue.module.business.account.entity.OtcAccount;
import com.zilue.module.business.account.entity.OtcAccountUserRelation;
import com.zilue.module.business.account.service.IAccountService;
import com.zilue.module.business.account.service.impl.AccountServiceImpl;
import com.zilue.module.business.account.service.impl.AccountUserRelationServiceImpl;
import com.zilue.module.business.salesindicators.entity.OtcSalesmanLevelSalesIndicators;
import com.zilue.module.business.salesindicators.entity.OtcSalesmanSalesIndicators;
import com.zilue.module.business.salesindicators.service.ILevelSalesIndicatorsService;
import com.zilue.module.business.salesindicators.service.ISalesIndicatorsService;
import com.zilue.module.business.salesperformance.entity.OtcSalesmanSalesPerformance;
import com.zilue.module.business.salesperformance.service.ISalesPerformanceService;
import com.zilue.module.business.salesreport.entity.OtcSalesReportDeptMonth;
import com.zilue.module.business.salesreport.entity.OtcSalesReportDeptYear;
import com.zilue.module.business.salesreport.entity.OtcSalesReportMonth;
import com.zilue.module.business.salesreport.entity.OtcSalesReportYear;
import com.zilue.module.business.salesreport.service.ISalesReportDeptMonthService;
import com.zilue.module.business.salesreport.service.ISalesReportDeptYearService;
import com.zilue.module.business.salesreport.service.ISalesReportService;
import com.zilue.module.business.salesreport.service.ISalesReportYearService;
import com.zilue.module.business.signreached.entity.OtcSalesReportSignReached;
import com.zilue.module.business.signreached.service.ISignReachedService;
import com.zilue.module.business.salesreport.entity.*;
import com.zilue.module.business.salesreport.service.*;
import com.zilue.module.organization.dto.DepartmentTreeDto;
import com.zilue.module.organization.entity.Department;
import com.zilue.module.organization.entity.User;
import com.zilue.module.organization.entity.UserDeptRelation;
import com.zilue.module.organization.service.IUserService;
import com.zilue.module.organization.service.impl.DepartmentServiceImpl;
import com.zilue.module.organization.vo.DepartmentTreeVo;
import com.zilue.module.wechat.sign.dto.CountUserOctSignDto;
import com.zilue.module.wechat.sign.mapper.OtcSignInMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/15 下午1:41
 * @description
 */
@Service
@AllArgsConstructor
@Slf4j
public class SalesReportServiceImpl implements ISalesReportService {

    private final ISalesIndicatorsService indicatorsService;
    private final ILevelSalesIndicatorsService levelSalesIndicatorsService;
    private final ISalesPerformanceService performanceService;
    private final SalesReportMonthServiceImpl reportMonthService;
    private final ISalesReportYearService reportYearService;

    private final LevelSalesReportMonthServiceImpl reportMonthLevelService;
    private final ILevelSalesReportYearService reportYearLevelService;


    private final ISalesReportDeptMonthService reportDeptMonthService;
    private final ISalesReportDeptYearService reportDeptYearService;
    private final IUserService userService;
    private final DepartmentServiceImpl departmentService;
    private final RedisUtil redisUtil;
    private final OtcSignInMapper otcSignInMapper;
    private final ISignReachedService signReachedService;

//    private final IAccountService accountService;

    private final SalesReportKaMonthServiceImpl reportKaMonthService;
    private final SalesReportKaYearServiceImpl reportKaYearService;

    private final AccountUserRelationServiceImpl accountUserRelationService;

    private final ISalesReportKaHeadStoreMonthService kaHeadStoreMonthService;
    private final ISalesReportKaHeadStoreYearService kaHeadStoreYearService;

    /**
     * 按基层业务员汇总数据（按月，包括排名）
     *
     * @param month
     */
    @Override
    public void salesLevelReportMonth(String month) {
        List<User> userList = userService.lambdaQuery().eq(User::getIsDisabled, 0).list();
        //指标
        Map<String, BigDecimal> indicatorsMap = getLevelSalesManIndicators(month, month);
        //业绩
        Map<String, BigDecimal> performanceMap = getSalesPerformance(month, month);
        //去年同期业绩
        String lastYearMonth = getLastYearMonth(month);
        Map<String, BigDecimal> lastYearPerformanceMap = getSalesPerformance(lastYearMonth, lastYearMonth);

        List<OtcLevelSalesReportMonth> list = new ArrayList<>();

        for (User user : userList) {
            OtcLevelSalesReportMonth bean = new OtcLevelSalesReportMonth();
            bean.setUserCode(user.getCode());
            bean.setPlanAmount(indicatorsMap.getOrDefault(user.getCode(), BigDecimal.ZERO));
            bean.setFinishAmount(performanceMap.getOrDefault(user.getCode(), BigDecimal.ZERO));
            bean.setBusinessMonth(month);
            bean.setLastYearPeriodFinishAmount(lastYearPerformanceMap.getOrDefault(user.getCode(), BigDecimal.ZERO));
            list.add(bean);
        }
        //根据实际完成的业绩倒序，然后设置排名
        Collections.sort(list, (o1, o2) -> o2.getFinishAmount().compareTo(o1.getFinishAmount()));
        assignRank(list);
        //删除后再重新插入
        reportMonthLevelService.remove(new LambdaQueryWrapper<OtcLevelSalesReportMonth>().eq(OtcLevelSalesReportMonth::getBusinessMonth, month));
        reportMonthLevelService.saveBatch(list);
    }


    /**
     * 按基层代表汇总数据（按年，包括排名）
     *
     * @param year
     */
    @Override
    public void salesLevelReportYear(String year) {
        List<User> userList = userService.lambdaQuery().eq(User::getIsDisabled, 0).list();
        String startMonth = getStartMonth(year);
        String endMonth = getEndMonth(year);
        //指标
        Map<String, BigDecimal> indicatorsMap = getLevelSalesManIndicators(startMonth, endMonth);
        //业绩
        Map<String, BigDecimal> performanceMap = getSalesPerformance(startMonth, endMonth);
        List<OtcLevelSalesReportYear> list = new ArrayList<>();
        for (User user : userList) {
            OtcLevelSalesReportYear bean = new OtcLevelSalesReportYear();
            bean.setUserCode(user.getCode());
            bean.setPlanAmount(indicatorsMap.getOrDefault(user.getCode(), BigDecimal.ZERO));
            bean.setFinishAmount(performanceMap.getOrDefault(user.getCode(), BigDecimal.ZERO));
            bean.setBusinessYear(year);
            list.add(bean);
        }
        //根据实际完成的业绩倒序，然后设置排名
        Collections.sort(list, (o1, o2) -> o2.getFinishAmount().compareTo(o1.getFinishAmount()));
        assignRank(list);
        //删除后再重新插入
        reportYearLevelService.remove(new LambdaQueryWrapper<OtcLevelSalesReportYear>().eq(OtcLevelSalesReportYear::getBusinessYear, year));
        reportYearLevelService.saveBatch(list);
    }

    /**
     * 按代表汇总数据（按月，包括排名）
     *
     * @param month yyyy-MM
     */
    @Override
//    @Transactional(rollbackFor = Exception.class)
    public void salesReportMonth(String month) {
        List<User> userList = userService.lambdaQuery().eq(User::getIsDisabled, 0).list();
        //指标
        Map<String, BigDecimal> indicatorsMap = getSalesManIndicators(month, month);
        //业绩
        Map<String, BigDecimal> performanceMap = getSalesPerformance(month, month);
        //去年同期业绩
        String lastYearMonth = getLastYearMonth(month);
        Map<String, BigDecimal> lastYearPerformanceMap = getSalesPerformance(lastYearMonth, lastYearMonth);

        List<OtcSalesReportMonth> list = new ArrayList<>();

        for (User user : userList) {
            OtcSalesReportMonth bean = new OtcSalesReportMonth();
            bean.setUserCode(user.getCode());
            bean.setPlanAmount(indicatorsMap.getOrDefault(user.getCode(), BigDecimal.ZERO));
            bean.setFinishAmount(performanceMap.getOrDefault(user.getCode(), BigDecimal.ZERO));
            bean.setBusinessMonth(month);
            bean.setLastYearPeriodFinishAmount(lastYearPerformanceMap.getOrDefault(user.getCode(), BigDecimal.ZERO));
            list.add(bean);
        }
        //根据实际完成的业绩倒序，然后设置排名
        Collections.sort(list, (o1, o2) -> o2.getFinishAmount().compareTo(o1.getFinishAmount()));
        assignRank(list);
        //删除后再重新插入
        reportMonthService.remove(new LambdaQueryWrapper<OtcSalesReportMonth>().eq(OtcSalesReportMonth::getBusinessMonth, month));
        reportMonthService.saveBatch(list);
    }

    private String getLastYearMonth(String month) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        // 解析输入的年月字符串
        YearMonth yearMonth = YearMonth.parse(month, formatter);
        // 年份减 1
        YearMonth reducedYearMonth = yearMonth.minusYears(1);
        // 格式化输出
        return reducedYearMonth.format(formatter);
    }

    private Map<String, BigDecimal> getSalesPerformance(String startMonth, String endMonth) {
        List<OtcSalesmanSalesPerformance> list = performanceService.queryPerformance(getFirstDayOfMonth(startMonth), getLastDayOfMonth(endMonth));
        return list.stream().collect(Collectors.toMap(OtcSalesmanSalesPerformance::getUserCode, OtcSalesmanSalesPerformance::getAmount));

    }

    public static String getFirstDayOfMonth(String yearMonthStr) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        YearMonth yearMonth = YearMonth.parse(yearMonthStr, formatter);
        LocalDate firstDay = yearMonth.atDay(1);
        return firstDay.format(formatter.ofPattern("yyyy-MM-dd"));
    }

    public static String getLastDayOfMonth(String yearMonthStr) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        YearMonth yearMonth = YearMonth.parse(yearMonthStr, formatter);
        LocalDate lastDay = yearMonth.atEndOfMonth();
        return lastDay.format(formatter.ofPattern("yyyy-MM-dd"));
    }

    //用于统计机构的指标
    private Map<String, BigDecimal> getSalesManIndicators(String startMonth, String endMonth) {
        List<OtcSalesmanSalesIndicators> list = indicatorsService.queryIndicators(startMonth, endMonth);
        return list.stream().collect(Collectors.toMap(OtcSalesmanSalesIndicators::getUserCode, OtcSalesmanSalesIndicators::getAmount));
    }

    //基层代表指标
    private Map<String, BigDecimal> getLevelSalesManIndicators(String startMonth, String endMonth) {
        List<OtcSalesmanLevelSalesIndicators> list = levelSalesIndicatorsService.queryIndicators(startMonth, endMonth);
        return list.stream().collect(Collectors.toMap(OtcSalesmanLevelSalesIndicators::getUserCode, OtcSalesmanLevelSalesIndicators::getAmount));
    }


    /**
     * 按代表汇总数据（按年，包括排名）
     *
     * @param year
     */
    @Override
//    @Transactional(rollbackFor = Exception.class)
    public void salesReportYear(String year) {
        List<User> userList = userService.lambdaQuery().eq(User::getIsDisabled, 0).list();
        String startMonth = getStartMonth(year);
        String endMonth = getEndMonth(year);
        //指标
        Map<String, BigDecimal> indicatorsMap = getSalesManIndicators(startMonth, endMonth);
        //业绩
        Map<String, BigDecimal> performanceMap = getSalesPerformance(startMonth, endMonth);
        List<OtcSalesReportYear> list = new ArrayList<>();
        for (User user : userList) {
            OtcSalesReportYear bean = new OtcSalesReportYear();
            bean.setUserCode(user.getCode());
            bean.setPlanAmount(indicatorsMap.getOrDefault(user.getCode(), BigDecimal.ZERO));
            bean.setFinishAmount(performanceMap.getOrDefault(user.getCode(), BigDecimal.ZERO));
            bean.setBusinessYear(year);
            list.add(bean);
        }
        //根据实际完成的业绩倒序，然后设置排名
        Collections.sort(list, (o1, o2) -> o2.getFinishAmount().compareTo(o1.getFinishAmount()));
        assignRank(list);
        //删除后再重新插入
        reportYearService.remove(new LambdaQueryWrapper<OtcSalesReportYear>().eq(OtcSalesReportYear::getBusinessYear, year));
        reportYearService.saveBatch(list);
    }


    /**
     * 团队汇总按月
     *
     * @param month
     */
    @Override
//    @Transactional(rollbackFor = Exception.class)
    public void salesReportDeptMonth(String month) {
        List<OtcSalesReportMonth> reportMonths = reportMonthService.lambdaQuery().eq(OtcSalesReportMonth::getBusinessMonth, month).list();
        if (CollectionUtil.isEmpty(reportMonths)) {
            log.warn("[按团队汇总数据按月] 代表汇总报表为空，停止执行，月份{}", month);
            return;
        }
        //指标
        Map<String, BigDecimal> indicatorsMap = reportMonths.stream().collect(Collectors.toMap(OtcSalesReportMonth::getUserCode, OtcSalesReportMonth::getPlanAmount));
        //业绩
        Map<String, BigDecimal> performanceMap = reportMonths.stream().collect(Collectors.toMap(OtcSalesReportMonth::getUserCode, OtcSalesReportMonth::getFinishAmount));

        List<DepartmentTreeVo> departmentTrees = departmentService.treeEnabled(new DepartmentTreeDto());
        if (CollectionUtil.isEmpty(departmentTrees)) {
            log.warn("[按团队汇总数据按月] 团队为空，停止执行，月份{}", month);
            return;
        }

        List<OtcSalesReportDeptMonth> resultList = new ArrayList<>();

        salesReportDeptMonth(month, departmentTrees, "root", resultList, indicatorsMap, performanceMap);

        if (CollectionUtil.isEmpty(resultList)) {
            log.warn("[按团队汇总数据按月] 执行完成，没有规整出任何数据，月份{}", month);
            return;
        }
        //同层级下进行排名
        Map<String, List<OtcSalesReportDeptMonth>> groupForSortMap = resultList.stream()
                .collect(Collectors.groupingBy(OtcSalesReportDeptMonth::getParentDeptCode));

        groupForSortMap.forEach((parentDeptCode, list) -> {
            Collections.sort(list, (o1, o2) -> o2.getFinishAmount().compareTo(o1.getFinishAmount()));
            assignRank(list);
        });

        //保存数据，先删后插
        reportDeptMonthService.remove(new LambdaQueryWrapper<OtcSalesReportDeptMonth>().eq(OtcSalesReportDeptMonth::getBusinessMonth, month));
        reportDeptMonthService.saveBatch(resultList);
    }


    public void salesReportDeptMonth(String month, List<DepartmentTreeVo> nodes, String parentDepartId, List<OtcSalesReportDeptMonth> resultList,
                                     Map<String, BigDecimal> indicatorsMap, Map<String, BigDecimal> performanceMap) {
        for (DepartmentTreeVo node : nodes) {
            //获取当前部门的所有用户
            List<String> userCodes = userService.getUserCodeByDept(node.getId());
            if (CollectionUtil.isEmpty(userCodes)) {
                log.warn("[按团队汇总数据按月] 团队用户为空，停止执行，月份{}，团队{}", month, node.getId());
                continue;
            }
            OtcSalesReportDeptMonth bean = new OtcSalesReportDeptMonth();
            bean.setParentDeptCode(parentDepartId);
            bean.setDeptCode(String.valueOf(node.getId()));
            bean.setBusinessMonth(month);
            //部门指标汇总计算
            bean.setPlanAmount(calculateDeptIndicators(indicatorsMap, userCodes));
            //部门业绩汇总计算
            bean.setFinishAmount(calculateDeptPerformance(performanceMap, userCodes));
            resultList.add(bean);
            //处理子部门
            if (node.getChildren() != null && !node.getChildren().isEmpty()) {
                salesReportDeptMonth(month, node.getChildren(), String.valueOf(node.getId()), resultList, indicatorsMap, performanceMap);
            }
        }
    }

    private BigDecimal calculateDeptPerformance(Map<String, BigDecimal> performanceMap, List<String> userCodes) {
        return userCodes.stream()
                .map(performanceMap::get)  // 获取每个键对应的值
                .filter(Objects::nonNull)  // 过滤掉 null 值
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private BigDecimal calculateDeptIndicators(Map<String, BigDecimal> indicatorsMap, List<String> userCodes) {
        return userCodes.stream().map(indicatorsMap::get).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 团队汇总按年
     *
     * @param year
     */
    @Override
//    @Transactional(rollbackFor = Exception.class)
    public void salesReportDeptYear(String year) {
        List<OtcSalesReportMonth> reportYears = reportMonthService.getBaseMapper().querySumGroupByUserCode(getStartMonth(year), getEndMonth(year));
        if (CollectionUtil.isEmpty(reportYears)) {
            log.warn("[按团队汇总数据按年] 代表汇总报表为空，停止执行，年份{}", year);
            return;
        }
        //指标
        Map<String, BigDecimal> indicatorsMap = reportYears.stream().collect(Collectors.toMap(OtcSalesReportMonth::getUserCode, OtcSalesReportMonth::getPlanAmount));
        //业绩
        Map<String, BigDecimal> performanceMap = reportYears.stream().collect(Collectors.toMap(OtcSalesReportMonth::getUserCode, OtcSalesReportMonth::getFinishAmount));

        List<DepartmentTreeVo> departmentTrees = departmentService.treeEnabled(new DepartmentTreeDto());
        if (CollectionUtil.isEmpty(departmentTrees)) {
            log.warn("[按团队汇总数据按年] 团队为空，停止执行，年份{}", year);
            return;
        }

        List<OtcSalesReportDeptYear> resultList = new ArrayList<>();

        salesReportDeptYear(year, departmentTrees, "root", resultList, indicatorsMap, performanceMap);

        if (CollectionUtil.isEmpty(resultList)) {
            log.warn("[按团队汇总数据按年] 执行完成，没有规整出任何数据，年份{}", year);
            return;
        }
        //同层级下进行排名
        Map<String, List<OtcSalesReportDeptYear>> groupForSortMap = resultList.stream()
                .collect(Collectors.groupingBy(OtcSalesReportDeptYear::getParentDeptCode));

        groupForSortMap.forEach((parentDeptCode, list) -> {
            Collections.sort(list, (o1, o2) -> o2.getFinishAmount().compareTo(o1.getFinishAmount()));
            assignRank(list);
        });

        //保存数据，先删后插
        reportDeptYearService.remove(new LambdaQueryWrapper<OtcSalesReportDeptYear>().eq(OtcSalesReportDeptYear::getBusinessYear, year));
        reportDeptYearService.saveBatch(resultList);
    }


    public void salesReportDeptYear(String year, List<DepartmentTreeVo> nodes, String parentDepartId, List<OtcSalesReportDeptYear> resultList,
                                    Map<String, BigDecimal> indicatorsMap, Map<String, BigDecimal> performanceMap) {
        for (DepartmentTreeVo node : nodes) {
            //获取当前部门的所有用户
            List<String> userCodes = userService.getUserCodeByDept(node.getId());
            if (CollectionUtil.isEmpty(userCodes)) {
                log.warn("[按团队汇总数据按年] 团队用户为空，停止执行，年份{}，团队{}", year, node.getId());
                continue;
            }
            OtcSalesReportDeptYear bean = new OtcSalesReportDeptYear();
            bean.setParentDeptCode(parentDepartId);
            bean.setDeptCode(String.valueOf(node.getId()));
            bean.setBusinessYear(year);
            //部门指标汇总计算
            bean.setPlanAmount(calculateDeptIndicators(indicatorsMap, userCodes));
            //部门业绩汇总计算
            bean.setFinishAmount(calculateDeptPerformance(performanceMap, userCodes));
            resultList.add(bean);
            //处理子部门
            if (node.getChildren() != null && !node.getChildren().isEmpty()) {
                salesReportDeptYear(year, node.getChildren(), String.valueOf(node.getId()), resultList, indicatorsMap, performanceMap);
            }
        }
    }


    @Override
    public void visitTargetRank(Date yesterday) {
        //获取每日的目标，获取不到直接返回
        Integer goal = otcSignInMapper.selectDailyTaskGoal();
        if (ObjectUtil.isNull(goal)) {
            log.error("获取每日目标失败");
            return;
        }
        // 先删除之前跑的结果
        signReachedService.remove(new LambdaQueryWrapper<OtcSalesReportSignReached>()
                .between(OtcSalesReportSignReached::getReportDate, DateUtil.beginOfDay(yesterday), DateUtil.endOfDay(yesterday)));
        //获取全部的部门
        List<Department> departmentList = redisUtil.get(GlobalConstant.DEP_CACHE_KEY,
                new TypeReference<List<Department>>() {
                });
        //获取倒数二级的部门分组
        Map<Long, List<Department>> secondLevelDepartments = new HashMap<>();
        //获取最后一级的部门分组
        Map<Long, List<Department>> thirdLevelDepartments = new HashMap<>();
        //倒数第三级的部门分组
        Map<Long, List<Department>> firstLevelDepartments = new HashMap<>();
        departmentList.forEach(department -> {
            int dashCount = StrUtil.count(department.getHierarchy(), StrPool.DASHED);
            if (dashCount == 1) {
                firstLevelDepartments.computeIfAbsent(department.getParentId(), k -> new ArrayList<>()).add(department);
            } else if (dashCount == 2) {
                secondLevelDepartments.computeIfAbsent(department.getParentId(), k -> new ArrayList<>()).add(department);
            } else if (dashCount == 3) {
                thirdLevelDepartments.computeIfAbsent(department.getParentId(), k -> new ArrayList<>()).add(department);
            }
        });

        //获取全部的人员和部门之间的关系
        List<UserDeptRelation> deptRelationList = redisUtil.get(GlobalConstant.USER_DEPT_RELATION_CACHE_KEY,
                new TypeReference<List<UserDeptRelation>>() {
                });
        //转换成部门和用户 id 的 Map
        Map<Long, List<Long>> deptUserIdListMap = deptRelationList.stream()
                .collect(Collectors.groupingBy(UserDeptRelation::getDeptId,
                        Collectors.mapping(UserDeptRelation::getUserId, Collectors.toList())));
        Map<Long, List<OtcSalesReportSignReached>> thirdLevelDepartmentsMap = new HashMap<>();
        // 遍历每个最下级的部门，计算每个部门的拜访达成率
        LocalDateTime reportDate = DateUtil.toLocalDateTime(yesterday);
        thirdLevelDepartments.forEach((parentId, departments) -> {
            for (Department department : departments) {
                List<Long> userIdListMapOrDefault = deptUserIdListMap.getOrDefault(department.getId(),
                        CollUtil.newArrayList());
                OtcSalesReportSignReached signReachedDto = new OtcSalesReportSignReached();
                signReachedDto.setDeptId(department.getId());
                signReachedDto.setParentDeptId(department.getParentId());
                signReachedDto.setDeptPersonNum(userIdListMapOrDefault.size());
                signReachedDto.setRankNum(1L);
                signReachedDto.setReportDate(reportDate);
                signReachedDto.setCreateDate(LocalDateTime.now());
                signReachedDto.setModifyDate(LocalDateTime.now());

                int reachNum = 0;
                if (CollUtil.isNotEmpty(userIdListMapOrDefault)) {
                    // 获取前一天的每个人的拜访达成数
                    List<CountUserOctSignDto> octSignDtos = otcSignInMapper.selectCountUserOctSign(DateUtil
                            .format(yesterday, DatePattern.NORM_DATE_PATTERN), userIdListMapOrDefault);
                    if (CollUtil.isNotEmpty(octSignDtos)) {
                        for (CountUserOctSignDto octSignDto : octSignDtos) {
                            if (null != octSignDto.getSignCount() && octSignDto.getSignCount() >= goal) {
                                reachNum++;
                            }
                        }
                    }
                }
                signReachedDto.setReachNum(reachNum);
                signReachedDto.setReachRate((ObjectUtil.equal(0, reachNum)
                        || (ObjectUtil.equal(0, signReachedDto.getDeptPersonNum())) ?
                        BigDecimal.ZERO : BigDecimal.valueOf(reachNum).divide(BigDecimal.valueOf(signReachedDto.getDeptPersonNum())
                        , 2, RoundingMode.HALF_UP)));
                thirdLevelDepartmentsMap.computeIfAbsent(parentId, k -> new ArrayList<>())
                        .add(signReachedDto);
                log.info("部门：{}，用户拜访数：{}", department, thirdLevelDepartmentsMap);
            }
        });
        //最小部门 组内进行排序并插入
        sortAndSaveData(thirdLevelDepartmentsMap);
        Map<Long, List<OtcSalesReportSignReached>> secondLevelDepartmentsMap =
                aggregateDepartmentData(secondLevelDepartments, thirdLevelDepartmentsMap, reportDate, deptUserIdListMap, goal);
        sortAndSaveData(secondLevelDepartmentsMap);

        Map<Long, List<OtcSalesReportSignReached>> firstLevelDepartmentsMap =
                aggregateDepartmentData(firstLevelDepartments, secondLevelDepartmentsMap, reportDate, deptUserIdListMap, goal);
        sortAndSaveData(firstLevelDepartmentsMap);

    }


    private Map<Long, List<OtcSalesReportSignReached>> aggregateDepartmentData(Map<Long, List<Department>> parentDepartments,
                                                                               Map<Long, List<OtcSalesReportSignReached>> childDepartmentMap,
                                                                               LocalDateTime reportDate, Map<Long, List<Long>> deptUserIdListMap, Integer goal) {
        Map<Long, List<OtcSalesReportSignReached>> result = new HashMap<>();
        parentDepartments.forEach((parentId, departments) -> {
            for (Department department : departments) {
                List<Long> userIdListMapOrDefault = deptUserIdListMap.getOrDefault(department.getId(),
                        CollUtil.newArrayList());
                int reachNum = 0;
                if (CollUtil.isNotEmpty(userIdListMapOrDefault)) {
                    // 获取前一天的每个人的拜访达成数
                    List<CountUserOctSignDto> octSignDtos = otcSignInMapper.selectCountUserOctSign(DateUtil
                            .format(reportDate, DatePattern.NORM_DATE_PATTERN), userIdListMapOrDefault);
                    if (CollUtil.isNotEmpty(octSignDtos)) {
                        for (CountUserOctSignDto octSignDto : octSignDtos) {
                            if (null != octSignDto.getSignCount() && octSignDto.getSignCount() >= goal) {
                                reachNum++;
                            }
                        }
                    }
                }
                List<OtcSalesReportSignReached> reportSignReacheds = childDepartmentMap.getOrDefault(department.getId(), new ArrayList<>());
                OtcSalesReportSignReached signReachedDto = createReportSignReached(reportSignReacheds, department, reportDate, userIdListMapOrDefault.size(), reachNum);
                result.computeIfAbsent(parentId, k -> new ArrayList<>()).add(signReachedDto);
                log.info("部门：{}，用户拜访数：{}", department, result);
            }
        });
        return result;
    }

    private OtcSalesReportSignReached createReportSignReached(List<OtcSalesReportSignReached> reportSignReacheds, Department department, LocalDateTime reportDate, int needAddPersonNum, int needAddCount) {
        int deptPersonNum = reportSignReacheds.stream().mapToInt(OtcSalesReportSignReached::getDeptPersonNum).sum() + needAddPersonNum;
        int reachNum = reportSignReacheds.stream().mapToInt(OtcSalesReportSignReached::getReachNum).sum() + needAddCount;
        BigDecimal reachRate = (reachNum == 0 || deptPersonNum == 0) ?
                BigDecimal.ZERO :
                BigDecimal.valueOf(reachNum).divide(BigDecimal.valueOf(deptPersonNum), 2, RoundingMode.HALF_UP);
        OtcSalesReportSignReached signReachedDto = new OtcSalesReportSignReached();
        signReachedDto.setDeptId(department.getId());
        signReachedDto.setParentDeptId(department.getParentId());
        signReachedDto.setDeptPersonNum(deptPersonNum);
        signReachedDto.setReachNum(reachNum);
        signReachedDto.setReachRate(reachRate);
        signReachedDto.setRankNum(1L);
        signReachedDto.setReportDate(reportDate);
        signReachedDto.setCreateDate(LocalDateTime.now());
        signReachedDto.setModifyDate(LocalDateTime.now());
        return signReachedDto;
    }

    private void sortAndSaveData(Map<Long, List<OtcSalesReportSignReached>> thirdLevelDepartmentsMap) {
        for (List<OtcSalesReportSignReached> signReacheds : thirdLevelDepartmentsMap.values()) {
            // 1. 排序从大到小
            signReacheds.sort(Comparator.comparing(OtcSalesReportSignReached::getReachRate).reversed());
            // 2. 检查是否有非零的 reachRate
            if (signReacheds.stream()
                    .anyMatch(item -> item.getReachRate()
                            .compareTo(BigDecimal.ZERO) > 0)) {
                long currentRank = 1;
                BigDecimal previousReachRate = null;

                for (int i = 0; i < signReacheds.size(); i++) {
                    OtcSalesReportSignReached item = signReacheds.get(i);

                    // 如果当前 reachRate 与前一个不同，更新排名
                    if (ObjectUtil.isNull(previousReachRate) ||
                            item.getReachRate().compareTo(previousReachRate) != 0) {
                        currentRank = i + 1;
                    }

                    item.setRankNum(currentRank);
                    previousReachRate = item.getReachRate();
                }
            }
            signReachedService.saveBatch(signReacheds);
        }
    }


    @NotNull
    private String getEndMonth(String year) {
        return year + "-12";
    }

    @NotNull
    private String getStartMonth(String year) {
        return year + "-01";
    }


    /**
     * 连锁总部汇总按月
     *
     * @param month
     */
    @Override
//    @Transactional(rollbackFor = Exception.class)
    public void salesReportHeadStoreMonth(String month) {
        //指标
        Map<String, BigDecimal> indicatorsMap = indicatorsService.queryIndicatorsGroupByHeardStore(month)
                .stream().collect(Collectors.toMap(OtcSalesmanSalesIndicators::getHeadquartersCode, OtcSalesmanSalesIndicators::getAmount));
        //业绩
        Map<String, BigDecimal> performanceMap = performanceService.queryPerformanceGroupByHeardStore(getFirstDayOfMonth(month), getLastDayOfMonth(month))
                .stream().collect(Collectors.toMap(OtcSalesmanSalesPerformance::getHeadquartersCode, OtcSalesmanSalesPerformance::getAmount));

        Map<String, String> userHeadCodeMap = getUserHeadCodeRelation().stream().collect(Collectors.toMap(OtcAccountUserRelation::getAccountId, OtcAccountUserRelation::getOwnerNumber, (existingValue, newValue) -> newValue));

        //查询所有的总部编码
     /*   Set<String> headCodes = accountService.lambdaQuery().eq(OtcAccount::getTerminalType,0).list()
                .stream().map(OtcAccount::getAccountId).collect(Collectors.toSet());*/
        List<OtcSalesReportKaMonth> list = new ArrayList<>();

        userHeadCodeMap.forEach((headCode, userCode) -> {
            OtcSalesReportKaMonth bean = new OtcSalesReportKaMonth();
            bean.setDeptCode(headCode);
            bean.setPlanAmount(indicatorsMap.getOrDefault(headCode, BigDecimal.ZERO));
            bean.setFinishAmount(performanceMap.getOrDefault(headCode, BigDecimal.ZERO));
            bean.setBusinessMonth(month);
            bean.setUserCode(userCode);
            list.add(bean);
        });
        //根据实际完成的业绩倒序，然后设置排名
        Collections.sort(list, (o1, o2) -> o2.getFinishAmount().compareTo(o1.getFinishAmount()));
        assignRank(list);
        //删除后再重新插入
        reportKaMonthService.remove(new LambdaQueryWrapper<OtcSalesReportKaMonth>().eq(OtcSalesReportKaMonth::getBusinessMonth, month));
        reportKaMonthService.saveBatch(list);
    }

    private List<OtcAccountUserRelation> getUserHeadCodeRelation() {
        return accountUserRelationService.selectJoinList(OtcAccountUserRelation.class, MPJWrappers.<OtcAccountUserRelation>lambdaJoin()
                .innerJoin(OtcAccount.class, OtcAccount::getAccountId, OtcAccountUserRelation::getAccountId)
                .eq(OtcAccountUserRelation::getDeleteMark, 0)
                .eq(OtcAccount::getTerminalType, 0)
                .eq(OtcAccount::getDeleteMark, 0)
        );
    }

    /**
     * 连锁总部汇总按年
     *
     * @param year
     */
    @Override
//    @Transactional(rollbackFor = Exception.class)
    public void salesReportHeadStoreYear(String year) {

        List<OtcSalesReportKaMonth> reportYears = reportKaMonthService.getBaseMapper().querySumGroupByStoreCode(getStartMonth(year), getEndMonth(year));
        if (CollectionUtil.isEmpty(reportYears)) {
            log.warn("[连锁总部汇总按年] 汇总报表为空，停止执行，年份{}", year);
            return;
        }
        List<OtcSalesReportKaYear> list = new ArrayList<>();
        for (OtcSalesReportKaMonth report : reportYears) {
            OtcSalesReportKaYear bean = new OtcSalesReportKaYear();
            bean.setUserCode(report.getUserCode());
            bean.setDeptCode(report.getDeptCode());
            bean.setPlanAmount(report.getPlanAmount());
            bean.setFinishAmount(report.getFinishAmount());
            bean.setBusinessYear(year);
            list.add(bean);
        }
        //根据实际完成的业绩倒序，然后设置排名
        Collections.sort(list, (o1, o2) -> o2.getFinishAmount().compareTo(o1.getFinishAmount()));
        assignRank(list);
        //删除后再重新插入
        reportKaYearService.remove(new LambdaQueryWrapper<OtcSalesReportKaYear>().eq(OtcSalesReportKaYear::getBusinessYear, year));
        reportKaYearService.saveBatch(list);
    }

    /**
     * KA连锁总部汇总按月
     *
     * @param month
     */
    @Override
    public void salesReportKaMonth(String month) {
        List<OtcSalesReportKaMonth> reports = reportKaMonthService.getBaseMapper().querySumGroupByUserCode(month);
        if (CollectionUtil.isEmpty(reports)) {
            log.warn("[KA连锁总部汇总按月] 汇总报表为空，停止执行，年份{}", month);
            return;
        }
        List<OtcSalesReportKaHeadStoreMonth> list = new ArrayList<>();
        for (OtcSalesReportKaMonth report : reports) {
            OtcSalesReportKaHeadStoreMonth bean = new OtcSalesReportKaHeadStoreMonth();
            bean.setUserCode(report.getUserCode());
            bean.setPlanAmount(report.getPlanAmount());
            bean.setFinishAmount(report.getFinishAmount());
            bean.setBusinessMonth(month);
            list.add(bean);
        }

        //根据实际完成的业绩倒序，然后设置排名
        Collections.sort(list, (o1, o2) -> o2.getFinishAmount().compareTo(o1.getFinishAmount()));
        assignRank(list);
        //删除后再重新插入
        kaHeadStoreMonthService.remove(new LambdaQueryWrapper<OtcSalesReportKaHeadStoreMonth>().eq(OtcSalesReportKaHeadStoreMonth::getBusinessMonth, month));
        kaHeadStoreMonthService.saveBatch(list);
    }

    /**
     * 连锁总部汇总按年
     *
     * @param year
     */
    @Override
    public void salesReportKaYear(String year) {

        List<OtcSalesReportKaMonth> reports = reportKaMonthService.getBaseMapper().querySumByYearGroupByUserCode(getStartMonth(year), getEndMonth(year));
        if (CollectionUtil.isEmpty(reports)) {
            log.warn("[KA连锁总部汇总按年] 汇总报表为空，停止执行，年份{}", year);
            return;
        }
        List<OtcSalesReportKaHeadStoreYear> list = new ArrayList<>();
        for (OtcSalesReportKaMonth report : reports) {
            OtcSalesReportKaHeadStoreYear bean = new OtcSalesReportKaHeadStoreYear();
            bean.setUserCode(report.getUserCode());
            bean.setPlanAmount(report.getPlanAmount());
            bean.setFinishAmount(report.getFinishAmount());
            bean.setBusinessYear(year);
            list.add(bean);
        }

        //根据实际完成的业绩倒序，然后设置排名
        Collections.sort(list, (o1, o2) -> o2.getFinishAmount().compareTo(o1.getFinishAmount()));
        assignRank(list);
        //删除后再重新插入
        kaHeadStoreYearService.remove(new LambdaQueryWrapper<OtcSalesReportKaHeadStoreYear>().eq(OtcSalesReportKaHeadStoreYear::getBusinessYear, year));
        kaHeadStoreYearService.saveBatch(list);
    }


    private <T extends OtcSalesPerformanceRank> void assignRank(List<T> sortedList) {
        if (sortedList == null || sortedList.isEmpty()) {
            return;
        }
        Long rank = 1L;
        BigDecimal previousAmount = sortedList.get(0).getFinishAmount();
        sortedList.get(0).setRankNum(rank);
        for (int i = 1; i < sortedList.size(); i++) {
            T current = sortedList.get(i);
            if (!current.getFinishAmount().equals(previousAmount)) {
                rank = (long) (i + 1);
            }
            current.setRankNum(rank);
            previousAmount = current.getFinishAmount();
        }
    }

}
