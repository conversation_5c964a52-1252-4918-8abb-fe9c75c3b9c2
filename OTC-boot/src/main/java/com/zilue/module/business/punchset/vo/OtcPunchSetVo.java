package com.zilue.module.business.punchset.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalTime;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.util.List;

/**
* @title: 表单出参
* <AUTHOR>
* @Date: 2024-12-26
* @Version 1.0
*/
@Data
public class OtcPunchSetVo {

    /**
    * 
    */
    @ApiModelProperty("")
    private Long id;
    /**
    * 允许打卡范围
    */
    @ApiModelProperty("允许打卡范围")
    private Integer outerScope;
    /**
    * 可超范围打卡(1=是 2=否)
    */
    @ApiModelProperty("可超范围打卡(1=是 2=否)")
    private Integer superableScope;
    /**
    * 可超范围文字说明(1=是 2=否)
    */
    @ApiModelProperty("可超范围文字说明(1=是 2=否)")
    private Integer superableExplain;



}
