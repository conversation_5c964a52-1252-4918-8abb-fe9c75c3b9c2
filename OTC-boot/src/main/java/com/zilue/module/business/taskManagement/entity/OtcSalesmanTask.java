package com.zilue.module.business.taskManagement.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * +* @title: 目标管理+* <AUTHOR> @Date: 2024-12-31+* @Version 1.0+
 */
@Data
@TableName("otc_salesman_task")
@ApiModel(value = "目标管理对象", description = "目标管理")
public class OtcSalesmanTask implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty("")
    @TableId
    private Long id;
    /**
     * 任务类型任务类型0拜访任务1协访任务2拓客任务3业绩任务4客户数发展目标
     */
    @ApiModelProperty("任务类型任务类型 门店拜访1连锁分部拜访2连锁总部拜访34连锁分部协防5销售业绩6 贴柜培训11 铁杆积累12 进销存采集13 竞品采集14 陈列检查15 看字典")
    private String taskType;
    /**
     * 开始年月
     */

    @ApiModelProperty("开始年月")
    private String startYearMonth;
    /**
     * 结束年月
     */
    @ApiModelProperty("结束年月")
    private String endYearMonth;
    /**
     * 任务状态-1创建中0待开始1进行中2已过期
     */
    @ApiModelProperty("任务状态-创建中0待开始1进行中2已过期3 看字典")
    private String taskStatus;
    /**
     * 核算频率，0代表日，1代表月
     */
    @ApiModelProperty("核算频率，1代表日，2代表月 看字典")
    private String frequency;
    @ApiModelProperty("执行人岗位")
    private String postId;
    /**
     * 核算频率，0代表日，1代表月
     */
    @ApiModelProperty("0 停用，1启用 看字典")
    private String isEnabled;
    /**
     * 创建人ID
     */
    @ApiModelProperty("创建人ID")
    @TableField(fill = FieldFill.INSERT)
    private Long createUserId;
    /**
     * 创建日期
     */

    @ApiModelProperty("创建日期")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createDate;
    /**
     * 修改人ID
     */

    @ApiModelProperty("修改人ID")
    @TableField(fill = FieldFill.UPDATE)
    private Long modifyUserId;
    /**
     * 修改日期
     */

    @ApiModelProperty("修改日期")
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime modifyDate;
    /**
     * 删除标记
     */

    @ApiModelProperty("删除标记")
    @TableField(fill = FieldFill.INSERT)
    @TableLogic
    private Integer deleteMark;
    /**
     * 有效标记
     */
    @ApiModelProperty("有效标记")
    @TableField(fill = FieldFill.INSERT)
    private Integer enabledMark;
}