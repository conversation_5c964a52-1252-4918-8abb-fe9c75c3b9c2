package com.zilue.module.form.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.zilue.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 自定义表单 发布表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
@TableName("xjr_form_release")
@ApiModel(value = "FormRelease对象", description = "自定义表单 发布表")
@Data
@EqualsAndHashCode(callSuper = false)
public class FormRelease extends AuditEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("表单id 关联 xjr_form_template 表")
    private Long formId;

    @ApiModelProperty("菜单id 关联 xjr_module表  唯一")
    private Long menuId;

    @ApiModelProperty("自定义表单发布配置json")
    private String configJson;

    @ApiModelProperty("排序号")
    private Integer sortCode;

    @ApiModelProperty("备注")
    private String remark;


}
