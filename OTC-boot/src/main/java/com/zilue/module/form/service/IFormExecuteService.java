package com.zilue.module.form.service;

import cn.hutool.db.Entity;
import cn.hutool.db.Session;
import com.zilue.common.page.PageOutput;
import com.zilue.module.desktop.dto.*;
import com.zilue.module.form.dto.*;
import com.zilue.module.form.entity.FormTemplate;
import com.zilue.module.form.vo.DeskFormReleaseVo;
import com.zilue.module.form.vo.DeskTableInfoVo;
import com.zilue.module.generator.entity.QueryConfig;
import org.apache.commons.lang3.tuple.Triple;

import java.util.List;
import java.util.Map;

/**
 * @Author: zilue
 * @Date: 2023/5/11 14:57
 */
public interface IFormExecuteService {
    /**
     * 获取自定义表单不分页列表数据
     * @param dto
     * @return
     */
    List<Entity> list(FormExecuteListDto dto);

    /**
     * App 获取自定义表单不分页列表数据
     * @param dto
     * @return
     */
    List<Entity> appList(AppFormExecuteListDto dto);

    /**
     * 获取自定义表单分页列表数据
     * @param dto
     * @return
     */
    PageOutput<Entity> page(FormExecutePageDto dto);

    /**
     * App 获取自定义表单分页列表数据
     * @param dto
     * @return
     */
    PageOutput<Entity> appPage(AppFormExecutePageDto dto);

    /**
     * 获取自定义表单 表单数据
     * @return
     */
    Object info(FormExecuteInfoDto dto);

    /**
     * App 获取自定义表单 表单数据
     * @return
     */
    Object appInfo(AppFormExecuteInfoDto dto);

    /**
     * 自定义表单 新增
     * @param dto
     * @return
     */
    Boolean add(FormExecuteAddOrUpdateDto dto);

    /**
     * App 自定义表单 新增
     * @param dto
     * @return
     */
    Boolean appAdd(AppFormExecuteAddOrUpdateDto dto);

    /**
     * 自定义表单 修改
     * @param dto
     * @return
     */
    Boolean update(FormExecuteAddOrUpdateDto dto);

    /**
     * app 自定义表单 修改
     * @param dto
     * @return
     */
    Boolean appUpdate(AppFormExecuteAddOrUpdateDto dto);

    /**
     * 自定义表单 删除
     * @param dto
     * @return
     */
    Boolean delete(FormExecuteDeleteDto dto);

    /**
     * app 自定义表单 删除
     * @param dto
     * @return
     */
    Boolean appDelete(AppFormExecuteDeleteDto dto);

    /**
     * 工作流调用 自定义表单 新增
     * @param dto
     * @return
     */
    Triple<Session, Long, Object> workflowAdd(FormExecuteWorkflowAddDto dto);


    /**
     * 工作流调用 自定义表单 修改
     * @param dto
     * @return
     */
    Triple<Session, Long, Object> workflowUpdate(FormExecuteWorkflowUpdateDto dto);

    /**
     * 工作流调用 自定义表单 新增或者修改
     * @param dto
     * @return
     */
    Triple<Session, Boolean, Object> workflowAddOrUpdate(FormExecuteWorkflowUpdateDto dto);

    /**
     * 获取自定义表单 表单数据
     * @return
     */
    Object workFlowInfo(FormExecuteWorkflowInfoDto dto);

    /**
     * 批量新增主表数据
     * @return
     */
    Boolean saveMainBatch(Long formTemplateId, List<Map<String, Object>> dataList);

    /**
     * 桌面设计调用新增
     * @param dto
     * @return
     */
    boolean complexAdd(AddDeskComplexDto dto);


    /**
     * 桌面设计调用编辑
     * @param dto
     * @return
     */
    boolean complexUpdate(UpdateDeskComplexDto dto);

    /**
     * 桌面设计调用删除
     * @param dto
     * @return
     */
    boolean complexDelete(DeleteDeskComplexDto dto);

    /**
     * 桌面设计调用删除
     * @param dto
     * @return
     */
    Object complexInfo(DeskComplexInfoDto dto);

    /**
     * 桌面设计调用自定义表单获取发布id
     * @param formId
     * @return
     */
    List<DeskFormReleaseVo> getReleaseInfo(Long formId);

    List<QueryConfig> complexQuery(ComplexQueryDto dto);

    /**
     * 桌面设计-复杂列表页-获取列表数据 分页
     * @param dto
     * @return
     */
    PageOutput<Entity> complexPage(ComplexPageDto dto);

    DeskTableInfoVo getTableInfo(Long formId);

    /**
     *  新增表单数据
     * @param formData 表单数据
     * @param template 表单模板
     * @return 新增结果
     */
    boolean insertFormData(Map<String, Object> formData, FormTemplate template);
}
