package com.zilue.module.form.dto;

import com.zilue.common.model.generator.FormConfig;
import com.zilue.module.generator.entity.ListConfig;
import com.zilue.module.generator.entity.MenuConfig;
import com.zilue.module.generator.entity.OutputConfig;
import com.zilue.module.generator.entity.TableConfig;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;
@Data
public class DateTestDto {


    @NotNull
    private Long formReleaseId;

    @NotNull
    private Long formTemplateId;
    /**
     * 数据库id
     */
    private String databaseId;

    /**
     * 表关联信息
     */
    private List<TableConfig> tableConfigs;

    /**
     * 表单设计json
     */
    private FormConfig formJson;

    /**
     * 列表配置
     */
    private ListConfig listConfig;

    /**
     * 输出配置
     */
    @NotNull(message = "输出配置不能为空！")
    @Valid
    private OutputConfig outputConfig;

    /**
     * 菜单配置
     */
    private MenuConfig menuConfig;


}
