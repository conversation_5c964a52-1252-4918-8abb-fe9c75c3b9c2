package com.zilue.module.form.dto;

import com.zilue.common.page.PageInput;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Range;

/**
 * @Author: zilue
 * @Date: 2023/5/9 16:05
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class FormTemplatePageDto extends PageInput {

    @Range(min = 0, max = 1, message = "表单类型只能为0或1")
    private Integer type;

    private Long category = 0L;
}
