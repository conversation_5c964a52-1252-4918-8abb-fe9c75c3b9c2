package com.zilue.module.form.controller;

import cn.hutool.core.bean.BeanUtil;
import com.github.yulichang.toolkit.MPJWrappers;
import com.zilue.common.annotation.XjrLog;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.model.result.R;
import com.zilue.common.utils.VoToColumnUtil;
import com.zilue.module.form.dto.FormHistoryChangeDto;
import com.zilue.module.form.entity.FormHistory;
import com.zilue.module.form.entity.FormTemplate;
import com.zilue.module.form.service.IFormHistoryService;
import com.zilue.module.form.vo.FormHistoryListVo;
import com.zilue.module.form.vo.FormTemplateVo;
import com.zilue.module.organization.entity.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 自定义表单历史表  每次修改自定义表单会新增一条数据 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
@RestController
@RequestMapping(GlobalConstant.FORM_MODULE_PREFIX + "/history")
@Api(value = GlobalConstant.FORM_MODULE_PREFIX + "/history", tags = "自定义表单历史表")
@AllArgsConstructor
public class FormHistoryController {

    private final IFormHistoryService formHistoryService;

    @GetMapping(value = "/list")
    @ApiOperation(value = "历史记录")
    @XjrLog(value = "自定义表单历史记录")
    public R list(@RequestParam Long formId) {

        List<FormHistoryListVo> formHistoryListVos = formHistoryService.selectJoinList(FormHistoryListVo.class,
                MPJWrappers.<FormHistory>lambdaJoin()
                        .eq(FormHistory::getFormId, formId)
                        .select(FormHistory::getId)
                        .select(FormHistory.class, x -> VoToColumnUtil.fieldsToColumns(FormHistoryListVo.class).contains(x.getProperty()))
                        .selectAs(User::getName, FormHistoryListVo::getCreateUserName)
                        .leftJoin(User.class, User::getId, FormHistory::getCreateUserId)
                        .orderByDesc(FormTemplate::getCreateDate)
        );

        List<FormHistoryListVo> listVos = BeanUtil.copyToList(formHistoryListVos, FormHistoryListVo.class);
        return R.ok(listVos);
    }

    @PutMapping("/set-version")
    @ApiOperation(value = "代码生成，更新到此版本")
    public R change(@Valid @RequestBody FormHistoryChangeDto dto) {
        return R.ok(formHistoryService.change(dto));
    }

    @GetMapping("/preview")
    @ApiOperation(value = "历史记录的xml")
    public R preview(@RequestParam Long historyId){
        FormHistory history = formHistoryService.getById(historyId);
        return R.ok(history.getFormJson());
    }

    @GetMapping(value = "/info")
    @ApiOperation(value = "表单设计模板详情")
    @XjrLog(value = "表单设计模板详情")
    public R info(@RequestParam Long id) {
        FormHistory history = formHistoryService.getById(id);
        if (history == null) {
            return R.error("找不到此数据！");
        }
        return R.ok(BeanUtil.toBean(history, FormTemplateVo.class));
    }

}
