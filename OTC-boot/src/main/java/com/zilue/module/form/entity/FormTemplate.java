package com.zilue.module.form.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zilue.common.model.base.AuditEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 表单设计模板 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
@TableName("xjr_form_template")
@ApiModel(value = "FormTemplate对象", description = "表单设计模板 ")
@Data
@EqualsAndHashCode(callSuper = false)
public class FormTemplate extends AuditEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("表单模板名称")
    private String name;

    @ApiModelProperty("表单分类 关联 数据字典")
    private Long category;

    @ApiModelProperty("表单类型:0 系统表单 1 自定义表单")
    private Integer formType;

    @ApiModelProperty("表单设计类型（0-数据优先，1-界面优先，2-简易模板）")
    private Integer formDesignType;

    @ApiModelProperty("表单设计json")
    private String formJson;

    @ApiModelProperty("排序号")
    private Integer sortCode;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("界面设计及菜单设计部分json")
    private String configJson;

}
