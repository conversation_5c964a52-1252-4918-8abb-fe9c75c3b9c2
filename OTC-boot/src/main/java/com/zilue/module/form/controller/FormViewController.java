package com.zilue.module.form.controller;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zilue.common.annotation.XjrLog;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.enums.YesOrNoEnum;
import com.zilue.common.model.result.R;
import com.zilue.module.form.dto.*;
import com.zilue.module.form.entity.FormView;
import com.zilue.module.form.service.IFormViewService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 表单视图 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-23
 */
@RestController
@RequestMapping(GlobalConstant.FORM_MODULE_PREFIX + "/view")
@Api(value = GlobalConstant.FORM_MODULE_PREFIX + "/view", tags = "表单视图")
@AllArgsConstructor
public class FormViewController {

    private final IFormViewService formViewService;

    @GetMapping("/page")
    @ApiOperation(value = "表单视图列表")
    public R page(FormViewPageDto dto){
        List<FormView> list = formViewService.list(Wrappers.<FormView>lambdaQuery()
                .eq(FormView::getObjectId, dto.getFormId())
                .eq(StrUtil.isNotBlank(dto.getKeyword()), FormView::getName, dto.getKeyword()));
        return R.ok(list);
    }

    @PostMapping
    @ApiOperation(value = "表单视图新增")
    @XjrLog(value = "表单视图新增")
    public R add(@Valid @RequestBody AddFormViewDto dto) {
        FormView formView = BeanUtil.toBean(dto, FormView.class);
        formViewService.save(formView);
        return R.ok(true);
    }

    @PutMapping
    @ApiOperation(value = "表单视图修改")
    @XjrLog(value = "表单视图修改" )
    public R update(@Valid @RequestBody UpdateFormViewDto dto) {
        FormView formView = BeanUtil.toBean(dto, FormView.class);
        formViewService.updateById(formView);
        return R.ok(true);
    }

    @DeleteMapping
    @ApiOperation(value = "删除")
    @XjrLog(value = "删除")
    public R delete(@Valid @RequestBody List<Long> ids) {
        return R.ok(formViewService.removeBatchByIds(ids));
    }

    @GetMapping(value = "/info")
    @ApiOperation(value = "视图详情")
    @XjrLog(value = "视图详情")
    public R info(@RequestParam Long id) {
        //因为多表关联 会有多个表都使用了id字段，  所以必须专门指定主表的Id
        FormView formView = formViewService.getById(id);
        if (formView == null) {
            return R.error("视图数据不存在");
        }
        return R.ok(formView);
    }

    @GetMapping(value = "/get-user-view")
    @ApiOperation(value = "获取用户所具有的视图")
    @XjrLog(value = "获取用户所具有的视图")
    public R getUserView(@RequestParam Long objectId) {
        //通过传进来的自定义表单或者系统表单的id，获取当前用户所具有的表单视图
        List<FormView> list = formViewService.list(Wrappers.<FormView>lambdaQuery()
                .eq(FormView::getObjectId, objectId).eq(FormView::getIsPublic, YesOrNoEnum.NO.getCode()) //公共的视图
                .or()
                .eq(FormView::getObjectId, objectId).eq(FormView::getIsPublic, YesOrNoEnum.YES.getCode())
                .eq(FormView::getCreateUserId, StpUtil.getLoginIdAsLong()));//个人的视图
        return R.ok(list);
    }
}
