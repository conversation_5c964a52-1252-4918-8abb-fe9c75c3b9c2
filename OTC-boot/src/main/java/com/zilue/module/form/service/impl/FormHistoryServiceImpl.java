package com.zilue.module.form.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.zilue.common.enums.YesOrNoEnum;
import com.zilue.module.form.dto.FormHistoryChangeDto;
import com.zilue.module.form.entity.FormHistory;
import com.zilue.module.form.entity.FormTemplate;
import com.zilue.module.form.mapper.FormHistoryMapper;
import com.zilue.module.form.mapper.FormTemplateMapper;
import com.zilue.module.form.service.IFormHistoryService;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 自定义表单历史表  每次修改自定义表单会新增一条数据 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
@Service
@AllArgsConstructor
public class FormHistoryServiceImpl extends MPJBaseServiceImpl<FormHistoryMapper, FormHistory> implements IFormHistoryService {

    private final FormHistoryMapper formHistoryMapper;

    private final FormTemplateMapper formTemplateMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @SneakyThrows
    public Boolean change(FormHistoryChangeDto dto) {

        //全部设置为非活动版本
        LambdaQueryWrapper<FormHistory> wrapper = Wrappers.lambdaQuery(FormHistory.class).eq(FormHistory::getFormId, dto.getFormId());
        FormHistory formHistory1 = new FormHistory();
        formHistory1.setActivityFlag(YesOrNoEnum.NO.getCode());
        formHistoryMapper.update(formHistory1, wrapper);

        //把选择的版本设置为活动版本
        FormHistory formHistory = formHistoryMapper.selectById(dto.getId());
        formHistory.setActivityFlag(YesOrNoEnum.YES.getCode());
        formHistoryMapper.updateById(formHistory);

        //更新formTemplate表的数据
        LambdaQueryWrapper<FormTemplate> formTemplateLambdaQuery = Wrappers.lambdaQuery(FormTemplate.class).eq(FormTemplate::getId, dto.getFormId());
        FormTemplate formTemplate = new FormTemplate();
        formTemplate.setFormJson(formHistory.getFormJson());
        formTemplateMapper.update(formTemplate,formTemplateLambdaQuery);

        return true;
    }
}
