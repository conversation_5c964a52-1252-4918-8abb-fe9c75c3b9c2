package com.zilue.module.form.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.github.yulichang.toolkit.MPJWrappers;
import com.zilue.common.annotation.XjrLog;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.enums.YesOrNoEnum;
import com.zilue.common.model.generator.FormConfig;
import com.zilue.common.model.result.R;
import com.zilue.common.page.ConventPage;
import com.zilue.common.page.PageOutput;
import com.zilue.common.utils.VoToColumnUtil;
import com.zilue.module.form.dto.*;
import com.zilue.module.form.entity.FormDesignConfig;
import com.zilue.module.form.entity.FormHistory;
import com.zilue.module.form.entity.FormRelease;
import com.zilue.module.form.entity.FormTemplate;
import com.zilue.module.form.service.IFormHistoryService;
import com.zilue.module.form.service.IFormReleaseService;
import com.zilue.module.form.service.IFormTemplateService;
import com.zilue.module.form.vo.FormReleaseVo;
import com.zilue.module.form.vo.FormTemplateListVo;
import com.zilue.module.form.vo.FormTemplatePageVo;
import com.zilue.module.form.vo.FormTemplateVo;
import com.zilue.module.generator.dto.DataFirstPreviewDto;
import com.zilue.module.generator.entity.ListConfig;
import com.zilue.module.generator.entity.MenuConfig;
import com.zilue.module.generator.entity.TableConfig;
import com.zilue.module.generator.service.IGeneratorService;
import com.zilue.module.organization.entity.User;
import com.zilue.module.system.entity.DictionaryDetail;
import com.zilue.module.system.entity.Menu;
import com.zilue.module.system.service.ICodeSchemaService;
import com.zilue.module.system.service.IDictionarydetailService;
import com.zilue.module.system.service.IMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 表单设计模板  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
@RestController
@RequestMapping(GlobalConstant.FORM_MODULE_PREFIX + "/template")
@Api(value = GlobalConstant.FORM_MODULE_PREFIX + "/template", tags = "自定义表单-代码生成器 模板设计")
@AllArgsConstructor
public class FormTemplateController {

    private final IFormTemplateService formTemplateService;

    private final IFormHistoryService formHistoryService;

    private final IFormReleaseService formReleaseService;

    private final IGeneratorService generatorService;

    private final ICodeSchemaService codeSchemaService;

    private final IDictionarydetailService dictionarydetailService;

    private final IMenuService menuService;



    @GetMapping(value = "/list")
    @ApiOperation(value = "表单设计模板(不分页)")
    @XjrLog(value = "表单设计模板")
    public R list(@Valid FormTemplateListDto dto) {
        List<FormTemplate> list = formTemplateService.list(Wrappers.lambdaQuery(FormTemplate.class)
                .eq(FormTemplate::getFormType, dto.getType())
                .eq(dto.getCategory() != 0, FormTemplate::getCategory, dto.getCategory())
                .like(StrUtil.isNotBlank(dto.getKeyword()), FormTemplate::getName, dto.getKeyword())
                .orderByDesc(FormTemplate::getCreateDate)
                .select(FormTemplate.class, x -> VoToColumnUtil.fieldsToColumns(FormTemplateListVo.class).contains(x.getProperty())));

        List<FormTemplateListVo> formTemplateListVos = BeanUtil.copyToList(list, FormTemplateListVo.class);
        return R.ok(formTemplateListVos);
    }

    @GetMapping(value = "/page")
    @ApiOperation(value = "表单设计模板(分页),过滤未启用数据")
    @XjrLog(value = "表单设计模板")
    public R page(@Valid FormTemplatePageDto dto) {

        List<Menu> list = menuService.list(Wrappers.<Menu>lambdaQuery().ne(Menu::getComponent,"IFrame").select(Menu::getId, Menu::getFormId, Menu::getComponent));
        List<Long> formIdList = list.stream().map(Menu::getFormId).collect(Collectors.toList());
        //因为多表关联 会有多个表都使用了id字段，  所以必须专门指定主表的Id
        IPage<FormTemplatePageVo> page = formTemplateService.selectJoinListPage(ConventPage.getPage(dto), FormTemplatePageVo.class,
                MPJWrappers.<FormTemplate>lambdaJoin().disableSubLogicDel()
                        .orderByDesc(FormTemplate::getCreateDate)
                        .like(StrUtil.isNotBlank(dto.getKeyword()), FormTemplate::getName, dto.getKeyword())
                        .eq(FormTemplate::getFormType, dto.getType())
                        .eq(ObjectUtil.isNotNull(dto.getCategory()) && dto.getCategory() != 0, FormTemplate::getCategory, dto.getCategory())
                        .eq(FormTemplate::getEnabledMark, YesOrNoEnum.YES.getCode())
                        .in(dto.getType() == YesOrNoEnum.NO.getCode(),FormTemplate::getId, formIdList) //如果是系统表单就过滤掉在menu表里面没有的系统表单，不需要使用
                        .select(FormTemplate::getId)
                        .selectAs(User::getName, FormTemplatePageVo::getCreateUserName)
                        .select(FormTemplate.class, x -> VoToColumnUtil.fieldsToColumns(FormTemplatePageVo.class).contains(x.getProperty()))
                        .selectAs(DictionaryDetail::getName, FormTemplatePageVo::getCategoryName)
                        .leftJoin(DictionaryDetail.class, DictionaryDetail::getId, FormTemplate::getCategory)
                        .leftJoin(User.class, User::getId, FormTemplate::getCreateUserId));

        List<FormTemplatePageVo> removeRecord = new ArrayList<>();
        if (dto.getType()== YesOrNoEnum.NO.getCode()){//系统表单
            List<FormTemplatePageVo> records = page.getRecords();
            if (records.size() > 0) {
                for (FormTemplatePageVo record : records) {
                    //去menu表里面找道对应的功能名称和功能模块名称
                    Menu menu = list.stream().filter(x -> ObjectUtil.isNotEmpty(x.getFormId()) && x.getFormId().equals(record.getId())).findFirst().orElse(new Menu());
                    if (menu != null && ObjectUtil.isNotEmpty(menu.getId())) {
                        String[] split = menu.getComponent().split(StringPool.SLASH);
                        record.setFunctionalModule(split[1]);
                        record.setFunctionName(split[2]);
                    } else {
                        removeRecord.add(record);
                    }
                }
                long size = removeRecord.size();
                page.setTotal(page.getTotal() - size);
                records.removeAll(removeRecord);
                page.setRecords(records);
            }
        }

        PageOutput<FormTemplatePageVo> pageOutput = ConventPage.getPageOutput(page);
        return R.ok(pageOutput);
    }

    @GetMapping(value = "/enabled-page")
    @ApiOperation(value = "表单设计模板(分页)，不过滤未启用数据")
    @XjrLog(value = "表单设计模板")
    public R pageEnabled(@Valid FormTemplatePageDto dto) {

        //因为多表关联 会有多个表都使用了id字段，  所以必须专门指定主表的Id
        IPage<FormTemplatePageVo> page = formTemplateService.selectJoinListPage(ConventPage.getPage(dto), FormTemplatePageVo.class,
                MPJWrappers.<FormTemplate>lambdaJoin().disableSubLogicDel()
                        .orderByDesc(FormTemplate::getCreateDate)
                        .like(StrUtil.isNotBlank(dto.getKeyword()), FormTemplate::getName, dto.getKeyword())
                        .eq(FormTemplate::getFormType, dto.getType())
                        .eq(ObjectUtil.isNotNull(dto.getCategory()) && dto.getCategory() != 0, FormTemplate::getCategory, dto.getCategory())
                        .select(FormTemplate::getId)
                        .selectAs(User::getName, FormTemplatePageVo::getCreateUserName)
                        .select(FormTemplate.class, x -> VoToColumnUtil.fieldsToColumns(FormTemplatePageVo.class).contains(x.getProperty()))
                        .selectAs(DictionaryDetail::getName, FormTemplatePageVo::getCategoryName)
                        .leftJoin(DictionaryDetail.class, DictionaryDetail::getId, FormTemplate::getCategory)
                        .leftJoin(User.class, User::getId, FormTemplate::getCreateUserId));

        PageOutput<FormTemplatePageVo> pageOutput = ConventPage.getPageOutput(page);
        return R.ok(pageOutput);
    }

    @GetMapping(value = "/info")
    @ApiOperation(value = "表单设计模板详情")
    @XjrLog(value = "表单设计模板详情")
    public R info(@RequestParam Long id) {
        FormTemplate template = formTemplateService.getById(id);
        if (template == null) {
            return R.error("找不到此数据！");
        }
        FormTemplateVo formTemplateVo = BeanUtil.toBean(template, FormTemplateVo.class);
        if (template.getFormType() == YesOrNoEnum.NO.getCode()){//系统表单
            //去menu表里面找道对应的功能名称和功能模块名称
            Menu menu = menuService.getOne(Wrappers.<Menu>lambdaQuery().eq(Menu::getFormId, template.getId()));
            if (menu != null){
                String[] split = menu.getComponent().split(StringPool.SLASH);
                formTemplateVo.setFunctionalModule(split[1]);
                formTemplateVo.setFunctionName(split[2]);
            }else {
                return R.error("找不到对应的系统表单功能名称和功能模块名称");
            }
        }
        return R.ok(formTemplateVo);
    }

    @GetMapping(value = "/info/multi")
    @ApiOperation(value = "表单设计模板详情(批量查询)")
    @XjrLog(value = "表单设计模板详情(批量查询)")
    public R multiInfo(@RequestParam String id) {
        List<FormTemplate> formTemplates = formTemplateService.listByIds(Arrays.asList(id.split(StringPool.COMMA)));
        List<FormTemplateVo> formTemplateVos = BeanUtil.copyToList(formTemplates, FormTemplateVo.class);
        return R.ok(formTemplateVos);
    }

    @PutMapping("/status")
    @ApiOperation(value = "修改表单状态")
    @XjrLog(value = "修改表单状态")
    public R updateEnabled(@Valid @RequestBody UpdateTemplateStatusDto dto) {
        //根据id修改表单enabledMark
        return R.ok(formTemplateService.update(Wrappers.<FormTemplate>update().lambda().set(FormTemplate::getEnabledMark, dto.getEnabledMark()).eq(FormTemplate::getId, dto.getId())));
    }

    @PostMapping(value = "/data-first")
    @ApiOperation(value = "数据优先 新增")
    @XjrLog(value = "数据优先新增")
    public R addDataFirst(@Valid @RequestBody AddFormDataFirstDto dto) {
        return R.ok(formTemplateService.addDataFirst(dto));
    }

    @PostMapping(value = "/code-first")
    @ApiOperation(value = "界面优先 或者  简易模板 新增")
    @XjrLog(value = "界面优先 或者 简易模板新增")
    public R addCodeFirst(@Valid @RequestBody AddFormCodeFirstDto dto) throws Exception {
        return R.ok(formTemplateService.addCodeFirst(dto));
    }

    @PutMapping(value = "/data-first")
    @ApiOperation(value = "修改数据优先")
    @XjrLog(value = "修改数据优先")
    public R updateDataFirst(@Valid @RequestBody UpdateFormDataFirstDto dto) throws JsonProcessingException {
        return R.ok(formTemplateService.updateDataFirst(dto));
    }


    @PutMapping(value = "/code-first")
    @ApiOperation(value = "修改界面优先")
    @XjrLog(value = "修改界面优先")
    public R updateCodeFirst(@Valid @RequestBody UpdateFormCodeFirstDto dto) throws Exception {
        return R.ok(formTemplateService.updateCodeFirst(dto));
    }

    @DeleteMapping
    @ApiOperation(value = "删除")
    public R delete(@Valid @RequestBody List<Long> ids) {
        List<FormReleaseVo> formReleaseList = formReleaseService.selectJoinList(FormReleaseVo.class,MPJWrappers.<FormRelease>lambdaJoin()
                .select(FormRelease::getId)
                .selectAs(FormTemplate::getName, FormReleaseVo::getFormName)
                .leftJoin(FormTemplate.class, FormTemplate::getId, FormRelease::getFormId)
                .eq(ids.size() == 1, FormRelease::getFormId, ids.get(0))
                .in(ids.size() > 1, FormRelease::getFormId, ids));
        if (CollectionUtils.isNotEmpty(formReleaseList)) {
            if (ids.size() == 1) {
                return R.error("该表单已经发布成功能，不允许删除。");
            } else {
                return R.error("选择的数据中有已经发布成功能的模板，不允许删除。");
            }
        }
        formHistoryService.remove(Wrappers.lambdaQuery(FormHistory.class).in(FormHistory::getFormId, ids));
        return R.ok(formTemplateService.removeBatchByIds(ids));
    }

    @PostMapping(value = "/test-code")
    @ApiOperation(value = "表单测试代码生成器")
    @XjrLog(value = "测试表单预览代码生成器")
    public R test(@Valid @RequestBody DateTestDto dto){
        FormRelease formRelease = formReleaseService.getById(dto.getFormReleaseId());
        FormTemplate formTemplate = formTemplateService.getById(dto.getFormTemplateId());
        JSONObject configJson = JSON.parseObject(formRelease.getConfigJson());
        JSONObject formJson = JSON.parseObject(formTemplate.getFormJson());
        dto.setDatabaseId(formJson.get("databaseId").toString());
        dto.setTableConfigs(JSONObject.parseArray(formJson.getString("tableConfigs"),TableConfig.class));
        dto.setFormJson(JSON.parseObject(formJson.getString("formJson"),FormConfig.class));
        dto.setListConfig(JSON.parseObject(configJson.getString("listConfig"), ListConfig.class));
        dto.setMenuConfig(JSON.parseObject(configJson.getString("menuConfig"), MenuConfig.class));
        dto.setOutputConfig(dto.getOutputConfig());
        return R.ok(generatorService.getPreviewCodes(BeanUtil.toBean(dto, DataFirstPreviewDto.class)));
    }

    @GetMapping(value = "/desk-form-page")
    @ApiOperation(value = "桌面设计-复杂列表页使用(分页),过滤未启用数据")
    @XjrLog(value = "表单设计模板")
    public R deskFormPage(@Valid FormTemplateDeskPageDto dto) {
        //只需要已发布的，和系统表单的数据
        List<Long> releaseIds = formReleaseService.list().stream().map(FormRelease::getFormId).collect(Collectors.toList());
        //因为多表关联 会有多个表都使用了id字段，  所以必须专门指定主表的Id
        IPage<FormTemplatePageVo> page = formTemplateService.selectJoinListPage(ConventPage.getPage(dto), FormTemplatePageVo.class,
                MPJWrappers.<FormTemplate>lambdaJoin().disableSubLogicDel()
                        .orderByDesc(FormTemplate::getCreateDate)
                        .like(StrUtil.isNotBlank(dto.getKeyword()), FormTemplate::getName, dto.getKeyword())
                        .eq(FormTemplate::getFormType, dto.getType())
                        .eq(ObjectUtil.isNotNull(dto.getCategory()) && dto.getCategory() != 0, FormTemplate::getCategory, dto.getCategory())
                        .eq(FormTemplate::getEnabledMark,YesOrNoEnum.YES.getCode())
                        .in(dto.getType() == YesOrNoEnum.YES.getCode() && CollectionUtil.isNotEmpty(releaseIds),FormTemplate::getId,releaseIds) //自定义表单需要过滤出来已发布的数据
                        .select(FormTemplate::getId)
                        .selectAs(User::getName, FormTemplatePageVo::getCreateUserName)
                        .select(FormTemplate.class, x -> VoToColumnUtil.fieldsToColumns(FormTemplatePageVo.class).contains(x.getProperty()))
                        .selectAs(DictionaryDetail::getName, FormTemplatePageVo::getCategoryName)
                        .leftJoin(DictionaryDetail.class, DictionaryDetail::getId, FormTemplate::getCategory)
                        .leftJoin(User.class, User::getId, FormTemplate::getCreateUserId));

        List<FormTemplatePageVo> records = page.getRecords();
        if (records.size() > 0){
            for (FormTemplatePageVo record : records) {
                if (record.getFormType() == YesOrNoEnum.NO.getCode()){//如果是系统表单的数据，需要返回给你类名等数据
                    //去menu表里面找道对应的功能名称和功能模块名称
                    Menu menu = menuService.getOne(Wrappers.<Menu>lambdaQuery().eq(Menu::getFormId, record.getId()));
                    if (menu != null) {
                        String[] split = menu.getComponent().split(StringPool.SLASH);
                        record.setFunctionalModule(split[1]);
                        record.setFunctionName(split[2]);
                    }
                }
            }
        }
        PageOutput<FormTemplatePageVo> pageOutput = ConventPage.getPageOutput(page);
        return R.ok(pageOutput);
    }


    @GetMapping("/export")
    @ApiOperation(value = "表单设计导出")
    @SneakyThrows
    public R export(@RequestParam Long id) {
        FormTemplate formTemplate = formTemplateService.getById(id);
        if (formTemplate == null) {
            return R.error("找不到此表单信息！");
        }
        AddFormCodeFirstDto addFormCodeFirstDto = new AddFormCodeFirstDto();
        addFormCodeFirstDto.setName(formTemplate.getName());
        addFormCodeFirstDto.setFormType(formTemplate.getFormType());
        addFormCodeFirstDto.setFormDesignType(formTemplate.getFormDesignType());
        addFormCodeFirstDto.setRemark(formTemplate.getRemark());
        addFormCodeFirstDto.setCategory(formTemplate.getCategory());
        FormDesignConfig formDesignConfig = JSONUtil.toBean(formTemplate.getFormJson(),FormDesignConfig.class);
        addFormCodeFirstDto.setFormJson(formDesignConfig);
        return R.ok(JSONUtil.toJsonStr(addFormCodeFirstDto));
    }

    @PostMapping("/import")
    @ApiOperation(value = "表单设计导入")
    @SneakyThrows
    public R importForm(@RequestParam(value = "file") MultipartFile multipartFile) {
        return R.ok(formTemplateService.importForm(multipartFile));
    }

}

