package com.zilue.module.form.entity;

import com.zilue.common.model.generator.FormConfig;
import com.zilue.module.generator.entity.TableConfig;
import com.zilue.module.generator.entity.TableStructureConfig;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 表单设计的json
 * @Author: zilue
 * @Date: 2023/5/11 16:44
 */
@Data
public class FormDesignConfig {


    @ApiModelProperty("备注")
    @NotBlank(message = "数据库id不能为空！")
    private String databaseId;

    /**
     * 表结构配置  代码优先！！！！
     */
    @NotNull(message = "表结构配置不能为空！")
    @Valid
    private List<TableStructureConfig> tableStructureConfigs;

    @ApiModelProperty("表单事件")
    private Map<String, Object>  formEventConfig;

    /**
     * 表关联信息  数据优先！！！！
     */
    @NotNull(message = "表配置不能为空！")
    @Valid
    private List<TableConfig> tableConfigs;



    @NotNull(message = "表单配置不能为空！")
    @Valid
    private FormConfig formJson;

    /**
     * 是否配置数据权限
     */
    private Boolean isDataAuth;

    /**
     * 表单设计是否转换为数据优先
     */
    private Boolean toDataFirst;

    /**
     * 是否添加审计字段
     */
    private Boolean isCommonFields = false;
    /**
     * 选择的数据权限id集合
     */
    private List<String> dataAuthList;
}
