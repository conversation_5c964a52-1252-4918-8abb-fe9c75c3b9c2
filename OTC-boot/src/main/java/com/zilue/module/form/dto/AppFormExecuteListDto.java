package com.zilue.module.form.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * @Author: zilue
 * @Date: 2023/8/17 14:40
 */
@Data
public class AppFormExecuteListDto {

    /**
     * 功能id
     */
    @NotNull(message = "功能id")
    private Long funcId;

    /**
     * 自定义表单 查询参数
     */
    private Map<String,Object> params;

    /**
     * 导出配置（是否是导出模板）
     */
    private Boolean isTemplate = false;

    /**
     * 是否翻译为显示值
     */
    private Boolean isTrans  = true;
}
