package com.zilue.module.form.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: zilue
 * @Date: 2023/5/9 15:56
 */
@Data
public class FormTemplatePageVo {
    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("表单模板名称")
    private String name;

    @ApiModelProperty("表单分类 关联 数据字典")
    private String category;

    @ApiModelProperty("表单分类 关联 数据字典")
    private String categoryName;

    @ApiModelProperty("表单类型:0 系统表单 1 自定义表单")
    private Integer formType;

    @ApiModelProperty("表单设计类型（0-数据优先，1-界面优先，2-简易模板）")
    private Integer formDesignType;

    @ApiModelProperty("排序号")
    private Integer sortCode;

    @ApiModelProperty("状态")
    private Integer enabledMark;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("创建时间")
    private LocalDateTime createDate;

    @ApiModelProperty("创建人名称")
    private String createUserName;

    /**
     * 功能模块名称
     */
    private String functionalModule;

    /**
     * 功能名称
     */
    private String functionName;

    /**
     * Form页面名称
     */
    private String functionFormName;

}
