package com.zilue.module.form.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: zilue
 * @Date: 2023/5/10 16:18
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class UpdateFormViewDto {

    @ApiModelProperty("主键")
    private Long id;
    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("可见范围，0-公开，1-个人,默认为个人")
    private Integer isPublic;

    @ApiModelProperty("表单类型，0-系统表单，1-自定义表单发布")
    private Integer formViewType;

    @ApiModelProperty("查询类型，0-快捷查询，1-高级查询，默认快捷查询")
    private Integer queryType;

    @ApiModelProperty("系统表单formId,自定义表单releaseId的id值")
    private Long objectId;

    @ApiModelProperty("视图配置json")
    private String configJson;

    @ApiModelProperty("组合公式")
    private String combinationFormula;

    @ApiModelProperty("排序号")
    private Integer sortCode;

    @ApiModelProperty("备注")
    private String remark;
}
