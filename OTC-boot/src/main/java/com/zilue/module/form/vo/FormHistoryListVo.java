package com.zilue.module.form.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: zilue
 * @Date: 2023/5/10 10:38
 */
@Data
public class FormHistoryListVo {

    private Long id;

    private Long formId;

    @ApiModelProperty("版本号")
    private Integer version;

    @ApiModelProperty("是否为活动版本（1-是，0-否）")
    private Integer activityFlag;

    private String createUserName;

    private LocalDateTime createDate;

    private Integer status;
}
