package com.zilue.module.form.dto;

import com.zilue.common.page.PageInput;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * @Author: zilue
 * @Date: 2023/5/11 14:59
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class FormExecutePageDto extends PageInput {
    /**
     * 发布id
     */
    @NotNull(message = "发布id")
    private Long releaseId;

    /**
     * 自定义表单 查询参数
     */
    private Map<String,Object> params;

    /**
     * 导出配置（是否是导出模板）
     */
    private Boolean isTemplate = false;

    /**
     * 是否翻译为显示值
     */
    private Boolean isTrans  = true;
}
