package com.zilue.module.form.service;

import com.github.yulichang.base.MPJBaseService;
import com.zilue.module.form.dto.AddFormReleaseDto;
import com.zilue.module.form.dto.UpdateFormReleaseDto;
import com.zilue.module.form.entity.FormRelease;

/**
 * <p>
 * 自定义表单 发布表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
public interface IFormReleaseService extends MPJBaseService<FormRelease> {
    /**
     * 自定义表单发布
     * @param dto
     * @return
     */
    Boolean addFormRelease(AddFormReleaseDto dto);

    /**
     * 修改自定义表单发布
     * @param dto
     * @return
     */
    Boolean updateFormRelease(UpdateFormReleaseDto dto);
}
