package com.zilue.module.form.service;

import com.github.yulichang.base.MPJBaseService;
import com.zilue.module.form.dto.FormHistoryChangeDto;
import com.zilue.module.form.entity.FormHistory;

/**
 * <p>
 * 自定义表单历史表  每次修改自定义表单会新增一条数据 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
public interface IFormHistoryService extends MPJBaseService<FormHistory> {

    Boolean change(FormHistoryChangeDto dto);

}
