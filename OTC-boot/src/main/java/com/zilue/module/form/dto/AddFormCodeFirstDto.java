package com.zilue.module.form.dto;

import com.zilue.module.form.entity.FormDesignConfig;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author: zilue
 * @Date: 2023/5/9 16:46
 */
@Data
public class AddFormCodeFirstDto {

    @ApiModelProperty("表单模板名称")
    @NotBlank(message = "表单模板名称不能为空")
    private String name;

    @ApiModelProperty("表单分类 关联 数据字典")
    private Long category;

    @ApiModelProperty("表单类型:0 系统表单 1 自定义表单")
    @Range(min = 0, max = 1, message = "表单类型错误")
    private Integer formType = 1;

    @ApiModelProperty("表单设计类型（0-数据优先，1-界面优先，2-简易模板）")
    private Integer formDesignType;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("表单设计json")
    @NotNull(message = "表单设计json不能为空")
    private FormDesignConfig formJson;



}
