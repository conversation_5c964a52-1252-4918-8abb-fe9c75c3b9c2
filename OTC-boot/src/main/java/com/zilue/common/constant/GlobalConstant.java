package com.zilue.common.constant;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.annotation.DbType;

import java.util.Arrays;
import java.util.List;

/**
 * @Author: zilue
 * @Date: 2023/3/3 16:35
 */
public interface GlobalConstant {


    /**
     * 超级管理员角色id
     */
    Long SUPER_ADMIN_ROLE_ID = 1L;

    /**
     * 超级管理员用户默认id
     */
    Long SUPER_ADMIN_USER_ID = 1000000000000000000L;

    /**
     * 框架 约定 默认数据源的名称
     */
    String DEFAULT_DATASOURCE_KEY = "master";


    /**
     * 框架 约定 默认数据库连接的id
     */
    Long DEFAULT_DATABASELINKID= 0L;

    /**
     * @des 账户加密字符串
     * */
    String SECRET_KEY = "xxxxxxxxxxxxxxxx";

    /**
     * @des 对称加密密钥
     * */
    String AES_SECRET_KEY = "123456789abcdefg";

    /**
     * ureport http-basic 验证 账号密码
     */
    String UREPORT_ACCOUNT = "zilue";

    /**
     * ureport http-basic 验证 账号密码
     */
    String UREPORT_PASSWORD = "123456";

    /**
     * @des 登录界面返回前端 token 的 key
     * */
    String TOKEN_KEY = "token";

    /**
     * @des sa-token 登陆人信息key
     *
     * */
    String LOGIN_USER_INFO_KEY = "LOGIN_USER_INFO_KEY";

    /**
     * @des sa-token 登陆人权限Code key
     * */
    String LOGIN_USER_AUTH_CODE_KEY = "LOGIN_USER_AUTH_CODE_KEY";

    /**
     * @des sa-token 登陆人自定义接口权限Code key
     * */
    String LOGIN_USER_INTERFACE_AUTH_CODE_KEY = "LOGIN_USER_INTERFACE_AUTH_CODE_KEY";

    /**
     * @des sa-token 登陆人角色Code key
     * */
    String LOGIN_USER_ROLE_CODE_KEY = "LOGIN_USER_ROLE_CODE_KEY";

    /**
     * @des sa-token 登陆人角色ID key
     * */
    String LOGIN_USER_ROLE_ID_KEY = "LOGIN_USER_ROLE_ID_KEY";

    /**
     * @des sa-token 登陆人当前部门信息 key
     * */
    String LOGIN_USER_DEPT_INFO_KEY = "LOGIN_USER_DEPT_INFO_KEY";

    /**
     * @des sa-token 登陆人所有部门集合 key
     * */
    String LOGIN_USER_DEPT_LIST_KEY = "LOGIN_USER_DEPT_LIST_KEY";

    /**
     * @des sa-token 登陆人当前岗位信息 key
     * */
    String LOGIN_USER_POST_INFO_KEY = "LOGIN_USER_POST_INFO_KEY";

    /**
     * @des sa-token 登陆人所有岗位集合 key
     * */
    String LOGIN_USER_POST_LIST_KEY = "LOGIN_USER_POST_LIST_KEY";

    /**
     * @des sa-token 开发者接口权限 key
     * */
    String LOGIN_DEVELOPER_AUTH_LIST_KEY = "LOGIN_DEVELOPER_AUTH_LIST_KEY";

    /**
     * @des 系统功能模块 前缀
     * */
    String SYSTEM_MODULE_PREFIX = "/system";

    /**
     * @des 第三方登录模块 前缀
     * */
    String OAUTH_MODULE_PREFIX = "/oauth";

    /**
     * @des bi功能模块 前缀
     * */
    String BI_MODULE_PREFIX = "/bi";

    /**
     * @des 组织架构模块 前缀
     * */
    String ORGANIZATION_MODULE_PREFIX = "/organization";

    /**
     * @des 表单模块 前缀
     * */
    String FORM_MODULE_PREFIX = "/form";

    /**
     * @des 工作流模块 前缀
     * */
    String WORKFLOW_MODULE_PREFIX = "/workflow";

    /**
     * @des 报表模块 前缀
     * */
    String REPORT_MODULE_PREFIX = "/report";

    /**
     * @des OA模块 前缀
     * */
    String OA_MODULE_PREFIX = "/oa";
	
    /**
     * @Des 翻译管理模块
     */
    String LANGUAGE_MODULE_PREFIX = "/language";

    /**
     * @Des magic-api模块
     */
    String MAGICAPI_MODULE_PREFIX = "/interface";

    /**
     * @Des liteflow模块
     */
    String LITEFLOW_MODULE_PREFIX = "/liteflow";

    /**
     * @Des 桌面设计模块
     */
    String DESKTOP_MODULE_PREFIX = "/desktop";

    /**
     * @Des 数据权限模块模块
     */
    String AUTHORITY_MODULE_PREFIX = "/authority";

    /**
     * @Des 产品模块
     */
    String PRODUCT_MODULE_PREFIX = "/product";

    /**
     * appModel模块
     */
    String APP_MODEL = "/app";
    /**
     * @des 系统功能模块 前缀
     * */
    String WECHAT_MODULE_PREFIX = "/wechat";
    /**
     * @des 系统功能模块 前缀
     * */
    String BUSINESS_MODULE_PREFIX = "/business";

    /**
     * @des 排序  降序
     * */

    String ORDER_DESC = "desc";

    /**
     * 代码生成器 默认生成路劲
     */
    String GENERATOR_DEFAULT_PATH = "com.zilue.module";


    /**
     * 数据库表 固定审计字段 创建人id
     */
    String CREATE_USER_ID = "create_user_id";

    /**
     * 数据库表 固定审计属性 创建人id
     */
    String CREATE_USER_ID_PROPERTY = "createUserId";

    /**
     * 数据库表 固定审计字段 创建人id
     */
    String CREATE_USER_NAME = "create_user_name";

    /**
     * 数据库表 固定审计属性 创建人id
     */
    String CREATE_USER_NAME_PROPERTY = "createUserName";

    /**
     * 数据库表 固定审计字段 创建时间
     */
    String CREATE_DATE = "create_date";

    /**
     * 数据库表 固定审计属性 创建时间
     */
    String CREATE_DATE_PROPERTY = "createDate";

    /**
     * 数据库表 固定审计字段 修改人id
     */
    String MODIFY_USER_ID = "modify_user_id";

    /**
     * 数据库表 固定审计属性 修改人id
     */
    String MODIFY_USER_ID_PROPERTY = "modifyUserId";

    /**
     * 数据库表 固定审计字段 修改人时间
     */
    String MODIFY_DATE = "modify_date";

    /**
     * 数据库表 固定审计属性 修改人时间
     */
    String MODIFY_DATE_PROPERTY = "modifyDate";

    /**
     * 数据库表 固定审计字段 删除标记
     */
    String DELETE_MARK = "delete_mark";

    /**
     * 数据库表 固定审计属性 删除标记
     */
    String DELETE_MARK_PROPERTY = "deleteMark";

    /**
     * 数据库表 固定审计字段 修改标记
     */
    String ENABLED_MARK = "enabled_mark";


    /**
     * 数据库表 固定审计属性 修改标记
     */
    String ENABLED_MARK_PROPERTY = "enabledMark";

    /**
     * 数据库表 数据权限字段
     */
    String AUTH_USER_ID = "rule_user_id";

    /**
     * 数据库表 数据权限属性名字
     */
    String AUTH_USER_ID_PROPERTY = "ruleUserId";

    /**
     * BI删除标记
     */
    String BI_DELETE_MARK_PROPERTY = "isDeleted";

    /**
     * BI删除标记
     */
    String BI_STATUS_PROPERTY = "status";
    /**
     * 自动填充的字段
     */
    List<String> AUTO_INSERT = Arrays.asList(CREATE_USER_ID, CREATE_USER_NAME,CREATE_DATE, DELETE_MARK, ENABLED_MARK, AUTH_USER_ID);

    /**
     * 自动填充的字段
     */
    List<String> AUTO_UPDATE = Arrays.asList(MODIFY_USER_ID, MODIFY_DATE);

    /**
     * 新增自动填充的属性
     */
    List<String> AUTO_INSERT_PROPERTY = Arrays.asList(CREATE_USER_ID_PROPERTY, CREATE_DATE_PROPERTY, DELETE_MARK_PROPERTY, ENABLED_MARK_PROPERTY);

    /**
     * 修改自动填充的属性
     */
    List<String> AUTO_UPDATE_PROPERTY = Arrays.asList(MODIFY_USER_ID_PROPERTY, MODIFY_DATE_PROPERTY);

    /**
     * 数据库排序 关键字
     */
    String ORDER_BY = "ORDER BY";

    /**
     * 数据库 约定 自定义表单 代码生成器 生成 时间区间的字段  开始时间后缀
     */
    String START_TIME_SUFFIX = "Start";

    /**
     * 数据库 约定 自定义表单 代码生成器 生成 时间区间的字段  结束时间后缀
     */
    String END_TIME_SUFFIX = "End";

    /**
     * 框架 约定 代码生成器 生成 数据库表 默认主键名
     */
    String DEFAULT_PK = "id";

    /**
     * 框架 约定 代码生成器 生成 数据库表 默认主键类型
     */
    String DEFAULT_PK_TYPE = "Long";

    /**
     * 框架 约定 代码生成器 生成 数据库表 父子表 关联字段
     */
    String DEFAULT_FK = "parent_id";

    /**
     * 框架 约定 代码生成器 生成 数据库表 默认 文本类型长度
     */
    String DEFAULT_TEXT_LENGTH = "50";

    /**
     * 框架约定树结构的根节点parentId 统一设置为0
     */
    Long  FIRST_NODE_VALUE  = 0L;

    /**
     * 框架用户表缓存key
     */
    String  USER_CACHE_KEY  = "ALL_USER";


    /**
     * 框架角色缓存key
     */
    String  ROLE_CACHE_KEY  = "ALL_ROLE";

    /**
     * 框架部门缓存key
     */
    String  DEP_CACHE_KEY  = "ALL_DEP";

    /**
     * 用户-角色 关联数据 数据 缓存key
     */
    String  USER_ROLE_RELATION_CACHE_KEY  = "ALL_USER_ROLE_RELATION";

    /**
     * 用户-岗位 关联数据 数据 缓存key
     */
    String  USER_POST_RELATION_CACHE_KEY  = "ALL_USER_POST_RELATION";

    /**
     * 用户-组织 关联数据 数据 缓存key
     */
    String  USER_DEPT_RELATION_CACHE_KEY  = "ALL_USER_DEPT_RELATION";

    /**
     * 框架岗位缓存key
     */
    String  POST_CACHE_KEY  = "ALL_POST";

    /**
     * 数据字典分类
     */
    String  DIC_ITEM_CACHE_KEY  = "ALL_DIC_ITEM";


    /**
     * 数据字典详情
     */
    String  DIC_DETAIL_CACHE_KEY  = "ALL_DIC_DETAIL";

    /**
     * 数据权限
     */
    String  DATA_AUTH_CACHE_KEY  = "ALL_DATA_AUTH";


    /**
     * 数据权限 配置
     */
    String  DATA_AUTH_CONFIG_CACHE_KEY  = "ALL_DATA_AUTH_CONFIG";


    /**
     * 数据权限 关联
     */
    String  DATA_AUTH_RELATION_CACHE_KEY  = "ALL_DATA_AUTH_RELATION";

    /**
     * 数据权限 与表 关联
     */
    String  DATA_AUTH_TABLE_RELATION_CACHE_KEY  = "ALL_DATA_AUTH_TABLE_RELATION";


    /**
     * yyyy-MM-dd HH:mm:ss 24小时制
     */
    String YYYY_MM_DD_HH_MM_SS_24 = "yyyy-MM-dd HH:mm:ss";

    /**
     * yyyy-MM-dd hh:mm:ss 12小时制
     */
    String YYYY_MM_DD_HH_MM_SS_12 = "yyyy-MM-dd hh:mm:ss";


    /**
     * yyyy-MM-dd
     */
    String YYYY_MM_DD = "yyyy-MM-dd";

    /**
     *  HH:mm:ss 24小时制
     */
    String HH_MM_SS_24 = "HH:mm:ss";

    /**
     * yyyy-hh:mm:ss 12小时制-dd
     */
    String HH_MM_SS_12 = "hh:mm:ss";



    /**
     * 验证码
     */
    String CAPTCHA =  "captcha";

    /**
     * 验证法发送次数前缀
     */
    String CACHE_COUNT_SMS_CODE_PREFIX = "COUNT_SMS_CODE_";

    /**
     * chatgpt 缓存前缀
     */
    String CHATGPT_PREFIX =  "chatgpt-";

    /**
     * 工作流 暂存缓存前缀
     */
    String STAGING_PREFIX =  "staging-";

    /**
     * 登录身份的缓存key 前缀
     */
    String LOGIN_IDENTITY_CACHE_PREFIX =  "identityCache:";

    /**
     * 登录错误次数缓存 前缀
     */
    String LOGIN_ERROR_NUMBER = "loginErrorNumber:";

    /**
     * 登录人权限的缓存key 前缀
     */
    String PERMISSION_CACHE_PREFIX =  "permission:";

    /**
     * 所支持的图片格式
     */
    List<String> imageType = ListUtil.toList("jpg","png","gif","jpeg","image","bmp");

    /**
     * 所支持的图片格式
     */
    List<String> videoType = ListUtil.toList("mp4","avi");


    /**
     * 微信token
     */
    String WechatAccessTokenKey = "WECHAT_ACCESS_TOKEN";

    /**
     * 钉钉token
     */
    String DingtalkAccessTokenKey = "DINGTALK_ACCTSS_TOKEN";

    /**
     * 外部流程发起人-缓存前缀
     */
    String ORIGINATOR_NODE =  "originatorNode-";

    DbType[] UPPERCASE_DB_TYPE_ARRAY = new DbType[]{DbType.ORACLE, DbType.ORACLE_12C, DbType.DM, DbType.DB2};

    /**
     * 防重复提交前缀
     */
    String PREVENT_DUPLICATION_PREFIX = "REPEAT_SUBMIT:";

    /**
     * 用户登录的方式
     */
    String USER_LOGIN_TYPE = "userLoginType:";
    int SIGINTYPE=1;//签到
    String daily="1";//每日
    String monthly="2";//每月
}
