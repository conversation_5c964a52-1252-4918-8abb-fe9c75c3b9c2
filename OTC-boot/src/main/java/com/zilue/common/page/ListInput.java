package com.zilue.common.page;

import cn.hutool.core.util.StrUtil;
import com.zilue.common.xss.SQLFilter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

/**
 * @Author: zilue
 * @Date: 2023/4/28 15:33
 */
@Data
@ToString
@ApiModel(value = "ListInput", description = "不分页入参")
public class ListInput {
    public ListInput(){
    }

    @ApiModelProperty(value = "排序字段")
    private String field;

    @ApiModelProperty(value = "排序方式 asc  desc")
    private String order;

    @ApiModelProperty(value = "关键词")
    @Length(max = 50, message = "关键词长度不能超过50")
    private String keyword;

    public String getField() {
        return SQLFilter.sqlInject(StrUtil.toUnderlineCase(this.field));
    }

    @ApiModelProperty(value = "树结构查询参数(url加密)")
    private String treeConditions;

    @ApiModelProperty(value = "高级查询参数")
    private String advancedQueryConditions;

    @ApiModelProperty(value = "高级查询组合公式")
    private String combinationFormula;

    public String getTreeConditions() {
        if (StrUtil.isNotBlank(this.treeConditions)) {
            try {
                return URLDecoder.decode(this.treeConditions, "UTF-8");
            } catch (UnsupportedEncodingException ignored) {
            }
        }
        return this.treeConditions;
    }
}
