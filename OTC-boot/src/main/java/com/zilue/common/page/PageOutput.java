package com.zilue.common.page;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: zilue
 * @Date:2022/3/7 14:55
 */
@Data
@ApiModel(value = "PageOutput", description = "分页出参")
public class PageOutput<T> implements Serializable {
    @ApiModelProperty(value = "总页数")
    public Integer totalPage;
    @ApiModelProperty(value = "当前页标")
    public Integer currentPage;
    @ApiModelProperty(value = "单页大小")
    public Integer pageSize;
    @ApiModelProperty(value = "总数")
    public Integer total;
    @ApiModelProperty(value = "总数···")
    public List<T> list;
}
