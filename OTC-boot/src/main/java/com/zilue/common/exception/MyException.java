package com.zilue.common.exception;

import com.zilue.common.enums.ResponseCode;

/**
 * 自定义异常
 * <AUTHOR>
 * @version 1.0
 */
public class MyException extends RuntimeException {
	private static final long serialVersionUID = 1L;
	
    private String msg;
    private int code = ResponseCode.INTERNAL_SERVER_ERROR.getCode();
    
    public MyException(String msg) {
		super(msg);
		this.msg = msg;
	}
	
	public MyException(String msg, Throwable e) {
		super(msg, e);
		this.msg = msg;
	}
	
	public MyException(String msg, int code) {
		super(msg);
		this.msg = msg;
		this.code = code;
	}
	
	public MyException(String msg, int code, Throwable e) {
		super(msg, e);
		this.msg = msg;
		this.code = code;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public int getCode() {
		return code;
	}

	public void setCode(int code) {
		this.code = code;
	}
	
	
}
