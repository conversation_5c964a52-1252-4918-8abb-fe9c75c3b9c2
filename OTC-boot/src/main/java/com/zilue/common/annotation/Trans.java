package com.zilue.common.annotation;

import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.zilue.common.enums.TransType;

import java.lang.annotation.*;

/**
 * @Author: zilue
 * @Date: 2023/1/11 14:17
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Trans {

    /**
     * 转换类型
     * @return 转换类型
     */
    TransType type();

    /**
     * 如果转换类型是  Api, 装换类型是数据字典则是数据字典id
     * @return Api 的 id/ 数据字典id
     */
    String id() default "";

    /**
     * 是否多个值
     * @return true or false
     */
    boolean isMulti() default false;

    /**
     * 多个值分隔符
     * @return 分隔符
     */
    String separator() default StringPool.SLASH;

    /**
     * 多个值显示格式
     * @return 显示格式
     */
    String showFormat() default "all";

}
