package com.zilue.common.satoken;

import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.StpInterface;
import cn.dev33.satoken.stp.StpUtil;
import com.zilue.common.constant.GlobalConstant;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 保证此类被SpringBoot扫描，完成Sa-Token的自定义权限验证扩展
 *
 * <AUTHOR>
 */
@Component
public class StpInterfaceImpl implements StpInterface {

    /**
     * 返回一个账号所拥有的权限码集合 
     */
    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {
        SaSession tokenSession = StpUtil.getTokenSession();
        return tokenSession.get(GlobalConstant.LOGIN_USER_AUTH_CODE_KEY, new ArrayList<>());
    }

    /**
     * 返回一个账号所拥有的角色标识集合 (权限与角色可分开校验)
     */
    @Override
    public List<String> getRoleList(Object loginId, String loginType) {
        SaSession tokenSession = StpUtil.getTokenSession();
        return tokenSession.get(GlobalConstant.LOGIN_USER_ROLE_CODE_KEY, new ArrayList<>());
    }

}