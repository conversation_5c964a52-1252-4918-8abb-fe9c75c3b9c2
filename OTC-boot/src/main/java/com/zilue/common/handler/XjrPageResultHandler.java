package com.zilue.common.handler;

import cn.hutool.db.Entity;
import cn.hutool.db.PageResult;
import cn.hutool.db.handler.RsHandler;

import java.sql.ResultSet;
import java.sql.SQLException;

public class XjrPageResultHandler implements RsHandler<PageResult<Entity>> {

    private final PageResult<Entity> pageResult;
    /**
     * 是否大小写不敏感
     */
    private final boolean caseInsensitive;

    /**
     * 创建一个 EntityHandler对象<br>
     * 结果集根据给定的分页对象查询数据库，填充结果
     *
     * @param pageResult 分页结果集空对象
     * @return EntityHandler对象
     */
    public static XjrPageResultHandler create(PageResult<Entity> pageResult) {
        return new XjrPageResultHandler(pageResult);
    }

    /**
     * 构造<br>
     * 结果集根据给定的分页对象查询数据库，填充结果
     *
     * @param pageResult 分页结果集空对象
     */
    public XjrPageResultHandler(PageResult<Entity> pageResult) {
        this(pageResult, false);
    }

    /**
     * 构造<br>
     * 结果集根据给定的分页对象查询数据库，填充结果
     *
     * @param pageResult      分页结果集空对象
     * @param caseInsensitive 是否大小写不敏感
     */
    public XjrPageResultHandler(PageResult<Entity> pageResult, boolean caseInsensitive) {
        this.pageResult = pageResult;
        this.caseInsensitive = caseInsensitive;
    }

    @Override
    public PageResult<Entity> handle(ResultSet rs) throws SQLException {
        return XjrHandleHelper.handleRs(rs, pageResult, this.caseInsensitive);
    }
}
