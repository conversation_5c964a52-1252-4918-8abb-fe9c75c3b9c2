package com.zilue.common.handler;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.enums.DeleteMark;
import com.zilue.common.enums.EnabledMark;
import com.zilue.common.utils.RedisUtil;
import com.zilue.module.organization.entity.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;


/**
 * @title 数据审计处理器
 * @desc  用于新增或者更新  自动插入 相应字段
 * <AUTHOR>
 * @create 2020年11月9日 11:30:59
 * */
@Slf4j
@Component
public class MyMetaObjectHandler implements MetaObjectHandler {

    /**
     * @title 新增自动填充
     * @desc  fieldName 使用实体类字段名 而不是数据库字段名
     * */
    @Override
    public void insertFill(MetaObject metaObject) {
        long userId = 0L;
        String userName = StringPool.EMPTY;
        try {
            userId = StpUtil.getLoginIdAsLong();
            RedisUtil redisUtil = SpringUtil.getBean(RedisUtil.class);
            List<User> userList= redisUtil.get(GlobalConstant.USER_CACHE_KEY, new TypeReference<List<User>>() {
            });
            long finalUserId = userId;
            User user = userList.stream().filter(x -> x.getId().equals(finalUserId)).findFirst().orElse(new User());
            if (ObjectUtil.isNotNull(user) && StrUtil.isNotBlank(user.getName())){
                userName = user.getName();
            }
        }
        catch (Exception e) {
            log.error("获取当前登录用户Id失败", e);
        }

        //如果需要自定义主键生成策略 在此按照自己想法生成主键值
        //this.strictInsertFill(metaObject, {主键字段名}, {主键字段类型.class}, {自己生成的主键id值});
        //例如： this.strictInsertFill(metaObject, "id", Integer.class, 10000000000000);

        //默认插入创建人Id
        this.strictInsertFill(metaObject, GlobalConstant.CREATE_USER_ID_PROPERTY, Long.class, userId);
        //默认插入创建时间
        this.strictInsertFill(metaObject, GlobalConstant.CREATE_DATE_PROPERTY, LocalDateTime.class, LocalDateTime.now());
        //默认插入未删除
        this.strictInsertFill(metaObject, GlobalConstant.DELETE_MARK_PROPERTY, Integer.class, DeleteMark.NODELETE.getCode());
        //默认插入已启用
        this.strictInsertFill(metaObject, GlobalConstant.ENABLED_MARK_PROPERTY, Integer.class, EnabledMark.ENABLED.getCode());
        //权限所属人id
        this.strictInsertFill(metaObject, GlobalConstant.AUTH_USER_ID_PROPERTY, Long.class, userId);

        this.strictInsertFill(metaObject, GlobalConstant.CREATE_USER_NAME_PROPERTY, String.class, userName);

        //默认插入已启用
        this.strictInsertFill(metaObject, GlobalConstant.BI_DELETE_MARK_PROPERTY, Integer.class, DeleteMark.NODELETE.getCode());
        //权限所属人id
        this.strictInsertFill(metaObject, GlobalConstant.BI_STATUS_PROPERTY, Integer.class, 1);

    }


    /**
     * @title 修改自动填充
     * @desc  fieldName 使用实体类字段名 而不是数据库字段名
     * */
    @Override
    public void updateFill(MetaObject metaObject) {
        long userId = 0L;
        try {
            userId = StpUtil.getLoginIdAsLong();
        }
        catch (Exception e) {
            log.error("获取当前登录用户Id失败", e);
        }
        //默认插入修改人Id
        this.strictUpdateFill(metaObject, GlobalConstant.MODIFY_USER_ID_PROPERTY, Long.class, userId);
        //默认插入修改时间
        this.strictUpdateFill(metaObject, GlobalConstant.MODIFY_DATE_PROPERTY, LocalDateTime.class, LocalDateTime.now());
    }


}
