package com.zilue.common.handler;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.DataFormatData;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.AbstractVerticalCellStyleStrategy;
import lombok.Data;

@Data
public class FormContentStyleStrategy extends AbstractVerticalCellStyleStrategy {

//    private List<String> columnTypeList;
//
//    public FormContentStyleStrategy(List<String> columnTypeList) {
//        this.columnTypeList = columnTypeList;
//    }

    @Override
    protected WriteCellStyle contentCellStyle(Head head) {
        WriteCellStyle style = new WriteCellStyle();
//        for (int i = 0; i < columnTypeList.size(); i++) {
//            if (head.getColumnIndex() == i) {
//            }
//        }
        DataFormatData dataFormatData = new DataFormatData();
        dataFormatData.setIndex((short)49);
        style.setDataFormatData(dataFormatData);
        return style;
    }


}
