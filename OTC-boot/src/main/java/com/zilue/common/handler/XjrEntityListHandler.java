package com.zilue.common.handler;

import cn.hutool.db.Entity;
import cn.hutool.db.handler.RsHandler;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class XjrEntityListHandler implements RsHandler<List<Entity>> {

    /** 是否大小写不敏感 */
    private final boolean caseInsensitive;

    /**
     * 创建一个 EntityListHandler对象
     * @return EntityListHandler对象
     */
    public static XjrEntityListHandler create() {
        return new XjrEntityListHandler();
    }

    /**
     * 构造
     */
    public XjrEntityListHandler() {
        this(false);
    }

    /**
     * 构造
     *
     * @param caseInsensitive 是否大小写不敏感
     */
    public XjrEntityListHandler(boolean caseInsensitive) {
        this.caseInsensitive = caseInsensitive;
    }

    @Override
    public List<Entity> handle(ResultSet rs) throws SQLException {
        return XjrHandleHelper.handleRs(rs, new ArrayList<>(), this.caseInsensitive);
    }
}
