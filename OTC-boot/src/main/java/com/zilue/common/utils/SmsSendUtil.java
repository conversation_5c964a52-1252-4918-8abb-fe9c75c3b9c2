package com.zilue.common.utils;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zilue.module.organization.vo.SmsDO;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import static ch.qos.logback.core.spi.ComponentTracker.DEFAULT_TIMEOUT;

@Slf4j
public class SmsSendUtil {

    public static void send(SmsDO smsDO) {

        Integer val = 0;
        String res = null;
        try {
            //组装运营商要求的短信格式内容
            JSONObject templateParam = JSON.parseObject(smsDO.getTemplateParams());
            Set<String> keySet = templateParam.keySet();
            // 参数
            JSONObject parameter = new JSONObject();
            parameter.put("appkey", smsDO.getAppId());
            parameter.put("appsecret", smsDO.getAppSecret()); // 明文：**********
            parameter.put("phone", smsDO.getPhone()); // content
            parameter.put("templateid", smsDO.getTemplateCode()); // content

            String url = smsDO.getEndPoint();
            if (ObjectUtil.isNotNull(url)) {
                if (url.endsWith("sendFullTextSms")) {
                    String text = smsDO.getContent();
                    for (String key : keySet) {
                        if (key.equals("account")) {
                            text = text.replace("${" + key + "}", smsDO.getPhone());
                        } else {
                            text = text.replace("${" + key + "}", templateParam.getString(key));
                        }
                    }
                    parameter.put("content", text);

                } else if (url.endsWith("smssend")) {
                    List<String> params = new ArrayList<>();
                    for (String key : keySet) {
                        params.add(templateParam.getString(key));
                    }
                    parameter.put("templateparams", params);
                }
            }
            // 短信内容 放到url中
            res = HttpUtil.post(url, parameter.toJSONString(), DEFAULT_TIMEOUT);
            int s = 0;
        } catch (Exception e) {
            log.error("短信发送失败" + e.getMessage());

        }

    }
}
