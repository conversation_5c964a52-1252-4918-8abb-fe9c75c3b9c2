package com.zilue.common.utils;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;
 
public class TimeUtils {
 
    public static long calculateTimestampInterval(long timestamp1, long timestamp2) {
        return TimeUnit.SECONDS.convert(timestamp2 - timestamp1, TimeUnit.MILLISECONDS);
    }

    // 计算两个日期之间获取总分钟数
    public static long getMinutesBetweenDates(LocalDateTime startTime, LocalDateTime endTime) {
        // 计算两个日期之间的持续时间
        Duration duration = Duration.between(startTime, endTime);
        // 获取总分钟数
        return duration.toMinutes();
    }
}