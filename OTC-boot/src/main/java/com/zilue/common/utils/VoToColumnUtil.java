package com.zilue.common.utils;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: zilue
 * @Date:2022/3/7 15:50
 */
public class VoToColumnUtil {

    /**
    * 序列化uid 字符串
    */
    public final static String UID_STRING = "serialVersionUID";

    /**
     * vo属性转为数据库列
     * @param clz
     * @return List<String>
     */
    public static List<String> fieldsToColumns(Class<?> clz){
        return Arrays.stream(clz.getDeclaredFields()).map(Field::getName).filter(name -> !UID_STRING.equals(name)).collect(Collectors.toList());
    }
}
