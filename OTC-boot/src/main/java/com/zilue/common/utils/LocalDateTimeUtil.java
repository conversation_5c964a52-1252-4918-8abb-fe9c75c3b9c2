package com.zilue.common.utils;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import org.apache.commons.lang3.StringUtils;

import java.time.*;
import java.time.format.DateTimeFormatter;

public final class LocalDateTimeUtil {
    private LocalDateTimeUtil(){}

    public final static String LOCAL_TIME_FORMAT = "HH:mm:ss";

    public final static String LOCAL_DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    public final static String LOCAL_DATE_FORMAT = "yyyy-MM-dd";

    public final static String YEAR_MONTH_FORMAT = "yyyy-MM";

    public final static String YEAR_FORMAT = "yyyy";

    public static LocalTime parseTime(String value) {
        return parseTime(value, LOCAL_TIME_FORMAT);
    }

    public static LocalTime parseTime(String value, String format) {
        if (StrUtil.isBlank(value)) {
            return null;
        }
        return LocalTime.parse(value, DateTimeFormatter.ofPattern(format));
    }

    public static LocalDateTime parseDate(String value) {
        return parseDate(value, LOCAL_DATE_TIME_FORMAT);
    }

    public static LocalDateTime parseDate(String value, String format) {
        if (StrUtil.isBlank(value)) {
            return null;
        }
        // 根据format切掉多余的时间
        value = StringUtils.substring(value, 0, format.length());
        LocalDateTime localDateTime = null;
        format = convertFontFormat(format);
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(format);
        switch (format) {
            case YEAR_FORMAT:
                Year year = Year.parse(value, dateTimeFormatter);
                localDateTime =  LocalDateTime.from(year.atDay(1).atStartOfDay());
                break;
            case YEAR_MONTH_FORMAT:
                YearMonth yearMonth = YearMonth.parse(value, dateTimeFormatter);
                localDateTime = LocalDateTime.from(yearMonth.atDay(1).atStartOfDay());
                break;
            case LOCAL_DATE_FORMAT:
                LocalDate localDate = LocalDate.parse(value, dateTimeFormatter);
                localDateTime = LocalDateTime.from(localDate.atStartOfDay());
                break;
            default:
                localDateTime = LocalDateTime.parse(value, dateTimeFormatter);
                break;
        }
        return localDateTime;
    }

    public static String convertFontFormat(String fontFormat) {
        if (StrUtil.isEmpty(fontFormat)) {
            return fontFormat;
        }
        return fontFormat.replace("YYYY", "yyyy").replace("DD", "dd");
    }

    public static String format(LocalDateTime dateTime, String format) {
        if (dateTime == null) {
            return StringPool.EMPTY;
        }
        return dateTime.format(DateTimeFormatter.ofPattern(format));
    }

    public static String format(LocalTime time) {
        if (time == null) {
            return StringPool.EMPTY;
        }
        return time.format(DateTimeFormatter.ofPattern(LOCAL_TIME_FORMAT));
    }

    /**
     * 将时间字符串转换成数据库对应的时间类型
     * @param time 时间字符串 格式 HH:mm:ss
     * @param dbType 数据库类型
     * @return 据库对应的时间类型对象
     */
    public static Object parseDbTime(String time, DbType dbType) {
        if (StringUtils.isBlank(time)) {
            return null;
        }
        return parseTime(time);
    }

    public static LocalDateTime parseDateByLength(String value) {
        if (StrUtil.isNotBlank(value)) {
            value = value.trim();
            int length = value.length();
            if (LOCAL_DATE_FORMAT.length() == length) {
                LocalDate localDate = LocalDate.parse(value, DateTimeFormatter.ofPattern(LOCAL_DATE_FORMAT));
                return LocalDateTime.from(localDate.atStartOfDay());
            } else if (LOCAL_DATE_TIME_FORMAT.length() == length) {
                return LocalDateTime.parse(value, DateTimeFormatter.ofPattern(LOCAL_DATE_TIME_FORMAT));
            } else if (YEAR_MONTH_FORMAT.length() == length) {
                YearMonth yearMonth = YearMonth.parse(value, DateTimeFormatter.ofPattern(YEAR_MONTH_FORMAT));
                return LocalDateTime.from(yearMonth.atDay(1).atStartOfDay());
            } else if (YEAR_FORMAT.length() == length) {
                Year year = Year.parse(value, DateTimeFormatter.ofPattern(YEAR_FORMAT));
                return LocalDateTime.from(year.atDay(1).atStartOfDay());
            }
        }
        return null;
    }

//    public static LocalTime convertIntervalToLocalTime(INTERVALDS intervalDS) {
//        if (intervalDS == null) {
//            return null;
//        }
//        String str = intervalDS.stringValue();
//        String timeStr = str.substring(str.indexOf(StringPool.SPACE) + 1, str.indexOf(StringPool.DOT));
//        String[] array = timeStr.split(StringPool.COLON);
//        return LocalTime.of(Integer.parseInt(array[0]), Integer.parseInt(array[1]), Integer.parseInt(array[2]));
//    }

    public static String tranDurationToShow(Duration duration) {

        StringBuilder result = new StringBuilder();
        long days = duration.toDays();
        if (days >= 1) {
            result.append(days).append("天");
        }
        long hours = duration.toHours();
        if (hours >= 1) {
            if (days >= 1) {
                long l = hours % 24;
                if (l > 0) {
                    result.append(l).append("小时");
                }
                return result.toString();
            }else {
                result.append(hours).append("小时");
            }
        }
        long minutes = duration.toMinutes();
        if (minutes >= 1) {
            if (hours >= 1) {
                long l = minutes % 60;
                if (l > 0) {
                    result.append(l).append("分钟");
                }
                return result.toString();
            }
            result.append(minutes).append("分钟");
        }
        if (result.length() == 0) {
            result.append("1分钟内");
        }
        return result.toString();
    }

    /**
     * 计算时间差转换为分钟进行显示
     * @param duration
     * @return
     */
    public static String tranDurationMinute(Duration duration) {

        StringBuilder result = new StringBuilder();
        long days = duration.toDays();
        if (days >= 1) {
            result.append(days).append("天");
        }
        long hours = duration.toHours();
        if (hours >= 1) {
            if (days >= 1) {
                long l = hours % 24;
                if (l > 0) {
                    result.append(l).append("小时");
                }
            }else {
                result.append(hours).append("小时");
            }
        }
        long minutes = duration.toMinutes();
        if (minutes >= 1) {
            if (hours >= 1) {
                long l = minutes % 60;
                if (l > 0) {
                    result.append(l).append("分钟");
                }
                return result.toString();
            }
            result.append(minutes).append("分钟");
        }
        if (result.length() == 0) {
            result.append("1分钟内");
        }
        return result.toString();
    }
}
