package com.zilue.common.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.util.List;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;


public class JsonUtil {
    public static <T> List<T> convertJsonToList(String jsonStr, Class<T> clazz) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            objectMapper.registerModule(new JavaTimeModule());
            // 使用Jackson的ObjectMapper将JSON字符串转换为List<T>
            return objectMapper.readValue(jsonStr, objectMapper.getTypeFactory().constructCollectionType(List.class, clazz));
        } catch (IOException e) {
            e.printStackTrace();
            // 处理异常，例如返回空列表或抛出自定义异常
            return null;
        }
    }
}
