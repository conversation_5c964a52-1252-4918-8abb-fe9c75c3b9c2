package com.zilue.common.utils;


import cn.hutool.core.util.StrUtil;

public final class FormatUtil {
    private FormatUtil(){}

    /**
     * @description: 格式化数字
     * @param valueStr	数字值
     * @param format	格式
     * @return
     */
    public static String formatNumber(String valueStr, String format) {
        if (StrUtil.isEmpty(valueStr)) {
            return format;
        }
        if (StrUtil.isEmpty(format) || valueStr.length() >= format.length()) {
            return valueStr;
        }
        return StrUtil.sub(format, 0, format.length() - valueStr.length()) + valueStr;
    }
}
