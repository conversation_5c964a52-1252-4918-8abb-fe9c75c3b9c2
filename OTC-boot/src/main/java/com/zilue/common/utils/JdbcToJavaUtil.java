package com.zilue.common.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.db.meta.Column;
import cn.hutool.db.meta.JdbcType;
import org.apache.commons.lang3.ObjectUtils;

/**
 * @Author: zilue
 * @Date: 2023/4/25 15:37
 */
public class JdbcToJavaUtil {

    /**
     * 根据jdbc类型 返回java类型字符串
     */
    public static String getClassName(Column column) {
        JdbcType type = ObjectUtils.defaultIfNull(column.getTypeEnum(), JdbcType.NULL);
        switch (type) {
            case BIT:
                return "Boolean";
            case TINYINT:
                return "Byte";
            case SMALLINT:
                return "Short";
            case INTEGER:
                return "Integer";
            case BIGINT:
                return "Long";
            case FLOAT:
                return "Float";
            case REAL:
            case DOUBLE:
                return "Double";
            case NUMERIC:
            case DECIMAL:
                if (column.getDigit() == 0) {
                    if (column.getSize() > 11) {
                        return "Long";
                    } else {
                        return "Integer";
                    }
                }
                return "BigDecimal";
            case CHAR:
            case VARCHAR:
            case LONGVARCHAR:
            case NVARCHAR:
                return "String";
            case DATE:
            case TIMESTAMP:
                return "LocalDateTime";
            case TIME:
                return "LocalTime";
            case BINARY:
            case CLOB:
            case BLOB:
            case VARBINARY:
            case LONGVARBINARY:
                return "Byte[]";
            case DATETIMEOFFSET:
            default:
                if (StrUtil.equalsIgnoreCase(column.getTypeName(), "NVARCHAR2")
                        || StrUtil.equalsIgnoreCase(column.getTypeName(), "NCLOB")) {
                    return "String";
                } else if (StrUtil.startWithIgnoreCase(column.getTypeName(), "INTERVAL DAY")) {
                    return "LocalTime";
                }
                return "Object";

        }
    }

}
