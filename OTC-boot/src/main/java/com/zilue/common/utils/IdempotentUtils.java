package com.zilue.common.utils;

import org.apache.commons.collections4.map.LRUMap;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/19 9:12
 * 幂等校验工具类--id
 */
public class IdempotentUtils {
    // 根据 LRU(Least Recently Used，最近最少使用)算法淘汰数据的 Map 集合，最大容量 2 个
    private static LRUMap<Long, Integer> reqCache = new LRUMap<>(100);

    /**
     * 幂等性判断
     * @return
     */
    public static boolean judge(Long id, Object lockClass) {
        synchronized (lockClass) {
            // 重复请求判断
            if (reqCache.containsKey(id)) {
                // 重复请求
                return false;
            }
            // 非重复请求，存储请求 id
            reqCache.put(id, 1);
        }
        return true;
    }

    public static void removeKey(Long id){
        reqCache.remove(id);
    }
}
