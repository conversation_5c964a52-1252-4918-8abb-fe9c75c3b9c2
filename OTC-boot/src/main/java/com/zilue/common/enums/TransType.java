package com.zilue.common.enums;

/**
 * @Author: zilue
 * @Date: 2023/1/11 14:27
 */
public enum TransType {
    /**
     * 数据字典
     */
    DIC(0, "数据字典"),

    /**
     * 用户
     */
    USER(1, "用户"),
    /**
     * 机构
     */
    DEPT(2, "机构"),

    /**
     * 机构
     */
    POST(3, "岗位"),


    /**
     * API
     */
    API(4, "API"),


    /**
     * 行政区域
     */
    AREA(5, "行政区域"),


    /**
     * 级联
     */
    CASCADE(6, "级联"),


    /**
     * 树选择
     */
    TREE(7, "树选择"),


    /**
     * 角色
     */
    ROLE(8, "角色"),


    /**
     * 机构编码
     */
    DEPT_CODE(8, "机构编码");

    final int code;
    final String value;

    public int getCode() {
        return this.code;
    }

    public String getValue() {
        return this.value;
    }

    TransType(final int code, final String message) {
        this.code = code;
        this.value = message;
    }
}
