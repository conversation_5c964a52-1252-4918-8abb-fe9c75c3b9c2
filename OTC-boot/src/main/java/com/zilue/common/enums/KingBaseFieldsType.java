package com.zilue.common.enums;

public enum KingBaseFieldsType {
    /**
     * 短文本
     * */
    VARCHAR(0, "varchar", "短文本"),

    /**
     * 阿里云
     * */
    VARCHARMAX(1, "text", "长文本"),

    /**
     * 数字类型
     * */
    INT(2, "int", "数字"),

    /**
     * 小数
     * */
    FLOAT(3, "float8", "小数"),

    /**
     * 日期
     * */
    DATE(4, "timestamp", "日期"),

    /**
     * 日期时间类型
     * */
    DATETIME(5, "timestamp", "日期时间"),
    /**
     * 主表主键
     * */
    FK(6, "bigint", "主表主键"),
    /**
     * 长整数
     * */
    LONG(7, "bigint", "长整数"),
    /**
     * 时间
     * */
    TIME(8, "time", "时间");

    final int code;
    final String type;
    final String message;

    public int getCode() {
        return this.code;
    }

    public String getType() {
        return this.type;
    }

    public String getMessage() {
        return this.message;
    }

    public static KingBaseFieldsType getFieldType(Integer fieldType) {
        KingBaseFieldsType[] var1 = values();
        int var2 = var1.length;

        for (KingBaseFieldsType type : var1) {
            if (type.code == fieldType) {
                return type;
            }
        }

        return VARCHAR;
    }

    KingBaseFieldsType(final int code, final String type, final String message) {
        this.code = code;
        this.type = type;
        this.message = message;
    }
}
