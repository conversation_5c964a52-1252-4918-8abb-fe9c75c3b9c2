package com.zilue.common.enums;

/**
 * @Author: zilue
 * @Date: 2023/10/18 14:32
 */
public enum WorkflowAutoAgreeType {
    /**
     * 无
     */
    NONE(0, "无"),
    /**
     * 候选审批人包含流程任务发起人
     */
    APPROVED_INCLUDE_INITIATOR(1, "候选审批人包含流程任务发起人"),

    /**
     * 候选审批人包含上一节点审批人
     */
    APPROVED_INCLUDE_PREV(2, "候选审批人包含上一节点审批人"),

    /**
     * 候选审批人在流传中审批过
     */
    APPROVED_IN_PROCESS(3, "候选审批人在流传中审批过");

    final int code;
    final String value;

    public int getCode() {
        return this.code;
    }

    public String getValue() {
        return this.value;
    }

    WorkflowAutoAgreeType(final int code, final String message) {
        this.code = code;
        this.value = message;
    }
}
