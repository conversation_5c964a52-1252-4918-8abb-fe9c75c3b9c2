package com.zilue.common.enums;

/**
 * 工作流事件 执行类型
 * @Author: zilue
 * @Date: 2023/6/7 14:10
 */
public enum WorkflowEventExType {
    /**
     * API
     */
    API(0, "API"),

    /**
     * 规则
     */
    LITEFLOW(1, "规则"),

    MESSAGE(2,"推送消息");


    final int code;
    final String value;

    public int getCode() {
        return this.code;
    }

    public String getValue() {
        return this.value;
    }

    WorkflowEventExType(final int code, final String message) {
        this.code = code;
        this.value = message;
    }
}
