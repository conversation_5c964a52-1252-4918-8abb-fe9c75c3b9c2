package com.zilue.common.enums;

public enum TaskFrequencyEnum {
    /**
     * 日
     */
    DAY("1", "日"),
    /**
     * 月
     */
    MOUTH("2", "月");

    final String code;
    final String value;

    public String getCode() {
        return this.code;
    }

    public String getValue() {
        return this.value;
    }

    TaskFrequencyEnum(final String code, final String message) {
        this.code = code;
        this.value = message;
    }
}
