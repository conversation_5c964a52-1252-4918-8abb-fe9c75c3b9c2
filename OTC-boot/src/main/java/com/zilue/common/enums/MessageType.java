package com.zilue.common.enums;

/**
 * 消息类型
 * @Author: zilue
 * @Date: 2023/10/26 16:12
 */
public enum MessageType {
    /**
     * 日程
     */
    SCHEDULE(0, "日程"),
    /**
     * 工作流审批
     */
    APPROVE(1, "工作流审批"),
    /**
     * 工作流审批
     */
    CIRCULATED(2, "工作流传阅"),

    /**
     * 工作流审批
     */
    TIMEOUT(3, "超时"),

    /**
     * 消息推送-系统消息
     */
    MESSAGE_SEND(4,"消息推送-系统消息"),

    /**
     * 工作流催办
     */
    URGE(5,"工作流催办");

    final int code;
    final String value;

    public int getCode() {
        return this.code;
    }

    public String getValue() {
        return this.value;
    }

    MessageType(final int code, final String message) {
        this.code = code;
        this.value = message;
    }
}
