package com.zilue.common.enums;

/**
 * @Author: zilue
 * @Date: 2023/2/21 14:09
 */
public enum StampEnum {
    /**
     * 私人签章
     */
    PRIVATE(0, "私人签章"),

    /**
     * 公共签章
     */
    PUBLIC(1, "公共签章");

    final int code;
    final String value;

    public int getCode() {
        return this.code;
    }

    public String getValue() {
        return this.value;
    }

    StampEnum(final int code, final String message) {
        this.code = code;
        this.value = message;
    }
}
