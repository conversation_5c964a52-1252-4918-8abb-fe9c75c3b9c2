package com.zilue.common.enums;

/**
 * @Author: zilue
 * @Date: 2023/5/29 16:41
 */
public enum WorkflowMultiInstanceFinishType {
    /**
     * 全部
     */
    ALL(0, "全部"),
    /**
     * 单个
     */
    SINGLE(1, "单个"),
    /**
     * 百分比
     */
    PERCENTAGE(2, "百分比");

    final int code;
    final String value;

    public int getCode() {
        return this.code;
    }

    public String getValue() {
        return this.value;
    }

    WorkflowMultiInstanceFinishType(final int code, final String message) {
        this.code = code;
        this.value = message;
    }
}
