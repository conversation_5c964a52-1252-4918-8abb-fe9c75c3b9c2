package com.zilue.common.enums;

/**
 * @Author: zilue
 * @Date: 2023/2/27 15:22
 */
public enum DataAuthMethodEnum {
    /*
     *
     *  简易
     * */
    SIMPLE(0, "简易"),
    /*
     *  自定义
     * */
    CUSTOM(1, "自定义");


    final int code;
    final String value;

    public int getCode() {
        return this.code;
    }

    public String getValue() {
        return this.value;
    }

    DataAuthMethodEnum(final int code, final String message) {
        this.code = code;
        this.value = message;
    }

}
