package com.zilue.common.enums;

/**
 * 工作流人员权限配置
 * @Author: zilue
 * @Date: 2023/9/26 15:58
 */
public enum WorkflowAuth {
    /**
     * 全部
     */
    ALL(0, "全部"),
    /**
     * 指定
     */
    ASSIGN(1, "指定");

    final int code;
    final String value;

    public int getCode() {
        return this.code;
    }

    public String getValue() {
        return this.value;
    }

    WorkflowAuth(final int code, final String message) {
        this.code = code;
        this.value = message;
    }
}
