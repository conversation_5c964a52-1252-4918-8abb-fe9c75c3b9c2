package com.zilue.common.enums;

/**
 * 系统配置枚举
 * @Author: zilue
 * @Date: 2024/1/16 14:05
 */
public enum SystemConfigEnum {
    /**
     * 邮件
     */
    EMAIL(0, "邮件"),

    /**
     * 短信
     */
    MESSAGE(1, "短信"),
    /**
     * 企业微信
     */
    WECHAT(2, "企业微信"),

    /**
     * 钉钉
     */
    DINGTALK(3, "钉钉"),

    /**
     * 页面钩子
     */
    WEBHOOK(4, "页面钩子"),

    /**
     * 微信公众号
     */
    WECHAT_OFFICIAL(5, "微信公众号"),

    /**
     * 系统消息
     */
    SYSTEM_MESSAGE(6,"系统消息");

    final int code;
    final String value;


    public int getCode() {
        return this.code;
    }

    public String getValue() {
        return this.value;
    }

    SystemConfigEnum(final int code, final String message) {
        this.code = code;
        this.value = message;
    }
}
