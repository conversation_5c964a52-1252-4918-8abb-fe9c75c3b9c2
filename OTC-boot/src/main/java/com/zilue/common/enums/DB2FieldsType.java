package com.zilue.common.enums;

public enum DB2FieldsType {

    /**
     * 短文本
     * */
    VARCHAR(0, "VARCHAR", "短文本"),

    /**
     * 长文本
     * */
    VARCHARMAX(1, "LONG VARCHAR", "长文本"),

    /**
     * 数字类型
     * */
    INT(2, "INTEGER", "数字"),

    /**
     * 小数
     * */
    FLOAT(3, "DOUBLE", "小数"),

    /**
     * 日期
     * */
    DATE(4, "DATE", "日期"),

    /**
     * 日期时间类型
     * */
    DATETIME(5, "TIMESTAMP", "日期时间"),
    /**
     * 主表主键
     * */
    FK(6, "BIGINT", "主表主键"),
    /**
     * 长整数
     * */
    LONG(7, "BIGINT", "长整数"),
    /**
     * 时间
     * */
    TIME(8, "TIME", "时间");

    final int code;
    final String type;
    final String message;

    public int getCode() {
        return this.code;
    }

    public String getType() {
        return this.type;
    }

    public String getMessage() {
        return this.message;
    }

    public static DB2FieldsType getFieldType(Integer fieldType) {
        DB2FieldsType[] var1 = values();
        int var2 = var1.length;

        for (DB2FieldsType type : var1) {
            if (type.code == fieldType) {
                return type;
            }
        }

        return VARCHAR;
    }

    DB2FieldsType(final int code, final String type, final String message) {
        this.code = code;
        this.type = type;
        this.message = message;
    }
}
