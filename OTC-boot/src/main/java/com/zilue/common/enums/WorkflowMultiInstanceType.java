package com.zilue.common.enums;

/**
 * 工作流多实例类型
 * @Author: zilue
 * @Date: 2023/10/19 14:41
 */
public enum WorkflowMultiInstanceType {
    /**
     * 无
     */
    NONE(0, "无"),
    /**
     * 同步执行（并行）
     */
    PARALLEL(1, "同步执行（并行）"),
    /**
     * 顺序执行（串行）
     */
    SEQUENTIAL(2, "顺序执行（串行）");

    final int code;
    final String value;

    public int getCode() {
        return this.code;
    }

    public String getValue() {
        return this.value;
    }

    WorkflowMultiInstanceType(final int code, final String message) {
        this.code = code;
        this.value = message;
    }
}
