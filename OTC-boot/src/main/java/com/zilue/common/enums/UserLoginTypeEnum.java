package com.zilue.common.enums;

public enum UserLoginTypeEnum {

    // PC登录
    PC("PC", "PC登录"),

    // APP登录
    APP("APP", "APP登录"),
    // APP登录
    THIRD("THIRD", "三方登录");

    final String code;
    final String value;

    public String getCode() {
        return this.code;
    }

    public String getValue() {
        return this.value;
    }

    UserLoginTypeEnum(final String code, final String message) {
        this.code = code;
        this.value = message;
    }
}
