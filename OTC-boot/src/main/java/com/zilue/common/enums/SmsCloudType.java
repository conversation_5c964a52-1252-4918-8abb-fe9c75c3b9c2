package com.zilue.common.enums;

/**
 * @Author: zilue
 * @Date: 2023/6/6 10:24
 */
public enum SmsCloudType {
    /**
     * 阿里云
     * */
    ALI_CLOUD(0, "阿里云"),
    /**
     * 腾讯 短信
     * */
    TENCENT_CLOUD(1, "腾讯"),
    /**
     * 华为云
     * */
    HW_CLOUD(2, "华为云"),
    /**
     * 合一 短信
     * */
    HEYI_CLOUD(3, "合一"),

    /**
     * 京东 短信
     * */
    JD_CLOUD(4, "京东"),

    /**
     * 容联 短信
     * */
    RONGLIAN_CLOUD(5, "容联"),

    /**
     * 亿美 短信
     * */
    YIMEI_CLOUD(6, "亿美"),

    /**
     * 天翼云 短信
     * */
    TIANYI_CLOUD(7, "天翼云"),

    /**
     * 云片短信
     * */
    YUNPIAN_CLOUD(8, "云片");

    final int code;
    final String value;

    public int getCode() {
        return this.code;
    }

    public String getValue() {
        return this.value;
    }

    SmsCloudType(final int code, final String message) {
        this.code = code;
        this.value = message;
    }
}
