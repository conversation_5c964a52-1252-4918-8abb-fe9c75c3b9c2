package com.zilue.common.enums;

/**
 * @Author: zilue
 * @Date: 2023/3/3 16:37
 */
public enum EnabledMark {
    /*
    *  未启用
    * */
    DISABLED(0, "未启用"),
    /*
     *  启用
     * */
    ENABLED(1, "已启用");

    final int code;
    final String value;

    public int getCode() {
        return this.code;
    }

    public String getValue() {
        return this.value;
    }

    EnabledMark(final int code, final String message) {
        this.code = code;
        this.value = message;
    }
}
