package com.zilue.common.enums;

/**
 * <AUTHOR>
 * @Date: 2023/9/19 15:12
 */
public enum DataAuthConditionTypeEnum {

    EQUAL_TO(0, "等于"),

    GREATER_THAN(1, "大于"),

    GREATER_THAN_EQUAL(2,"大于等于"),

    MINOR_THAN(3, "小于"),

    MINOR_THAN_EQUAL(4,"小于等于"),

    CONTAIN(5,"包含"),

    CONTAINED_IN(6,"包含于"),

    NO_EQUAL_TO(7, "不等于"),

    NO_CONTAIN(8,"不包含"),

    NO_CONTAINED_IN(9,"不包含于");

    final int code;
    final String value;

    public int getCode() {
        return this.code;
    }

    public String getValue() {
        return this.value;
    }

    DataAuthConditionTypeEnum(final int code, final String message) {
        this.code = code;
        this.value = message;
    }
}
