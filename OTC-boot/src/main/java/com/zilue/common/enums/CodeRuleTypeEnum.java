package com.zilue.common.enums;

/**
 * @Author: zilue
 * @Date: 2023/6/27 14:19
 */
public enum CodeRuleTypeEnum {
    /**
     * 字符串
     */
    CUSTOM(0, "自定义"),
    /**
     * 日期
     */
    DATE(1, "日期"),
    /**
     * 流水号
     */
    SERIAL(2, "流水号"),
    /**
     * 部门
     */
    DEPARTMENT(3, "部门"),
    /**
     * 用户
     */
    USER(4, "用户"),
    /**
     * 公司
     */
    COMPANY(5, "公司");

    final int code;
    final String value;

    public int getCode() {
        return this.code;
    }

    public String getValue() {
        return this.value;
    }

    CodeRuleTypeEnum(final int code, final String message) {
        this.code = code;
        this.value = message;
    }
}
