package com.zilue.common.enums;

/**
 * @Author: zilue
 * @Date: 2024/1/30 14:45
 */
public enum HttpAuthTypeEnum {

    /**
     * BASIC 认证
     */
    BASIC(0, "BASIC 认证"),
    /*
     *  DIGEST 认证
     */
    DIGEST(1, "DIGEST 认证"),

    /**
     * SSL 认证
     */
    SSL(2, "SSL 认证"),

    /*
     *  FORM 认证
     */
    FORM(3, "FORM 认证");

    final int code;
    final String value;

    public int getCode() {
        return this.code;
    }

    public String getValue() {
        return this.value;
    }

    HttpAuthTypeEnum(final int code, final String message) {
        this.code = code;
        this.value = message;
    }
}
