package com.zilue.common.enums;

public enum OracleFieldsType {
    /**
     * 短文本
     * */
    VARCHAR(0, "VARCHAR2", "短文本"),

    /**
     * 长文本
     * */
    VARCHARMAX(1, "NVARCHAR2", "长文本"),

    /**
     * 数字类型
     * */
    INT(2, "NUMBER", "数字"),

    /**
     * 小数
     * */
    FLOAT(3, "NUMBER", "小数"),

    /**
     * 日期
     * */
    DATE(4, "DATE", "日期"),

    /**
     * 日期时间类型
     * */
    DATETIME(5, "TIMESTAMP", "日期时间"),
    /**
     * 主表主键
     * */
    FK(6, "NUMBER", "主表主键"),
    /**
     * 长整数
     * */
    LONG(7, "NUMBER", "长整数"),
    /**
     * 时间
     * */
    TIME(8, "INTERVAL DAY(0) TO SECOND", "时间");

    final int code;
    final String type;
    final String message;

    public int getCode() {
        return this.code;
    }

    public String getType() {
        return this.type;
    }

    public String getMessage() {
        return this.message;
    }

    public static OracleFieldsType getFieldType(Integer fieldType) {
        OracleFieldsType[] var1 = values();
        int var2 = var1.length;

        for (OracleFieldsType type : var1) {
            if (type.code == fieldType) {
                return type;
            }
        }

        return VARCHAR;
    }

    OracleFieldsType(final int code, final String type, final String message) {
        this.code = code;
        this.type = type;
        this.message = message;
    }
}
