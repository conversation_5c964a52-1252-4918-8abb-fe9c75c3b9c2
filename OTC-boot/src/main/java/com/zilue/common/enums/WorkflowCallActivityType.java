package com.zilue.common.enums;

/**
 * @Author: zilue
 * @Date: 2023/11/18 15:56
 */
public enum WorkflowCallActivityType  {

    /**
     * 单个
     */
    SINGLE(0,"单实例"),

    /**
     * 多实例
     */
    MULTI(1,"多实例");


    final int code;
    final String value;

    public int getCode() {
        return this.code;
    }

    public String getValue() {
        return this.value;
    }

    WorkflowCallActivityType(final int code, final String message) {
        this.code = code;
        this.value = message;
    }
}
