
package com.zilue.common.factory;


import cn.hutool.extra.spring.SpringUtil;
import com.zilue.common.enums.CloudType;
import com.zilue.config.OSSConfig;

/**
 * 文件上传Factory
 *
 * <AUTHOR>
 */
public final class OssFactory {

    private static final OSSConfig OSS_CONFIG;


    static {
        OSS_CONFIG = SpringUtil.getBean(OSSConfig.class);
    }

    public static CloudStorageService build(){

        if(OSS_CONFIG.getCloudType().getCode() == CloudType.QINIUCLOUD.getCode()){
            return new QiniuCloudStorageService(OSS_CONFIG);
        }else if(OSS_CONFIG.getCloudType().getCode() == CloudType.ALICLOUD.getCode()){
            return new AliyunCloudStorageService(OSS_CONFIG);
        }else if(OSS_CONFIG.getCloudType().getCode() == CloudType.QCLOUD.getCode()){
            return new QcloudCloudStorageService(OSS_CONFIG);
        }else if(OSS_CONFIG.getCloudType().getCode() == CloudType.HWCLOUD.getCode()){
            return new HwCloudStorageService(OSS_CONFIG);
        }else if(OSS_CONFIG.getCloudType().getCode() == CloudType.MINIO.getCode()){
            return new MinioService(OSS_CONFIG);
        }
        return null;
    }

}
