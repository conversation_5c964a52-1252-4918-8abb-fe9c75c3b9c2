
package com.zilue.common.model.result;

import com.zilue.common.enums.ResponseCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.io.Serializable;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * @Author: zhaoqi
 * @Description: 返回结果类
 * @Date:2025/01/06
 */
@Data
@Slf4j
@ApiModel("返回信息")
public class R<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 状态码
     */
    public static final String CODE_TAG = "code";

    /**
     * 返回内容
     */
    public static final String MSG_TAG = "msg";

    /**
     * 数据对象
     */
    public static final String DATA_TAG = "data";

    /**
     * 状态码
     */
    @ApiModelProperty("状态码")
    private int code;

    /**
     * 返回内容
     */
    @ApiModelProperty("返回内容")
    private String msg;

    /**
     * 数据对象
     */
    @ApiModelProperty("数据对象")
    public T data;

    /**
     * 私有化唯一的构造器，是对象创建只能由create进行
     */
    private R() {
    }

    /**
     * 唯一创建对象实例的方法，实际创建的是子类对象RImpl
     *
     * @param code 状态码
     * @param msg  返回提示消息
     * @param data 返回是数据
     * @param <T>  数据类型
     * @return 返回R，但实际类型为R的子类RImpl
     */
    public static <T> R<T> create(int code, String msg, T data) {
        RImpl<T> result = new RImpl<>();
        result.setCode(code);
        result.setMsg(msg);
        result.setData(data);
        return result;
    }

    /**
     * 成功
     *
     * @return
     */
    public static R<Void> ok() {
        return R.okMsg("成功");
    }

    /**
     * 返回成功数据
     *
     * @return
     */
    public static <T> R<T> ok(T data) {
        return R.ok("成功", data);
    }

    /**
     * 返回成功消息
     *
     * @param msg 返回内容
     * @return
     */
    public static R<Void> okMsg(String msg) {
        return R.ok(msg, null);
    }

    /**
     * 返回成功消息
     *
     * @param msg  返回内容
     * @param data 数据对象
     * @return
     */
    public static <T> R<T> ok(String msg, T data) {
        return R.create(ResponseCode.SUCCESS.getCode(), msg, data);
    }

    /**
     * 返回错误消息
     *
     * @return
     */
    public static R<Void> error() {
        return R.error("未知异常，请联系管理员");
    }

    /**
     * 返回错误消息
     *
     * @param msg 返回内容
     * @return
     */
    public static <T> R<T> error(String msg) {
        return R.error(msg, null);
    }

    /**
     * 返回错误消息
     *
     * @param msg  返回内容
     * @param data 数据对象
     * @return
     */
    public static <T> R<T> error(String msg, T data) {
        return R.create(ResponseCode.INTERNAL_SERVER_ERROR.getCode(), msg, data);
    }

    /**
     * 返回错误消息
     *
     * @param code 状态码
     * @param msg  返回内容
     * @return
     */
    public static R<Void> error(int code, String msg) {
        return R.create(code, msg, null);
    }

    /**
     * 方便链式调用
     */
    public R<T> put(String key, Object value) {
        return this;
    }

    public static ResponseEntity<byte[]> fileStream(byte[] bot, String fileName) {

        HttpHeaders headers = new HttpHeaders();
        headers.setContentDispositionFormData("attachment", Arrays.toString(fileName.getBytes(StandardCharsets.UTF_8)));
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);

        return new ResponseEntity<byte[]>(bot, headers, HttpStatus.OK);
    }

    public static class RImpl<T> extends R<T> implements Map<String, Object> {
        private static final long serialVersionUID = -4341558310175295064L;

        /**
         * 定义实际保存数据的容器
         */
        private final Map<String, Object> dataMap;

        /**
         * 创建子类对象时创建数据容器
         */
        public RImpl() {
            dataMap = new HashMap<>();
        }

        /**
         * 重写getCode，从数据容器中获取code
         *
         * @return r
         */
        @Override
        public int getCode() {
            return (Integer) dataMap.getOrDefault(CODE_TAG, 0);
        }

        /**
         * 重写getMsg，从数据容器中获取msg
         *
         * @return r
         */
        @Override
        public String getMsg() {
            return (String) dataMap.get(MSG_TAG);
        }

        /**
         * 重写getData，从数据容器中获取data
         *
         * @return r
         */
        @Override
        @SuppressWarnings("all")
        public T getData() {
            return (T) dataMap.get(DATA_TAG);
        }

        /**
         * 重写setCode，把code放到数据容器中
         */
        @Override
        public void setCode(int code) {
            dataMap.put(CODE_TAG, code);
        }

        /**
         * 重写setCode，把msg放到数据容器中
         */
        @Override
        public void setMsg(String msg) {
            dataMap.put(MSG_TAG, msg);
        }

        /**
         * 重写setData，把data放到数据容器中
         */
        @Override
        public void setData(T data) {
            dataMap.put(DATA_TAG, data);
        }

        /**
         * 重写put，直接使用数据容器的put方法，并返回this
         *
         * @return 返回R类型的RImpl实例对象
         */
        @Override
        public R<T> put(String key, Object value) {
            dataMap.put(key, value);
            return this;
        }

        //以下方法为Map接口需要实现的方法，直接使用数据容器的对应方法即可
        @Override
        public String toString() {
            return dataMap.toString();
        }

        @Override
        public int size() {
            return dataMap.size();
        }

        @Override
        public boolean isEmpty() {
            return dataMap.isEmpty();
        }

        @Override
        public boolean containsKey(Object key) {
            return dataMap.containsKey(key);
        }

        @Override
        public boolean containsValue(Object value) {
            return dataMap.containsValue(value);
        }

        @Override
        public Object get(Object key) {
            return dataMap.get(key);
        }

        @Override
        public Object remove(Object key) {
            return dataMap.remove(key);
        }

        @Override
        public void putAll(Map<? extends String, ?> m) {
            dataMap.putAll(m);
        }

        @Override
        public void clear() {
            dataMap.clear();
        }

        @Override
        public Set<String> keySet() {
            return dataMap.keySet();
        }

        @Override
        public Collection<Object> values() {
            return dataMap.values();
        }

        @Override
        public Set<Entry<String, Object>> entrySet() {
            return dataMap.entrySet();
        }
    }

}

