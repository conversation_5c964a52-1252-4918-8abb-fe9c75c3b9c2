package com.zilue.common.runner;

import com.zilue.module.liteflow.entity.LiteflowChain;
import com.zilue.module.liteflow.service.ILiteflowChainService;
import com.yomahub.liteflow.builder.el.LiteFlowChainELBuilder;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: zilue
 * @Date: 2023/4/7 10:41
 */
@Component
@AllArgsConstructor
@Slf4j
public class LiteflowRunner  implements CommandLineRunner {

    private ILiteflowChainService liteflowChainService;

    @Override
    public void run(String... args) {
        log.info("zilue: 加载所有规则开始");
        List<LiteflowChain> list = liteflowChainService.list();

        for (LiteflowChain liteflowChain : list) {
            LiteFlowChainELBuilder.createChain().setChainId(liteflowChain.getChainName()).setEL(liteflowChain.getElData()).build();
        }
        log.info("zilue: 加载所有规则结束");
    }
}
