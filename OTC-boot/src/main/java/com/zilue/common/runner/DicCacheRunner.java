package com.zilue.common.runner;

import com.zilue.module.system.service.IDictionarydetailService;
import com.zilue.module.system.service.IDictionaryitemService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * 默认缓存数据字典
 *
 * @Author: zilue
 * @Date: 2023/1/11 11:14
 */
@Component
@AllArgsConstructor
@Slf4j
public class DicCacheRunner implements CommandLineRunner {

    private IDictionaryitemService dictionaryitemService;

    private IDictionarydetailService dictionarydetailService;


    @Override
    public void run(String... args) {
        dictionaryitemService.loadCaches();
        dictionarydetailService.loadCaches();
    }

}
