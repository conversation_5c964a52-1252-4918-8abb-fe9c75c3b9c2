package com.zilue.common.runner;

import cn.hutool.db.Entity;
import cn.hutool.db.Page;
import cn.hutool.db.PageResult;
import cn.hutool.db.SqlConnRunner;
import cn.hutool.db.dialect.Dialect;
import cn.hutool.db.sql.SqlBuilder;
import com.zilue.common.handler.XjrPageResultHandler;

import java.sql.Connection;
import java.sql.SQLException;

public class XjrSqlConnRunner extends SqlConnRunner {
    public XjrSqlConnRunner(Dialect dialect) {
        super(dialect);
    }

    public XjrSqlConnRunner(String driverClassName) {
        super(driverClassName);
    }

    @Override
    public PageResult<Entity> page(Connection conn, SqlBuilder sqlBuilder, Page page) throws SQLException {
        final XjrPageResultHandler pageResultHandler = new XjrPageResultHandler(
                new PageResult<>(page.getPageNumber(), page.getPageSize(), (int) count(conn, sqlBuilder)),
                this.caseInsensitive);
        return page(conn, sqlBuilder, page, pageResultHandler);
    }
}
