package com.zilue.common.runner;

import cn.hutool.json.JSONUtil;
import com.zilue.common.constant.GlobalConstant;
import com.zilue.common.utils.RedisUtil;
import com.zilue.module.authority.entity.DataAuth;
import com.zilue.module.authority.entity.DataAuthConfig;
import com.zilue.module.authority.entity.DataAuthRelation;
import com.zilue.module.authority.entity.DataAuthTableRelation;
import com.zilue.module.authority.service.IDataAuthConfigService;
import com.zilue.module.authority.service.IDataAuthRelationService;
import com.zilue.module.authority.service.IDataAuthService;
import com.zilue.module.authority.service.IDataAuthTableRelationService;
import com.zilue.module.workflow.entity.WorkflowSchema;
import com.zilue.module.workflow.model.WorkflowSchemaConfig;
import com.zilue.module.workflow.service.IWorkflowSchemaService;
import com.zilue.module.workflow.utils.WorkFlowUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * @Description 加载数据权限 缓存
 * @Author: zilue
 * @Date: 2023/2/28 14:49
 */
@Component
@AllArgsConstructor
@Slf4j
public class DataAuthRunner  implements CommandLineRunner {

    private RedisUtil redisUtil;

    private IDataAuthService dataAuthService;

    private IDataAuthRelationService dataAuthRelationService;

    private IDataAuthConfigService dataAuthConfigService;

    private IDataAuthTableRelationService dataAuthTableRelationService;

    private IWorkflowSchemaService workflowSchemaService;

    @Override
    public void run(String... args) {
        loadDataAuthCache();
        loadDataAuthConfigCache();
        loadDataAuthRelationCache();
        loadDataAuthTableRelationCache();
        loadEventCache();
    }

    @Async
    void loadDataAuthCache() {
        log.info("zilue: 加载所有数据权限缓存开始");
        List<DataAuth> list = dataAuthService.list();
        redisUtil.set(GlobalConstant.DATA_AUTH_CACHE_KEY, list);
        log.info("zilue: 加载所有数据权限缓存结束");
    }

    @Async
    void loadDataAuthConfigCache() {
        log.info("zilue: 加载所有数据权限配置缓存开始");
        List<DataAuthConfig> list = dataAuthConfigService.list();
        redisUtil.set(GlobalConstant.DATA_AUTH_CONFIG_CACHE_KEY, list);
        log.info("zilue: 加载所有数据权限配置缓存结束");
    }

    @Async
    void loadDataAuthRelationCache() {
        log.info("zilue: 加载所有数据权限关联缓存开始");
        List<DataAuthRelation> list = dataAuthRelationService.list();
        redisUtil.set(GlobalConstant.DATA_AUTH_RELATION_CACHE_KEY, list);
        log.info("zilue: 加载所有数据权限关联缓存结束");
    }

    @Async
    void loadDataAuthTableRelationCache() {
        log.info("zilue: 加载所有数据权限与表关联缓存开始");
        List<DataAuthTableRelation> list = dataAuthTableRelationService.list();
        redisUtil.set(GlobalConstant.DATA_AUTH_TABLE_RELATION_CACHE_KEY, list);
        log.info("zilue: 加载所有数据权限与表关联缓存结束");
    }

    @Async
    void loadEventCache() {
        log.info("zilue: 加载所有工作流模板开始与结束事件缓存开始");
        List<WorkflowSchema> list = workflowSchemaService.list();
        for (WorkflowSchema workflowSchema : list) {
            //获取到整个流程模板的配置
            WorkflowSchemaConfig workflowSchemaConfig = JSONUtil.toBean(workflowSchema.getJsonContent(), WorkflowSchemaConfig.class);
            CompletableFuture.runAsync(() -> {
                //先删除，后新增
                WorkFlowUtil.removeNodeListener(workflowSchema.getDeploymentId());
                WorkFlowUtil.cacheNodeListener(workflowSchema.getDeploymentId(), workflowSchemaConfig.getChildNodeConfig());
            });
        }
        log.info("zilue: 加载所有工作流模板开始与结束事件缓存结束");
    }
}