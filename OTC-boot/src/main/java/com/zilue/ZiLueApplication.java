package com.zilue;

import com.zilue.common.annotation.UniqueNameGenerator;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 */
@SpringBootApplication
@EnableAspectJAutoProxy(exposeProxy = true)
@ComponentScan(nameGenerator = UniqueNameGenerator.class)
@EnableScheduling
@MapperScan("com.zilue.module.**.mapper")
public class ZiLueApplication {

    public static void main(String[] args) {
        SpringApplication.run(ZiLueApplication.class, args);
    }



}