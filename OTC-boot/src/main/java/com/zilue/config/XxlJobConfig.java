package com.zilue.config;

import com.xxl.job.core.executor.XxlJobExecutor;
import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
//@ConditionalOnProperty(prefix = "zilue.job.enabled", name = "xxl-job", havingValue = "true")
@Slf4j
public class XxlJobConfig {

    @Value("${zilue.job.admin.addresses}")
    private String adminAddresses;

    @Value("${zilue.job.executor.appname}")
    private String appName;

    @Value("${zilue.job.executor.ip}")
    private String ip;

    @Value("${zilue.job.executor.port}")
    private int port;

    @Value("${zilue.job.accessToken}")
    private String accessToken;

    @Value("${zilue.job.executor.logpath}")
    private String logPath;

    @Value("${zilue.job.executor.logretentiondays}")
    private int logRetentionDays;


    @Bean
    public XxlJobExecutor xxlJobExecutor() {
        log.info("------------xxl-job config init---------------");
       /* XxlJobExecutor xxlJobExecutor = new XxlJobExecutor();
        xxlJobExecutor.setAdminAddresses(adminAddresses);
        xxlJobExecutor.setAppname(appName);
        xxlJobExecutor.setIp(ip);
        xxlJobExecutor.setPort(port);
        xxlJobExecutor.setAccessToken(accessToken);
        xxlJobExecutor.setLogPath(logPath);
        xxlJobExecutor.setLogRetentionDays(logRetentionDays);*/


        XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
        xxlJobSpringExecutor.setAdminAddresses(adminAddresses);
        xxlJobSpringExecutor.setAppname(appName);
        xxlJobSpringExecutor.setIp(ip);
        xxlJobSpringExecutor.setPort(port);
        xxlJobSpringExecutor.setAccessToken(accessToken);
        xxlJobSpringExecutor.setLogPath(logPath);
        xxlJobSpringExecutor.setLogRetentionDays(logRetentionDays);
        return xxlJobSpringExecutor;
    }
}