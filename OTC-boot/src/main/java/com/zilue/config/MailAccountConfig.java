package com.zilue.config;

import cn.hutool.extra.mail.MailAccount;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;

/**
 * 邮箱账户配置类
 * @Author: zilue
 * @Date: 2023/10/27 10:58
 */
//@Configuration
@Slf4j
public class MailAccountConfig {

    public String host;

    public Integer port;

    public Boolean auth;

    public String from;

    public String user;

    public String pass;


    @Bean
    public MailAccount mailAccount(){
        log.info("------------mail account config init---------------");
        MailAccount account = new MailAccount();
        account.setHost(host);
        account.setPort(port);
        account.setAuth(auth);
        account.setFrom(from);
        account.setUser(user);
        account.setPass(pass);
        return account;
    }
}
