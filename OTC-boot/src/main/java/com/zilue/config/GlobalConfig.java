package com.zilue.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @Author: zilue
 * @Date: 2023/3/8 9:01
 */
@Data
@Component
@ConfigurationProperties("zilue")
public class GlobalConfig {
    /**
     * oss配置
     */
    private OSSConfig ossConfig;

    /**
     * 生成地址配置
     */
    private GeneratePathConfig generatePathConfig;

    /**
     * 短信配置
     */
    private XjrSmsConfig xjrSmsConfig;


}
