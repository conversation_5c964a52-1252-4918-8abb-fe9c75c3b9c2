package com.zilue.config;

import com.zilue.common.utils.SwaggerUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2WebMvc;

import java.util.Arrays;
import java.util.List;

@Configuration
@EnableSwagger2WebMvc
public class Knife4jConfig{

    private String  BASE_PACKAGES = "com.zilue";

    @Bean
    public Docket systemDocket() {
        return docket("系统模块",
                Arrays.asList(BASE_PACKAGES + ".module.system", BASE_PACKAGES + ".module.form",BASE_PACKAGES + ".module.authority", BASE_PACKAGES + ".module.generator", BASE_PACKAGES + ".module.organization"));
    }
    @Bean
    public Docket Docket() {
        return docket("demo模块",  Arrays.asList(BASE_PACKAGES + ".module.demo"));
    }
    @Bean
    public Docket wechatDocket() {
        return docket("小程序模块",  Arrays.asList(BASE_PACKAGES + ".module.wechat"));
    }

    @Bean
    public Docket appDocket() {
        return docket("app模块",  Arrays.asList(BASE_PACKAGES + ".module.app"));
    }

    @Bean
    public Docket businessDocket() {
        return docket("后台业务模块",  Arrays.asList(BASE_PACKAGES + ".module.business"));
    }
    private Docket docket(String groupName, List<String> basePackages) {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName(groupName)
                .apiInfo(apiInfo())
                .select()
                .apis(SwaggerUtil.basePackages(basePackages))
                //.apis(RequestHandlerSelectors.withMethodAnnotation(ApiOperation.class))
                .paths(PathSelectors.any())
                .build();
    }
    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("ZILUE RESTful APIs")
                .description("# ZILUE RESTful APIs")
                .termsOfServiceUrl("https://www.zilueit.com")
                .contact("")
                .version("1.0")
                .build();
    }
}