package com.zilue.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @Author: zilue
 * @Date: 2023/11/29 10:44
 */
@Data
@Component
@ConfigurationProperties("zilue.keycloak")
public class KeyCloakConfig {
//    private String url;
//
//    private String realm;
//
//    private String clientId;
//
//    private String secret;
//
//    private String userName;
//
//    private String password;

    private String payload;
}
