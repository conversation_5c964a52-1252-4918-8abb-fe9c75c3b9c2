server:
  port: 8080


spring:
  # 环境 dev|test|prod
  profiles:
    active: dev
  # jackson时间格式化
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      enabled: true
  mvc:
    throw-exception-if-no-handler-found: true
    pathmatch:
      matching-strategy: ant_path_matcher


magic-api:
  #配置web页面入口
  web: /magic/web
  resource: #配置存储方式
    type: database # 配置存储在数据库中
    tableName: magic_api_file # 数据库中的表名
#    datasource: master #指定数据源（单数据源时无需配置，多数据源时默认使用主数据源，如果存在其他数据源中需要指定。）
    prefix: /magic-api # key前缀
    readonly: false # 是否是只读模式
#  security: # 安全配置
#    username: admin # 登录用的用户名
#    password: 123456 # 登录用的密码
  prefix: /magic-api
  editor-config: classpath:./magic-editor-config.js #编辑器配置
  date-pattern: # 配置请求参数支持的日期格式
    - yyyy-MM-dd
    - yyyy-MM-dd HH:mm:ss
    - yyyyMMddHHmmss
    - yyyyMMdd
  response: |- #配置JSON格式，格式为magic-script中的表达式
    {
      code: code,
      msg: message,
      data,
      timestamp,
      requestTime,
      executeTime,
    }
  response-code:
    success: 0 #执行成功的code值
    invalid: 10400 #参数验证未通过的code值
    exception: 10500 #执行出现异常的code值
  backup: #备份相关配置
    enable: true #是否启用
    max-history: -1 #备份保留天数，-1为永久保留
    table-name: magic_api_backup #使用数据库存储备份时的表名
  crud: # CRUD相关配置
    logic-delete-column: delete_mark #逻辑删除列
    logic-delete-value: 1 #逻辑删除值
  page:
    size: size # 页大小的参数名称
    page: limit # 页码的参数名称
    default-page: 1 # 未传页码时的默认首页
    default-size: 10 # 未传页大小时的默认页大小
  debug:
      timeout: 60 # 断点超时时间，默认60s
  throw-exception: true # 执行出错时，异常将抛出处理
  auto-import-module: db,http,log,request,response,env,magic #自动导入模块






# Sa-Token配置
sa-token:
  # sa-token-temp-jwt 模块的秘钥 （随便乱摁几个字母就行了）
  jwt-secret-key: xxxxxxxxx
  # token前缀
  token-prefix: Bearer
  # token名称 (同时也是cookie名称)
  token-name: Authorization
  # token有效期，单位s 默认1天, -1代表永不过期
  timeout: 86400
  # token临时有效期 (指定时间内无操作就视为token过期) 单位: 秒
  activity-timeout: 1800
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: true
  # token风格
  token-style: uuid
  # 是否输出操作日志
  is-log: false

mybatis-plus-join:
  #是否打印 mybatis plus join banner 默认true
  banner: true
  #全局启用副表逻辑删除(默认true) 关闭后关联查询不会加副表逻辑删除
  sub-table-logic: true
  #拦截器MappedStatement缓存(默认true)
  ms-cache: true
  #表别名(默认 t)
  table-alias: t



#mybatis
mybatis-plus:
  mapper-locations: classpath:/mappers/*/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.zilue.modules.*.entity
  global-config:
    #数据库相关配置
    db-config:
      #主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID", ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: ASSIGN_ID
      logic-delete-value: 1
      logic-not-delete-value: 0
    banner: false
  #原生配置
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'


camunda:
  bpm:
    admin-user:
      id: admin
      password: admin
      #        first-name: admin
      #      filter:
      #        create: All tasks
      #        #指定数据库类型
      #        database:
      #          type: mysql
      #自动部署resources下面的bpmn文件
      #    auto-deployment-enabled: true
      #禁止index跳转到Camunda自带的管理界面，默认true
  #    webapp:
  #      index-redirect-enabled: false
    database:
     jdbc-batch-processing: false #oracle使用camunda工作流报错关闭批处理
#      type: oracle  #  kingbase 需要根据模式来填写type  是 oracle  还是 postgres




