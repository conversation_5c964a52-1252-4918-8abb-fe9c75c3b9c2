package ${package};

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.yulichang.base.MPJBaseMapper;
import com.zilue.module.${outputArea?lower_case}.${className?lower_case}.entity.${entityClass};
import org.apache.ibatis.annotations.Mapper;

/**
* @title: ${tableComment}
* <AUTHOR>
* @Date: ${date}
* @Version 1.0
*/
@Mapper
public interface ${entityClass}Mapper extends ${isMulti?string("MPJBaseMapper","BaseMapper")}<${entityClass}> {

}
