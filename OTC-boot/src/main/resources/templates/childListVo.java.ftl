package ${package};

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import com.zilue.common.page.MultiListOutput;
<#--子表遍历-->
<#if childTables?? >
    <#list childTables as childTable>
import com.zilue.module.${outputArea?lower_case}.${className?lower_case}.entity.${childTable?cap_first};
    </#list>
</#if>
import java.util.List;

/**
* @title: ${tableComment}
* <AUTHOR>
* @Date: ${date}
* @Version 1.0
*/
@Data
public class ${entityClass}ChildListVo extends MultiListOutput {

<#--子表遍历-->
<#if childTables?? >
<#list childTables as childTable>
    /**
    * ${childTable}
    */
    @ApiModelProperty("${childTable}子表")
    private List<${childTable?cap_first}ListVo> ${childTable}List;
</#list>
</#if>

}
