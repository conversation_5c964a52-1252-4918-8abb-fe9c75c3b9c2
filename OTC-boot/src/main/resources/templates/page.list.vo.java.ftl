package ${package};

<#if isImport || isExport>
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.write.style.ContentStyle;
</#if>
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import com.zilue.common.annotation.Trans;
import com.zilue.common.enums.TransType;
import java.time.LocalTime;
import java.time.LocalDateTime;
import java.math.BigDecimal;

/**
* @title: ${tableComment}
* <AUTHOR>
* @Date: ${date}
* @Version 1.0
*/
@Data
public class ${entityClass}${isPage?string("Page","List")}Vo {

<#assign index = 0>
<#--属性遍历-->
<#list fields as field>
    /**
    * ${(field.fieldComment)!''}
    */
    <#if !(field.pk || field.fieldName == "ruleUserId")>
    <#if isImport || isExport>
<#--    所有单元格设置成文本格式-->
    @ContentStyle(dataFormat = 49)
    @ExcelProperty(value = "${field.label}<#if field.required>${requiredSuffix}</#if>", index =${index})
    <#assign index++>
    </#if>
    <#else>
    <#if isImport || isExport>
    @ExcelIgnore
    </#if>
    </#if>
    @ApiModelProperty("${(field.fieldComment)!''}")
    <#if field.fieldType == "LocalDateTime"  && field.pattern??>
    @JsonFormat(pattern = "${field.pattern}")
    </#if>
    <#assign multi = "">
    <#if field.multi><#assign multi = ", isMulti = true"></#if>
    <#if field.datasourceType??>
        <#if field.datasourceType = "dic">
    @Trans(type = TransType.DIC, id = "${field.datasourceId}"${multi})
        </#if>
        <#if field.datasourceType = "api">
    @Trans(type = TransType.API, id = "${field.datasourceId}"${multi})
        </#if>
    </#if>
    <#if field.componentType??>
        <#if field.componentType = "user">
    @Trans(type = TransType.USER)
        </#if>
        <#if field.componentType = "organization">
    @Trans(type = TransType.DEPT)
        </#if>
        <#if field.componentType = "area">
    @Trans(type = TransType.AREA)
        </#if>
        <#if field.componentType = "cascader">
    @Trans(type = TransType.CASCADE, id = "${field.datasourceId}", separator = "${field.separator}", showFormat = "${field.showFormat}")
        </#if>
        <#if field.componentType = "tree-select-component">
    @Trans(type = TransType.TREE, id = "${field.datasourceId}"${multi})
        </#if>
        <#if field.componentType = "post">
    @Trans(type = TransType.POST)
        </#if>
        <#if field.componentType = "role">
    @Trans(type = TransType.ROLE)
        </#if>
        <#if field.componentType = "organization_code">
    @Trans(type = TransType.DEPT_CODE)
        </#if>
    </#if>
    private ${field.fieldType} ${field.fieldName};
</#list>

}
