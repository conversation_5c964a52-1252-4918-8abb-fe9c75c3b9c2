package ${package};

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.extension.mapping.config.DeepConfig;
import com.github.yulichang.extension.mapping.relation.Relation;
<#if isMulti>
        <#--子表遍历-->
    <#list childTables as childTable>
import com.zilue.module.${outputArea?lower_case}.${className?lower_case}.entity.${childTable.tableName?cap_first};
import com.zilue.module.${outputArea?lower_case}.${className?lower_case}.mapper.${childTable.tableName?cap_first}Mapper;
    <#if childTable.subTable??>
import com.zilue.module.${outputArea?lower_case}.${className?lower_case}.entity.${childTable.subTable.tableName?cap_first};
import com.zilue.module.${outputArea?lower_case}.${className?lower_case}.mapper.${childTable.subTable.tableName?cap_first}Mapper;
    </#if>
    </#list>
</#if>
import com.zilue.module.${outputArea?lower_case}.${className?lower_case}.entity.${entityClass};
import com.zilue.module.${outputArea?lower_case}.${className?lower_case}.mapper.${entityClass}Mapper;
import com.zilue.module.${outputArea?lower_case}.${className?lower_case}.service.I${className}Service;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
<#if databaseId?? && databaseId != "master">
import com.baomidou.dynamic.datasource.annotation.DS;
</#if>
<#if isShowSubList?? && isShowSubList>
import com.zilue.module.${outputArea?lower_case}.${className?lower_case}.vo.${entityClass}ChildListVo;
<#list showSubNameList as showSubName>
import com.zilue.module.${outputArea?lower_case}.${className?lower_case}.vo.${showSubName?cap_first}ListVo;
</#list>
import java.util.Map;
import java.util.HashMap;
import cn.hutool.core.bean.BeanUtil;
</#if>
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.collections4.CollectionUtils;

/**
* @title: ${tableComment}
* <AUTHOR>
* @Date: ${date}
* @Version 1.0
*/
@Service
@AllArgsConstructor
<#if databaseId?? && databaseId != "master">
@DS("${databaseId}")
</#if>
public class ${className}ServiceImpl extends ${isMulti?string("MPJBaseService","Service")}Impl<${entityClass}Mapper, ${entityClass}> implements I${className}Service {
<#if isMulti>
    private final ${entityClass}Mapper ${className?uncap_first}${entityClass}Mapper;

    <#--子表遍历-->
    <#list childTables as childTable>
    private final ${childTable.tableName?cap_first}Mapper ${className?uncap_first}${childTable.tableName?cap_first}Mapper;
    <#if childTable.subTable?? >
    private final ${childTable.subTable.tableName?cap_first}Mapper ${className?uncap_first}${childTable.subTable.tableName?cap_first}Mapper;
    </#if>
    </#list>

    @Override
    public ${entityClass} info(${pkType} id) {
        ${entityClass} ${entityClass?uncap_first} = this.getById(id);
        if (${entityClass?uncap_first} != null) {
        <#list childTables as childTable>
            <#if childTable.subTable??>
            List<${childTable.tableName?cap_first}> ${childTable.tableName}List = Relation.mpjGetRelation(${className?uncap_first}${childTable.tableName?cap_first}Mapper.selectList(Wrappers.lambdaQuery(${childTable.tableName?cap_first}.class).eq(${childTable.tableName?cap_first}::get${childTable.relationField?cap_first}, ${entityClass?uncap_first}.get${childTable.relationTableField?cap_first}())), DeepConfig.defaultConfig());
            <#else>
            List<${childTable.tableName?cap_first}> ${childTable.tableName}List = ${className?uncap_first}${childTable.tableName?cap_first}Mapper.selectList(Wrappers.lambdaQuery(${childTable.tableName?cap_first}.class).eq(${childTable.tableName?cap_first}::get${childTable.relationField?cap_first}, ${entityClass?uncap_first}.get${childTable.relationTableField?cap_first}()));
            </#if>
            ${entityClass?uncap_first}.set${childTable.tableName?cap_first}List(${childTable.tableName}List);
        </#list>
        }
        return ${entityClass?uncap_first};
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean add(${entityClass} ${entityClass?uncap_first}) {
        ${className?uncap_first}${entityClass}Mapper.insert(${entityClass?uncap_first});
    <#--子表遍历-->
    <#list childTables as childTable>
        for (${childTable.tableName?cap_first} ${childTable.tableName} : ${entityClass?uncap_first}.get${childTable.tableName?cap_first}List()) {
            ${childTable.tableName}.set${childTable.relationField?cap_first}(${entityClass?uncap_first}.get${childTable.relationTableField?cap_first}());
            ${className?uncap_first}${childTable.tableName?cap_first}Mapper.insert(${childTable.tableName});
            <#if childTable.subTable?? >
            for (${childTable.subTable.tableName?cap_first} ${childTable.subTable.tableName} : ${childTable.tableName}.get${childTable.subTable.tableName?cap_first}List()) {
                ${childTable.subTable.tableName}.set${childTable.subTable.relationField?cap_first}(${childTable.tableName}.get${childTable.subTable.relationTableField?cap_first}());
                ${className?uncap_first}${childTable.subTable.tableName?cap_first}Mapper.insert(${childTable.subTable.tableName});
            }
            </#if>
        }
    </#list>

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(${entityClass} ${entityClass?uncap_first}) {
        ${className?uncap_first}${entityClass}Mapper.updateById(${entityClass?uncap_first});
    <#--子表遍历-->
    <#list childTables as childTable>
        //********************************* ${childTable.tableName?cap_first}  增删改  开始 *******************************************/
        {
            // 查出所有子级的id
            List<${childTable.tableName?cap_first}> ${childTable.tableName}List = ${className?uncap_first}${childTable.tableName?cap_first}Mapper.selectList(Wrappers.lambdaQuery(${childTable.tableName?cap_first}.class).eq(${childTable.tableName?cap_first}::get${childTable.relationField?cap_first}, ${entityClass?uncap_first}.get${childTable.relationTableField?cap_first}()).select(${childTable.tableName?cap_first}::get${childTable.pkField?cap_first}));
            List<${childTable.pkType}> ${childTable.tableName}Ids = ${childTable.tableName}List.stream().map(${childTable.tableName?cap_first}::get${childTable.pkField?cap_first}).collect(Collectors.toList());
            //原有子表单 没有被删除的主键
            List<${childTable.pkType}> ${childTable.tableName}OldIds = ${entityClass?uncap_first}.get${childTable.tableName?cap_first}List().stream().map(${childTable.tableName?cap_first}::get${childTable.pkField?cap_first}).filter(Objects::nonNull).collect(Collectors.toList());
            //找到需要删除的id
            List<${childTable.pkType}> ${childTable.tableName}RemoveIds = ${childTable.tableName}Ids.stream().filter(item -> !${childTable.tableName}OldIds.contains(item)).collect(Collectors.toList());

            for (${childTable.tableName?cap_first} ${childTable.tableName} : ${entityClass?uncap_first}.get${childTable.tableName?cap_first}List()) {
                //如果不等于空则修改
                if (${childTable.tableName}.get${childTable.pkField?cap_first}() != null) {
                    ${className?uncap_first}${childTable.tableName?cap_first}Mapper.updateById(${childTable.tableName});
                }
                //如果等于空 则新增
                else {
                    //已经不存在的id 删除
                    ${childTable.tableName}.set${childTable.relationField?cap_first}(${entityClass?uncap_first}.get${childTable.relationTableField?cap_first}());
                    ${className?uncap_first}${childTable.tableName?cap_first}Mapper.insert(${childTable.tableName});
                }
                <#if childTable.subTable??>
                //********************************* ${childTable.subTable.tableName?cap_first}  增删改  开始 *******************************************/

                ${className?uncap_first}${childTable.subTable.tableName?cap_first}Mapper.delete(Wrappers.lambdaQuery(${childTable.subTable.tableName?cap_first}.class).eq(${childTable.subTable.tableName?cap_first}::get${childTable.subTable.relationField?cap_first}, ${childTable.tableName}.get${childTable.subTable.relationTableField?cap_first}()));
                for (${childTable.subTable.tableName?cap_first} ${childTable.subTable.tableName} : ${childTable.tableName}.get${childTable.subTable.tableName?cap_first}List()) {
                    ${childTable.subTable.tableName}.set${childTable.subTable.relationField?cap_first}(${childTable.tableName}.get${childTable.subTable.relationTableField?cap_first}());
                    ${className?uncap_first}${childTable.subTable.tableName?cap_first}Mapper.insert(${childTable.subTable.tableName});
                }

                //********************************* ${childTable.subTable.tableName?cap_first}  增删改  结束 *******************************************/
                </#if>
            }
            //已经不存在的id 删除
            if(${childTable.tableName}RemoveIds.size() > 0){
                ${className?uncap_first}${childTable.tableName?cap_first}Mapper.deleteBatchIds(${childTable.tableName}RemoveIds);
            }
        }
        //********************************* ${childTable.tableName?cap_first}  增删改  结束 *******************************************/

    </#list>
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(List<${pkType}> ids) {
        ${className?uncap_first}${entityClass}Mapper.deleteBatchIds(ids);
    <#--子表遍历-->
    <#list childTables as childTable>
        <#if childTable.subTable??>
        List<${childTable.tableName?cap_first}> ${childTable.tableName}List = ${className?uncap_first}${childTable.tableName?cap_first}Mapper.selectList(Wrappers.lambdaQuery(${childTable.tableName?cap_first}.class).select(${childTable.tableName?cap_first}::get${childTable.pkField?cap_first}).in(${childTable.tableName?cap_first}::get${childTable.relationField?cap_first}, ids));
        if (CollectionUtils.isNotEmpty(${childTable.tableName}List)) {
            List<${pkType}> ${childTable.tableName}IdList = ${childTable.tableName}List.stream().map(${childTable.tableName?cap_first}::get${childTable.pkField?cap_first}).collect(Collectors.toList());
            ${className?uncap_first}${childTable.tableName?cap_first}Mapper.deleteBatchIds(${childTable.tableName}IdList);
            ${className?uncap_first}${childTable.subTable.tableName?cap_first}Mapper.delete(Wrappers.lambdaQuery(${childTable.subTable.tableName?cap_first}.class).in(${childTable.subTable.tableName?cap_first}::get${childTable.subTable.relationField?cap_first}, ${childTable.tableName}IdList));
        }
        <#else>
        ${className?uncap_first}${childTable.tableName?cap_first}Mapper.delete(Wrappers.lambdaQuery(${childTable.tableName?cap_first}.class).in(${childTable.tableName?cap_first}::get${childTable.relationField?cap_first}, ids));
        </#if>
    </#list>

        return true;
    }

<#if isShowSubList?? && isShowSubList>
    @Override
    public ${entityClass}ChildListVo listChildrenData(${pkType} id) {
        ${entityClass}ChildListVo ${entityClass?uncap_first}ChildListVo = new ${entityClass}ChildListVo();
    <#list childTables as childTable>
        <#assign flag = false >
        <#list showSubNameList as showSubName>
            <#if showSubName == childTable.tableName>
                <#assign flag = true >
                <#break>
            </#if>
        </#list>
        <#if flag>
        List<${childTable.tableName?cap_first}> ${childTable.tableName?uncap_first}List = ${className?uncap_first}${childTable.tableName?cap_first}Mapper.selectList(Wrappers.lambdaQuery(${childTable.tableName?cap_first}.class).eq(${childTable.tableName?cap_first}::get${childTable.relationField?cap_first}, id));
        ${entityClass?uncap_first}ChildListVo.set${childTable.tableName?cap_first}List(BeanUtil.copyToList(${childTable.tableName?uncap_first}List, ${childTable.tableName?cap_first}ListVo.class));
        </#if>

    </#list>
        return ${entityClass?uncap_first}ChildListVo;
    }
</#if>
</#if>
}
