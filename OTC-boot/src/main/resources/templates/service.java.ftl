package ${package};

import com.baomidou.mybatisplus.extension.service.IService;
<#if isMulti>
import com.github.yulichang.base.MPJBaseService;
import com.github.yulichang.extension.mapping.base.MPJDeepService;
</#if>
import com.zilue.module.${outputArea?lower_case}.${className?lower_case}.entity.${entityClass};
import lombok.Data;
import java.util.List;
<#if isShowSubList?? && isShowSubList>
import com.zilue.module.${outputArea?lower_case}.${className?lower_case}.vo.${entityClass}ChildListVo;
import java.util.Map;
</#if>

/**
* @title: ${tableComment}
* <AUTHOR>
* @Date: ${date}
* @Version 1.0
*/
<#assign extendsCode = "IService<${entityClass}>">
<#if isMulti>
<#assign extendsCode = "MPJBaseService<${entityClass}>, MPJDeepService<${entityClass}>">
</#if>
public interface I${className}Service extends ${extendsCode} {
<#if isMulti>
    /**
    * 查询详情
    *
    * @param ${entityClass?uncap_first}
    * @return
    */
    ${entityClass} info(${pkType} id);

    /**
    * 新增
    *
    * @param ${entityClass?uncap_first}
    * @return
    */
    Boolean add(${entityClass} ${entityClass?uncap_first});

    /**
    * 更新
    *
    * @param ${entityClass?uncap_first}
    * @return
    */
    Boolean update(${entityClass} ${entityClass?uncap_first});

    /**
    * 删除
    *
    * @param ids
    * @return
    */
    Boolean delete(List<${pkType}> ids);

<#if isShowSubList?? && isShowSubList>
    /**
    * 查询列表展示的子表数据
    *
    * @return
    */
    ${entityClass}ChildListVo listChildrenData(${pkType} id);
</#if>
</#if>
}
