<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zilue.module.business.taskManagement.mapper.OtcSalesmanTaskMapper">

    <select id="selectPageList" resultType="com.zilue.module.business.taskManagement.vo.SalesPerformanceRespVo">
        select t1.name                                                                       emplName,
               t1.code                                                                       erpCode,
               IFNULL(t1.business_unit_id_name, '')                                          businessUnitIdName,
               t3.plan_amount                                                                yearPlanAmount,
               t3.finish_amount                                                              yearFinishAmount,
               t2.plan_amount                                                                monthPlanAmount,
               t2.finish_amount                                                              monthFinishAmount,
        CONCAT(ROUND(IFNULL((t2.finish_amount / t2.plan_amount), '0') * 100, 2), '%') finishRate,
        CONCAT( ROUND(IFNULL(((t2.finish_amount - t2.last_year_period_finish_amount) /
                                    t2.last_year_period_finish_amount) *
                                   100, '0'), 2)     , '%')                                  yoyGrowthRate,
               t5.hierarchy
        from xjr_user t1
            left join (select sum(temp.finish_amount)                  finish_amount,
                              sum(temp.plan_amount)                    plan_amount,
                              sum(temp.last_year_period_finish_amount) last_year_period_finish_amount,
                              temp.user_code                           user_code
                       from otc_level_sales_report_month temp
        where temp.enabled_mark = 1
          and temp.delete_mark = 0
          and temp.business_month in
        <foreach collection="req.monthDateList" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        group by user_code) t2 on t1.code = t2.user_code
            left join otc_level_sales_report_year t3 on
            t1.code = t3.user_code and t3.business_year = #{req.year}
            left join xjr_user_dept_relation t4 on t1.id = t4.user_id
            left join xjr_department t5 on t4.dept_id = t5.id
        where t1.enabled_mark = 1
          and t1.delete_mark = 0
          and t3.enabled_mark = 1
          and t3.delete_mark = 0
        <if test="req.keyword != null and req.keyword != ''">
            and (t1.name like CONCAT('%', #{req.keyword}, '%')
                or t1.code like CONCAT('%', #{req.keyword}, '%'))
        </if>
        <if test="req.deptIds != null and !req.deptIds.isEmpty()">
            and t4.dept_id in
            <foreach collection="req.deptIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectSummation" resultType="com.zilue.module.business.taskManagement.vo.SalesPerformanceSummationVo">
        select sum(t2.plan_amount)                                                           totalMonthPlan,
               sum(t2.finish_amount)                                                         totalMonthActual,
               concat(ROUND(IFNULL(sum(t2.finish_amount) / sum(t2.plan_amount), 0), 2), '%') totalMonthStatistics
        from xjr_user t1
            left join (select sum(temp.finish_amount)                  finish_amount,
                              sum(temp.plan_amount)                    plan_amount,
                              sum(temp.last_year_period_finish_amount) last_year_period_finish_amount,
                              temp.user_code                           user_code
                       from otc_level_sales_report_month temp
        where temp.enabled_mark = 1
          and temp.delete_mark = 0
          and temp.business_month in
        <foreach collection="req.monthDateList" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        group by user_code) t2 on t1.code = t2.user_code
            left join xjr_user_dept_relation t4 on t1.id = t4.user_id
        where t1.enabled_mark = 1
          and t1.delete_mark = 0
        <if test="req.keyword != null and req.keyword != ''">
            and (t1.name like CONCAT('%', #{req.keyword}, '%')
                or t1.code like CONCAT('%', #{req.keyword}, '%'))
        </if>
        <if test="req.deptIds != null and !req.deptIds.isEmpty()">
            and t4.dept_id in
            <foreach collection="req.deptIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectAllList" resultType="com.zilue.module.business.taskManagement.vo.SalesPerformanceRespVo">
        select t1.name                                                                       emplName,
               t1.code                                                                       erpCode,
               IFNULL(t1.business_unit_id_name, '')                                          businessUnitIdName,
               t3.plan_amount                                                                yearPlanAmount,
               t3.finish_amount                                                              yearFinishAmount,
               t2.plan_amount                                                                monthPlanAmount,
               t2.finish_amount                                                              monthFinishAmount,
               CONCAT(ROUND(IFNULL((t2.finish_amount / t2.plan_amount), '0') * 100, 2), '%') finishRate,
               CONCAT(ROUND(IFNULL(((t2.finish_amount - t2.last_year_period_finish_amount) /
                                    t2.last_year_period_finish_amount) *
                                   100, '0'), 2), '%')                                       yoyGrowthRate,
               t5.hierarchy
        from xjr_user t1
            left join (select sum(temp.finish_amount)                  finish_amount,
                              sum(temp.plan_amount)                    plan_amount,
                              sum(temp.last_year_period_finish_amount) last_year_period_finish_amount,
                              temp.user_code                           user_code
                       from otc_level_sales_report_month temp
        where temp.enabled_mark = 1
          and temp.delete_mark = 0
          and temp.business_month in
        <foreach collection="req.monthDateList" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        group by user_code) t2 on t1.code = t2.user_code
            left join otc_level_sales_report_year t3 on
            t1.code = t3.user_code and t3.business_year = #{req.year}
            left join xjr_user_dept_relation t4 on t1.id = t4.user_id
            left join xjr_department t5 on t4.dept_id = t5.id
        where t1.enabled_mark = 1
          and t1.delete_mark = 0
          and t3.enabled_mark = 1
          and t3.delete_mark = 0
        <if test="req.keyword != null and req.keyword != ''">
            and (t1.name like CONCAT('%', #{req.keyword}, '%')
                or t1.code like CONCAT('%', #{req.keyword}, '%'))
        </if>
        <if test="req.deptIds != null and !req.deptIds.isEmpty()">
            and t4.dept_id in
            <foreach collection="req.deptIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>