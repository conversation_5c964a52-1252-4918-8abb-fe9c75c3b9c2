<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zilue.module.system.mapper.DictionaryitemMapper">
    <select id="dicItemList" resultType="com.zilue.module.system.vo.DictionaryItemVo">
        SELECT dd.* from xjr_dictionary_detail dd join xjr_dictionary_item di on dd.item_id =di.id
        <where>
            <if test="dto.name!=null and dto.name!=''">
                AND di.name like CONCAT( '%',#{dto.name , jdbcType=VARCHAR}, '%')
            </if>
        </where>
    </select>
</mapper>