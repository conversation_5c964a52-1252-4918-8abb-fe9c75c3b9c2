<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zilue.module.organization.mapper.UserRoleRelationMapper">
    <select id="getUserRoleInfo" resultType="com.zilue.module.organization.dto.UserRoleInfoDto">
        SELECT r.id,r.name,u.id as userId from xjr_role r join xjr_user_role_relation rr on r.id=rr.role_id join
        xjr_user u on
        rr.user_id=u.id
        <where>
            r.delete_mark=0
            <if test="userIds != null and userIds.size() > 0">
                and (u.id in
                <foreach collection="userIds" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>)
            </if>
        </where>
    </select>
</mapper>
