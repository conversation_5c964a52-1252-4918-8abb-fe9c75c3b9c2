<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zilue.module.wechat.sign.mapper.OtcSignInMapper">
    <select id="pageList" resultType="com.zilue.module.business.sign.vo.OtcSignInAdminPageVo">
        SELECT
        a.person_name as personName,
        a.person_code as personCode,
        a.coached_person_name as coachedPersonName,
        a.coached_erp_code as coachedPersonCode,
        a.category,
        a.pur_merchant_name as purMerchantName,
        a.destination,
        a.remarks,
        a.superable_scope_remarks as superableScopeRemarks,
        a.create_date AS signDate,
        a.serial_number AS serialNumber,
        a.offset signOffset,
        a.exception_status AS signExceptionStatus,
        a.exception_reason as signExceptionReason,
        b.create_date AS signOutDate,
        b.exception_status AS signOutExceptionStatus,
        b.exception_reason AS signOutExceptionReason,
        b.offset signOutOffset,
        b.superable_scope_remarks as descrOutOfScope,
        b.pre_visit_plan as preVisitPlan,
        b.work_attitude as workAttitude,
        b.cus_relationship_management as cusRelationshipManagement,
        b.statement_interests as statementInterests,
        b.suggestion as suggestion,
        b.evaluate as evaluate,
        COALESCE(b.image_ids, a.image_ids) as imageIds
        FROM
        otc_sign_in a
        left JOIN otc_sign_in b ON a.serial_number = b.serial_number and b.sigin_type = 2
        <where>
            a.sigin_type = 1
            <if test="req.coachedType!=null and req.coachedType!=''">
                AND a.coached_type = #{req.coachedType}
            </if>
            <if test="req.coachedNameOrEdpCode!=null and req.coachedNameOrEdpCode!=''">
                AND (a.coached_person_name like CONCAT( #{req.coachedNameOrEdpCode ,jdbcType=VARCHAR}, '%') or
                a.coached_erp_code like CONCAT( #{req.coachedNameOrEdpCode ,jdbcType=VARCHAR}, '%') )
            </if>
            <if test="req.purMerchantName!=null and req.purMerchantName!=''">
                AND a.pur_merchant_name like CONCAT(#{req.purMerchantName , jdbcType=VARCHAR}, '%')
            </if>
            <if test="req.salesmanNameOrEdpCode!=null and req.salesmanNameOrEdpCode!=''">
                AND (a.person_name like CONCAT( #{req.salesmanNameOrEdpCode ,jdbcType=VARCHAR}, '%') or a.person_code
                like CONCAT( #{req.salesmanNameOrEdpCode ,jdbcType=VARCHAR}, '%') )
            </if>
            <if test="req.category!=null and req.category!=''">
                AND a.category = #{req.category}
            </if>
            <if test="req.startTime!= null">
                AND a.create_date &gt;= #{req.startTime}
            </if>
            <if test="req.endTime!= null">
                AND a.create_date &lt;= #{req.endTime}
            </if>
            <if test="req.isException != null and req.isException == 1">

                AND (a.exception_status = 2 or b.exception_status = 2 AND a.create_date  <![CDATA[ < ]]> CURDATE() OR
                b.create_date IS NULL )
            </if>
        </where>
        ORDER BY
        a.create_date DESC
    </select>

    <select id="selectTaskInfo" resultType="com.zilue.module.business.taskManagement.vo.OtcSalesmanTaskVo">
        SELECT t.id,t.task_type ,t.start_year_month,t.end_year_month,t.frequency,t.post_id,tv.goal
        from otc_salesman_task t
        join otc_salesman_task_visit tv on t.id = tv.task_id
        <where>
            t.enabled_mark = 1 and t.task_status=2 and t.is_enabled = 1 and tv.enabled_mark = 1
            <if test="req.taskType != null and req.taskType !=''">
                AND t.task_type = #{req.taskType}
            </if>
            <if test="req.frequency != null and req.frequency !=''">
                AND t.frequency = #{req.frequency}
            </if>
            <if test="req.postId != null and req.postId !=''">
                AND t.post_id like concat('%', #{req.postId}, '%')
            </if>
        </where>
    </select>

    <select id="selectTaskGoal" resultType="java.lang.Integer">
        SELECT tv.goal
        from otc_salesman_task t
        join otc_salesman_task_visit tv on t.id = tv.task_id
        <where>
            t.enabled_mark = 1 and t.task_status=2 and t.is_enabled = 1 and tv.enabled_mark = 1
            <if test="req.taskType != null and req.taskType !=''">
                AND t.task_type = #{req.taskType}
            </if>
            <if test="req.frequency != null and req.frequency !=''">
                AND t.frequency = #{req.frequency}
            </if>
            <if test="req.postId != null and req.postId !=''">
                AND t.post_id like concat('%', #{req.postId}, '%')
            </if>
        </where>
    </select>

    <select id="selectCountUserOctSign" resultType="com.zilue.module.wechat.sign.dto.CountUserOctSignDto">
        select sum(sign_count) signCount, person_id userId
        from otc_sign_in
        where sigin_type = 2
        and delete_mark = 0
        and enabled_mark = 1
        and create_date like concat('%', #{yesterday}, '%')
        and person_id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        group by person_id
    </select>


    <select id="selectUserSignStatistics" resultType="com.zilue.module.wechat.sign.dto.CountUserOctSignDto">
        select sum(sign_count) signCount, person_id userId
        from otc_sign_in
        where sigin_type = 2
        and delete_mark = 0
        and enabled_mark = 1
        <if test="list != null and list.size() !=0">
            and person_id in
            <foreach collection="list" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="taskType != null and taskType!=''">
            and category =#{taskType}
        </if>
        <if test="startTime != null and startTime!=''">
            and create_date <![CDATA[ >=#{startTime}]]>

        </if>
        <if test="endTime != null and endTime!=''">
            and create_date  <![CDATA[ <=#{endTime} ]]>
        </if>
        group by person_id
    </select>

    <select id="selectDailyTaskGoal" resultType="java.lang.Integer">
        select t2.goal
        from otc_salesman_task t1
                 left join otc_salesman_task_visit t2 on t1.id = t2.task_id
        where t1.delete_mark = 0
          and t1.enabled_mark = 1
          and t1.task_type = 1
          and t1.is_enabled = 1
          and t2.delete_mark = 0
          and t2.enabled_mark = 1
        order by t1.create_date desc limit 1
    </select>
</mapper>