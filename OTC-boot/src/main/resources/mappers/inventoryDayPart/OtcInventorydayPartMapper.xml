<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zilue.module.business.inventory.mapper.OtcInventoryDayPartMapper">


    <select id="queryPartCount" resultType="com.zilue.module.business.inventory.vo.OtcInventoryDayPartVo">
        SELECT sum(warehouse_num) as warehouseNumCount, sum(warehouse_money) as warehouseMoneyCount
        from otc_inventory_day_part
        <where>
            <if test="dto.isTen!=null and dto.isTen!=''">
                AND is_ten = #{dto.isTen}
            </if>
            <if test="dto.customerName!=null and dto.customerName!=''">
                AND customer_name like CONCAT( #{dto.customerName ,jdbcType=VARCHAR}, '%')
            </if>
            <if test="dto.parentCustomerName!=null and dto.parentCustomerName!=''">
                AND parent_customer_name like CONCAT( #{dto.parentCustomerName ,jdbcType=VARCHAR}, '%')
            </if>
            <if test="dto.provinceName!=null and dto.provinceName!=''">
                AND customer_provincial_name like CONCAT(#{dto.provinceName , jdbcType=VARCHAR}, '%')
            </if>
            <if test="dto.productGroup!=null and dto.productGroup!=''">
                AND product_group like CONCAT( #{dto.productGroup ,jdbcType=VARCHAR}, '%')
            </if>
            <if test="dto.product!=null and dto.product!=''">
                AND product like CONCAT( #{dto.product ,jdbcType=VARCHAR}, '%')
            </if>
            <if test="dto.businessTime!=null and dto.businessTime!=''">
                AND business_time like CONCAT( #{dto.businessTime ,jdbcType=VARCHAR}, '%')
            </if>
        </where>
    </select>

</mapper>
