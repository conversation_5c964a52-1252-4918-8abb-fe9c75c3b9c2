<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zilue.module.business.inventory.mapper.OtcInventoryDayProductMapper">


    <select id="queryProductCount" resultType="com.zilue.module.business.inventory.vo.OtcInventoryDayPartVo">
        SELECT sum(warehouse_num) as warehouseNumCount, sum(warehouse_money) as warehouseMoneyCount
        from otc_inventory_day_product
        <where>
            <if test="dto.productGroup!=null and dto.productGroup!=''">
                AND product_group like CONCAT( #{dto.productGroup ,jdbcType=VARCHAR}, '%')
            </if>
            <if test="dto.product!=null and dto.product!=''">
                AND product like CONCAT( #{dto.product ,jdbcType=VARCHAR}, '%')
            </if>
            <if test="dto.businessTime!=null and dto.businessTime!=''">
                AND business_time like CONCAT( #{dto.businessTime ,jdbcType=VARCHAR}, '%')
            </if>
        </where>
    </select>

</mapper>
