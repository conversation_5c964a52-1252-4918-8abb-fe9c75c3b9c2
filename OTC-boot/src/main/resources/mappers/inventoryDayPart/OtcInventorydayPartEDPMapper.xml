<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zilue.module.business.inventory.mapper.OtcInventoryDayPartEDPMapper">

    <select id="queryList"
            resultType="com.zilue.module.business.inventory.vo.OtcInventoryDayPartVo">
        SELECT 客户名称             AS customerName,
               new_accountid        AS accountId,
               new_otcaccountid     AS parentAccountId,
               上级客户名称         AS parentCustomerName,
               CASE CAST(是否十大连锁 AS NVARCHAR(36))
                   WHEN '是' THEN 1
                   ELSE 0
                   END              AS isTen,
               分部客户所在省份     AS customerProvincialName,
               品种编码             AS productGroupCode,
               品种                 AS productGroup,
               品规编码             AS productCode,
               品规                 AS product,
               ROUND(考核价, 2)     AS examPrice,
               库存数量             AS warehouseNum,
               ROUND(库存总金额, 2) AS warehouseMoney,
               业务期间             As BusinessTime,
               期初库存             As beginWarehouseNumCount,
               本期购进             As periodComeCount,
               流到商业             As outCommerceCount,
               流到终端             As outTerminalCount,
               计价单位             As unit
        FROM crm_OTC_channelstock
        WHERE 业务期间 = #{syncTime}
        ORDER BY 业务期间 ASC
        OFFSET #{skip} ROWS FETCH NEXT ${offset} ROWS ONLY

    </select>

    <select id="queryCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM crm_OTC_channelstock
        WHERE 业务期间 = #{syncTime}
    </select>

</mapper>
