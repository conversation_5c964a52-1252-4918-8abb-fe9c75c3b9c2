<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zilue.module.business.inventory.mapper.OtcInventoryCustomerMapper">


    <select id="queryCustomerList" resultType="com.zilue.module.business.inventory.vo.OtcInventoryCustomerVo">
        SELECT id,
               account_id as accountId,
               customer_name as customerName,
               parent_account_id as parentAccountId,
               parent_customer_id as parentCustomerId,
               parent_customer_name as parentCustomerName,
               terminal_type as terminalType,
               is_ten as isTen,
               provincial_company_unit_id as provincialCompanyUnitId,
               provincial_company_id as provincialCompanyId,
               provincial_company_name as provincialCompanyName,
               area_company_unit_id as areaCompanyUnitId,
               area_company_id as areaCompanyId,
               area_company_name as areaCompanyName,
               customer_provincial_name as customerProvincialName
        from otc_inventory_customer
    </select>

    <select id="queryCustomerOwnerList" resultType="com.zilue.module.business.inventory.vo.OtcInventoryCustomerVo">
        SELECT oic.account_id as accountId,owner_name as personName from otc_inventory_customer oic left join otc_account_user_relation oaur on oic.account_id=oaur.account_id where oic.terminal_type=0
    </select>

</mapper>
