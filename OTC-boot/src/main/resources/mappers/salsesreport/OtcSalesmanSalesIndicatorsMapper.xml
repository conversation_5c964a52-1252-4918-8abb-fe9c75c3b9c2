<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zilue.module.business.salesindicators.mapper.OtcSalesmanSalesIndicatorsMapper">

    <select id="queryList"
            resultType="com.zilue.module.business.salesindicators.entity.OtcSalesmanSalesIndicators">
        SELECT 数据主键 AS edpId,
           人员姓名 AS userName,
           人员编号 AS userCode,
           所在部门 AS deptCode,
           连锁总部名称 AS headquartersName,
           连锁总部主键长编码 AS headquartersCode,
           连锁分部名称 AS subName,
           连锁分部主键长编码 AS subCode,
           终端名称 AS storeName,
           CASE CAST(终端主键长编码 AS NVARCHAR(36))
             WHEN '00000000-0000-0000-0000-000000000000' THEN ''
             ELSE CAST(终端主键长编码 AS NVARCHAR(36))
           END AS storeCode,
           产品名称 AS productName,
           产品编码 AS productCode,
           ROUND(指标值,2) AS edpAmount,
           业务日期 AS businessDay,
           更新时间 AS syncTime,
           ROUND(数量,0) AS edpSalesNum,
           CAST(OTC考核单价 AS DECIMAL(10, 2)) AS unitPrice,
           产品线 AS productLine
        FROM crm_OTC_BP
        WHERE  更新时间 > #{syncTime}
        ORDER BY edpId
        OFFSET #{skip} ROWS FETCH NEXT ${offset} ROWS ONLY

    </select>
    <select id="queryCount" resultType="java.lang.Integer">
        SELECT COUNT
            ( * )
        FROM
            crm_OTC_BP
        WHERE
            更新时间 > #{ syncTime}
    </select>

</mapper>
