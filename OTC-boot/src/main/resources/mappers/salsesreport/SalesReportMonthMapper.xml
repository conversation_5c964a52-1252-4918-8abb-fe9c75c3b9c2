<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zilue.module.business.salesreport.mapper.OtcSalesReportMonthMapper">

    <select id="querySumGroupByUserCode"
            resultType="com.zilue.module.business.salesreport.entity.OtcSalesReportMonth">
        select
            user_code userCode,
            sum( plan_Amount ) AS planAmount,
            sum( finish_Amount ) AS finishAmount
        from otc_sales_report_month
        where
            delete_mark = 0
            AND user_code IS NOT NULL
            <![CDATA[
                and business_month >= #{startMonth}
                and business_month <= #{endMonth}
            ]]>
        group by user_code

    </select>
</mapper>
