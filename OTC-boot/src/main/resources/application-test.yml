spring:
  autoconfigure:
    #自动化配置 例外处理
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    dynamic:
      primary: master
      datasource:
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: **************************************************************************************************************
          username: zilue
          password: zilue
      druid:
        initial-size: 10
        max-active: 100
        min-idle: 10
        max-wait: 60000
        pool-prepared-statements: true
        max-pool-prepared-statement-per-connection-size: 20
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        #Oracle需要打开注释
        #validation-query: SELECT 1 FROM DUAL
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
#        stat-view-servlet:
#          enabled: true
#          url-pattern: /druid/*
#          #login-username: admin
#          #login-password: admin
#        filter:
#          stat:
#            log-slow-sql: true
#            slow-sql-millis: 1000
#            merge-sql: false
#          wall:
#            config:
#              multi-statement-allow: true
