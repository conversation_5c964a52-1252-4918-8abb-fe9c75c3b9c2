spring:
  autoconfigure:
    #自动化配置 例外处理
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    dynamic:
      primary: master
      strict: true  # 根据数据id找不到数据源报错抛异常
      datasource:
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: **********************************************************************************************************************************
          username: root
          password: KJd#120sn@912
        slave:
          driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
          url: **********************************************************
          username: kanion_zsotc
          password: zsotc123321
      druid:
        proxy-filters:
          - sqlLogInterceptor


  redis:
    database: 7
    host: **************
    port: 6379
    password: 123456aA    # 密码（默认为空）
    timeout: 6000ms  # 连接超时时长（毫秒）
    jedis:
      pool:
        max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1ms      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10      # 连接池中的最大空闲连接
        min-idle: 5       # 连接池中的最小空闲连接

    #rabbitmq:
    #requested-heartbeat: 0  # 关闭心跳检测
    #host: *************
    #port: 5672
    #username: admin
    #password: admin
    #virtual-host: my_vhost # 注意：这里前面不能带/，默认的“/”理解成字符串就行，和Linux的目录斜杠还不是一回事

  data:
    mongodb:
      uri: ***********************************************************


logging:
  level:
    org.camunda: debug #打印camunda 日志  一般用于 查看camunda  执行sql

zilue:
  oss:
    #enabled: true
    cloud-type: minio
    access-key: minioadmin
    secret-key: minioadmin
    endpoint: http://*************:9000
    bucket-name: zilue
    prefix: zilue
  job:
    enabled: true                                         # 执行器通讯TOKEN [必填]：是否启用定时任务功能；
    accessToken:                                          # 执行器通讯TOKEN [选填]：非空时启用；
    admin:
      addresses: http://************:8081/xxl-job-admin   # 调度中心部署跟地址 [选填]：如调度中心集群部署存在多个地址则用逗号分隔。执行器将会使用该地址进行"执行器心跳注册"和"任务结果回调"；为空则关闭自动注册；
    executor:
      address:                                            # 执行器注册 [选填]：优先使用该配置作为注册地址，为空时使用内嵌服务 ”IP:PORT“ 作为注册地址。从而更灵活的支持容器类型执行器动态IP和动态映射端口问题。
      appname: zilue                                   #k 执行器AppName [选填]：执行器心跳注册分组依据；为空则关闭自动注册
      ip:                                                 # 执行器IP [选填]：默认为空表示自动获取IP，多网卡时可手动设置指定IP，该IP不会绑定Host仅作为通讯实用；地址信息用于 "执行器注册" 和 "调度中心请求并触发任务"；
      port: 0                                             # 执行器端口号 [选填]：小于等于0则自动获取；默认端口为9999，单机部署多个执行器时，注意要配置不同执行器端口；
      log-path: /data/logs/xxl-job/job-logs               # 执行器运行日志文件存储磁盘路径 [选填] ：需要对该路径拥有读写权限；为空则使用默认路径；
      log-retention-days: 30                              # 执行器日志文件保存天数 [选填] ： 过期日志自动清理, 限制值大于等于3时生效; 否则, 如-1, 关闭自动清理功能；
  generate:
    webPath: C:\workspace\otc\zilue-vue3 #生成代码的路径
    appPath: C:\workspace\otc\zilue-uni   #前端app
  common:
    appoint-assignee-null: false # 工作流指定审批人为空时，true-超级管理员审批，false-throw抛出异常,默认抛出异常
    error-type: SYSTEM
    druid-account: admin # druid 监控账户
    druid-password: admin # druid 监控密码
    default-password: "000000" #默认密码（用户重置密码后为该密码）
    white-list:
      - 192.168.3.163
    exclude-urls:
      - /favicon.ico  #网站图标
      - /webjars/**   #(knife4j)  接口文档必要资源
      - /swagger-resources/** #swagger(knife4j) 接口文档必要资源
      - /v2/api-docs           #swagger(knife4j)  接口文档必要资源
      - /doc.html              #swagger(knife4j) 接口文档必要资源
      - /druid/**         #druid 监控
      - /ureport/**
      - /system/captcha    # 验证码
      - /system/login      # 登录接口
      - /system/token      # keycloak登录接口
      - /system/logout     # 登出接口
      - /system/thirdLogin      # 三方登录接口
      - /business/account/syncAccount
      - /business/account/syncAccountRelation   #同步终端客户的负责人信息
      - /business/account/syncAccountChainRelation   #同步连锁分部的负责人信息
      - /business/account/syncAccountZongBuRelation   #同步连锁总部的负责人信息
      - /business/account/addAccountRelation   #同步新增员工负责门店
      - /business/account/addSyncAccount
      - /business/account/saveOrUpdateAccount
      - /business/account/saveOrUpdateAccountRelation
      - /business/account/page
      - /organization/user/dealWithUser #同步机构用户
      - /organization/department/dealtree #机构树
      - /organization/department/list
      - /organization/department/page
      - /organization/department/enabled-tree
      - /organization/department/tree
      - /system/developer-login      # 开发者登录接口
      - /system/guest-login  # 游客登录接口
      - /camunda/**        # camunda工作台
      - /language/**        # language
      - /${magic-api.web}/**    # magic-api web
      - /${magic-api.prefix}/** # magic-api 前缀
      - /bi/project/info # 桌面
      - /system/qrcode-login # 扫码登录
      - /oauth/callback/** #回调
      - /system/logoConfig/logo-info #登录之后加载图片的接口
      - /websocket/**
      - /oa/news/box
      - /oa/schedule/box
      - /oa/message/box
      - /oa/message/list
      - /business/salesperformance/*
      - /business/salesindicators/*
      - /business/Product/pageProductGroup
      - /business/Product/pageProduct
      - /business/inventorycustomer/import
      - /business/inventorycustomer/queryCustomerInfoList
      - /business/inventorycustomer/partInventoryPage
      - /business/inventorycustomer/headInventoryPage
      - /business/inventorycustomer/productInventoryPage
      - /business/inventorycustomer/getPartStaticCount
      - /business/inventorycustomer/getHeadStaticCount
      - /business/inventorycustomer/getProductStaticCount
      - /business/inventorycustomer/partInventoryAnalysePage
      - /business/inventorycustomer/headInventoryAnalysePage
      - /business/punchset/updateVisitMatters
      - /business/inventorycustomer/syncPartInventory
      - /business/inventorycustomer/syncInventoryDayHead
      - /business/inventorycustomer/syncInventoryDayProduct
      - /business/account/export
      - /business/inventorycustomer/export
      - /business/sign/modifySignHistoryData
      - /business/taskmanagement/updateSalesmanTaskStatusForJob
      - /wechat/sign/listByDate
      - /business/sign/adminPage
      - /system/file
      - /organization/department/childList
      - /organization/department/deptAllUserInfo
      - /business/reportForms/visitReportForm
      - /business/reportForms/visitReportFormDetails
      - /business/reportForms/selectUserSignStatistics
      - /business/reportForms/saleMoneyReportForm
      - /business/reportForms/selectUserSaleMoneyStatistics
      - /business/taskmanagement/saveOtcSalesmanTask
      - /business/taskDetail/visitList
      - /organization/department/enabled-tree
      - /organization/department/tree
      - /organization/department/allChildList
      - /wechat/sign/getOtcWeekReportTaskDetail
      - /wechat/sign/saveOtcWeekReport
      - /wechat/sign/getPageList
      - /wechat/sign/getOtcWeekReportDetail
      - /wechat/sign/getOtcWeekReportList
      - /business/taskmanagement/page
      - /business/account/import
      - /wechat/sign/delOtcWeekReport
      - /wechat/sign/getWechatHomePage
      - /business/taskmanagement/salesPerformanceDetailPage
      - /business/taskmanagement/UpdateOtcSalesmanTask
      - /wechat/sign/listByDate
      - /business/taskmanagement/exportSalesPerformanceDetail
      - /business/contractTemplateFiled/add
      - /wechat/sign/saveSignIn
    guest-include-urls:
      - /system/questionnaire/execute # 调查问卷接口，对外二维码访问是需要放行
  email:
    host:  #邮件服务器的SMTP地址，可选，默认为smtp.<发件人邮箱后缀>
    port:  # 邮件服务器的SMTP端口，可选，默认25
    auth: true
    from:  # 发件人（必须正确，否则发送失败
    user: # 用户名，默认为发件人邮箱前缀
    pass: nnwdvwmwtzfsbjeg      # 密码（注意，某些邮箱需要为SMTP服务单独设置授权码，详情查看相关帮助
  wechatenterprise:
    appkey: ww00ce9acc4f1913f18
    appSecret: 5bYCMH3ULPHzyrJviIFW2CIdX0OTyQoFqfnel_noyrwo
    agentid: 1000005
    redirectUri: http://www.tzx.test.com:8080/oauth/callback/wechat_enterprise
    frontUrl: http://localhost:3100/#/login
  dingtalk:
    appkey: dingaex2gok1rllumlq3s
    appSecret: Nv2pe-UoR0Z_Iw8d29laZfL3kH6Elmh3ZpRdPwI7SvmKJmaCI29qCrSlFEiMM88MB
    agentid: *********
    redirectUri: http://www.tzx.test.com:8080/oauth/callback/dingtalk
    frontUrl: http://localhost:3100/#/login
  ureport:
    account: zilue #ureport 账号
    password: 123456 #ureport 密码
  keycloak:
    payload: preferred_username #从payload 某个key中取值 登录
  chatgpt:
    proxy-type: 0 # 不开启代理 == -1 http == 0   socket == 1
    proxy: *************
    port: 1081
    api-key: ****************************************************   #your api-key. It can be generated in the link https://beta.openai.com/docs/quickstart/adjust-your-settings
    # some properties as below have default values. Of course, you can change them.
    max-tokens: 4000           # The maximum number of tokens to generate in the completion.The token count of your prompt plus max_tokens cannot exceed the model's context length. Most models have a context length of 2048 tokens (except for the newest models, which support 4096).
  #  model: text-davinci-003   # GPT-3 models can understand and generate natural language. We offer four main models with different levels of power suitable for different tasks. Davinci is the most capable model, and Ada is the fastest.
  #  temperature: 0.0          # What sampling temperature to use. Higher values means the model will take more risks. Try 0.9 for more creative applications, and 0 (argmax sampling) for ones with a well-defined answer.We generally recommend altering this or top_p but not both.
  #  top-p: 1.0                # An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered.We generally recommend altering this or temperature but not both.
  sms:
    limit-time: 24 # 短信限制时间（单位：小时，正整数）
    limit-count: 10 # 短信限制次数，与limit-time一起使用，限制时长内允许发送的次数
    platform: HW_CLOUD #默认使用短信服务商
    captcha-sender: 8823053028920 #验证码通道号
    captcha-template-id: 214316029097017394 # 验证码 模板id
    register-template-id: 214316029097017393 # 账号注册默认密码通知
    notify-sender: 8823053111843 # 提示类提示短信 通道号
    notify-template-id: d424de0f2a92444ac9d2f2685cd158635 #提示类短信 模板id
    circulated-sender: 8823053111843 # 提示类传阅短信 通道号
    circulated-template-id: 5c179070a724467c79568ac22371be803 #提示类传阅短信 模板id
    timeout-sender: 8823053111843 # 提示类短超时信 通道号
    timeout-template-id: 5c179070a72447c79568ac2723781be803 #提示类超时短信 模板id
  license:
    enabled: false                  #是否需要验证
    login-max: 100                  #最大登陆人数
    start-time: 2023-12-14 00:00:00 #开始时间
    end-time: 2023-12-14 17:35:00   #结束时间
    company-name: xxx               #公司名
    contact-number: xxx             #联系人
  bi:
    address: http://*************:6080 # bi前端项目地址
  sql-log:
    enable: true

sms:
  huawei:
    #华为短信appKey
    appKey: 69s6YyLsrLgWW99BJ5KL141K26WiF29
    #华为短信appSecret
    app-secret: RuE1YajkI6FC4Gh2Wj1OVtOpychRGL
    #短信签名
    signature: 资略信息
    #通道号
    sender: 88230530289220
    #模板ID 如果使用自定义模板发送方法可不设定
    template-id: 97c6fcf92f2d0149b39c0ed0e53480116f
    #华为回调地址，如不需要可不设置或为空
    statusCallBack:
    #华为分配的app请求地址
    url: https://smsapi.cn-north-4.myhuaweicloud.com:443



# 验证码配置
#smst:
#  sp: huawei # 短信服务商，huawei(华为),ali(阿里巴巴),tencent(腾讯)
#  url: https://smsapi.cn-north-4.myhuaweicloud.com:443/sms/batchSendSms/v1 # APP接入地址+接口访问URI
#  app_key: 69s6YyLsrLgWW99B24J5K1K26WiF29
#  secret_id: RuE1YajkI6FC4Gh224Wj5VtOpychRGL # API秘钥
#  secret_key: RuE1YajkI6FC4Gh2Wj5VtOpychRGL # 目前只有腾讯用到
#  sender: 88230530328920   # 验证码类发送人手机号通道号
#  message_sender: 88423053111843 # 通知类发送人手机号通道号
#  signature: 资略信息    # 签名名称
#  region: default # 地区名，阿里和腾讯需要配置，默认default
#  templates:
#    - type: login # 手机登录验证 模板ID  keys是短信模板内容中的参数名，只有阿里的短信模板需要key去填充，其他两个则是传入的数组参数顺序填充
#      id: 97c6fcf9fd01449b39c0ed0e53480116f
#      keys: code
#    - type: reset # 忘记密码-验证手机号码 模板ID
#      id: 614a2cd08b7045cf393321e1df054e3df
#      keys: code
#    - type: message # 消息管理发送短信通道码
#      id: d424de0f2a924456ac9d2f2685cd158635
#      keys: code

# opc ua sdk 自动配置类  必须配置false
kangaroohy:
  milo:
    enabled: false

# RSA加密配置
rsa:
  key:
    publicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjqwvCEnkCqiy0gPPyHUu0yS8xqdSI95IPL+1vUCUmVdyqsYaPZCZa6WHMZRcDi/Vm/wiiQNen1aOl8dNejRLrRjQYaZin+v06ljV00QiQY5o2KRZB4xmJMrkeum9LKNL8aB8vycAPoqSD5k70rzSF3QMRGxMzyRGls85VIHQQWHLReR+NKhQUNBAq6UBDNZkdbRUgSgPImUDXw2uL5D++sdKPMGVjyXyrS6T0iYndc90l61PuKZ5SbQJnMdLtbECBiYJiA3ed0xkXaVlpNuPfoMpHrN8A6Qpjz3nLUPiZQmnqwqKTca95ej9Ks5A90qs41nbbh1RneLEFGdGmylEawIDAQAB
    privateKey: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCOrC8ISeQKqLLSA8/IdS7TJLzGp1Ij3kg8v7W9QJSZV3Kqxho9kJlrpYcxlFwOL9Wb/CKJA16fVo6Xx016NEutGNBhpmKf6/TqWNXTRCJBjmjYpFkHjGYkyuR66b0so0vxoHy/JwA+ipIPmTvSvNIXdAxEbEzPJEaWzzlUgdBBYctF5H40qFBQ0ECrpQEM1mR1tFSBKA8iZQNfDa4vkP76x0o8wZWPJfKtLpPSJid1z3SXrU+4pnlJtAmcx0u1sQIGJgmIDd53TGRdpWWk249+gykes3wDpCmPPectQ+JlCaerCopNxr3l6P0qzkD3SqzjWdtuHVGd4sQUZ0abKURrAgMBAAECggEBAIUoMqU7wXgpRuw33n+qzs8FpKKLm2Aan7UDAC3nzkjnkv/L8MWw5V/Ql8MwQlKDVoxE4D6cRH3WHTOPMcJyeIbPd/0Le1TitizaSIbrOkWREk3P5b/jy5lfO6tz57l3lvOm44bMca/tSLECRRFEZZi6GJDruyxBIRizw0AaADJkGd5dLQxyU+fP62ZlmjO74FFAFqIAiJo5c/NYqcjweE7KrswNMLuSTbM0chCi801COWznn2Esz9taNsYT1VjekXqL/QXUHFpvb3CspZVUfBc+UlDlArdYdzak4u8jdKbasGDtrJnMBIPzDdvUJBmIXWpRzccMNg/AOBis0p0YyoECgYEA2RVphlhX169XLufjokOLf6ocpXA/C2G1ombRUNMQbJJ+83XPezOyeLEe5CH8/0Zjo/Mh1g307M2bfbP7Ph584dGa99l8cgcQcmoAvUBL2tJzpReFnuL+4Kv+AfO2k/7u4rHJeB08j0ytg+ZM3A68lA4YiKTX4r5wow8GlvNtCuECgYEAqD/WDUY7H+Jnp0FJNxqQIJUMLcoFxhMPf5YSCbK+fiuQ5clujgDNAx1ozaz5xVoYxnFLlK8WXFgpC2j7nhtxlxKG744zzAVO5ZZ6M2EhDEn61WtWQyGFBZdeffBoUTF/acKX8ZrWE9iXd9UgGi0vJykmM7gYPDjcLXTA/l1qJMsCgYBj00XgYpWaeGYWCsqR3o+ymE5uAWzsVfVJhhBxI23lD4TTffR4SjCi8YgsDjkiRoMhbHe90FM15eK/7lj1mt2Alnu1x8xDP15fzsEiRgPbz3UgkO2AQBBGzrCSwzjNW6kMlAb6di9YEkGkau4jL5+mUckUZQFlon93bDxyg+Q74QKBgDtUN2D68QJvTenWxcCep1A1/tNut9Z/BioRe07cLvxC1JeTDl8YvTJp/nj4lCMZgD8J2IMXsqThmaeiX4gDWWzSZa630GATRx0CkRjpGURCGieD8Ck/+q5cEEAMtFxdPfbHXdkepnrdF5x0FRvl7wi6I/R86F7kFhAZaazIuBM9AoGBAIQPpjwoYJulOsQl5O+3Zm4Ec2ZJKkfFAlTcScNi7wvdV8o0FtP90+JzJQEIqfHcSStd65hn8D+N5/w3ct7CMIvzQtJvdX4ysPJBU+tDtdQvgfUUHrqpESI7N7Ug74cdjhbADhfVcP4SFAIBXdBOYzEwuAzc9FRTM0x5sjmzBkSo

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl #开启sql日志
baidu:
  discovery:
    queryRouting: https://api.map.baidu.com/direction/v2/driving?
    ak: WOs10S4oZpQXe6hdfbzswsG0RT3A9Ozu

domain:
  name: https://test-ksotc.guoyaoplat.com

# 合同系统
kyContract:
  url: https://htgldev.kanion.com/
  clientId: kanion-admin
  clientSecret: p01vOhbX6qK2aE3KJfsiZXfZHOo4lDnaY8Cm0ZRj