package com.zilue.boot;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;

import java.io.IOException;
import java.util.Collections;
import java.util.List;

class CodeGenerator {


    public static void main(String[] args) throws IOException {

        final String dbUrl = "*****************************************************************************************************************************************************************************************************************"; //数据库连接地址
        final String dbUserName = "root";//数据库账号
        final String dbPassword = "zilue"; //数据库密码
        final String authorName = "tzx";// 设置作者
        final String outputDir = getOutputDir(); // 指定输出目录
        final String parentModuleName = "com.zilue.module";// 设置父包名
        final String moduelName = "im";// 设置父包模块名
        final List<String> tableNames = ListUtil.toList("xjr_im_unread"); //所需要生成 的表名称 可以多个
        final List<String> prefixs = ListUtil.toList("xjr_"); //所需要忽略的前缀 可以多个

        //拼接设置mapperXml生成路径
        StringBuilder mapperPath = new StringBuilder(outputDir);
        for (String s : parentModuleName.split("\\.")) {
            mapperPath.append(StringPool.SLASH).append(s);
        }
        mapperPath.append(StringPool.SLASH + moduelName + StringPool.SLASH + "mapper");


        FastAutoGenerator.create(dbUrl, dbUserName, dbPassword)
                .globalConfig(builder -> {
                    builder.author(authorName)// 设置作者
                            .enableSwagger() // 开启 swagger 模式
                            .outputDir(outputDir);// 指定输出目录
                })
                .packageConfig(builder -> {
                    builder.parent(parentModuleName)// 设置父包名
                            .moduleName(moduelName)// 设置父包模块名
                            .pathInfo(Collections.singletonMap(OutputFile.mapper, mapperPath.toString())); // 设置mapperXml生成路径
                })
                .strategyConfig(builder -> {
                    builder.addInclude(tableNames) // 设置需要生成的表名
                            .addTablePrefix(prefixs); // 设置过滤表前缀
                })
//                .templateEngine(new FreemarkerTemplateEngine()) // 使用Freemarker引擎模板，默认的是Velocity引擎模板
                .execute();
    }

    /**
     * 获取生成目录 默认生成到src/main/java/com/zilue/module
     */
    private static String getOutputDir() {
        return System.getProperty("user.dir") + StringPool.SLASH + "src" + StringPool.SLASH + "main" + StringPool.SLASH + "java";
    }


}
