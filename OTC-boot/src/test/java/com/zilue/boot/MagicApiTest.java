package com.zilue.boot;


import com.zilue.ZiLueApplication;
import com.zilue.common.utils.TreeUtil;
import com.zilue.module.magicapi.vo.MagicApiTreeVo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.ssssssss.magicapi.core.model.Group;
import org.ssssssss.magicapi.core.model.MagicEntity;
import org.ssssssss.magicapi.core.model.TreeNode;
import org.ssssssss.magicapi.core.service.MagicResourceService;

import java.util.ArrayList;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ZiLueApplication.class)
public class MagicApiTest {

    @Autowired
    private MagicResourceService magicResourceService;


    @Test
    public void testMagicApiList(){


        TreeNode<Group> tree = magicResourceService.tree("api");

        List<TreeNode<Group>> children = tree.getChildren();

        List<MagicApiTreeVo> treelist = buildTree(children);
        List<MagicApiTreeVo> treeVoList = TreeUtil.build(treelist);

        System.out.println(treeVoList);
    }

    private List<MagicApiTreeVo> buildTree(List<TreeNode<Group>> treeNodes){

        List<MagicApiTreeVo> treeVoList = new ArrayList<>();
        for (TreeNode<Group> treeNode : treeNodes) {
            Group node = treeNode.getNode();

            MagicApiTreeVo groupVo = new MagicApiTreeVo();

            groupVo.setId(node.getId());
            groupVo.setParentId(node.getParentId());
            groupVo.setType("group");
            groupVo.setName(node.getName());

            treeVoList.add(groupVo);

            List<MagicEntity> nodeEntity = magicResourceService.listFiles(node.getId());

            for (MagicEntity magicEntity : nodeEntity) {
                MagicApiTreeVo vo = new MagicApiTreeVo();
                vo.setId(magicEntity.getId());
                vo.setType("api");
                vo.setParentId(magicEntity.getGroupId());
                vo.setName(magicEntity.getName());


                treeVoList.add(vo);
            }


            if(treeNode.getChildren().size() > 0){
                treeVoList.addAll(buildTree(treeNode.getChildren()));
            }

        }

        return treeVoList;
    }
}
