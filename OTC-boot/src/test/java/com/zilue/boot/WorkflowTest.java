//package com.xjrsoft.xjrsoftboot;
//
//import cn.hutool.extra.spring.SpringUtil;
//import cn.hutool.json.JSONUtil;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.baomidou.mybatisplus.core.toolkit.StringPool;
//import com.baomidou.mybatisplus.core.toolkit.Wrappers;
//import com.zilue.XjrSoftApplication;
//import com.zilue.common.enums.EnabledMark;
//import com.zilue.common.exception.MyException;
//import com.zilue.module.form.service.IFormExecuteService;
//import com.zilue.module.system.entity.File;
//import com.zilue.module.system.service.IFileService;
//import com.zilue.module.workflow.constant.WorkflowConstant;
//import com.zilue.module.workflow.dto.LaunchDto;
//import com.zilue.module.workflow.entity.WorkflowSchema;
//import com.zilue.module.workflow.model.CallActivityConfig;
//import com.zilue.module.workflow.model.CallActivityParamConfig;
//import com.zilue.module.workflow.service.IWorkflowExecuteService;
//import com.zilue.module.workflow.service.IWorkflowSchemaService;
//import com.zilue.module.workflow.utils.WorkFlowUtil;
//import com.zilue.module.workflow.vo.LaunchAndApproveVo;
//import org.camunda.bpm.cockpit.db.CommandExecutor;
//import org.camunda.bpm.engine.*;
//import org.camunda.bpm.engine.history.HistoricActivityInstance;
//import org.camunda.bpm.engine.history.HistoricProcessInstance;
//import org.camunda.bpm.engine.history.HistoricTaskInstance;
//import org.camunda.bpm.engine.impl.context.Context;
//import org.camunda.bpm.engine.impl.interceptor.CommandContext;
//import org.camunda.bpm.engine.impl.persistence.entity.ExecutionEntity;
//import org.camunda.bpm.engine.impl.persistence.entity.ExecutionManager;
//import org.camunda.bpm.engine.impl.persistence.entity.TaskEntity;
//import org.camunda.bpm.engine.impl.persistence.entity.TaskManager;
//import org.camunda.bpm.engine.repository.Deployment;
//import org.camunda.bpm.engine.repository.ProcessDefinition;
//import org.camunda.bpm.engine.runtime.ActivityInstance;
//import org.camunda.bpm.engine.runtime.Execution;
//import org.camunda.bpm.engine.runtime.ProcessInstance;
//import org.camunda.bpm.engine.runtime.VariableInstance;
//import org.camunda.bpm.engine.task.Comment;
//import org.camunda.bpm.engine.task.Task;
//import org.camunda.bpm.engine.task.TaskQuery;
//import org.camunda.bpm.engine.variable.VariableMap;
//import org.camunda.bpm.engine.variable.Variables;
//import org.camunda.bpm.model.bpmn.BpmnModelInstance;
//import org.camunda.bpm.model.bpmn.impl.instance.FlowNodeImpl;
//import org.camunda.bpm.model.bpmn.impl.instance.UserTaskImpl;
//import org.camunda.bpm.model.bpmn.instance.FlowNode;
//import org.camunda.bpm.model.bpmn.instance.SequenceFlow;
//import org.camunda.bpm.model.bpmn.instance.StartEvent;
//import org.camunda.bpm.model.xml.instance.DomElement;
//import org.camunda.bpm.model.xml.instance.ModelElementInstance;
//import org.camunda.bpm.model.xml.type.ModelElementType;
//import org.junit.jupiter.api.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.util.*;
//
///**
// * 工作流测试
// *
// * @Author: zilue
// * @Date: 2023/10/20 10:25
// */
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = XjrSoftApplication.class)
//class WorkflowTest {
//
//    @Autowired
//    private RepositoryService repositoryService;
//
//    @Autowired
//    private RuntimeService runtimeService;
//
//    @Autowired
//    private TaskService taskService;
//
//    @Autowired
//    private IFormExecuteService formExecuteService;
//
//    @Autowired
//    private HistoryService historyService;
//
//    @Autowired
//    private ManagementService managementService;
//
//    @Autowired
//    private IWorkflowSchemaService workflowSchemaService;
//
//    @Autowired
//    private IWorkflowExecuteService workflowExecuteService;
//
//    @Autowired
//    private IFileService fileService;
//
//
//    @Test
//    public void testLaunch(){
////        WorkflowSchema schema = workflowSchemaService.getById("1599684995851616258");
//        Map<String,Map<String,Object>> formDataMaps = new HashMap<>();
//        Map<String,Object> formData = new HashMap<>();
//        formData.put("name","xx");
//
//        LaunchDto dto = new LaunchDto();
//        dto.setSchemaId(1599684995851616258L);
//        dto.setFormData(formDataMaps);
//
//
//        formDataMaps.put("1590180962175619074",formData);
//
//        workflowExecuteService.newLaunch(dto);
//
//    }
//
//    @Test
//    public void testListeners() {
//        // 1 发布流程
//        Deployment deploy = repositoryService.createDeployment().name("测试多实例")
//                .addClasspathResource("test_multi.bpmn")
//                .deploy();
//
//        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().deploymentId(deploy.getId()).singleResult();
//
//        List<Long> userIds = new ArrayList<>();
//
//        VariableMap variableMap = Variables.createVariables().putValue(WorkflowConstant.TASK_MULTI_ASSIGNEE_VAR_KEY, userIds);
//
//        Collection<StartEvent> modelElementsByType = repositoryService.getBpmnModelInstance(processDefinition.getId()).getModelElementsByType(StartEvent.class);
//
//        for (StartEvent startEvent : modelElementsByType) {
//
//            //获取到子节点
//            List<FlowNode> list = startEvent.getSucceedingNodes().list();
//
//
//        }
//
//
////        ProcessInstance processInstance = runtimeService.startProcessInstanceById(processDefinition.getId(), variableMap);
////
////
////        List<Task> list = taskService.createTaskQuery().processVariableValueLike(WorkflowConstant.TASK_MULTI_ASSIGNEE_VAR_KEY, "%1%").list();
////
////        System.out.println(list);
//
//    }
//
//    /**
//     * 测试获取下一个节点
//     */
//    @Test
//    public void testGetNextNode() {
//        // 1 发布流程
//        Deployment deploy = repositoryService.createDeployment().name("测试多实例")
//                .addClasspathResource("testGetNextNode.bpmn")
//                .deploy();
//
//        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().deploymentId(deploy.getId()).singleResult();
//
//        VariableMap variableMap = Variables.createVariables().putValue("age", 22);
//
//        ProcessInstance processInstance = runtimeService.startProcessInstanceById(processDefinition.getId(), variableMap);
//
//        //找到当前节点 element 对象
//        Collection<StartEvent> modelElementsByType = repositoryService.getBpmnModelInstance(processDefinition.getId()).getModelElementsByType(StartEvent.class);
//        List<StartEvent> startEventList = new ArrayList<>(modelElementsByType);
//        //获取到子节点
//        List<FlowNode> childNode = startEventList.get(0).getSucceedingNodes().list();
//
//        String nextNodeId = WorkFlowUtil.getNextTaskNode(variableMap, childNode);
//
//        System.out.println(nextNodeId);
//    }
//
//    @Test
//    public void testMultiTask() {
//        // 1 发布流程
//        Deployment deploy = repositoryService.createDeployment().name("测试多实例串行")
//                .addClasspathResource("testMultiTask.bpmn")
//                .deploy();
//
//
//        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().deploymentId(deploy.getId()).singleResult();
//
//        List<String> approveList = new LinkedList<>();
//        approveList.add("5555");
//        approveList.add("6666");
//        approveList.add("7777");
//        approveList.add("8888");
//
//        VariableMap variableMap = Variables.createVariables().putValue("approveList", approveList);
//
//        Task task = taskService.createTaskQuery().singleResult();
//
//
////        //找到当前节点 element 对象
////        Collection<StartEvent> modelElementsByType = repositoryService.getBpmnModelInstance(processDefinition.getId()).getModelElementsByType(StartEvent.class);
////        List<StartEvent> startEventList = new ArrayList<>(modelElementsByType);
////        //获取到子节点
////        List<FlowNode> childNode = startEventList.get(0).getSucceedingNodes().list();
////
////        String nextNodeId = WorkFlowUtil.getNextTaskNode(variableMap,childNode);
//
//
//        ProcessInstance processInstance = runtimeService.startProcessInstanceById(processDefinition.getId(), variableMap);
//
//
//    }
//
//    @Test
//    public void testFormWorkflowAdd() {
//
//        Map<String, Object> map = new HashMap<>();
//        map.put("111", "111");
//        map.put("222", "111");
//        map.put("333", "111");
//
//        VariableMap variableMap = Variables.createVariables().putValue("xx", map);
//
//        testMap(map);
//        System.out.println(map);
//        System.out.println(variableMap);
//
//
//    }
//
//    private void testMap(Map<String, Object> map) {
//        map.put("444", "444");
//    }
//
//
//    @Test
//    public void testMytask() {
//        // 1 发布流程
//        Deployment deploy = repositoryService.createDeployment().name("测试我的流程，是否发起流程后 能在history表中找到 当前流程实例数据")
//                .addClasspathResource("testGetNextNode.bpmn")
//                .deploy();
//
//
//        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().deploymentId(deploy.getId()).singleResult();
//
//        VariableMap variableMap = Variables.createVariables().putValue("age", 22);
//
//        ProcessInstance processInstance = runtimeService.startProcessInstanceById(processDefinition.getId(), variableMap);
//
//    }
//
//    @Test
//    public void testMytaskSetVar() {
//        runtimeService.setVariable("a975a580-58f3-11ed-a972-a2510bb8dd14", "guoqi", "1");
//
//        HistoricTaskInstance guoqi = historyService.createHistoricTaskInstanceQuery()
//                .processVariableValueEquals("guoqi", "1").singleResult();
//
//        System.out.println(guoqi.getId());
//    }
//
//    @Test
//    public void testActivityId() {
//        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId("a975a580-58f3-11ed-a972-a2510bb8dd14").singleResult();
//        System.out.println(historicProcessInstance.getStartActivityId());
//
//    }
//
//    @Test
//    public void testVariablesSaveMap() {
//
//        // 1 发布流程
//        Deployment deploy = repositoryService.createDeployment().name("测试变量序列化222222")
//                .addClasspathResource("test_multi.bpmn")
//                .deploy();
//
//
//        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().deploymentId(deploy.getId()).singleResult();
//
//
//        Map<String, Object> map = new HashMap<>();
//        map.put("111", "111");
//        map.put("222", "111");
//        map.put("333", "111");
//
//        VariableMap variableMap = Variables.createVariables().putValue("test", map);
//
//        variableMap.putValue("assigneeList", Arrays.asList("111", "222"));
//
//        ProcessInstance processInstance = runtimeService.startProcessInstanceById(processDefinition.getId(), variableMap);
//
//        runtimeService.setVariables(processInstance.getId(), variableMap);
//    }
//
//
//    @Test
//    public void testScriptTask() {
//
//        // 1 发布流程
//        Deployment deploy = repositoryService.createDeployment().name("测试脚本任务")
//                .addClasspathResource("testScriptTask.bpmn")
//                .deploy();
//
//
//        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().deploymentId(deploy.getId()).singleResult();
//
//
//        System.out.println("开始流程");
//        ProcessInstance processInstance = runtimeService.startProcessInstanceById(processDefinition.getId());
//        System.out.println("存入缓存");
//
//        Task task = taskService.createTaskQuery().processInstanceId(processInstance.getId()).singleResult();
//
//        taskService.complete(task.getId());
//        taskService.createComment(task.getId(), processInstance.getId(), "测试审批意见！！！！");
//
//        List<Comment> taskComments = taskService.getTaskComments(task.getId());
//
//        System.out.println(taskComments);
//    }
//
//
//    @Test
//    public void testSubProcess() {
//
//        // 1 发布流程
//        Deployment deploy = repositoryService.createDeployment().name("测试子流程")
//                .addClasspathResource("test_sub_process.bpmn")
//                .deploy();
//
//
//        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().deploymentId(deploy.getId()).singleResult();
//        ProcessInstance processInstance = runtimeService.startProcessInstanceById(processDefinition.getId());
//
//
//        List<String> userIds = new ArrayList<>();
//        userIds.add("AAAAAAAA");
//        userIds.add("BBBBBBBB");
//        userIds.add("CCCCCCCC");
//
//        runtimeService.setVariable(processInstance.getId(), "assigneeList_Activity_0tutirb", userIds);
//
////        runtimeService.setVariable(processInstance.getId(), "assignee", "xxxxxx");
//
//
//        Task task = taskService.createTaskQuery().processInstanceId(processInstance.getId()).singleResult();
//
//        taskService.complete(task.getId());
//
//        List<ProcessInstance> list = runtimeService.createProcessInstanceQuery().superProcessInstanceId(processInstance.getId()).list();
//
//        for (ProcessInstance instance : list) {
//            Task task1 = taskService.createTaskQuery().processInstanceId(instance.getId()).singleResult();
//            taskService.complete(task1.getId());
//        }
//
//
////        Task AAAAAAAA = taskService.createTaskQuery().processVariableValueEquals("startUserIdKey", "AAAAAAAA").singleResult();
////        taskService.complete(AAAAAAAA.getId());
////        Task BBBBBBBB = taskService.createTaskQuery().processVariableValueEquals("startUserIdKey", "BBBBBBBB").singleResult();
////        taskService.complete(BBBBBBBB.getId());
////        Task CCCCCCCC = taskService.createTaskQuery().processVariableValueEquals("startUserIdKey", "CCCCCCCC").singleResult();
////        taskService.complete(CCCCCCCC.getId());
//
//
//        System.out.println(task.getName());
//
//    }
//
//    @Test
//    public void getProcessBySuperProcess() {
//
//        TaskQuery assignee = taskService.createTaskQuery().taskVariableValueLike("assignee", "%AAA%");
//
//
//    }
//
//    @Test
//    public void testSubProcessSingle() {
//
//        // 1 发布流程
//        Deployment deploy = repositoryService.createDeployment().name("测试外部流程")
//                .addClasspathResource("test_sub_process_single.bpmn")
//                .deploy();
//
//
//        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().deploymentId(deploy.getId()).singleResult();
//        ProcessInstance processInstance = runtimeService.startProcessInstanceById(processDefinition.getId());
//
////        runtimeService.setVariable(processInstance.getId(), "startUserIdKey", "xxxxxxxxxxxx");
//
//        Task task = taskService.createTaskQuery().processInstanceId(processInstance.getId()).singleResult();
//
//        taskService.complete(task.getId());
//
//        ProcessInstance subProcessInstance = runtimeService.createProcessInstanceQuery().superProcessInstanceId(processInstance.getId()).singleResult();
//        Task subProcessTask = taskService.createTaskQuery().processInstanceId(subProcessInstance.getId()).singleResult();
//
//        taskService.complete(subProcessTask.getId());
//
//        Task callTask = taskService.createTaskQuery().processInstanceId(processInstance.getId()).singleResult();
//
//        taskService.complete(callTask.getId());
////        System.out.println(task.getName());
//
//    }
//
//
//    /**
//     * 测试 外部流程  在 监听器设置 审批人变量
//     */
//    @Test
//    public void testCallActivitySetVar() {
//        // 1 发布流程
//        Deployment deploy = repositoryService.createDeployment().name("测试子流程")
//                .addClasspathResource("test_sub_process.bpmn")
//                .deploy();
//
//
//        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().deploymentId(deploy.getId()).singleResult();
//        ProcessInstance processInstance = runtimeService.startProcessInstanceById(processDefinition.getId());
//
//
//        //默认通过主流程第一个用户任务
//        Task task = taskService.createTaskQuery().processInstanceId(processInstance.getId()).singleResult();
//        taskService.complete(task.getId());
//
//
//        List<ProcessInstance> list = runtimeService.createProcessInstanceQuery().superProcessInstanceId(processInstance.getId()).list();
//
//        for (ProcessInstance instance : list) {
//            Task task1 = taskService.createTaskQuery().processInstanceId(instance.getId()).singleResult();
//            taskService.complete(task1.getId());
//        }
//
//
//        System.out.println(task.getName());
//    }
//
//    @Test
//    public void testFlow() {
//        // 1 发布流程
//        Deployment deploy = repositoryService.createDeployment().name("测试流程线")
//                .addClasspathResource("test_flow.bpmn")
//                .deploy();
//
//
//        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().deploymentId(deploy.getId()).singleResult();
//        Map<String, Object> map = new HashMap<>();
//        map.put("aaaa", 1);
//        map.put("bbbb", 0);
//
//        List<String> approve = new ArrayList<>();
//        approve.add("user1");
//        approve.add("user2");
//
//        VariableMap variableMap = Variables.createVariables()
//                .putValue("age", 11)
//                .putValue("approve", approve)
//                .putValue("map", map);
//
//        ProcessInstance processInstance = runtimeService.startProcessInstanceById(processDefinition.getId(), variableMap);
//
//
//    }
//
//    /**
//     * 测试加签
//     */
//    @Test
//    public void testAddSign() {
//        String processInstanceId = "f013e10f-7120-11ed-8abf-a2510bb8dd14";
//        String activityId = "Activity_1f28bxr";
//
//        runtimeService.createProcessInstanceModification(processInstanceId)
//                .startBeforeActivity(activityId)
//                .setVariable("assignee", "user3")
//                .execute();
//    }
//
//    /**
//     * 测试减签
//     */
//    @Test
//    public void testReduceSign() {
//        String processInstanceId = "1d36f418-71ea-11ed-97b9-a2510bb8dd14";
//        String activityId = "Activity_1f28bxr";
//
//
//        runtimeService.createProcessInstanceModification(processInstanceId)
//                .cancelActivityInstance("Activity_1f28bxr:1d4217cb-71ea-11ed-97b9-a2510bb8dd14")
//                .execute();
//    }
//
//
//    @Test
//    public void callActivity(){
//        CallActivityConfig config = new CallActivityConfig();
//        config.setSchemaId("");
//        config.setFinishType(0);
//        config.setCallActivityType(0);
//        config.setPercentOf(0);
//
//        CallActivityParamConfig paramConfig = new CallActivityParamConfig();
//        List<CallActivityParamConfig> dd = new ArrayList<>();
//        dd.add(paramConfig);
//
//        config.setInParams(dd);
//        config.setOutParams(dd);
//
//        String s = JSONUtil.toJsonStr(config);
//
//    }
//
//
//    @Test
//    public void testNewListener(){
////        // 1 发布流程
////        Deployment deploy = repositoryService.createDeployment().name("测试流程线")
////                .addClasspathResource("test_flow.bpmn")
////                .deploy();
//
//
//        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().deploymentId("bb582ffb-760b-11ed-995b-a2510bb8dd14").singleResult();
//        Map<String, Object> map = new HashMap<>();
//        map.put("aaaa", 1);
//        map.put("bbbb", 0);
//
//
//        VariableMap variableMap = Variables.createVariables()
//                .putValue("age", 11)
//                .putValue("map", map);
//
//        ProcessInstance processInstance = runtimeService.startProcessInstanceById(processDefinition.getId(), variableMap);
//
//    }
//
//    @Test
//    public void testVarQuery(){
//        List<VariableInstance> list = runtimeService.createVariableInstanceQuery()
//                .processInstanceIdIn("91d9cda6-75f9-11ed-ae30-a2510bb8dd14")
//                .variableNameLike(WorkflowConstant.PROCESS_FORMDATA_PREFIX_KEY + StringPool.PERCENT)
//                .list();
//
//        for (VariableInstance variableInstance : list) {
//            Object value = variableInstance.getValue();
//            System.out.println(value);
//        }
//
//        long xxx = historyService.createHistoricProcessInstanceQuery().variableValueEquals(WorkflowConstant.PROCESS_SCHEMA_ID_KEY, "xxx").count();
//    }
//
//    @Test
//    public void complete(){
//
//        List<HistoricActivityInstance> list = historyService.createHistoricActivityInstanceQuery().processInstanceId("4e7acde6-9fae-11ed-b918-a2510bb8dd14").list();
//
//        BpmnModelInstance bpmnModelInstance = repositoryService.getBpmnModelInstance(list.get(0).getProcessDefinitionId());
//
//        List<DomElement> childElements = bpmnModelInstance.getDocument().getRootElement().getChildElements();
//
//        Collection<FlowNode> modelElementsByType = bpmnModelInstance.getModelElementsByType(FlowNode.class);
//
//        for (HistoricActivityInstance historicActivityInstance : list) {
//            ModelElementInstance modelElementById = bpmnModelInstance.getModelElementById(historicActivityInstance.getActivityId());
//            Collection<SequenceFlow> incoming = ((FlowNodeImpl) modelElementById).getIncoming();
//            for (SequenceFlow sequenceFlow : incoming) {
//                String id = sequenceFlow.getId();
//            }
//            ModelElementType elementType = modelElementById.getElementType();
//        }
//
////        taskService.complete("e7ac22d7-9fad-11ed-ac89-a2510bb8dd14");
////        taskService.complete("e7b4121d-9fad-11ed-ac89-a2510bb8dd14");
//
//    }
//
//    @Test
//    public void testChuanyue(){
//        // 1 发布流程
//        Deployment deploy = repositoryService.createDeployment().name("测试会签")
//                .addClasspathResource("testHQ.bpmn")
//                .deploy();
//
//
//        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().deploymentId(deploy.getId()).singleResult();
//
//        List<String> users = new ArrayList<>();
//        users.add("111");
//        users.add("222");
//
//        VariableMap variableMap = Variables.createVariables().putValue("users", users);
//
//        ProcessInstance processInstance = runtimeService.startProcessInstanceById(processDefinition.getId(), variableMap);
//
//
//    }
//
//    @Test
//    public void changeVar(){
//
//        taskService.complete("153e1f70-ab66-11ed-873c-a2510bb8dd14");
//        taskService.complete("153e94a2-ab66-11ed-873c-a2510bb8dd14");
//
//    }
//
//
//    @Test
//    public void getNextNode(){
//
//        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().deploymentId("b82cb646-a1f9-11ed-9638-a2510bb8dd14").singleResult();
//
//        //找到当前节点 element 对象
//        BpmnModelInstance bpmnModelInstance = repositoryService.getBpmnModelInstance(processDefinition.getId());
//
//        List<DomElement> childElements = bpmnModelInstance.getDocument().getRootElement().getChildElements();
//
//        Collection<FlowNode> modelElementsByType = bpmnModelInstance.getModelElementsByType(FlowNode.class);
////
////        WorkFlowUtil.getNextTaskNode()
//
//    }
//
//
//    @Test
//    public void testProcessVar(){
//        // 1 发布流程
//        Deployment deploy = repositoryService.createDeployment().name("测试传阅")
//                .addClasspathResource("test_chuanyue.bpmn")
//                .deploy();
//
//
//        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().deploymentId(deploy.getId()).singleResult();
//
//        VariableMap variableMap = Variables.createVariables().putValue("global", "global");
//
//        ProcessInstance processInstance = runtimeService.startProcessInstanceById(processDefinition.getId(), variableMap);
//
//        Task task = taskService.createTaskQuery().processInstanceId(processInstance.getId()).singleResult();
//
//        VariableMap taskVarMap = Variables.createVariables().putValue("local", "local");
//        taskService.setVariablesLocal(task.getId(),taskVarMap);
//
//    }
//
//    @Test
//    public void testFileUpdate(){
//
//        List<Long> fileFolderIds = new ArrayList<>();
//        fileFolderIds.add(1602138204681555970L);
//        fileFolderIds.add(686901469202485248L);
//
//
//        File file = new File();
//        file.setProcessId("xxxxxxx");
//        LambdaQueryWrapper<File> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        lambdaQueryWrapper.in(File::getFolderId, fileFolderIds);
//        fileService.update(file, lambdaQueryWrapper);
//    }
//
//    @Test
//    public void testScript(){
//        // 1 发布流程
//        Deployment deploy = repositoryService.createDeployment().name("测试脚本")
//                .addClasspathResource("testScript.bpmn")
//                .deploy();
//
//
//        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().deploymentId(deploy.getId()).singleResult();
////
//        VariableMap variableMap = Variables.createVariables().putValue("global", "global");
//
//        ProcessInstance processInstance = runtimeService.startProcessInstanceById(processDefinition.getId(), variableMap);
//    }
//
//    @Test
//    public void testExp(){
//        // 1 发布流程
//        Deployment deploy = repositoryService.createDeployment().name("测试表达式")
//                .addClasspathResource("testExp.bpmn")
//                .deploy();
//
//        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().deploymentId(deploy.getId()).singleResult();
//
//        Map<String,Object> formData = new HashMap<>();
//        formData.put("aaa",1);
////
//        VariableMap variableMap = Variables.createVariables().putValue("formData", formData);
//
//        ProcessInstance processInstance = runtimeService.startProcessInstanceById(processDefinition.getId(), variableMap);
//
//    }
//
//
//    @Test
//    public void testProcessParam() {
//        // 1 发布流程
//        Deployment deploy = repositoryService.createDeployment().name("测试流程数据")
//                .addClasspathResource("processParam.bpmn")
//                .deploy();
//
//
//        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().deploymentId(deploy.getId()).singleResult();
//
//        Map<String, Object> user = new HashMap<>();
//        user.put("age", "5");
//
//        VariableMap variableMap = Variables.createVariables().putValue("processParam", user);
//
//        ProcessInstance processInstance = runtimeService.startProcessInstanceById(processDefinition.getId(), variableMap);
//
//        Task task = taskService.createTaskQuery().processInstanceId(processInstance.getId()).singleResult();
//
//        taskService.complete(task.getId());
//
//        Task ssss = taskService.createTaskQuery().processInstanceId(processInstance.getId()).singleResult();
//
//
//    }
//    @Test
//    public void app(){
//        taskService.complete("ac751c83-ffab-11ed-a4b3-a2510bb8dd14");
//
//        Task task = taskService.createTaskQuery().processInstanceId("a8eb808b-ffab-11ed-a4b3-a2510bb8dd14").singleResult();
//
//    }
//}
