ALTER TABLE otc_inventory_day_part ADD COLUMN begin_warehouse_num_count int COMMENT '期初库存数量总计' AFTER business_time;
ALTER TABLE otc_inventory_day_part ADD COLUMN period_come_count int COMMENT '本期购进' AFTER business_time;
ALTER TABLE otc_inventory_day_part ADD COLUMN out_commerce_count int COMMENT '流到商业' AFTER business_time;
ALTER TABLE otc_inventory_day_part ADD COLUMN out_terminal_count int COMMENT '流到终端' AFTER business_time;
ALTER TABLE otc_inventory_day_part ADD COLUMN unit VARCHAR(10) COMMENT '计价单位' AFTER business_time;

ALTER TABLE otc_inventory_day_head ADD COLUMN begin_warehouse_num_count int COMMENT '期初库存数量总计' AFTER business_time;
ALTER TABLE otc_inventory_day_head ADD COLUMN period_come_count int COMMENT '本期购进' AFTER business_time;
ALTER TABLE otc_inventory_day_head ADD COLUMN out_commerce_count int COMMENT '流到商业' AFTER business_time;
ALTER TABLE otc_inventory_day_head ADD COLUMN out_terminal_count int COMMENT '流到终端' AFTER business_time;
ALTER TABLE otc_inventory_day_head ADD COLUMN unit VARCHAR(10) COMMENT '计价单位' AFTER business_time;

ALTER TABLE otc_inventory_day_product ADD COLUMN begin_warehouse_num_count int COMMENT '期初库存数量总计' AFTER business_time;
ALTER TABLE otc_inventory_day_product ADD COLUMN period_come_count int COMMENT '本期购进' AFTER business_time;
ALTER TABLE otc_inventory_day_product ADD COLUMN out_commerce_count int COMMENT '流到商业' AFTER business_time;
ALTER TABLE otc_inventory_day_product ADD COLUMN out_terminal_count int COMMENT '流到终端' AFTER business_time;
ALTER TABLE otc_inventory_day_product ADD COLUMN unit VARCHAR(10) COMMENT '计价单位' AFTER business_time;
