<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.5</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>com.zilue</groupId>
    <artifactId>zilue-boot</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <name>zilue-boot</name>
    <description>zilue-boot</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>11</java.version>
        <commons.lang.version>2.6</commons.lang.version>
        <commons.fileupload.version>1.5</commons.fileupload.version>
        <commons.io.version>2.15.1</commons.io.version>
        <commons.codec.version>1.10</commons.codec.version>
        <commons.configuration.version>2.10.1</commons.configuration.version>
        <hutool.version>5.8.25</hutool.version>
        <satoken.version>1.34.0</satoken.version>
        <satoken.redis.version>1.34.0</satoken.redis.version>
        <lombok.version>1.18.4</lombok.version>
        <fastjson.version>1.2.83</fastjson.version>
        <joda.time.version>2.9.9</joda.time.version>
        <gson.version>2.8.9</gson.version>
        <mybatisplus.join.version>1.4.11</mybatisplus.join.version>
        <mybatisplus.version>3.5.6</mybatisplus.version>
        <mybatisplus.generator.version>3.5.7</mybatisplus.generator.version>
        <mybatisplus.dynamic.version>3.5.1</mybatisplus.dynamic.version>
        <druid.version>1.2.22</druid.version>
        <knife4j.version>2.0.7</knife4j.version>
        <freemarker.version>2.3.30</freemarker.version>
        <hibernatevalidator.version>6.0.20.Final</hibernatevalidator.version>
        <qiniu.version>7.2.23</qiniu.version>
        <aliyun.oss.version>2.8.3</aliyun.oss.version>
        <qcloud.cos.version>4.4</qcloud.cos.version>
        <huawei.obs.version>3.19.7</huawei.obs.version>
        <minio.oss.version>7.1.0</minio.oss.version>
        <jaxb.api.version>2.3.0</jaxb.api.version>
        <log4j.core.version>2.17.2</log4j.core.version>
        <xxl.job.version>2.4.1</xxl.job.version>
        <screw.version>1.0.5</screw.version>
        <ureport.version>2.2.9</ureport.version>
        <camunda.version>7.18.0</camunda.version>
        <groovy.version>3.0.17</groovy.version>
        <javax.mail.version>1.6.2</javax.mail.version>
        <magic.api.version>2.1.1</magic.api.version>
        <keycloak.version>20.0.1</keycloak.version>
        <aliyun.core.version>4.5.1</aliyun.core.version>
        <tencentcloud.version>3.1.1000</tencentcloud.version>
        <chatgpt.java.version>1.0.6</chatgpt.java.version>
        <okio.version>3.3.0</okio.version>
        <easyexcel.version>3.1.4</easyexcel.version>
        <poi.version>3.17</poi.version>
        <ooxml.schemas.version>1.3</ooxml.schemas.version>
        <javassist.version>3.29.2-GA</javassist.version>
        <liteflow.version>2.10.1</liteflow.version>
        <microsoft.version>8.4.1.jre8</microsoft.version>
        <pinyin4j.version>2.5.1</pinyin4j.version>
        <sms4j.version>2.1.0</sms4j.version>
        <license.version>2.0</license.version>
        <justauth.version>1.16.5</justauth.version>
        <dingtalk.version>2.0.26</dingtalk.version>
        <version.tomcat>9.0.81</version.tomcat>
        <clickhouse.version>0.3.2</clickhouse.version>
        <websocket.version>2.7.12</websocket.version>
        <!--<opcua.milo.version>3.0.5</opcua.milo.version>-->
        <netty.version>4.1.108.Final</netty.version>
        <logback.version>1.2.13</logback.version>
        <postgresql.version>42.3.8</postgresql.version>
        <scala.version>2.13.9</scala.version>
        <microsoft.version>8.4.1.jre8</microsoft.version>


    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.dom4j</groupId>
                <artifactId>dom4j</artifactId>
                <version>2.1.3</version>
            </dependency>
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>3.2.2</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>1.26.0</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>32.0.1-jre</version>
            </dependency>
            <dependency>
                <groupId>com.h2database</groupId>
                <artifactId>h2</artifactId>
                <version>2.2.220</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>2.16.0</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>2.16.0</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>2.16.0</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.jackson</groupId>
                <artifactId>jackson-mapper-asl</artifactId>
                <version>1.9.13</version>
            </dependency>
            <dependency>
                <groupId>com.jamesmurty.utils</groupId>
                <artifactId>java-xmlbuilder</artifactId>
                <version>1.2</version>
            </dependency>
            <dependency>
                <groupId>org.json</groupId>
                <artifactId>json</artifactId>
                <version>20230618</version>
            </dependency>
            <dependency>
                <groupId>org.keycloak</groupId>
                <artifactId>keycloak-common</artifactId>
                <version>25.0.2</version>
            </dependency>
            <dependency>
                <groupId>org.keycloak</groupId>
                <artifactId>keycloak-core</artifactId>
                <version>25.0.2</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-scratchpad</artifactId>
                <version>5.2.1</version>
            </dependency>
            <dependency>
                <groupId>org.scala-lang</groupId>
                <artifactId>scala-library</artifactId>
                <version>${scala.version}</version>
            </dependency>
            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>2.0</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot</artifactId>
                <version>2.7.17</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-core</artifactId>
                <version>5.3.27</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>5.3.27</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-webmvc</artifactId>
                <version>5.3.27</version>
            </dependency>
            <dependency>
                <groupId>org.testng</groupId>
                <artifactId>testng</artifactId>
                <version>7.5.1</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
<!--        <dependency>-->
<!--            <groupId>org.apache.velocity</groupId>-->
<!--            <artifactId>velocity-engine-core</artifactId>-->
<!--            <version>2.3</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>


        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-core</artifactId>
            <version>${version.tomcat}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-el</artifactId>
            <version>${version.tomcat}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-websocket</artifactId>
            <version>${version.tomcat}</version>
        </dependency>

        <!--引入MySql依赖-->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <version>8.3.0</version>
        </dependency>


        <!--引入hutool依赖-->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.version}</version>
        </dependency>

        <!--引入hutool依赖 邮件依赖-->
        <dependency>
            <groupId>com.sun.mail</groupId>
            <artifactId>javax.mail</artifactId>
            <version>${javax.mail.version}</version>
        </dependency>

        <!-- Sa-Token 权限认证, 在线文档：http://sa-token.dev33.cn/ -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-spring-boot-starter</artifactId>
            <version>${satoken.version}</version>
        </dependency>

        <!-- Sa-Token 整合 Redis （使用jdk默认序列化方式） -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-dao-redis</artifactId>
            <version>${satoken.redis.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-temp-jwt</artifactId>
            <version>${satoken.version}</version>
        </dependency>


        <!--引入Lombok依赖-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <optional>true</optional>
        </dependency>

        <!--引入mybatis-plus依赖-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybatisplus.version}</version>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>${mybatisplus.generator.version}</version>
        </dependency>

        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-spring-boot-starter</artifactId>
            <version>${knife4j.version}</version>
        </dependency>

        <!--引入fastjson依赖-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${fastjson.version}</version>
        </dependency>

        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>${commons.lang.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
            <version>${commons.fileupload.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>${commons.io.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>${commons.codec.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-configuration2</artifactId>
            <version>${commons.configuration.version}</version>
        </dependency>

        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>${joda.time.version}</version>
        </dependency>

        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>${gson.version}</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>${druid.version}</version>
        </dependency>

        <!--引入模板依赖-->
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
            <version>${freemarker.version}</version>
        </dependency>

        <!--引入注解验证依赖-->
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
            <version>${hibernatevalidator.version}</version>
        </dependency>

        <!-- 动态数据源 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
            <version>${mybatisplus.dynamic.version}</version>
        </dependency>

        <dependency>
            <groupId>com.qiniu</groupId>
            <artifactId>qiniu-java-sdk</artifactId>
            <version>${qiniu.version}</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>${aliyun.oss.version}</version>
            <exclusions>
                <exclusion>
                    <!--静止有漏洞jar-->
                    <groupId>org.jdom</groupId>
                    <artifactId>jdom</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <!--重新导入修复漏洞的jar-->
            <groupId>org.jdom</groupId>
            <artifactId>jdom2</artifactId>
            <version>2.0.6</version>
        </dependency>

        <dependency>
            <groupId>com.huaweicloud</groupId>
            <artifactId>esdk-obs-java</artifactId>
            <version>${huawei.obs.version}</version>
        </dependency>

        <dependency>
            <groupId>com.qcloud</groupId>
            <artifactId>cos_api</artifactId>
            <version>${qcloud.cos.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
            <version>${minio.oss.version}</version>
        </dependency>


        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <!--jdk8  可以不需要-->
        <dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
            <version>${jaxb.api.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
            <version>${log4j.core.version}</version>
        </dependency>

        <!-- mybatis-plus 多表关联 -->
        <dependency>
            <groupId>com.github.yulichang</groupId>
            <artifactId>mybatis-plus-join</artifactId>
            <version>${mybatisplus.join.version}</version>
        </dependency>

        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
            <version>${xxl.job.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.smallbun.screw</groupId>
            <artifactId>screw-core</artifactId>
            <version>${screw.version}</version>
        </dependency>

        <dependency>
            <groupId>com.syyai.spring.boot</groupId>
            <artifactId>ureport-spring-boot-starter</artifactId>
            <version>${ureport.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>dom4j</groupId>
                    <artifactId>dom4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-beanutils</groupId>
                    <artifactId>commons-beanutils-core</artifactId>
                </exclusion>
<!--                <exclusion>-->
<!--                    <groupId>org.apache.velocity</groupId>-->
<!--                    <artifactId>velocity</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>org.apache.poi</groupId>-->
<!--                    <artifactId>ooxml-schemas</artifactId>-->
<!--                </exclusion>-->
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.camunda.bpm.springboot</groupId>
            <artifactId>camunda-bpm-spring-boot-starter</artifactId>
            <version>${camunda.version}</version>
        </dependency>

        <dependency>
            <groupId>org.camunda.bpm.springboot</groupId>
            <artifactId>camunda-bpm-spring-boot-starter-webapp</artifactId>
            <version>${camunda.version}</version>
        </dependency>

        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-all</artifactId>
            <version>${groovy.version}</version>
            <classifier>sources</classifier>
            <type>java-source</type>
        </dependency>

        <!-- 钉钉用到的jar包 -->
        <dependency>
            <groupId>taobao-sdk-java-auto</groupId>
            <artifactId>taobao-sdk-java-auto</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/taobao-sdk-java-auto.jar</systemPath>
        </dependency>


        <dependency>
            <groupId>org.ssssssss</groupId>
            <artifactId>magic-api-spring-boot-starter</artifactId>
            <version>${magic.api.version}</version>
        </dependency>

        <!-- 腾讯云短信 -->
        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java</artifactId>
            <version>${tencentcloud.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.squareup.okhttp</groupId>
                    <artifactId>okhttp</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 阿里云短信 -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
            <version>${aliyun.core.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.ini4j</groupId>
                    <artifactId>ini4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- keycloak -->
        <dependency>
            <groupId>org.keycloak</groupId>
            <artifactId>keycloak-authz-client</artifactId>
            <version>${keycloak.version}</version>
        </dependency>

        <dependency>
            <groupId>com.unfbx</groupId>
            <artifactId>chatgpt-java</artifactId>
            <version>${chatgpt.java.version}</version>
        </dependency>

        <dependency>
            <groupId>com.squareup.okio</groupId>
            <artifactId>okio</artifactId>
            <version>${okio.version}</version>
        </dependency>

        <!-- excel导入导出 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>${easyexcel.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi-ooxml-schemas</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>${poi.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>${poi.version}</version>
        </dependency>


        <!--动态给类新增属性 的工具-->
        <dependency>
            <groupId>org.javassist</groupId>
            <artifactId>javassist</artifactId>
            <version>${javassist.version}</version>
        </dependency>

        <!-- 规则引擎-->
        <dependency>
            <groupId>com.yomahub</groupId>
            <artifactId>liteflow-spring-boot-starter</artifactId>
            <version>${liteflow.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>cn.hutool</groupId>
                    <artifactId>hutool-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 第三方拼音依赖包，配合 hutool-all 包中的 PinyinUtil 拼音工具使用 start -->
        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>${pinyin4j.version}</version>
        </dependency>
        <!-- 第三方拼音依赖包，配合 hutool-all 包中的 PinyinUtil 拼音工具使用 start -->

        <dependency>
            <groupId>org.dromara.sms4j</groupId>
            <artifactId>sms4j-spring-boot-starter</artifactId>
            <version>${sms4j.version}</version>
        </dependency>

        <dependency>
            <groupId>org.smartboot.license</groupId>
            <artifactId>license-client</artifactId>
            <version>${license.version}</version>
        </dependency>

        <dependency>
            <groupId>me.zhyd.oauth</groupId>
            <artifactId>JustAuth</artifactId>
            <version>${justauth.version}</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>dingtalk</artifactId>
            <version>${dingtalk.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.ini4j</groupId>
                    <artifactId>ini4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 引入postgres依赖 -->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <groupId>ru.yandex.clickhouse</groupId>
            <artifactId>clickhouse-jdbc</artifactId>
            <version>${clickhouse.version}</version>
        </dependency>

        <!-- websocket dependency -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
            <version>${websocket.version}</version>
        </dependency>

 <!--       <dependency>
            <groupId>com.kangaroohy</groupId>
            <artifactId>milo-spring-boot-starter</artifactId>
            <version>${opcua.milo.version}</version>
        </dependency>-->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>
        <dependency>
            <groupId>org.gavaghan</groupId>
            <artifactId>geodesy</artifactId>
            <version>1.1.3</version>
        </dependency>
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
            <version>${microsoft.version}</version>
        </dependency>

        <!--MongoDB依赖-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!--解开以下注释可以指定jdk版本-->
            <!--            <plugin>-->
            <!--                <groupId>org.apache.maven.plugins</groupId>-->
            <!--                <artifactId>maven-compiler-plugin</artifactId>-->
            <!--                <version>3.8.1</version>-->
            <!--                <configuration>-->
            <!--                    <source>1.8</source>-->
            <!--                    <target>1.8</target>-->
            <!--                    <encoding>${project.build.sourceEncoding}</encoding>-->
            <!--                </configuration>-->
            <!--            </plugin>-->

            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
            <!--解开以下注释  打包时 会使用 maven插件 上传到docker-->
            <!--            &lt;!&ndash;使用docker-maven-plugin插件&ndash;&gt;-->
            <!--            <plugin>-->
            <!--                <groupId>com.spotify</groupId>-->
            <!--                <artifactId>docker-maven-plugin</artifactId>-->
            <!--                <version>1.2.2</version>-->
            <!--                &lt;!&ndash;将插件绑定在某个phase执行&ndash;&gt;-->
            <!--                <executions>-->
            <!--                    <execution>-->
            <!--                        <id>build-image</id>-->
            <!--                        &lt;!&ndash;用户只需执行mvn package ，就会自动执行mvn docker:build&ndash;&gt;-->
            <!--                        <phase>package</phase>-->
            <!--                        <goals>-->
            <!--                            <goal>build</goal>-->
            <!--                        </goals>-->
            <!--                    </execution>-->
            <!--                </executions>-->
            <!--                <configuration>-->
            <!--                    &lt;!&ndash;指定生成的镜像名,这里是我们的作者名+项目名&ndash;&gt;-->
            <!--                    <imageName>${project.artifactId}</imageName>-->
            <!--                    &lt;!&ndash;指定标签 这里指定的是镜像的版本，我们默认版本是latest&ndash;&gt;-->
            <!--                    <imageTags>-->
            <!--                        <imageTag>latest</imageTag>-->
            <!--                    </imageTags>-->
            <!--                    &lt;!&ndash;指定基础镜像jdk11&ndash;&gt;-->
            <!--                    <baseImage>openjdk:11-jre</baseImage>-->
            <!--                    &lt;!&ndash;-->
            <!--                    镜像制作人本人信息&ndash;&gt;-->
            <!--                    <maintainer><EMAIL></maintainer>-->
            <!--                    &lt;!&ndash;切换到ROOT目录&ndash;&gt;-->
            <!--                    <workdir>/ROOT</workdir>-->
            <!--                    &lt;!&ndash;查看我们的java版本&ndash;&gt;-->
            <!--                    <cmd>["java", "-version"]</cmd>-->
            <!--                    &lt;!&ndash;${project.build.finalName}.jar是打包后生成的jar包的名字&ndash;&gt;-->
            <!--                    <entryPoint>["java", "-jar", "/${project.build.finalName}.jar"]</entryPoint>-->
            <!--                    &lt;!&ndash;指定远程 docker api地址&ndash;&gt;-->
            <!--                    <dockerHost>http://************:2375</dockerHost>-->
            <!--                    &lt;!&ndash; 这里是复制 jar 包到 docker 容器指定目录配置 &ndash;&gt;-->
            <!--                    <resources>-->
            <!--                        <resource>-->
            <!--                            <targetPath>/</targetPath>-->
            <!--                            &lt;!&ndash;jar 包所在的路径  此处配置的 即对应 target 目录&ndash;&gt;-->
            <!--                            <directory>${project.build.directory}</directory>-->
            <!--                            &lt;!&ndash;用于指定需要复制的文件 需要包含的 jar包 ，这里对应的是 Dockerfile中添加的文件名　&ndash;&gt;-->
            <!--                            <include>${project.build.finalName}.jar</include>-->
            <!--                        </resource>-->
            <!--                    </resources>-->

            <!--                </configuration>-->
            <!--            </plugin>-->

        </plugins>
    </build>

</project>
